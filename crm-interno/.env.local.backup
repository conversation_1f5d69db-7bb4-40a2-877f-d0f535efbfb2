# Google APIs Configuration
# Configurado com as credenciais reais do projeto CRM Criadores

# Google Sheets + Calendar API
GOOGLE_PROJECT_ID=crmcriadores
GOOGLE_PRIVATE_KEY_ID=a23a43d57506d684472491e9848273f0f295fc5d
*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
GOOGLE_CLIENT_EMAIL=<EMAIL>
GOOGLE_CLIENT_ID=113660609859941708871

# IDs específicos
GOOGLE_SPREADSHEET_ID=14yzga-y6A-3kae92Lr3knQGDaVVXMZv3tOggUL43dCI
GOOGLE_CALENDAR_ID=<EMAIL>

# ✅ CREDENCIAIS CONFIGURADAS!
#
# PRÓXIMOS PASSOS PARA ATIVAR A INTEGRAÇÃO:
#
# 1. ✅ CREDENCIAIS: Já configuradas com o arquivo JSON fornecido
#
# 2. 📊 COMPARTILHAR PLANILHA:
#    - Abra: https://docs.google.com/spreadsheets/d/14yzga-y6A-3kae92Lr3knQGDaVVXMZv3tOggUL43dCI/edit
#    - Clique em "Compartilhar"
#    - Adicione: <EMAIL>
#    - Permissão: "Editor"
#    - Desmarque: "Notificar pessoas"
#    - Clique: "Compartilhar"
#
# 3. 📋 ESTRUTURAR PLANILHA:
#    - Crie aba "Businesses"
#    - Adicione cabeçalhos: id, businessName, journeyStage, nextAction, contactDate, value, description, creators, campaigns
#    - Veja template em: TEMPLATE_PLANILHA.md
#
# 4. 📅 CONFIGURAR CALENDÁRIO (OPCIONAL):
#    - Crie calendário específico no Google Calendar
#    - Copie o Calendar ID
#    - Substitua GOOGLE_CALENDAR_ID acima
#    - Compartilhe calendário com: <EMAIL>
#
# 5. 🚀 TESTAR:
#    - Reinicie o servidor: npm run dev
#    - Acesse: http://localhost:3001/jornada
#    - Teste o drag & drop

# Google Sheets API Configuration
# Obtenha essas credenciais criando uma conta de serviço no Google Cloud Console
# e habilitando a Google Sheets API

GOOGLE_PROJECT_ID=your-project-id
GOOGLE_PRIVATE_KEY_ID=your-private-key-id
GOOGLE_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----\nyour-private-key-here\n-----END PRIVATE KEY-----"
GOOGLE_CLIENT_EMAIL=<EMAIL>
GOOGLE_CLIENT_ID=your-client-id
GOOGLE_SPREADSHEET_ID=your-spreadsheet-id

# Google Calendar API Configuration
GOOGLE_CALENDAR_ID=your-calendar-id

# Instruções:
# 1. V<PERSON> para https://console.cloud.google.com/
# 2. Crie um novo projeto ou selecione um existente
# 3. Habilite a Google Sheets API e Google Calendar API
# 4. Crie uma conta de serviço em "APIs & Services" > "Credentials"
# 5. Baixe o arquivo JSON da conta de serviço
# 6. Extraia os valores do JSON e cole aqui
# 7. Compartilhe sua planilha com o email da conta de serviço (GOOGLE_CLIENT_EMAIL)
# 8. Copie o ID da planilha da URL (parte entre /d/ e /edit)
# 9. Para o calendário, copie o ID do calendário das configurações do Google Calendar

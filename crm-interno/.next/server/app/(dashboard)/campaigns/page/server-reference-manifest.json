{"node": {"40416fe263e60966356356bb294165af55bf27c98c": {"workers": {"app/(dashboard)/campaigns/page": {"moduleId": "[project]/.next-internal/server/app/(dashboard)/campaigns/page/actions.js { ACTIONS_MODULE0 => \"[project]/app/actions/sheetsActions.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/app/actions/calendarActions.ts [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)", "async": false}}, "layer": {"app/(dashboard)/campaigns/page": "rsc"}}, "60b2397e35e4d22eb574fd297096247fb0b426b814": {"workers": {"app/(dashboard)/campaigns/page": {"moduleId": "[project]/.next-internal/server/app/(dashboard)/campaigns/page/actions.js { ACTIONS_MODULE0 => \"[project]/app/actions/sheetsActions.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/app/actions/calendarActions.ts [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)", "async": false}}, "layer": {"app/(dashboard)/campaigns/page": "rsc"}}, "700091b6dbbe7aa4a45146966c103d91060aef6152": {"workers": {"app/(dashboard)/campaigns/page": {"moduleId": "[project]/.next-internal/server/app/(dashboard)/campaigns/page/actions.js { ACTIONS_MODULE0 => \"[project]/app/actions/sheetsActions.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/app/actions/calendarActions.ts [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)", "async": false}}, "layer": {"app/(dashboard)/campaigns/page": "rsc"}}, "70c2e3b2963d1dbcaed9ada159154eae380101e11a": {"workers": {"app/(dashboard)/campaigns/page": {"moduleId": "[project]/.next-internal/server/app/(dashboard)/campaigns/page/actions.js { ACTIONS_MODULE0 => \"[project]/app/actions/sheetsActions.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/app/actions/calendarActions.ts [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)", "async": false}}, "layer": {"app/(dashboard)/campaigns/page": "rsc"}}, "4065d208bb127f3649bec43a1d30b211f5598ff96c": {"workers": {"app/(dashboard)/campaigns/page": {"moduleId": "[project]/.next-internal/server/app/(dashboard)/campaigns/page/actions.js { ACTIONS_MODULE0 => \"[project]/app/actions/sheetsActions.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/app/actions/calendarActions.ts [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)", "async": false}}, "layer": {"app/(dashboard)/campaigns/page": "rsc"}}, "406a8e6df362697922cdb6277d7cc5bfceb14b817a": {"workers": {"app/(dashboard)/campaigns/page": {"moduleId": "[project]/.next-internal/server/app/(dashboard)/campaigns/page/actions.js { ACTIONS_MODULE0 => \"[project]/app/actions/sheetsActions.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/app/actions/calendarActions.ts [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)", "async": false}}, "layer": {"app/(dashboard)/campaigns/page": "rsc"}}, "40b9886ee6eff374e831bc09f243f729deeddd84a3": {"workers": {"app/(dashboard)/campaigns/page": {"moduleId": "[project]/.next-internal/server/app/(dashboard)/campaigns/page/actions.js { ACTIONS_MODULE0 => \"[project]/app/actions/sheetsActions.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/app/actions/calendarActions.ts [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)", "async": false}}, "layer": {"app/(dashboard)/campaigns/page": "rsc"}}, "60866bed54a51640ac21f2293a398c31a013526ac7": {"workers": {"app/(dashboard)/campaigns/page": {"moduleId": "[project]/.next-internal/server/app/(dashboard)/campaigns/page/actions.js { ACTIONS_MODULE0 => \"[project]/app/actions/sheetsActions.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/app/actions/calendarActions.ts [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)", "async": false}}, "layer": {"app/(dashboard)/campaigns/page": "rsc"}}, "60a260f981de4be87965322f8cb81878793ff4836b": {"workers": {"app/(dashboard)/campaigns/page": {"moduleId": "[project]/.next-internal/server/app/(dashboard)/campaigns/page/actions.js { ACTIONS_MODULE0 => \"[project]/app/actions/sheetsActions.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/app/actions/calendarActions.ts [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)", "async": false}}, "layer": {"app/(dashboard)/campaigns/page": "rsc"}}}, "edge": {}}
{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/AI_Projects/CRMcriadores/crm-interno/node_modules/googleapis/build/src/apis/aiplatform/index.js"], "sourcesContent": ["\"use strict\";\n// Copyright 2020 Google LLC\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//    http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.AuthPlus = exports.aiplatform_v1beta1 = exports.aiplatform_v1 = exports.auth = exports.VERSIONS = void 0;\nexports.aiplatform = aiplatform;\n/*! THIS FILE IS AUTO-GENERATED */\nconst googleapis_common_1 = require(\"googleapis-common\");\nconst v1_1 = require(\"./v1\");\nObject.defineProperty(exports, \"aiplatform_v1\", { enumerable: true, get: function () { return v1_1.aiplatform_v1; } });\nconst v1beta1_1 = require(\"./v1beta1\");\nObject.defineProperty(exports, \"aiplatform_v1beta1\", { enumerable: true, get: function () { return v1beta1_1.aiplatform_v1beta1; } });\nexports.VERSIONS = {\n    v1: v1_1.aiplatform_v1.Aiplatform,\n    v1beta1: v1beta1_1.aiplatform_v1beta1.Aiplatform,\n};\nfunction aiplatform(versionOrOptions) {\n    return (0, googleapis_common_1.getAPI)('aiplatform', versionOrOptions, exports.VERSIONS, this);\n}\nconst auth = new googleapis_common_1.AuthPlus();\nexports.auth = auth;\nvar googleapis_common_2 = require(\"googleapis-common\");\nObject.defineProperty(exports, \"AuthPlus\", { enumerable: true, get: function () { return googleapis_common_2.AuthPlus; } });\n"], "names": [], "mappings": "AAAA;AACA,4BAA4B;AAC5B,kEAAkE;AAClE,mEAAmE;AACnE,0CAA0C;AAC1C,EAAE;AACF,gDAAgD;AAChD,EAAE;AACF,sEAAsE;AACtE,oEAAoE;AACpE,2EAA2E;AAC3E,sEAAsE;AACtE,iCAAiC;AACjC,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,QAAQ,QAAQ,GAAG,QAAQ,kBAAkB,GAAG,QAAQ,aAAa,GAAG,QAAQ,IAAI,GAAG,QAAQ,QAAQ,GAAG,KAAK;AAC/G,QAAQ,UAAU,GAAG;AACrB,gCAAgC,GAChC,MAAM;AACN,MAAM;AACN,OAAO,cAAc,CAAC,SAAS,iBAAiB;IAAE,YAAY;IAAM,KAAK;QAAc,OAAO,KAAK,aAAa;IAAE;AAAE;AACpH,MAAM;AACN,OAAO,cAAc,CAAC,SAAS,sBAAsB;IAAE,YAAY;IAAM,KAAK;QAAc,OAAO,UAAU,kBAAkB;IAAE;AAAE;AACnI,QAAQ,QAAQ,GAAG;IACf,IAAI,KAAK,aAAa,CAAC,UAAU;IACjC,SAAS,UAAU,kBAAkB,CAAC,UAAU;AACpD;AACA,SAAS,WAAW,gBAAgB;IAChC,OAAO,CAAC,GAAG,oBAAoB,MAAM,EAAE,cAAc,kBAAkB,QAAQ,QAAQ,EAAE,IAAI;AACjG;AACA,MAAM,OAAO,IAAI,oBAAoB,QAAQ;AAC7C,QAAQ,IAAI,GAAG;AACf,IAAI;AACJ,OAAO,cAAc,CAAC,SAAS,YAAY;IAAE,YAAY;IAAM,KAAK;QAAc,OAAO,oBAAoB,QAAQ;IAAE;AAAE", "ignoreList": [0], "debugId": null}}]}
{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/AI_Projects/CRMcriadores/crm-interno/node_modules/googleapis/build/src/apis/sqladmin/v1.js"], "sourcesContent": ["\"use strict\";\n// Copyright 2020 Google LLC\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//    http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.sqladmin_v1 = void 0;\n/* eslint-disable @typescript-eslint/no-explicit-any */\n/* eslint-disable @typescript-eslint/no-unused-vars */\n/* eslint-disable @typescript-eslint/no-empty-interface */\n/* eslint-disable @typescript-eslint/no-namespace */\n/* eslint-disable no-irregular-whitespace */\nconst googleapis_common_1 = require(\"googleapis-common\");\nvar sqladmin_v1;\n(function (sqladmin_v1) {\n    /**\n     * Cloud SQL Admin API\n     *\n     * API for Cloud SQL database instance management\n     *\n     * @example\n     * ```js\n     * const {google} = require('googleapis');\n     * const sqladmin = google.sqladmin('v1');\n     * ```\n     */\n    class Sqladmin {\n        context;\n        backupRuns;\n        Backups;\n        connect;\n        databases;\n        flags;\n        instances;\n        operations;\n        projects;\n        sslCerts;\n        tiers;\n        users;\n        constructor(options, google) {\n            this.context = {\n                _options: options || {},\n                google,\n            };\n            this.backupRuns = new Resource$Backupruns(this.context);\n            this.Backups = new Resource$Backups(this.context);\n            this.connect = new Resource$Connect(this.context);\n            this.databases = new Resource$Databases(this.context);\n            this.flags = new Resource$Flags(this.context);\n            this.instances = new Resource$Instances(this.context);\n            this.operations = new Resource$Operations(this.context);\n            this.projects = new Resource$Projects(this.context);\n            this.sslCerts = new Resource$Sslcerts(this.context);\n            this.tiers = new Resource$Tiers(this.context);\n            this.users = new Resource$Users(this.context);\n        }\n    }\n    sqladmin_v1.Sqladmin = Sqladmin;\n    class Resource$Backupruns {\n        context;\n        constructor(context) {\n            this.context = context;\n        }\n        delete(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://sqladmin.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl +\n                        '/v1/projects/{project}/instances/{instance}/backupRuns/{id}').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'DELETE',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['project', 'instance', 'id'],\n                pathParams: ['id', 'instance', 'project'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        get(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback || {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://sqladmin.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl +\n                        '/v1/projects/{project}/instances/{instance}/backupRuns/{id}').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'GET',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['project', 'instance', 'id'],\n                pathParams: ['id', 'instance', 'project'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        insert(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://sqladmin.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1/projects/{project}/instances/{instance}/backupRuns').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'POST',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['project', 'instance'],\n                pathParams: ['instance', 'project'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        list(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback || {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://sqladmin.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1/projects/{project}/instances/{instance}/backupRuns').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'GET',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['project', 'instance'],\n                pathParams: ['instance', 'project'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n    }\n    sqladmin_v1.Resource$Backupruns = Resource$Backupruns;\n    class Resource$Backups {\n        context;\n        constructor(context) {\n            this.context = context;\n        }\n        CreateBackup(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://sqladmin.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1/{+parent}/backups').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'POST',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['parent'],\n                pathParams: ['parent'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        DeleteBackup(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://sqladmin.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1/{+name}').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'DELETE',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['name'],\n                pathParams: ['name'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        GetBackup(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://sqladmin.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1/{+name}').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'GET',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['name'],\n                pathParams: ['name'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        ListBackups(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://sqladmin.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1/{+parent}/backups').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'GET',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['parent'],\n                pathParams: ['parent'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        UpdateBackup(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://sqladmin.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1/{+name}').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'PATCH',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['name'],\n                pathParams: ['name'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n    }\n    sqladmin_v1.Resource$Backups = Resource$Backups;\n    class Resource$Connect {\n        context;\n        constructor(context) {\n            this.context = context;\n        }\n        generateEphemeralCert(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://sqladmin.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl +\n                        '/v1/projects/{project}/instances/{instance}:generateEphemeralCert').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'POST',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['project', 'instance'],\n                pathParams: ['instance', 'project'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        get(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback || {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://sqladmin.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl +\n                        '/v1/projects/{project}/instances/{instance}/connectSettings').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'GET',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['project', 'instance'],\n                pathParams: ['instance', 'project'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n    }\n    sqladmin_v1.Resource$Connect = Resource$Connect;\n    class Resource$Databases {\n        context;\n        constructor(context) {\n            this.context = context;\n        }\n        delete(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback || {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://sqladmin.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl +\n                        '/v1/projects/{project}/instances/{instance}/databases/{database}').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'DELETE',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['project', 'instance', 'database'],\n                pathParams: ['database', 'instance', 'project'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        get(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback || {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://sqladmin.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl +\n                        '/v1/projects/{project}/instances/{instance}/databases/{database}').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'GET',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['project', 'instance', 'database'],\n                pathParams: ['database', 'instance', 'project'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        insert(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback || {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://sqladmin.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1/projects/{project}/instances/{instance}/databases').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'POST',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['project', 'instance'],\n                pathParams: ['instance', 'project'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        list(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback || {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://sqladmin.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1/projects/{project}/instances/{instance}/databases').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'GET',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['project', 'instance'],\n                pathParams: ['instance', 'project'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        patch(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback || {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://sqladmin.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl +\n                        '/v1/projects/{project}/instances/{instance}/databases/{database}').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'PATCH',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['project', 'instance', 'database'],\n                pathParams: ['database', 'instance', 'project'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        update(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback || {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://sqladmin.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl +\n                        '/v1/projects/{project}/instances/{instance}/databases/{database}').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'PUT',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['project', 'instance', 'database'],\n                pathParams: ['database', 'instance', 'project'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n    }\n    sqladmin_v1.Resource$Databases = Resource$Databases;\n    class Resource$Flags {\n        context;\n        constructor(context) {\n            this.context = context;\n        }\n        list(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback || {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://sqladmin.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1/flags').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'GET',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: [],\n                pathParams: [],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n    }\n    sqladmin_v1.Resource$Flags = Resource$Flags;\n    class Resource$Instances {\n        context;\n        constructor(context) {\n            this.context = context;\n        }\n        acquireSsrsLease(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://sqladmin.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl +\n                        '/v1/projects/{project}/instances/{instance}/acquireSsrsLease').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'POST',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['project', 'instance'],\n                pathParams: ['instance', 'project'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        addServerCa(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://sqladmin.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl +\n                        '/v1/projects/{project}/instances/{instance}/addServerCa').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'POST',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['project', 'instance'],\n                pathParams: ['instance', 'project'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        addServerCertificate(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://sqladmin.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl +\n                        '/v1/projects/{project}/instances/{instance}/addServerCertificate').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'POST',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['project', 'instance'],\n                pathParams: ['instance', 'project'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        clone(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback || {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://sqladmin.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1/projects/{project}/instances/{instance}/clone').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'POST',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['project', 'instance'],\n                pathParams: ['instance', 'project'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        delete(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback || {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://sqladmin.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1/projects/{project}/instances/{instance}').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'DELETE',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['project', 'instance'],\n                pathParams: ['instance', 'project'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        demote(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback || {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://sqladmin.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1/projects/{project}/instances/{instance}/demote').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'POST',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['project', 'instance'],\n                pathParams: ['instance', 'project'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        demoteMaster(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://sqladmin.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl +\n                        '/v1/projects/{project}/instances/{instance}/demoteMaster').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'POST',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['project', 'instance'],\n                pathParams: ['instance', 'project'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        export(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback || {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://sqladmin.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1/projects/{project}/instances/{instance}/export').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'POST',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['project', 'instance'],\n                pathParams: ['instance', 'project'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        failover(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://sqladmin.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1/projects/{project}/instances/{instance}/failover').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'POST',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['project', 'instance'],\n                pathParams: ['instance', 'project'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        get(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback || {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://sqladmin.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1/projects/{project}/instances/{instance}').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'GET',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['project', 'instance'],\n                pathParams: ['instance', 'project'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        import(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback || {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://sqladmin.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1/projects/{project}/instances/{instance}/import').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'POST',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['project', 'instance'],\n                pathParams: ['instance', 'project'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        insert(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback || {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://sqladmin.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1/projects/{project}/instances').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'POST',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['project'],\n                pathParams: ['project'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        list(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback || {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://sqladmin.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1/projects/{project}/instances').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'GET',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['project'],\n                pathParams: ['project'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        listServerCas(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://sqladmin.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl +\n                        '/v1/projects/{project}/instances/{instance}/listServerCas').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'GET',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['project', 'instance'],\n                pathParams: ['instance', 'project'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        ListServerCertificates(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://sqladmin.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl +\n                        '/v1/projects/{project}/instances/{instance}/listServerCertificates').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'GET',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['project', 'instance'],\n                pathParams: ['instance', 'project'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        patch(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback || {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://sqladmin.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1/projects/{project}/instances/{instance}').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'PATCH',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['project', 'instance'],\n                pathParams: ['instance', 'project'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        pointInTimeRestore(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://sqladmin.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1/{+parent}:pointInTimeRestore').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'POST',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['parent'],\n                pathParams: ['parent'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        promoteReplica(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://sqladmin.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl +\n                        '/v1/projects/{project}/instances/{instance}/promoteReplica').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'POST',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['project', 'instance'],\n                pathParams: ['instance', 'project'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        reencrypt(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://sqladmin.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1/projects/{project}/instances/{instance}/reencrypt').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'POST',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['project', 'instance'],\n                pathParams: ['instance', 'project'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        releaseSsrsLease(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://sqladmin.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl +\n                        '/v1/projects/{project}/instances/{instance}/releaseSsrsLease').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'POST',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['project', 'instance'],\n                pathParams: ['instance', 'project'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        resetSslConfig(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://sqladmin.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl +\n                        '/v1/projects/{project}/instances/{instance}/resetSslConfig').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'POST',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['project', 'instance'],\n                pathParams: ['instance', 'project'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        restart(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://sqladmin.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1/projects/{project}/instances/{instance}/restart').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'POST',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['project', 'instance'],\n                pathParams: ['instance', 'project'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        restoreBackup(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://sqladmin.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl +\n                        '/v1/projects/{project}/instances/{instance}/restoreBackup').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'POST',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['project', 'instance'],\n                pathParams: ['instance', 'project'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        rotateServerCa(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://sqladmin.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl +\n                        '/v1/projects/{project}/instances/{instance}/rotateServerCa').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'POST',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['project', 'instance'],\n                pathParams: ['instance', 'project'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        RotateServerCertificate(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://sqladmin.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl +\n                        '/v1/projects/{project}/instances/{instance}/rotateServerCertificate').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'POST',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['project', 'instance'],\n                pathParams: ['instance', 'project'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        startReplica(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://sqladmin.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl +\n                        '/v1/projects/{project}/instances/{instance}/startReplica').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'POST',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['project', 'instance'],\n                pathParams: ['instance', 'project'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        stopReplica(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://sqladmin.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl +\n                        '/v1/projects/{project}/instances/{instance}/stopReplica').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'POST',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['project', 'instance'],\n                pathParams: ['instance', 'project'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        switchover(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://sqladmin.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1/projects/{project}/instances/{instance}/switchover').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'POST',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['project', 'instance'],\n                pathParams: ['instance', 'project'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        truncateLog(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://sqladmin.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl +\n                        '/v1/projects/{project}/instances/{instance}/truncateLog').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'POST',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['project', 'instance'],\n                pathParams: ['instance', 'project'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        update(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback || {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://sqladmin.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1/projects/{project}/instances/{instance}').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'PUT',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['project', 'instance'],\n                pathParams: ['instance', 'project'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n    }\n    sqladmin_v1.Resource$Instances = Resource$Instances;\n    class Resource$Operations {\n        context;\n        constructor(context) {\n            this.context = context;\n        }\n        cancel(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://sqladmin.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1/projects/{project}/operations/{operation}/cancel').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'POST',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['project', 'operation'],\n                pathParams: ['operation', 'project'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        get(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback || {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://sqladmin.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1/projects/{project}/operations/{operation}').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'GET',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['project', 'operation'],\n                pathParams: ['operation', 'project'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        list(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback || {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://sqladmin.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1/projects/{project}/operations').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'GET',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['project'],\n                pathParams: ['project'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n    }\n    sqladmin_v1.Resource$Operations = Resource$Operations;\n    class Resource$Projects {\n        context;\n        instances;\n        constructor(context) {\n            this.context = context;\n            this.instances = new Resource$Projects$Instances(this.context);\n        }\n    }\n    sqladmin_v1.Resource$Projects = Resource$Projects;\n    class Resource$Projects$Instances {\n        context;\n        constructor(context) {\n            this.context = context;\n        }\n        getDiskShrinkConfig(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://sqladmin.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl +\n                        '/v1/projects/{project}/instances/{instance}/getDiskShrinkConfig').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'GET',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['project', 'instance'],\n                pathParams: ['instance', 'project'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        getLatestRecoveryTime(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://sqladmin.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl +\n                        '/v1/projects/{project}/instances/{instance}/getLatestRecoveryTime').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'GET',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['project', 'instance'],\n                pathParams: ['instance', 'project'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        performDiskShrink(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://sqladmin.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl +\n                        '/v1/projects/{project}/instances/{instance}/performDiskShrink').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'POST',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['project', 'instance'],\n                pathParams: ['instance', 'project'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        rescheduleMaintenance(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://sqladmin.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl +\n                        '/v1/projects/{project}/instances/{instance}/rescheduleMaintenance').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'POST',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['project', 'instance'],\n                pathParams: ['instance', 'project'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        resetReplicaSize(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://sqladmin.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl +\n                        '/v1/projects/{project}/instances/{instance}/resetReplicaSize').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'POST',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['project', 'instance'],\n                pathParams: ['instance', 'project'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        startExternalSync(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://sqladmin.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl +\n                        '/v1/projects/{project}/instances/{instance}/startExternalSync').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'POST',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['project', 'instance'],\n                pathParams: ['instance', 'project'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        verifyExternalSyncSettings(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params =\n                    {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://sqladmin.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl +\n                        '/v1/projects/{project}/instances/{instance}/verifyExternalSyncSettings').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'POST',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['project', 'instance'],\n                pathParams: ['instance', 'project'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n    }\n    sqladmin_v1.Resource$Projects$Instances = Resource$Projects$Instances;\n    class Resource$Sslcerts {\n        context;\n        constructor(context) {\n            this.context = context;\n        }\n        createEphemeral(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://sqladmin.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl +\n                        '/v1/projects/{project}/instances/{instance}/createEphemeral').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'POST',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['project', 'instance'],\n                pathParams: ['instance', 'project'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        delete(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback || {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://sqladmin.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl +\n                        '/v1/projects/{project}/instances/{instance}/sslCerts/{sha1Fingerprint}').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'DELETE',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['project', 'instance', 'sha1Fingerprint'],\n                pathParams: ['instance', 'project', 'sha1Fingerprint'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        get(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback || {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://sqladmin.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl +\n                        '/v1/projects/{project}/instances/{instance}/sslCerts/{sha1Fingerprint}').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'GET',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['project', 'instance', 'sha1Fingerprint'],\n                pathParams: ['instance', 'project', 'sha1Fingerprint'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        insert(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback || {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://sqladmin.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1/projects/{project}/instances/{instance}/sslCerts').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'POST',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['project', 'instance'],\n                pathParams: ['instance', 'project'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        list(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback || {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://sqladmin.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1/projects/{project}/instances/{instance}/sslCerts').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'GET',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['project', 'instance'],\n                pathParams: ['instance', 'project'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n    }\n    sqladmin_v1.Resource$Sslcerts = Resource$Sslcerts;\n    class Resource$Tiers {\n        context;\n        constructor(context) {\n            this.context = context;\n        }\n        list(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback || {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://sqladmin.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1/projects/{project}/tiers').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'GET',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['project'],\n                pathParams: ['project'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n    }\n    sqladmin_v1.Resource$Tiers = Resource$Tiers;\n    class Resource$Users {\n        context;\n        constructor(context) {\n            this.context = context;\n        }\n        delete(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback || {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://sqladmin.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1/projects/{project}/instances/{instance}/users').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'DELETE',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['project', 'instance'],\n                pathParams: ['instance', 'project'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        get(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback || {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://sqladmin.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl +\n                        '/v1/projects/{project}/instances/{instance}/users/{name}').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'GET',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['project', 'instance', 'name'],\n                pathParams: ['instance', 'name', 'project'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        insert(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback || {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://sqladmin.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1/projects/{project}/instances/{instance}/users').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'POST',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['project', 'instance'],\n                pathParams: ['instance', 'project'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        list(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback || {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://sqladmin.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1/projects/{project}/instances/{instance}/users').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'GET',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['project', 'instance'],\n                pathParams: ['instance', 'project'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        update(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback || {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://sqladmin.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1/projects/{project}/instances/{instance}/users').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'PUT',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['project', 'instance'],\n                pathParams: ['instance', 'project'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n    }\n    sqladmin_v1.Resource$Users = Resource$Users;\n})(sqladmin_v1 || (exports.sqladmin_v1 = sqladmin_v1 = {}));\n"], "names": [], "mappings": "AAAA;AACA,4BAA4B;AAC5B,kEAAkE;AAClE,mEAAmE;AACnE,0CAA0C;AAC1C,EAAE;AACF,gDAAgD;AAChD,EAAE;AACF,sEAAsE;AACtE,oEAAoE;AACpE,2EAA2E;AAC3E,sEAAsE;AACtE,iCAAiC;AACjC,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,QAAQ,WAAW,GAAG,KAAK;AAC3B,qDAAqD,GACrD,oDAAoD,GACpD,wDAAwD,GACxD,kDAAkD,GAClD,0CAA0C,GAC1C,MAAM;AACN,IAAI;AACJ,CAAC,SAAU,WAAW;IAClB;;;;;;;;;;KAUC,GACD,MAAM;QACF,QAAQ;QACR,WAAW;QACX,QAAQ;QACR,QAAQ;QACR,UAAU;QACV,MAAM;QACN,UAAU;QACV,WAAW;QACX,SAAS;QACT,SAAS;QACT,MAAM;QACN,MAAM;QACN,YAAY,OAAO,EAAE,MAAM,CAAE;YACzB,IAAI,CAAC,OAAO,GAAG;gBACX,UAAU,WAAW,CAAC;gBACtB;YACJ;YACA,IAAI,CAAC,UAAU,GAAG,IAAI,oBAAoB,IAAI,CAAC,OAAO;YACtD,IAAI,CAAC,OAAO,GAAG,IAAI,iBAAiB,IAAI,CAAC,OAAO;YAChD,IAAI,CAAC,OAAO,GAAG,IAAI,iBAAiB,IAAI,CAAC,OAAO;YAChD,IAAI,CAAC,SAAS,GAAG,IAAI,mBAAmB,IAAI,CAAC,OAAO;YACpD,IAAI,CAAC,KAAK,GAAG,IAAI,eAAe,IAAI,CAAC,OAAO;YAC5C,IAAI,CAAC,SAAS,GAAG,IAAI,mBAAmB,IAAI,CAAC,OAAO;YACpD,IAAI,CAAC,UAAU,GAAG,IAAI,oBAAoB,IAAI,CAAC,OAAO;YACtD,IAAI,CAAC,QAAQ,GAAG,IAAI,kBAAkB,IAAI,CAAC,OAAO;YAClD,IAAI,CAAC,QAAQ,GAAG,IAAI,kBAAkB,IAAI,CAAC,OAAO;YAClD,IAAI,CAAC,KAAK,GAAG,IAAI,eAAe,IAAI,CAAC,OAAO;YAC5C,IAAI,CAAC,KAAK,GAAG,IAAI,eAAe,IAAI,CAAC,OAAO;QAChD;IACJ;IACA,YAAY,QAAQ,GAAG;IACvB,MAAM;QACF,QAAQ;QACR,YAAY,OAAO,CAAE;YACjB,IAAI,CAAC,OAAO,GAAG;QACnB;QACA,OAAO,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAClD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UACF,6DAA6D,EAAE,OAAO,CAAC,gBAAgB;oBAC3F,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;oBAAW;oBAAY;iBAAK;gBAC7C,YAAY;oBAAC;oBAAM;oBAAY;iBAAU;gBACzC,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,IAAI,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAC/C,IAAI,SAAU,oBAAoB,CAAC;YACnC,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UACF,6DAA6D,EAAE,OAAO,CAAC,gBAAgB;oBAC3F,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;oBAAW;oBAAY;iBAAK;gBAC7C,YAAY;oBAAC;oBAAM;oBAAY;iBAAU;gBACzC,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,OAAO,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAClD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,wDAAwD,EAAE,OAAO,CAAC,gBAAgB;oBAClG,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;oBAAW;iBAAW;gBACvC,YAAY;oBAAC;oBAAY;iBAAU;gBACnC,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,KAAK,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAChD,IAAI,SAAU,oBAAoB,CAAC;YACnC,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,wDAAwD,EAAE,OAAO,CAAC,gBAAgB;oBAClG,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;oBAAW;iBAAW;gBACvC,YAAY;oBAAC;oBAAY;iBAAU;gBACnC,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;IACJ;IACA,YAAY,mBAAmB,GAAG;IAClC,MAAM;QACF,QAAQ;QACR,YAAY,OAAO,CAAE;YACjB,IAAI,CAAC,OAAO,GAAG;QACnB;QACA,aAAa,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YACxD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,uBAAuB,EAAE,OAAO,CAAC,gBAAgB;oBACjE,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAS;gBAC1B,YAAY;oBAAC;iBAAS;gBACtB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,aAAa,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YACxD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,aAAa,EAAE,OAAO,CAAC,gBAAgB;oBACvD,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAO;gBACxB,YAAY;oBAAC;iBAAO;gBACpB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,UAAU,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YACrD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,aAAa,EAAE,OAAO,CAAC,gBAAgB;oBACvD,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAO;gBACxB,YAAY;oBAAC;iBAAO;gBACpB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,YAAY,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YACvD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,uBAAuB,EAAE,OAAO,CAAC,gBAAgB;oBACjE,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAS;gBAC1B,YAAY;oBAAC;iBAAS;gBACtB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,aAAa,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YACxD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,aAAa,EAAE,OAAO,CAAC,gBAAgB;oBACvD,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAO;gBACxB,YAAY;oBAAC;iBAAO;gBACpB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;IACJ;IACA,YAAY,gBAAgB,GAAG;IAC/B,MAAM;QACF,QAAQ;QACR,YAAY,OAAO,CAAE;YACjB,IAAI,CAAC,OAAO,GAAG;QACnB;QACA,sBAAsB,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YACjE,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UACF,mEAAmE,EAAE,OAAO,CAAC,gBAAgB;oBACjG,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;oBAAW;iBAAW;gBACvC,YAAY;oBAAC;oBAAY;iBAAU;gBACnC,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,IAAI,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAC/C,IAAI,SAAU,oBAAoB,CAAC;YACnC,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UACF,6DAA6D,EAAE,OAAO,CAAC,gBAAgB;oBAC3F,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;oBAAW;iBAAW;gBACvC,YAAY;oBAAC;oBAAY;iBAAU;gBACnC,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;IACJ;IACA,YAAY,gBAAgB,GAAG;IAC/B,MAAM;QACF,QAAQ;QACR,YAAY,OAAO,CAAE;YACjB,IAAI,CAAC,OAAO,GAAG;QACnB;QACA,OAAO,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAClD,IAAI,SAAU,oBAAoB,CAAC;YACnC,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UACF,kEAAkE,EAAE,OAAO,CAAC,gBAAgB;oBAChG,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;oBAAW;oBAAY;iBAAW;gBACnD,YAAY;oBAAC;oBAAY;oBAAY;iBAAU;gBAC/C,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,IAAI,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAC/C,IAAI,SAAU,oBAAoB,CAAC;YACnC,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UACF,kEAAkE,EAAE,OAAO,CAAC,gBAAgB;oBAChG,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;oBAAW;oBAAY;iBAAW;gBACnD,YAAY;oBAAC;oBAAY;oBAAY;iBAAU;gBAC/C,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,OAAO,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAClD,IAAI,SAAU,oBAAoB,CAAC;YACnC,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,uDAAuD,EAAE,OAAO,CAAC,gBAAgB;oBACjG,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;oBAAW;iBAAW;gBACvC,YAAY;oBAAC;oBAAY;iBAAU;gBACnC,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,KAAK,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAChD,IAAI,SAAU,oBAAoB,CAAC;YACnC,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,uDAAuD,EAAE,OAAO,CAAC,gBAAgB;oBACjG,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;oBAAW;iBAAW;gBACvC,YAAY;oBAAC;oBAAY;iBAAU;gBACnC,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,MAAM,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YACjD,IAAI,SAAU,oBAAoB,CAAC;YACnC,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UACF,kEAAkE,EAAE,OAAO,CAAC,gBAAgB;oBAChG,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;oBAAW;oBAAY;iBAAW;gBACnD,YAAY;oBAAC;oBAAY;oBAAY;iBAAU;gBAC/C,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,OAAO,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAClD,IAAI,SAAU,oBAAoB,CAAC;YACnC,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UACF,kEAAkE,EAAE,OAAO,CAAC,gBAAgB;oBAChG,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;oBAAW;oBAAY;iBAAW;gBACnD,YAAY;oBAAC;oBAAY;oBAAY;iBAAU;gBAC/C,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;IACJ;IACA,YAAY,kBAAkB,GAAG;IACjC,MAAM;QACF,QAAQ;QACR,YAAY,OAAO,CAAE;YACjB,IAAI,CAAC,OAAO,GAAG;QACnB;QACA,KAAK,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAChD,IAAI,SAAU,oBAAoB,CAAC;YACnC,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,WAAW,EAAE,OAAO,CAAC,gBAAgB;oBACrD,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB,EAAE;gBAClB,YAAY,EAAE;gBACd,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;IACJ;IACA,YAAY,cAAc,GAAG;IAC7B,MAAM;QACF,QAAQ;QACR,YAAY,OAAO,CAAE;YACjB,IAAI,CAAC,OAAO,GAAG;QACnB;QACA,iBAAiB,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAC5D,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UACF,8DAA8D,EAAE,OAAO,CAAC,gBAAgB;oBAC5F,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;oBAAW;iBAAW;gBACvC,YAAY;oBAAC;oBAAY;iBAAU;gBACnC,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,YAAY,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YACvD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UACF,yDAAyD,EAAE,OAAO,CAAC,gBAAgB;oBACvF,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;oBAAW;iBAAW;gBACvC,YAAY;oBAAC;oBAAY;iBAAU;gBACnC,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,qBAAqB,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAChE,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UACF,kEAAkE,EAAE,OAAO,CAAC,gBAAgB;oBAChG,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;oBAAW;iBAAW;gBACvC,YAAY;oBAAC;oBAAY;iBAAU;gBACnC,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,MAAM,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YACjD,IAAI,SAAU,oBAAoB,CAAC;YACnC,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,mDAAmD,EAAE,OAAO,CAAC,gBAAgB;oBAC7F,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;oBAAW;iBAAW;gBACvC,YAAY;oBAAC;oBAAY;iBAAU;gBACnC,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,OAAO,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAClD,IAAI,SAAU,oBAAoB,CAAC;YACnC,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,6CAA6C,EAAE,OAAO,CAAC,gBAAgB;oBACvF,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;oBAAW;iBAAW;gBACvC,YAAY;oBAAC;oBAAY;iBAAU;gBACnC,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,OAAO,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAClD,IAAI,SAAU,oBAAoB,CAAC;YACnC,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,oDAAoD,EAAE,OAAO,CAAC,gBAAgB;oBAC9F,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;oBAAW;iBAAW;gBACvC,YAAY;oBAAC;oBAAY;iBAAU;gBACnC,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,aAAa,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YACxD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UACF,0DAA0D,EAAE,OAAO,CAAC,gBAAgB;oBACxF,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;oBAAW;iBAAW;gBACvC,YAAY;oBAAC;oBAAY;iBAAU;gBACnC,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,OAAO,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAClD,IAAI,SAAU,oBAAoB,CAAC;YACnC,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,oDAAoD,EAAE,OAAO,CAAC,gBAAgB;oBAC9F,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;oBAAW;iBAAW;gBACvC,YAAY;oBAAC;oBAAY;iBAAU;gBACnC,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,SAAS,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YACpD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,sDAAsD,EAAE,OAAO,CAAC,gBAAgB;oBAChG,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;oBAAW;iBAAW;gBACvC,YAAY;oBAAC;oBAAY;iBAAU;gBACnC,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,IAAI,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAC/C,IAAI,SAAU,oBAAoB,CAAC;YACnC,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,6CAA6C,EAAE,OAAO,CAAC,gBAAgB;oBACvF,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;oBAAW;iBAAW;gBACvC,YAAY;oBAAC;oBAAY;iBAAU;gBACnC,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,OAAO,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAClD,IAAI,SAAU,oBAAoB,CAAC;YACnC,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,oDAAoD,EAAE,OAAO,CAAC,gBAAgB;oBAC9F,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;oBAAW;iBAAW;gBACvC,YAAY;oBAAC;oBAAY;iBAAU;gBACnC,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,OAAO,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAClD,IAAI,SAAU,oBAAoB,CAAC;YACnC,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,kCAAkC,EAAE,OAAO,CAAC,gBAAgB;oBAC5E,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAU;gBAC3B,YAAY;oBAAC;iBAAU;gBACvB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,KAAK,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAChD,IAAI,SAAU,oBAAoB,CAAC;YACnC,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,kCAAkC,EAAE,OAAO,CAAC,gBAAgB;oBAC5E,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAU;gBAC3B,YAAY;oBAAC;iBAAU;gBACvB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,cAAc,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YACzD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UACF,2DAA2D,EAAE,OAAO,CAAC,gBAAgB;oBACzF,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;oBAAW;iBAAW;gBACvC,YAAY;oBAAC;oBAAY;iBAAU;gBACnC,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,uBAAuB,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAClE,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UACF,oEAAoE,EAAE,OAAO,CAAC,gBAAgB;oBAClG,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;oBAAW;iBAAW;gBACvC,YAAY;oBAAC;oBAAY;iBAAU;gBACnC,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,MAAM,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YACjD,IAAI,SAAU,oBAAoB,CAAC;YACnC,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,6CAA6C,EAAE,OAAO,CAAC,gBAAgB;oBACvF,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;oBAAW;iBAAW;gBACvC,YAAY;oBAAC;oBAAY;iBAAU;gBACnC,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,mBAAmB,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAC9D,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,kCAAkC,EAAE,OAAO,CAAC,gBAAgB;oBAC5E,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAS;gBAC1B,YAAY;oBAAC;iBAAS;gBACtB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,eAAe,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAC1D,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UACF,4DAA4D,EAAE,OAAO,CAAC,gBAAgB;oBAC1F,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;oBAAW;iBAAW;gBACvC,YAAY;oBAAC;oBAAY;iBAAU;gBACnC,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,UAAU,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YACrD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,uDAAuD,EAAE,OAAO,CAAC,gBAAgB;oBACjG,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;oBAAW;iBAAW;gBACvC,YAAY;oBAAC;oBAAY;iBAAU;gBACnC,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,iBAAiB,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAC5D,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UACF,8DAA8D,EAAE,OAAO,CAAC,gBAAgB;oBAC5F,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;oBAAW;iBAAW;gBACvC,YAAY;oBAAC;oBAAY;iBAAU;gBACnC,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,eAAe,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAC1D,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UACF,4DAA4D,EAAE,OAAO,CAAC,gBAAgB;oBAC1F,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;oBAAW;iBAAW;gBACvC,YAAY;oBAAC;oBAAY;iBAAU;gBACnC,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,QAAQ,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YACnD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,qDAAqD,EAAE,OAAO,CAAC,gBAAgB;oBAC/F,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;oBAAW;iBAAW;gBACvC,YAAY;oBAAC;oBAAY;iBAAU;gBACnC,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,cAAc,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YACzD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UACF,2DAA2D,EAAE,OAAO,CAAC,gBAAgB;oBACzF,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;oBAAW;iBAAW;gBACvC,YAAY;oBAAC;oBAAY;iBAAU;gBACnC,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,eAAe,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAC1D,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UACF,4DAA4D,EAAE,OAAO,CAAC,gBAAgB;oBAC1F,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;oBAAW;iBAAW;gBACvC,YAAY;oBAAC;oBAAY;iBAAU;gBACnC,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,wBAAwB,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YACnE,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UACF,qEAAqE,EAAE,OAAO,CAAC,gBAAgB;oBACnG,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;oBAAW;iBAAW;gBACvC,YAAY;oBAAC;oBAAY;iBAAU;gBACnC,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,aAAa,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YACxD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UACF,0DAA0D,EAAE,OAAO,CAAC,gBAAgB;oBACxF,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;oBAAW;iBAAW;gBACvC,YAAY;oBAAC;oBAAY;iBAAU;gBACnC,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,YAAY,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YACvD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UACF,yDAAyD,EAAE,OAAO,CAAC,gBAAgB;oBACvF,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;oBAAW;iBAAW;gBACvC,YAAY;oBAAC;oBAAY;iBAAU;gBACnC,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,WAAW,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YACtD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,wDAAwD,EAAE,OAAO,CAAC,gBAAgB;oBAClG,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;oBAAW;iBAAW;gBACvC,YAAY;oBAAC;oBAAY;iBAAU;gBACnC,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,YAAY,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YACvD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UACF,yDAAyD,EAAE,OAAO,CAAC,gBAAgB;oBACvF,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;oBAAW;iBAAW;gBACvC,YAAY;oBAAC;oBAAY;iBAAU;gBACnC,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,OAAO,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAClD,IAAI,SAAU,oBAAoB,CAAC;YACnC,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,6CAA6C,EAAE,OAAO,CAAC,gBAAgB;oBACvF,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;oBAAW;iBAAW;gBACvC,YAAY;oBAAC;oBAAY;iBAAU;gBACnC,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;IACJ;IACA,YAAY,kBAAkB,GAAG;IACjC,MAAM;QACF,QAAQ;QACR,YAAY,OAAO,CAAE;YACjB,IAAI,CAAC,OAAO,GAAG;QACnB;QACA,OAAO,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAClD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,sDAAsD,EAAE,OAAO,CAAC,gBAAgB;oBAChG,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;oBAAW;iBAAY;gBACxC,YAAY;oBAAC;oBAAa;iBAAU;gBACpC,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,IAAI,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAC/C,IAAI,SAAU,oBAAoB,CAAC;YACnC,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,+CAA+C,EAAE,OAAO,CAAC,gBAAgB;oBACzF,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;oBAAW;iBAAY;gBACxC,YAAY;oBAAC;oBAAa;iBAAU;gBACpC,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,KAAK,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAChD,IAAI,SAAU,oBAAoB,CAAC;YACnC,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,mCAAmC,EAAE,OAAO,CAAC,gBAAgB;oBAC7E,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAU;gBAC3B,YAAY;oBAAC;iBAAU;gBACvB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;IACJ;IACA,YAAY,mBAAmB,GAAG;IAClC,MAAM;QACF,QAAQ;QACR,UAAU;QACV,YAAY,OAAO,CAAE;YACjB,IAAI,CAAC,OAAO,GAAG;YACf,IAAI,CAAC,SAAS,GAAG,IAAI,4BAA4B,IAAI,CAAC,OAAO;QACjE;IACJ;IACA,YAAY,iBAAiB,GAAG;IAChC,MAAM;QACF,QAAQ;QACR,YAAY,OAAO,CAAE;YACjB,IAAI,CAAC,OAAO,GAAG;QACnB;QACA,oBAAoB,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAC/D,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UACF,iEAAiE,EAAE,OAAO,CAAC,gBAAgB;oBAC/F,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;oBAAW;iBAAW;gBACvC,YAAY;oBAAC;oBAAY;iBAAU;gBACnC,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,sBAAsB,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YACjE,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UACF,mEAAmE,EAAE,OAAO,CAAC,gBAAgB;oBACjG,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;oBAAW;iBAAW;gBACvC,YAAY;oBAAC;oBAAY;iBAAU;gBACnC,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,kBAAkB,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAC7D,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UACF,+DAA+D,EAAE,OAAO,CAAC,gBAAgB;oBAC7F,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;oBAAW;iBAAW;gBACvC,YAAY;oBAAC;oBAAY;iBAAU;gBACnC,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,sBAAsB,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YACjE,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UACF,mEAAmE,EAAE,OAAO,CAAC,gBAAgB;oBACjG,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;oBAAW;iBAAW;gBACvC,YAAY;oBAAC;oBAAY;iBAAU;gBACnC,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,iBAAiB,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAC5D,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UACF,8DAA8D,EAAE,OAAO,CAAC,gBAAgB;oBAC5F,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;oBAAW;iBAAW;gBACvC,YAAY;oBAAC;oBAAY;iBAAU;gBACnC,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,kBAAkB,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAC7D,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UACF,+DAA+D,EAAE,OAAO,CAAC,gBAAgB;oBAC7F,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;oBAAW;iBAAW;gBACvC,YAAY;oBAAC;oBAAY;iBAAU;gBACnC,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,2BAA2B,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YACtE,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SACI,CAAC;gBACL,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UACF,wEAAwE,EAAE,OAAO,CAAC,gBAAgB;oBACtG,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;oBAAW;iBAAW;gBACvC,YAAY;oBAAC;oBAAY;iBAAU;gBACnC,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;IACJ;IACA,YAAY,2BAA2B,GAAG;IAC1C,MAAM;QACF,QAAQ;QACR,YAAY,OAAO,CAAE;YACjB,IAAI,CAAC,OAAO,GAAG;QACnB;QACA,gBAAgB,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAC3D,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UACF,6DAA6D,EAAE,OAAO,CAAC,gBAAgB;oBAC3F,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;oBAAW;iBAAW;gBACvC,YAAY;oBAAC;oBAAY;iBAAU;gBACnC,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,OAAO,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAClD,IAAI,SAAU,oBAAoB,CAAC;YACnC,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UACF,wEAAwE,EAAE,OAAO,CAAC,gBAAgB;oBACtG,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;oBAAW;oBAAY;iBAAkB;gBAC1D,YAAY;oBAAC;oBAAY;oBAAW;iBAAkB;gBACtD,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,IAAI,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAC/C,IAAI,SAAU,oBAAoB,CAAC;YACnC,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UACF,wEAAwE,EAAE,OAAO,CAAC,gBAAgB;oBACtG,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;oBAAW;oBAAY;iBAAkB;gBAC1D,YAAY;oBAAC;oBAAY;oBAAW;iBAAkB;gBACtD,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,OAAO,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAClD,IAAI,SAAU,oBAAoB,CAAC;YACnC,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,sDAAsD,EAAE,OAAO,CAAC,gBAAgB;oBAChG,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;oBAAW;iBAAW;gBACvC,YAAY;oBAAC;oBAAY;iBAAU;gBACnC,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,KAAK,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAChD,IAAI,SAAU,oBAAoB,CAAC;YACnC,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,sDAAsD,EAAE,OAAO,CAAC,gBAAgB;oBAChG,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;oBAAW;iBAAW;gBACvC,YAAY;oBAAC;oBAAY;iBAAU;gBACnC,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;IACJ;IACA,YAAY,iBAAiB,GAAG;IAChC,MAAM;QACF,QAAQ;QACR,YAAY,OAAO,CAAE;YACjB,IAAI,CAAC,OAAO,GAAG;QACnB;QACA,KAAK,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAChD,IAAI,SAAU,oBAAoB,CAAC;YACnC,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,8BAA8B,EAAE,OAAO,CAAC,gBAAgB;oBACxE,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAU;gBAC3B,YAAY;oBAAC;iBAAU;gBACvB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;IACJ;IACA,YAAY,cAAc,GAAG;IAC7B,MAAM;QACF,QAAQ;QACR,YAAY,OAAO,CAAE;YACjB,IAAI,CAAC,OAAO,GAAG;QACnB;QACA,OAAO,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAClD,IAAI,SAAU,oBAAoB,CAAC;YACnC,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,mDAAmD,EAAE,OAAO,CAAC,gBAAgB;oBAC7F,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;oBAAW;iBAAW;gBACvC,YAAY;oBAAC;oBAAY;iBAAU;gBACnC,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,IAAI,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAC/C,IAAI,SAAU,oBAAoB,CAAC;YACnC,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UACF,0DAA0D,EAAE,OAAO,CAAC,gBAAgB;oBACxF,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;oBAAW;oBAAY;iBAAO;gBAC/C,YAAY;oBAAC;oBAAY;oBAAQ;iBAAU;gBAC3C,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,OAAO,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAClD,IAAI,SAAU,oBAAoB,CAAC;YACnC,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,mDAAmD,EAAE,OAAO,CAAC,gBAAgB;oBAC7F,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;oBAAW;iBAAW;gBACvC,YAAY;oBAAC;oBAAY;iBAAU;gBACnC,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,KAAK,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAChD,IAAI,SAAU,oBAAoB,CAAC;YACnC,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,mDAAmD,EAAE,OAAO,CAAC,gBAAgB;oBAC7F,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;oBAAW;iBAAW;gBACvC,YAAY;oBAAC;oBAAY;iBAAU;gBACnC,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,OAAO,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAClD,IAAI,SAAU,oBAAoB,CAAC;YACnC,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,mDAAmD,EAAE,OAAO,CAAC,gBAAgB;oBAC7F,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;oBAAW;iBAAW;gBACvC,YAAY;oBAAC;oBAAY;iBAAU;gBACnC,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;IACJ;IACA,YAAY,cAAc,GAAG;AACjC,CAAC,EAAE,eAAe,CAAC,QAAQ,WAAW,GAAG,cAAc,CAAC,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2636, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/AI_Projects/CRMcriadores/crm-interno/node_modules/googleapis/build/src/apis/sqladmin/v1beta4.js"], "sourcesContent": ["\"use strict\";\n// Copyright 2020 Google LLC\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//    http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.sqladmin_v1beta4 = void 0;\n/* eslint-disable @typescript-eslint/no-explicit-any */\n/* eslint-disable @typescript-eslint/no-unused-vars */\n/* eslint-disable @typescript-eslint/no-empty-interface */\n/* eslint-disable @typescript-eslint/no-namespace */\n/* eslint-disable no-irregular-whitespace */\nconst googleapis_common_1 = require(\"googleapis-common\");\nvar sqladmin_v1beta4;\n(function (sqladmin_v1beta4) {\n    /**\n     * Cloud SQL Admin API\n     *\n     * API for Cloud SQL database instance management\n     *\n     * @example\n     * ```js\n     * const {google} = require('googleapis');\n     * const sqladmin = google.sqladmin('v1beta4');\n     * ```\n     */\n    class Sqladmin {\n        context;\n        backupRuns;\n        backups;\n        connect;\n        databases;\n        flags;\n        instances;\n        operations;\n        projects;\n        sslCerts;\n        tiers;\n        users;\n        constructor(options, google) {\n            this.context = {\n                _options: options || {},\n                google,\n            };\n            this.backupRuns = new Resource$Backupruns(this.context);\n            this.backups = new Resource$Backups(this.context);\n            this.connect = new Resource$Connect(this.context);\n            this.databases = new Resource$Databases(this.context);\n            this.flags = new Resource$Flags(this.context);\n            this.instances = new Resource$Instances(this.context);\n            this.operations = new Resource$Operations(this.context);\n            this.projects = new Resource$Projects(this.context);\n            this.sslCerts = new Resource$Sslcerts(this.context);\n            this.tiers = new Resource$Tiers(this.context);\n            this.users = new Resource$Users(this.context);\n        }\n    }\n    sqladmin_v1beta4.Sqladmin = Sqladmin;\n    class Resource$Backupruns {\n        context;\n        constructor(context) {\n            this.context = context;\n        }\n        delete(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://sqladmin.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl +\n                        '/sql/v1beta4/projects/{project}/instances/{instance}/backupRuns/{id}').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'DELETE',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['project', 'instance', 'id'],\n                pathParams: ['id', 'instance', 'project'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        get(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback || {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://sqladmin.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl +\n                        '/sql/v1beta4/projects/{project}/instances/{instance}/backupRuns/{id}').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'GET',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['project', 'instance', 'id'],\n                pathParams: ['id', 'instance', 'project'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        insert(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://sqladmin.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl +\n                        '/sql/v1beta4/projects/{project}/instances/{instance}/backupRuns').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'POST',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['project', 'instance'],\n                pathParams: ['instance', 'project'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        list(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback || {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://sqladmin.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl +\n                        '/sql/v1beta4/projects/{project}/instances/{instance}/backupRuns').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'GET',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['project', 'instance'],\n                pathParams: ['instance', 'project'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n    }\n    sqladmin_v1beta4.Resource$Backupruns = Resource$Backupruns;\n    class Resource$Backups {\n        context;\n        constructor(context) {\n            this.context = context;\n        }\n        createBackup(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://sqladmin.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/sql/v1beta4/{+parent}/backups').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'POST',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['parent'],\n                pathParams: ['parent'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        deleteBackup(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://sqladmin.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/sql/v1beta4/{+name}').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'DELETE',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['name'],\n                pathParams: ['name'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        getBackup(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://sqladmin.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/sql/v1beta4/{+name}').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'GET',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['name'],\n                pathParams: ['name'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        listBackups(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://sqladmin.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/sql/v1beta4/{+parent}/backups').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'GET',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['parent'],\n                pathParams: ['parent'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        updateBackup(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://sqladmin.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/sql/v1beta4/{+name}').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'PATCH',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['name'],\n                pathParams: ['name'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n    }\n    sqladmin_v1beta4.Resource$Backups = Resource$Backups;\n    class Resource$Connect {\n        context;\n        constructor(context) {\n            this.context = context;\n        }\n        generateEphemeralCert(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://sqladmin.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl +\n                        '/sql/v1beta4/projects/{project}/instances/{instance}:generateEphemeralCert').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'POST',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['project', 'instance'],\n                pathParams: ['instance', 'project'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        get(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback || {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://sqladmin.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl +\n                        '/sql/v1beta4/projects/{project}/instances/{instance}/connectSettings').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'GET',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['project', 'instance'],\n                pathParams: ['instance', 'project'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n    }\n    sqladmin_v1beta4.Resource$Connect = Resource$Connect;\n    class Resource$Databases {\n        context;\n        constructor(context) {\n            this.context = context;\n        }\n        delete(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback || {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://sqladmin.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl +\n                        '/sql/v1beta4/projects/{project}/instances/{instance}/databases/{database}').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'DELETE',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['project', 'instance', 'database'],\n                pathParams: ['database', 'instance', 'project'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        get(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback || {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://sqladmin.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl +\n                        '/sql/v1beta4/projects/{project}/instances/{instance}/databases/{database}').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'GET',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['project', 'instance', 'database'],\n                pathParams: ['database', 'instance', 'project'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        insert(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback || {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://sqladmin.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl +\n                        '/sql/v1beta4/projects/{project}/instances/{instance}/databases').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'POST',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['project', 'instance'],\n                pathParams: ['instance', 'project'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        list(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback || {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://sqladmin.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl +\n                        '/sql/v1beta4/projects/{project}/instances/{instance}/databases').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'GET',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['project', 'instance'],\n                pathParams: ['instance', 'project'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        patch(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback || {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://sqladmin.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl +\n                        '/sql/v1beta4/projects/{project}/instances/{instance}/databases/{database}').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'PATCH',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['project', 'instance', 'database'],\n                pathParams: ['database', 'instance', 'project'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        update(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback || {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://sqladmin.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl +\n                        '/sql/v1beta4/projects/{project}/instances/{instance}/databases/{database}').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'PUT',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['project', 'instance', 'database'],\n                pathParams: ['database', 'instance', 'project'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n    }\n    sqladmin_v1beta4.Resource$Databases = Resource$Databases;\n    class Resource$Flags {\n        context;\n        constructor(context) {\n            this.context = context;\n        }\n        list(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback || {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://sqladmin.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/sql/v1beta4/flags').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'GET',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: [],\n                pathParams: [],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n    }\n    sqladmin_v1beta4.Resource$Flags = Resource$Flags;\n    class Resource$Instances {\n        context;\n        constructor(context) {\n            this.context = context;\n        }\n        acquireSsrsLease(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://sqladmin.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl +\n                        '/sql/v1beta4/projects/{project}/instances/{instance}/acquireSsrsLease').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'POST',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['project', 'instance'],\n                pathParams: ['instance', 'project'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        addServerCa(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://sqladmin.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl +\n                        '/sql/v1beta4/projects/{project}/instances/{instance}/addServerCa').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'POST',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['project', 'instance'],\n                pathParams: ['instance', 'project'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        addServerCertificate(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://sqladmin.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl +\n                        '/sql/v1beta4/projects/{project}/instances/{instance}/addServerCertificate').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'POST',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['project', 'instance'],\n                pathParams: ['instance', 'project'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        clone(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback || {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://sqladmin.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl +\n                        '/sql/v1beta4/projects/{project}/instances/{instance}/clone').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'POST',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['project', 'instance'],\n                pathParams: ['instance', 'project'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        delete(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback || {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://sqladmin.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/sql/v1beta4/projects/{project}/instances/{instance}').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'DELETE',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['project', 'instance'],\n                pathParams: ['instance', 'project'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        demote(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback || {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://sqladmin.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl +\n                        '/sql/v1beta4/projects/{project}/instances/{instance}/demote').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'POST',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['project', 'instance'],\n                pathParams: ['instance', 'project'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        demoteMaster(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://sqladmin.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl +\n                        '/sql/v1beta4/projects/{project}/instances/{instance}/demoteMaster').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'POST',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['project', 'instance'],\n                pathParams: ['instance', 'project'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        export(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback || {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://sqladmin.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl +\n                        '/sql/v1beta4/projects/{project}/instances/{instance}/export').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'POST',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['project', 'instance'],\n                pathParams: ['instance', 'project'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        failover(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://sqladmin.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl +\n                        '/sql/v1beta4/projects/{project}/instances/{instance}/failover').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'POST',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['project', 'instance'],\n                pathParams: ['instance', 'project'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        get(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback || {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://sqladmin.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/sql/v1beta4/projects/{project}/instances/{instance}').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'GET',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['project', 'instance'],\n                pathParams: ['instance', 'project'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        import(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback || {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://sqladmin.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl +\n                        '/sql/v1beta4/projects/{project}/instances/{instance}/import').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'POST',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['project', 'instance'],\n                pathParams: ['instance', 'project'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        insert(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback || {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://sqladmin.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/sql/v1beta4/projects/{project}/instances').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'POST',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['project'],\n                pathParams: ['project'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        list(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback || {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://sqladmin.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/sql/v1beta4/projects/{project}/instances').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'GET',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['project'],\n                pathParams: ['project'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        listServerCas(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://sqladmin.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl +\n                        '/sql/v1beta4/projects/{project}/instances/{instance}/listServerCas').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'GET',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['project', 'instance'],\n                pathParams: ['instance', 'project'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        ListServerCertificates(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://sqladmin.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl +\n                        '/sql/v1beta4/projects/{project}/instances/{instance}/listServerCertificates').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'GET',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['project', 'instance'],\n                pathParams: ['instance', 'project'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        patch(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback || {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://sqladmin.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/sql/v1beta4/projects/{project}/instances/{instance}').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'PATCH',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['project', 'instance'],\n                pathParams: ['instance', 'project'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        pointInTimeRestore(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://sqladmin.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/sql/v1beta4/{+parent}:pointInTimeRestore').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'POST',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['parent'],\n                pathParams: ['parent'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        promoteReplica(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://sqladmin.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl +\n                        '/sql/v1beta4/projects/{project}/instances/{instance}/promoteReplica').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'POST',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['project', 'instance'],\n                pathParams: ['instance', 'project'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        reencrypt(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://sqladmin.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl +\n                        '/sql/v1beta4/projects/{project}/instances/{instance}/reencrypt').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'POST',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['project', 'instance'],\n                pathParams: ['instance', 'project'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        releaseSsrsLease(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://sqladmin.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl +\n                        '/sql/v1beta4/projects/{project}/instances/{instance}/releaseSsrsLease').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'POST',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['project', 'instance'],\n                pathParams: ['instance', 'project'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        resetSslConfig(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://sqladmin.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl +\n                        '/sql/v1beta4/projects/{project}/instances/{instance}/resetSslConfig').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'POST',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['project', 'instance'],\n                pathParams: ['instance', 'project'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        restart(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://sqladmin.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl +\n                        '/sql/v1beta4/projects/{project}/instances/{instance}/restart').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'POST',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['project', 'instance'],\n                pathParams: ['instance', 'project'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        restoreBackup(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://sqladmin.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl +\n                        '/sql/v1beta4/projects/{project}/instances/{instance}/restoreBackup').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'POST',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['project', 'instance'],\n                pathParams: ['instance', 'project'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        rotateServerCa(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://sqladmin.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl +\n                        '/sql/v1beta4/projects/{project}/instances/{instance}/rotateServerCa').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'POST',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['project', 'instance'],\n                pathParams: ['instance', 'project'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        RotateServerCertificate(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://sqladmin.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl +\n                        '/sql/v1beta4/projects/{project}/instances/{instance}/rotateServerCertificate').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'POST',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['project', 'instance'],\n                pathParams: ['instance', 'project'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        startReplica(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://sqladmin.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl +\n                        '/sql/v1beta4/projects/{project}/instances/{instance}/startReplica').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'POST',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['project', 'instance'],\n                pathParams: ['instance', 'project'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        stopReplica(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://sqladmin.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl +\n                        '/sql/v1beta4/projects/{project}/instances/{instance}/stopReplica').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'POST',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['project', 'instance'],\n                pathParams: ['instance', 'project'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        switchover(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://sqladmin.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl +\n                        '/sql/v1beta4/projects/{project}/instances/{instance}/switchover').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'POST',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['project', 'instance'],\n                pathParams: ['instance', 'project'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        truncateLog(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://sqladmin.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl +\n                        '/sql/v1beta4/projects/{project}/instances/{instance}/truncateLog').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'POST',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['project', 'instance'],\n                pathParams: ['instance', 'project'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        update(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback || {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://sqladmin.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/sql/v1beta4/projects/{project}/instances/{instance}').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'PUT',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['project', 'instance'],\n                pathParams: ['instance', 'project'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n    }\n    sqladmin_v1beta4.Resource$Instances = Resource$Instances;\n    class Resource$Operations {\n        context;\n        constructor(context) {\n            this.context = context;\n        }\n        cancel(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://sqladmin.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl +\n                        '/sql/v1beta4/projects/{project}/operations/{operation}/cancel').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'POST',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['project', 'operation'],\n                pathParams: ['operation', 'project'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        get(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback || {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://sqladmin.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/sql/v1beta4/projects/{project}/operations/{operation}').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'GET',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['project', 'operation'],\n                pathParams: ['operation', 'project'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        list(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback || {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://sqladmin.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/sql/v1beta4/projects/{project}/operations').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'GET',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['project'],\n                pathParams: ['project'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n    }\n    sqladmin_v1beta4.Resource$Operations = Resource$Operations;\n    class Resource$Projects {\n        context;\n        instances;\n        constructor(context) {\n            this.context = context;\n            this.instances = new Resource$Projects$Instances(this.context);\n        }\n    }\n    sqladmin_v1beta4.Resource$Projects = Resource$Projects;\n    class Resource$Projects$Instances {\n        context;\n        constructor(context) {\n            this.context = context;\n        }\n        getDiskShrinkConfig(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://sqladmin.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl +\n                        '/sql/v1beta4/projects/{project}/instances/{instance}/getDiskShrinkConfig').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'GET',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['project', 'instance'],\n                pathParams: ['instance', 'project'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        getLatestRecoveryTime(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://sqladmin.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl +\n                        '/sql/v1beta4/projects/{project}/instances/{instance}/getLatestRecoveryTime').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'GET',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['project', 'instance'],\n                pathParams: ['instance', 'project'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        performDiskShrink(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://sqladmin.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl +\n                        '/sql/v1beta4/projects/{project}/instances/{instance}/performDiskShrink').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'POST',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['project', 'instance'],\n                pathParams: ['instance', 'project'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        rescheduleMaintenance(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://sqladmin.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl +\n                        '/sql/v1beta4/projects/{project}/instances/{instance}/rescheduleMaintenance').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'POST',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['project', 'instance'],\n                pathParams: ['instance', 'project'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        resetReplicaSize(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://sqladmin.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl +\n                        '/sql/v1beta4/projects/{project}/instances/{instance}/resetReplicaSize').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'POST',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['project', 'instance'],\n                pathParams: ['instance', 'project'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        startExternalSync(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://sqladmin.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl +\n                        '/sql/v1beta4/projects/{project}/instances/{instance}/startExternalSync').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'POST',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['project', 'instance'],\n                pathParams: ['instance', 'project'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        verifyExternalSyncSettings(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params =\n                    {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://sqladmin.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl +\n                        '/sql/v1beta4/projects/{project}/instances/{instance}/verifyExternalSyncSettings').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'POST',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['project', 'instance'],\n                pathParams: ['instance', 'project'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n    }\n    sqladmin_v1beta4.Resource$Projects$Instances = Resource$Projects$Instances;\n    class Resource$Sslcerts {\n        context;\n        constructor(context) {\n            this.context = context;\n        }\n        createEphemeral(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://sqladmin.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl +\n                        '/sql/v1beta4/projects/{project}/instances/{instance}/createEphemeral').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'POST',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['project', 'instance'],\n                pathParams: ['instance', 'project'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        delete(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback || {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://sqladmin.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl +\n                        '/sql/v1beta4/projects/{project}/instances/{instance}/sslCerts/{sha1Fingerprint}').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'DELETE',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['project', 'instance', 'sha1Fingerprint'],\n                pathParams: ['instance', 'project', 'sha1Fingerprint'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        get(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback || {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://sqladmin.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl +\n                        '/sql/v1beta4/projects/{project}/instances/{instance}/sslCerts/{sha1Fingerprint}').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'GET',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['project', 'instance', 'sha1Fingerprint'],\n                pathParams: ['instance', 'project', 'sha1Fingerprint'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        insert(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback || {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://sqladmin.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl +\n                        '/sql/v1beta4/projects/{project}/instances/{instance}/sslCerts').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'POST',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['project', 'instance'],\n                pathParams: ['instance', 'project'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        list(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback || {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://sqladmin.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl +\n                        '/sql/v1beta4/projects/{project}/instances/{instance}/sslCerts').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'GET',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['project', 'instance'],\n                pathParams: ['instance', 'project'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n    }\n    sqladmin_v1beta4.Resource$Sslcerts = Resource$Sslcerts;\n    class Resource$Tiers {\n        context;\n        constructor(context) {\n            this.context = context;\n        }\n        list(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback || {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://sqladmin.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/sql/v1beta4/projects/{project}/tiers').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'GET',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['project'],\n                pathParams: ['project'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n    }\n    sqladmin_v1beta4.Resource$Tiers = Resource$Tiers;\n    class Resource$Users {\n        context;\n        constructor(context) {\n            this.context = context;\n        }\n        delete(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback || {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://sqladmin.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl +\n                        '/sql/v1beta4/projects/{project}/instances/{instance}/users').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'DELETE',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['project', 'instance'],\n                pathParams: ['instance', 'project'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        get(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback || {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://sqladmin.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl +\n                        '/sql/v1beta4/projects/{project}/instances/{instance}/users/{name}').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'GET',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['project', 'instance', 'name'],\n                pathParams: ['instance', 'name', 'project'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        insert(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback || {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://sqladmin.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl +\n                        '/sql/v1beta4/projects/{project}/instances/{instance}/users').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'POST',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['project', 'instance'],\n                pathParams: ['instance', 'project'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        list(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback || {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://sqladmin.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl +\n                        '/sql/v1beta4/projects/{project}/instances/{instance}/users').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'GET',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['project', 'instance'],\n                pathParams: ['instance', 'project'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        update(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback || {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://sqladmin.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl +\n                        '/sql/v1beta4/projects/{project}/instances/{instance}/users').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'PUT',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['project', 'instance'],\n                pathParams: ['instance', 'project'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n    }\n    sqladmin_v1beta4.Resource$Users = Resource$Users;\n})(sqladmin_v1beta4 || (exports.sqladmin_v1beta4 = sqladmin_v1beta4 = {}));\n"], "names": [], "mappings": "AAAA;AACA,4BAA4B;AAC5B,kEAAkE;AAClE,mEAAmE;AACnE,0CAA0C;AAC1C,EAAE;AACF,gDAAgD;AAChD,EAAE;AACF,sEAAsE;AACtE,oEAAoE;AACpE,2EAA2E;AAC3E,sEAAsE;AACtE,iCAAiC;AACjC,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,QAAQ,gBAAgB,GAAG,KAAK;AAChC,qDAAqD,GACrD,oDAAoD,GACpD,wDAAwD,GACxD,kDAAkD,GAClD,0CAA0C,GAC1C,MAAM;AACN,IAAI;AACJ,CAAC,SAAU,gBAAgB;IACvB;;;;;;;;;;KAUC,GACD,MAAM;QACF,QAAQ;QACR,WAAW;QACX,QAAQ;QACR,QAAQ;QACR,UAAU;QACV,MAAM;QACN,UAAU;QACV,WAAW;QACX,SAAS;QACT,SAAS;QACT,MAAM;QACN,MAAM;QACN,YAAY,OAAO,EAAE,MAAM,CAAE;YACzB,IAAI,CAAC,OAAO,GAAG;gBACX,UAAU,WAAW,CAAC;gBACtB;YACJ;YACA,IAAI,CAAC,UAAU,GAAG,IAAI,oBAAoB,IAAI,CAAC,OAAO;YACtD,IAAI,CAAC,OAAO,GAAG,IAAI,iBAAiB,IAAI,CAAC,OAAO;YAChD,IAAI,CAAC,OAAO,GAAG,IAAI,iBAAiB,IAAI,CAAC,OAAO;YAChD,IAAI,CAAC,SAAS,GAAG,IAAI,mBAAmB,IAAI,CAAC,OAAO;YACpD,IAAI,CAAC,KAAK,GAAG,IAAI,eAAe,IAAI,CAAC,OAAO;YAC5C,IAAI,CAAC,SAAS,GAAG,IAAI,mBAAmB,IAAI,CAAC,OAAO;YACpD,IAAI,CAAC,UAAU,GAAG,IAAI,oBAAoB,IAAI,CAAC,OAAO;YACtD,IAAI,CAAC,QAAQ,GAAG,IAAI,kBAAkB,IAAI,CAAC,OAAO;YAClD,IAAI,CAAC,QAAQ,GAAG,IAAI,kBAAkB,IAAI,CAAC,OAAO;YAClD,IAAI,CAAC,KAAK,GAAG,IAAI,eAAe,IAAI,CAAC,OAAO;YAC5C,IAAI,CAAC,KAAK,GAAG,IAAI,eAAe,IAAI,CAAC,OAAO;QAChD;IACJ;IACA,iBAAiB,QAAQ,GAAG;IAC5B,MAAM;QACF,QAAQ;QACR,YAAY,OAAO,CAAE;YACjB,IAAI,CAAC,OAAO,GAAG;QACnB;QACA,OAAO,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAClD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UACF,sEAAsE,EAAE,OAAO,CAAC,gBAAgB;oBACpG,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;oBAAW;oBAAY;iBAAK;gBAC7C,YAAY;oBAAC;oBAAM;oBAAY;iBAAU;gBACzC,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,IAAI,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAC/C,IAAI,SAAU,oBAAoB,CAAC;YACnC,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UACF,sEAAsE,EAAE,OAAO,CAAC,gBAAgB;oBACpG,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;oBAAW;oBAAY;iBAAK;gBAC7C,YAAY;oBAAC;oBAAM;oBAAY;iBAAU;gBACzC,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,OAAO,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAClD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UACF,iEAAiE,EAAE,OAAO,CAAC,gBAAgB;oBAC/F,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;oBAAW;iBAAW;gBACvC,YAAY;oBAAC;oBAAY;iBAAU;gBACnC,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,KAAK,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAChD,IAAI,SAAU,oBAAoB,CAAC;YACnC,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UACF,iEAAiE,EAAE,OAAO,CAAC,gBAAgB;oBAC/F,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;oBAAW;iBAAW;gBACvC,YAAY;oBAAC;oBAAY;iBAAU;gBACnC,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;IACJ;IACA,iBAAiB,mBAAmB,GAAG;IACvC,MAAM;QACF,QAAQ;QACR,YAAY,OAAO,CAAE;YACjB,IAAI,CAAC,OAAO,GAAG;QACnB;QACA,aAAa,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YACxD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,gCAAgC,EAAE,OAAO,CAAC,gBAAgB;oBAC1E,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAS;gBAC1B,YAAY;oBAAC;iBAAS;gBACtB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,aAAa,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YACxD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,sBAAsB,EAAE,OAAO,CAAC,gBAAgB;oBAChE,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAO;gBACxB,YAAY;oBAAC;iBAAO;gBACpB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,UAAU,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YACrD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,sBAAsB,EAAE,OAAO,CAAC,gBAAgB;oBAChE,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAO;gBACxB,YAAY;oBAAC;iBAAO;gBACpB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,YAAY,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YACvD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,gCAAgC,EAAE,OAAO,CAAC,gBAAgB;oBAC1E,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAS;gBAC1B,YAAY;oBAAC;iBAAS;gBACtB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,aAAa,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YACxD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,sBAAsB,EAAE,OAAO,CAAC,gBAAgB;oBAChE,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAO;gBACxB,YAAY;oBAAC;iBAAO;gBACpB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;IACJ;IACA,iBAAiB,gBAAgB,GAAG;IACpC,MAAM;QACF,QAAQ;QACR,YAAY,OAAO,CAAE;YACjB,IAAI,CAAC,OAAO,GAAG;QACnB;QACA,sBAAsB,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YACjE,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UACF,4EAA4E,EAAE,OAAO,CAAC,gBAAgB;oBAC1G,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;oBAAW;iBAAW;gBACvC,YAAY;oBAAC;oBAAY;iBAAU;gBACnC,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,IAAI,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAC/C,IAAI,SAAU,oBAAoB,CAAC;YACnC,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UACF,sEAAsE,EAAE,OAAO,CAAC,gBAAgB;oBACpG,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;oBAAW;iBAAW;gBACvC,YAAY;oBAAC;oBAAY;iBAAU;gBACnC,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;IACJ;IACA,iBAAiB,gBAAgB,GAAG;IACpC,MAAM;QACF,QAAQ;QACR,YAAY,OAAO,CAAE;YACjB,IAAI,CAAC,OAAO,GAAG;QACnB;QACA,OAAO,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAClD,IAAI,SAAU,oBAAoB,CAAC;YACnC,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UACF,2EAA2E,EAAE,OAAO,CAAC,gBAAgB;oBACzG,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;oBAAW;oBAAY;iBAAW;gBACnD,YAAY;oBAAC;oBAAY;oBAAY;iBAAU;gBAC/C,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,IAAI,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAC/C,IAAI,SAAU,oBAAoB,CAAC;YACnC,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UACF,2EAA2E,EAAE,OAAO,CAAC,gBAAgB;oBACzG,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;oBAAW;oBAAY;iBAAW;gBACnD,YAAY;oBAAC;oBAAY;oBAAY;iBAAU;gBAC/C,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,OAAO,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAClD,IAAI,SAAU,oBAAoB,CAAC;YACnC,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UACF,gEAAgE,EAAE,OAAO,CAAC,gBAAgB;oBAC9F,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;oBAAW;iBAAW;gBACvC,YAAY;oBAAC;oBAAY;iBAAU;gBACnC,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,KAAK,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAChD,IAAI,SAAU,oBAAoB,CAAC;YACnC,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UACF,gEAAgE,EAAE,OAAO,CAAC,gBAAgB;oBAC9F,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;oBAAW;iBAAW;gBACvC,YAAY;oBAAC;oBAAY;iBAAU;gBACnC,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,MAAM,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YACjD,IAAI,SAAU,oBAAoB,CAAC;YACnC,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UACF,2EAA2E,EAAE,OAAO,CAAC,gBAAgB;oBACzG,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;oBAAW;oBAAY;iBAAW;gBACnD,YAAY;oBAAC;oBAAY;oBAAY;iBAAU;gBAC/C,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,OAAO,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAClD,IAAI,SAAU,oBAAoB,CAAC;YACnC,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UACF,2EAA2E,EAAE,OAAO,CAAC,gBAAgB;oBACzG,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;oBAAW;oBAAY;iBAAW;gBACnD,YAAY;oBAAC;oBAAY;oBAAY;iBAAU;gBAC/C,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;IACJ;IACA,iBAAiB,kBAAkB,GAAG;IACtC,MAAM;QACF,QAAQ;QACR,YAAY,OAAO,CAAE;YACjB,IAAI,CAAC,OAAO,GAAG;QACnB;QACA,KAAK,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAChD,IAAI,SAAU,oBAAoB,CAAC;YACnC,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,oBAAoB,EAAE,OAAO,CAAC,gBAAgB;oBAC9D,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB,EAAE;gBAClB,YAAY,EAAE;gBACd,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;IACJ;IACA,iBAAiB,cAAc,GAAG;IAClC,MAAM;QACF,QAAQ;QACR,YAAY,OAAO,CAAE;YACjB,IAAI,CAAC,OAAO,GAAG;QACnB;QACA,iBAAiB,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAC5D,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UACF,uEAAuE,EAAE,OAAO,CAAC,gBAAgB;oBACrG,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;oBAAW;iBAAW;gBACvC,YAAY;oBAAC;oBAAY;iBAAU;gBACnC,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,YAAY,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YACvD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UACF,kEAAkE,EAAE,OAAO,CAAC,gBAAgB;oBAChG,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;oBAAW;iBAAW;gBACvC,YAAY;oBAAC;oBAAY;iBAAU;gBACnC,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,qBAAqB,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAChE,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UACF,2EAA2E,EAAE,OAAO,CAAC,gBAAgB;oBACzG,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;oBAAW;iBAAW;gBACvC,YAAY;oBAAC;oBAAY;iBAAU;gBACnC,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,MAAM,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YACjD,IAAI,SAAU,oBAAoB,CAAC;YACnC,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UACF,4DAA4D,EAAE,OAAO,CAAC,gBAAgB;oBAC1F,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;oBAAW;iBAAW;gBACvC,YAAY;oBAAC;oBAAY;iBAAU;gBACnC,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,OAAO,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAClD,IAAI,SAAU,oBAAoB,CAAC;YACnC,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,sDAAsD,EAAE,OAAO,CAAC,gBAAgB;oBAChG,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;oBAAW;iBAAW;gBACvC,YAAY;oBAAC;oBAAY;iBAAU;gBACnC,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,OAAO,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAClD,IAAI,SAAU,oBAAoB,CAAC;YACnC,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UACF,6DAA6D,EAAE,OAAO,CAAC,gBAAgB;oBAC3F,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;oBAAW;iBAAW;gBACvC,YAAY;oBAAC;oBAAY;iBAAU;gBACnC,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,aAAa,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YACxD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UACF,mEAAmE,EAAE,OAAO,CAAC,gBAAgB;oBACjG,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;oBAAW;iBAAW;gBACvC,YAAY;oBAAC;oBAAY;iBAAU;gBACnC,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,OAAO,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAClD,IAAI,SAAU,oBAAoB,CAAC;YACnC,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UACF,6DAA6D,EAAE,OAAO,CAAC,gBAAgB;oBAC3F,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;oBAAW;iBAAW;gBACvC,YAAY;oBAAC;oBAAY;iBAAU;gBACnC,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,SAAS,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YACpD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UACF,+DAA+D,EAAE,OAAO,CAAC,gBAAgB;oBAC7F,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;oBAAW;iBAAW;gBACvC,YAAY;oBAAC;oBAAY;iBAAU;gBACnC,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,IAAI,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAC/C,IAAI,SAAU,oBAAoB,CAAC;YACnC,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,sDAAsD,EAAE,OAAO,CAAC,gBAAgB;oBAChG,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;oBAAW;iBAAW;gBACvC,YAAY;oBAAC;oBAAY;iBAAU;gBACnC,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,OAAO,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAClD,IAAI,SAAU,oBAAoB,CAAC;YACnC,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UACF,6DAA6D,EAAE,OAAO,CAAC,gBAAgB;oBAC3F,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;oBAAW;iBAAW;gBACvC,YAAY;oBAAC;oBAAY;iBAAU;gBACnC,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,OAAO,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAClD,IAAI,SAAU,oBAAoB,CAAC;YACnC,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,2CAA2C,EAAE,OAAO,CAAC,gBAAgB;oBACrF,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAU;gBAC3B,YAAY;oBAAC;iBAAU;gBACvB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,KAAK,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAChD,IAAI,SAAU,oBAAoB,CAAC;YACnC,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,2CAA2C,EAAE,OAAO,CAAC,gBAAgB;oBACrF,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAU;gBAC3B,YAAY;oBAAC;iBAAU;gBACvB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,cAAc,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YACzD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UACF,oEAAoE,EAAE,OAAO,CAAC,gBAAgB;oBAClG,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;oBAAW;iBAAW;gBACvC,YAAY;oBAAC;oBAAY;iBAAU;gBACnC,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,uBAAuB,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAClE,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UACF,6EAA6E,EAAE,OAAO,CAAC,gBAAgB;oBAC3G,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;oBAAW;iBAAW;gBACvC,YAAY;oBAAC;oBAAY;iBAAU;gBACnC,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,MAAM,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YACjD,IAAI,SAAU,oBAAoB,CAAC;YACnC,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,sDAAsD,EAAE,OAAO,CAAC,gBAAgB;oBAChG,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;oBAAW;iBAAW;gBACvC,YAAY;oBAAC;oBAAY;iBAAU;gBACnC,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,mBAAmB,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAC9D,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,2CAA2C,EAAE,OAAO,CAAC,gBAAgB;oBACrF,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAS;gBAC1B,YAAY;oBAAC;iBAAS;gBACtB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,eAAe,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAC1D,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UACF,qEAAqE,EAAE,OAAO,CAAC,gBAAgB;oBACnG,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;oBAAW;iBAAW;gBACvC,YAAY;oBAAC;oBAAY;iBAAU;gBACnC,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,UAAU,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YACrD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UACF,gEAAgE,EAAE,OAAO,CAAC,gBAAgB;oBAC9F,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;oBAAW;iBAAW;gBACvC,YAAY;oBAAC;oBAAY;iBAAU;gBACnC,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,iBAAiB,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAC5D,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UACF,uEAAuE,EAAE,OAAO,CAAC,gBAAgB;oBACrG,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;oBAAW;iBAAW;gBACvC,YAAY;oBAAC;oBAAY;iBAAU;gBACnC,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,eAAe,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAC1D,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UACF,qEAAqE,EAAE,OAAO,CAAC,gBAAgB;oBACnG,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;oBAAW;iBAAW;gBACvC,YAAY;oBAAC;oBAAY;iBAAU;gBACnC,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,QAAQ,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YACnD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UACF,8DAA8D,EAAE,OAAO,CAAC,gBAAgB;oBAC5F,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;oBAAW;iBAAW;gBACvC,YAAY;oBAAC;oBAAY;iBAAU;gBACnC,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,cAAc,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YACzD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UACF,oEAAoE,EAAE,OAAO,CAAC,gBAAgB;oBAClG,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;oBAAW;iBAAW;gBACvC,YAAY;oBAAC;oBAAY;iBAAU;gBACnC,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,eAAe,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAC1D,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UACF,qEAAqE,EAAE,OAAO,CAAC,gBAAgB;oBACnG,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;oBAAW;iBAAW;gBACvC,YAAY;oBAAC;oBAAY;iBAAU;gBACnC,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,wBAAwB,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YACnE,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UACF,8EAA8E,EAAE,OAAO,CAAC,gBAAgB;oBAC5G,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;oBAAW;iBAAW;gBACvC,YAAY;oBAAC;oBAAY;iBAAU;gBACnC,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,aAAa,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YACxD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UACF,mEAAmE,EAAE,OAAO,CAAC,gBAAgB;oBACjG,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;oBAAW;iBAAW;gBACvC,YAAY;oBAAC;oBAAY;iBAAU;gBACnC,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,YAAY,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YACvD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UACF,kEAAkE,EAAE,OAAO,CAAC,gBAAgB;oBAChG,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;oBAAW;iBAAW;gBACvC,YAAY;oBAAC;oBAAY;iBAAU;gBACnC,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,WAAW,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YACtD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UACF,iEAAiE,EAAE,OAAO,CAAC,gBAAgB;oBAC/F,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;oBAAW;iBAAW;gBACvC,YAAY;oBAAC;oBAAY;iBAAU;gBACnC,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,YAAY,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YACvD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UACF,kEAAkE,EAAE,OAAO,CAAC,gBAAgB;oBAChG,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;oBAAW;iBAAW;gBACvC,YAAY;oBAAC;oBAAY;iBAAU;gBACnC,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,OAAO,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAClD,IAAI,SAAU,oBAAoB,CAAC;YACnC,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,sDAAsD,EAAE,OAAO,CAAC,gBAAgB;oBAChG,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;oBAAW;iBAAW;gBACvC,YAAY;oBAAC;oBAAY;iBAAU;gBACnC,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;IACJ;IACA,iBAAiB,kBAAkB,GAAG;IACtC,MAAM;QACF,QAAQ;QACR,YAAY,OAAO,CAAE;YACjB,IAAI,CAAC,OAAO,GAAG;QACnB;QACA,OAAO,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAClD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UACF,+DAA+D,EAAE,OAAO,CAAC,gBAAgB;oBAC7F,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;oBAAW;iBAAY;gBACxC,YAAY;oBAAC;oBAAa;iBAAU;gBACpC,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,IAAI,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAC/C,IAAI,SAAU,oBAAoB,CAAC;YACnC,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,wDAAwD,EAAE,OAAO,CAAC,gBAAgB;oBAClG,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;oBAAW;iBAAY;gBACxC,YAAY;oBAAC;oBAAa;iBAAU;gBACpC,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,KAAK,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAChD,IAAI,SAAU,oBAAoB,CAAC;YACnC,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,4CAA4C,EAAE,OAAO,CAAC,gBAAgB;oBACtF,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAU;gBAC3B,YAAY;oBAAC;iBAAU;gBACvB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;IACJ;IACA,iBAAiB,mBAAmB,GAAG;IACvC,MAAM;QACF,QAAQ;QACR,UAAU;QACV,YAAY,OAAO,CAAE;YACjB,IAAI,CAAC,OAAO,GAAG;YACf,IAAI,CAAC,SAAS,GAAG,IAAI,4BAA4B,IAAI,CAAC,OAAO;QACjE;IACJ;IACA,iBAAiB,iBAAiB,GAAG;IACrC,MAAM;QACF,QAAQ;QACR,YAAY,OAAO,CAAE;YACjB,IAAI,CAAC,OAAO,GAAG;QACnB;QACA,oBAAoB,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAC/D,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UACF,0EAA0E,EAAE,OAAO,CAAC,gBAAgB;oBACxG,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;oBAAW;iBAAW;gBACvC,YAAY;oBAAC;oBAAY;iBAAU;gBACnC,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,sBAAsB,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YACjE,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UACF,4EAA4E,EAAE,OAAO,CAAC,gBAAgB;oBAC1G,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;oBAAW;iBAAW;gBACvC,YAAY;oBAAC;oBAAY;iBAAU;gBACnC,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,kBAAkB,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAC7D,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UACF,wEAAwE,EAAE,OAAO,CAAC,gBAAgB;oBACtG,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;oBAAW;iBAAW;gBACvC,YAAY;oBAAC;oBAAY;iBAAU;gBACnC,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,sBAAsB,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YACjE,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UACF,4EAA4E,EAAE,OAAO,CAAC,gBAAgB;oBAC1G,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;oBAAW;iBAAW;gBACvC,YAAY;oBAAC;oBAAY;iBAAU;gBACnC,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,iBAAiB,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAC5D,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UACF,uEAAuE,EAAE,OAAO,CAAC,gBAAgB;oBACrG,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;oBAAW;iBAAW;gBACvC,YAAY;oBAAC;oBAAY;iBAAU;gBACnC,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,kBAAkB,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAC7D,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UACF,wEAAwE,EAAE,OAAO,CAAC,gBAAgB;oBACtG,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;oBAAW;iBAAW;gBACvC,YAAY;oBAAC;oBAAY;iBAAU;gBACnC,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,2BAA2B,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YACtE,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SACI,CAAC;gBACL,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UACF,iFAAiF,EAAE,OAAO,CAAC,gBAAgB;oBAC/G,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;oBAAW;iBAAW;gBACvC,YAAY;oBAAC;oBAAY;iBAAU;gBACnC,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;IACJ;IACA,iBAAiB,2BAA2B,GAAG;IAC/C,MAAM;QACF,QAAQ;QACR,YAAY,OAAO,CAAE;YACjB,IAAI,CAAC,OAAO,GAAG;QACnB;QACA,gBAAgB,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAC3D,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UACF,sEAAsE,EAAE,OAAO,CAAC,gBAAgB;oBACpG,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;oBAAW;iBAAW;gBACvC,YAAY;oBAAC;oBAAY;iBAAU;gBACnC,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,OAAO,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAClD,IAAI,SAAU,oBAAoB,CAAC;YACnC,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UACF,iFAAiF,EAAE,OAAO,CAAC,gBAAgB;oBAC/G,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;oBAAW;oBAAY;iBAAkB;gBAC1D,YAAY;oBAAC;oBAAY;oBAAW;iBAAkB;gBACtD,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,IAAI,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAC/C,IAAI,SAAU,oBAAoB,CAAC;YACnC,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UACF,iFAAiF,EAAE,OAAO,CAAC,gBAAgB;oBAC/G,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;oBAAW;oBAAY;iBAAkB;gBAC1D,YAAY;oBAAC;oBAAY;oBAAW;iBAAkB;gBACtD,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,OAAO,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAClD,IAAI,SAAU,oBAAoB,CAAC;YACnC,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UACF,+DAA+D,EAAE,OAAO,CAAC,gBAAgB;oBAC7F,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;oBAAW;iBAAW;gBACvC,YAAY;oBAAC;oBAAY;iBAAU;gBACnC,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,KAAK,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAChD,IAAI,SAAU,oBAAoB,CAAC;YACnC,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UACF,+DAA+D,EAAE,OAAO,CAAC,gBAAgB;oBAC7F,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;oBAAW;iBAAW;gBACvC,YAAY;oBAAC;oBAAY;iBAAU;gBACnC,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;IACJ;IACA,iBAAiB,iBAAiB,GAAG;IACrC,MAAM;QACF,QAAQ;QACR,YAAY,OAAO,CAAE;YACjB,IAAI,CAAC,OAAO,GAAG;QACnB;QACA,KAAK,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAChD,IAAI,SAAU,oBAAoB,CAAC;YACnC,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,uCAAuC,EAAE,OAAO,CAAC,gBAAgB;oBACjF,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAU;gBAC3B,YAAY;oBAAC;iBAAU;gBACvB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;IACJ;IACA,iBAAiB,cAAc,GAAG;IAClC,MAAM;QACF,QAAQ;QACR,YAAY,OAAO,CAAE;YACjB,IAAI,CAAC,OAAO,GAAG;QACnB;QACA,OAAO,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAClD,IAAI,SAAU,oBAAoB,CAAC;YACnC,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UACF,4DAA4D,EAAE,OAAO,CAAC,gBAAgB;oBAC1F,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;oBAAW;iBAAW;gBACvC,YAAY;oBAAC;oBAAY;iBAAU;gBACnC,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,IAAI,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAC/C,IAAI,SAAU,oBAAoB,CAAC;YACnC,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UACF,mEAAmE,EAAE,OAAO,CAAC,gBAAgB;oBACjG,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;oBAAW;oBAAY;iBAAO;gBAC/C,YAAY;oBAAC;oBAAY;oBAAQ;iBAAU;gBAC3C,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,OAAO,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAClD,IAAI,SAAU,oBAAoB,CAAC;YACnC,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UACF,4DAA4D,EAAE,OAAO,CAAC,gBAAgB;oBAC1F,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;oBAAW;iBAAW;gBACvC,YAAY;oBAAC;oBAAY;iBAAU;gBACnC,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,KAAK,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAChD,IAAI,SAAU,oBAAoB,CAAC;YACnC,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UACF,4DAA4D,EAAE,OAAO,CAAC,gBAAgB;oBAC1F,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;oBAAW;iBAAW;gBACvC,YAAY;oBAAC;oBAAY;iBAAU;gBACnC,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,OAAO,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAClD,IAAI,SAAU,oBAAoB,CAAC;YACnC,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UACF,4DAA4D,EAAE,OAAO,CAAC,gBAAgB;oBAC1F,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;oBAAW;iBAAW;gBACvC,YAAY;oBAAC;oBAAY;iBAAU;gBACnC,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;IACJ;IACA,iBAAiB,cAAc,GAAG;AACtC,CAAC,EAAE,oBAAoB,CAAC,QAAQ,gBAAgB,GAAG,mBAAmB,CAAC,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5266, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/AI_Projects/CRMcriadores/crm-interno/node_modules/googleapis/build/src/apis/sqladmin/index.js"], "sourcesContent": ["\"use strict\";\n// Copyright 2020 Google LLC\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//    http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.AuthPlus = exports.sqladmin_v1beta4 = exports.sqladmin_v1 = exports.auth = exports.VERSIONS = void 0;\nexports.sqladmin = sqladmin;\n/*! THIS FILE IS AUTO-GENERATED */\nconst googleapis_common_1 = require(\"googleapis-common\");\nconst v1_1 = require(\"./v1\");\nObject.defineProperty(exports, \"sqladmin_v1\", { enumerable: true, get: function () { return v1_1.sqladmin_v1; } });\nconst v1beta4_1 = require(\"./v1beta4\");\nObject.defineProperty(exports, \"sqladmin_v1beta4\", { enumerable: true, get: function () { return v1beta4_1.sqladmin_v1beta4; } });\nexports.VERSIONS = {\n    v1: v1_1.sqladmin_v1.Sqladmin,\n    v1beta4: v1beta4_1.sqladmin_v1beta4.Sqladmin,\n};\nfunction sqladmin(versionOrOptions) {\n    return (0, googleapis_common_1.getAPI)('sqladmin', versionOrOptions, exports.VERSIONS, this);\n}\nconst auth = new googleapis_common_1.AuthPlus();\nexports.auth = auth;\nvar googleapis_common_2 = require(\"googleapis-common\");\nObject.defineProperty(exports, \"AuthPlus\", { enumerable: true, get: function () { return googleapis_common_2.AuthPlus; } });\n"], "names": [], "mappings": "AAAA;AACA,4BAA4B;AAC5B,kEAAkE;AAClE,mEAAmE;AACnE,0CAA0C;AAC1C,EAAE;AACF,gDAAgD;AAChD,EAAE;AACF,sEAAsE;AACtE,oEAAoE;AACpE,2EAA2E;AAC3E,sEAAsE;AACtE,iCAAiC;AACjC,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,QAAQ,QAAQ,GAAG,QAAQ,gBAAgB,GAAG,QAAQ,WAAW,GAAG,QAAQ,IAAI,GAAG,QAAQ,QAAQ,GAAG,KAAK;AAC3G,QAAQ,QAAQ,GAAG;AACnB,gCAAgC,GAChC,MAAM;AACN,MAAM;AACN,OAAO,cAAc,CAAC,SAAS,eAAe;IAAE,YAAY;IAAM,KAAK;QAAc,OAAO,KAAK,WAAW;IAAE;AAAE;AAChH,MAAM;AACN,OAAO,cAAc,CAAC,SAAS,oBAAoB;IAAE,YAAY;IAAM,KAAK;QAAc,OAAO,UAAU,gBAAgB;IAAE;AAAE;AAC/H,QAAQ,QAAQ,GAAG;IACf,IAAI,KAAK,WAAW,CAAC,QAAQ;IAC7B,SAAS,UAAU,gBAAgB,CAAC,QAAQ;AAChD;AACA,SAAS,SAAS,gBAAgB;IAC9B,OAAO,CAAC,GAAG,oBAAoB,MAAM,EAAE,YAAY,kBAAkB,QAAQ,QAAQ,EAAE,IAAI;AAC/F;AACA,MAAM,OAAO,IAAI,oBAAoB,QAAQ;AAC7C,QAAQ,IAAI,GAAG;AACf,IAAI;AACJ,OAAO,cAAc,CAAC,SAAS,YAAY;IAAE,YAAY;IAAM,KAAK;QAAc,OAAO,oBAAoB,QAAQ;IAAE;AAAE", "ignoreList": [0], "debugId": null}}]}
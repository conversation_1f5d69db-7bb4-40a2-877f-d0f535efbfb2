module.exports = {

"[project]/node_modules/googleapis/build/src/apis/dns/v1.js [app-rsc] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
// Copyright 2020 Google LLC
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//    http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.dns_v1 = void 0;
/* eslint-disable @typescript-eslint/no-explicit-any */ /* eslint-disable @typescript-eslint/no-unused-vars */ /* eslint-disable @typescript-eslint/no-empty-interface */ /* eslint-disable @typescript-eslint/no-namespace */ /* eslint-disable no-irregular-whitespace */ const googleapis_common_1 = __turbopack_context__.r("[project]/node_modules/googleapis-common/build/src/index.js [app-rsc] (ecmascript)");
var dns_v1;
(function(dns_v1) {
    /**
     * Cloud DNS API
     *
     *
     *
     * @example
     * ```js
     * const {google} = require('googleapis');
     * const dns = google.dns('v1');
     * ```
     */ class Dns {
        context;
        changes;
        dnsKeys;
        managedZoneOperations;
        managedZones;
        policies;
        projects;
        resourceRecordSets;
        responsePolicies;
        responsePolicyRules;
        constructor(options, google){
            this.context = {
                _options: options || {},
                google
            };
            this.changes = new Resource$Changes(this.context);
            this.dnsKeys = new Resource$Dnskeys(this.context);
            this.managedZoneOperations = new Resource$Managedzoneoperations(this.context);
            this.managedZones = new Resource$Managedzones(this.context);
            this.policies = new Resource$Policies(this.context);
            this.projects = new Resource$Projects(this.context);
            this.resourceRecordSets = new Resource$Resourcerecordsets(this.context);
            this.responsePolicies = new Resource$Responsepolicies(this.context);
            this.responsePolicyRules = new Resource$Responsepolicyrules(this.context);
        }
    }
    dns_v1.Dns = Dns;
    class Resource$Changes {
        context;
        constructor(context){
            this.context = context;
        }
        create(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://dns.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/dns/v1/projects/{project}/managedZones/{managedZone}/changes').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'POST',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'project',
                    'managedZone'
                ],
                pathParams: [
                    'managedZone',
                    'project'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        get(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://dns.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/dns/v1/projects/{project}/managedZones/{managedZone}/changes/{changeId}').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'GET',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'project',
                    'managedZone',
                    'changeId'
                ],
                pathParams: [
                    'changeId',
                    'managedZone',
                    'project'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        list(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://dns.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/dns/v1/projects/{project}/managedZones/{managedZone}/changes').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'GET',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'project',
                    'managedZone'
                ],
                pathParams: [
                    'managedZone',
                    'project'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
    }
    dns_v1.Resource$Changes = Resource$Changes;
    class Resource$Dnskeys {
        context;
        constructor(context){
            this.context = context;
        }
        get(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://dns.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/dns/v1/projects/{project}/managedZones/{managedZone}/dnsKeys/{dnsKeyId}').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'GET',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'project',
                    'managedZone',
                    'dnsKeyId'
                ],
                pathParams: [
                    'dnsKeyId',
                    'managedZone',
                    'project'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        list(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://dns.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/dns/v1/projects/{project}/managedZones/{managedZone}/dnsKeys').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'GET',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'project',
                    'managedZone'
                ],
                pathParams: [
                    'managedZone',
                    'project'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
    }
    dns_v1.Resource$Dnskeys = Resource$Dnskeys;
    class Resource$Managedzoneoperations {
        context;
        constructor(context){
            this.context = context;
        }
        get(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://dns.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/dns/v1/projects/{project}/managedZones/{managedZone}/operations/{operation}').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'GET',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'project',
                    'managedZone',
                    'operation'
                ],
                pathParams: [
                    'managedZone',
                    'operation',
                    'project'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        list(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://dns.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/dns/v1/projects/{project}/managedZones/{managedZone}/operations').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'GET',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'project',
                    'managedZone'
                ],
                pathParams: [
                    'managedZone',
                    'project'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
    }
    dns_v1.Resource$Managedzoneoperations = Resource$Managedzoneoperations;
    class Resource$Managedzones {
        context;
        constructor(context){
            this.context = context;
        }
        create(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://dns.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/dns/v1/projects/{project}/managedZones').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'POST',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'project'
                ],
                pathParams: [
                    'project'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        delete(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://dns.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/dns/v1/projects/{project}/managedZones/{managedZone}').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'DELETE',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'project',
                    'managedZone'
                ],
                pathParams: [
                    'managedZone',
                    'project'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        get(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://dns.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/dns/v1/projects/{project}/managedZones/{managedZone}').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'GET',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'project',
                    'managedZone'
                ],
                pathParams: [
                    'managedZone',
                    'project'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        getIamPolicy(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://dns.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/dns/v1/{+resource}:getIamPolicy').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'POST',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'resource'
                ],
                pathParams: [
                    'resource'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        list(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://dns.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/dns/v1/projects/{project}/managedZones').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'GET',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'project'
                ],
                pathParams: [
                    'project'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        patch(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://dns.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/dns/v1/projects/{project}/managedZones/{managedZone}').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'PATCH',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'project',
                    'managedZone'
                ],
                pathParams: [
                    'managedZone',
                    'project'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        setIamPolicy(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://dns.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/dns/v1/{+resource}:setIamPolicy').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'POST',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'resource'
                ],
                pathParams: [
                    'resource'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        testIamPermissions(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://dns.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/dns/v1/{+resource}:testIamPermissions').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'POST',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'resource'
                ],
                pathParams: [
                    'resource'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        update(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://dns.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/dns/v1/projects/{project}/managedZones/{managedZone}').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'PUT',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'project',
                    'managedZone'
                ],
                pathParams: [
                    'managedZone',
                    'project'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
    }
    dns_v1.Resource$Managedzones = Resource$Managedzones;
    class Resource$Policies {
        context;
        constructor(context){
            this.context = context;
        }
        create(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://dns.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/dns/v1/projects/{project}/policies').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'POST',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'project'
                ],
                pathParams: [
                    'project'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        delete(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://dns.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/dns/v1/projects/{project}/policies/{policy}').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'DELETE',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'project',
                    'policy'
                ],
                pathParams: [
                    'policy',
                    'project'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        get(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://dns.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/dns/v1/projects/{project}/policies/{policy}').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'GET',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'project',
                    'policy'
                ],
                pathParams: [
                    'policy',
                    'project'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        list(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://dns.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/dns/v1/projects/{project}/policies').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'GET',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'project'
                ],
                pathParams: [
                    'project'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        patch(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://dns.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/dns/v1/projects/{project}/policies/{policy}').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'PATCH',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'project',
                    'policy'
                ],
                pathParams: [
                    'policy',
                    'project'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        update(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://dns.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/dns/v1/projects/{project}/policies/{policy}').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'PUT',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'project',
                    'policy'
                ],
                pathParams: [
                    'policy',
                    'project'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
    }
    dns_v1.Resource$Policies = Resource$Policies;
    class Resource$Projects {
        context;
        constructor(context){
            this.context = context;
        }
        get(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://dns.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/dns/v1/projects/{project}').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'GET',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'project'
                ],
                pathParams: [
                    'project'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
    }
    dns_v1.Resource$Projects = Resource$Projects;
    class Resource$Resourcerecordsets {
        context;
        constructor(context){
            this.context = context;
        }
        create(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://dns.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/dns/v1/projects/{project}/managedZones/{managedZone}/rrsets').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'POST',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'project',
                    'managedZone'
                ],
                pathParams: [
                    'managedZone',
                    'project'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        delete(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://dns.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/dns/v1/projects/{project}/managedZones/{managedZone}/rrsets/{name}/{type}').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'DELETE',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'project',
                    'managedZone',
                    'name',
                    'type'
                ],
                pathParams: [
                    'managedZone',
                    'name',
                    'project',
                    'type'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        get(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://dns.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/dns/v1/projects/{project}/managedZones/{managedZone}/rrsets/{name}/{type}').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'GET',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'project',
                    'managedZone',
                    'name',
                    'type'
                ],
                pathParams: [
                    'managedZone',
                    'name',
                    'project',
                    'type'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        list(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://dns.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/dns/v1/projects/{project}/managedZones/{managedZone}/rrsets').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'GET',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'project',
                    'managedZone'
                ],
                pathParams: [
                    'managedZone',
                    'project'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        patch(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://dns.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/dns/v1/projects/{project}/managedZones/{managedZone}/rrsets/{name}/{type}').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'PATCH',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'project',
                    'managedZone',
                    'name',
                    'type'
                ],
                pathParams: [
                    'managedZone',
                    'name',
                    'project',
                    'type'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
    }
    dns_v1.Resource$Resourcerecordsets = Resource$Resourcerecordsets;
    class Resource$Responsepolicies {
        context;
        constructor(context){
            this.context = context;
        }
        create(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://dns.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/dns/v1/projects/{project}/responsePolicies').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'POST',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'project'
                ],
                pathParams: [
                    'project'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        delete(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://dns.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/dns/v1/projects/{project}/responsePolicies/{responsePolicy}').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'DELETE',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'project',
                    'responsePolicy'
                ],
                pathParams: [
                    'project',
                    'responsePolicy'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        get(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://dns.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/dns/v1/projects/{project}/responsePolicies/{responsePolicy}').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'GET',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'project',
                    'responsePolicy'
                ],
                pathParams: [
                    'project',
                    'responsePolicy'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        list(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://dns.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/dns/v1/projects/{project}/responsePolicies').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'GET',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'project'
                ],
                pathParams: [
                    'project'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        patch(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://dns.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/dns/v1/projects/{project}/responsePolicies/{responsePolicy}').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'PATCH',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'project',
                    'responsePolicy'
                ],
                pathParams: [
                    'project',
                    'responsePolicy'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        update(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://dns.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/dns/v1/projects/{project}/responsePolicies/{responsePolicy}').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'PUT',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'project',
                    'responsePolicy'
                ],
                pathParams: [
                    'project',
                    'responsePolicy'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
    }
    dns_v1.Resource$Responsepolicies = Resource$Responsepolicies;
    class Resource$Responsepolicyrules {
        context;
        constructor(context){
            this.context = context;
        }
        create(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://dns.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/dns/v1/projects/{project}/responsePolicies/{responsePolicy}/rules').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'POST',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'project',
                    'responsePolicy'
                ],
                pathParams: [
                    'project',
                    'responsePolicy'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        delete(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://dns.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/dns/v1/projects/{project}/responsePolicies/{responsePolicy}/rules/{responsePolicyRule}').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'DELETE',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'project',
                    'responsePolicy',
                    'responsePolicyRule'
                ],
                pathParams: [
                    'project',
                    'responsePolicy',
                    'responsePolicyRule'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        get(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://dns.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/dns/v1/projects/{project}/responsePolicies/{responsePolicy}/rules/{responsePolicyRule}').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'GET',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'project',
                    'responsePolicy',
                    'responsePolicyRule'
                ],
                pathParams: [
                    'project',
                    'responsePolicy',
                    'responsePolicyRule'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        list(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://dns.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/dns/v1/projects/{project}/responsePolicies/{responsePolicy}/rules').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'GET',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'project',
                    'responsePolicy'
                ],
                pathParams: [
                    'project',
                    'responsePolicy'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        patch(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://dns.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/dns/v1/projects/{project}/responsePolicies/{responsePolicy}/rules/{responsePolicyRule}').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'PATCH',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'project',
                    'responsePolicy',
                    'responsePolicyRule'
                ],
                pathParams: [
                    'project',
                    'responsePolicy',
                    'responsePolicyRule'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        update(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://dns.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/dns/v1/projects/{project}/responsePolicies/{responsePolicy}/rules/{responsePolicyRule}').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'PUT',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'project',
                    'responsePolicy',
                    'responsePolicyRule'
                ],
                pathParams: [
                    'project',
                    'responsePolicy',
                    'responsePolicyRule'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
    }
    dns_v1.Resource$Responsepolicyrules = Resource$Responsepolicyrules;
})(dns_v1 || (exports.dns_v1 = dns_v1 = {}));
}}),
"[project]/node_modules/googleapis/build/src/apis/dns/v1beta2.js [app-rsc] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
// Copyright 2020 Google LLC
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//    http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.dns_v1beta2 = void 0;
/* eslint-disable @typescript-eslint/no-explicit-any */ /* eslint-disable @typescript-eslint/no-unused-vars */ /* eslint-disable @typescript-eslint/no-empty-interface */ /* eslint-disable @typescript-eslint/no-namespace */ /* eslint-disable no-irregular-whitespace */ const googleapis_common_1 = __turbopack_context__.r("[project]/node_modules/googleapis-common/build/src/index.js [app-rsc] (ecmascript)");
var dns_v1beta2;
(function(dns_v1beta2) {
    /**
     * Cloud DNS API
     *
     *
     *
     * @example
     * ```js
     * const {google} = require('googleapis');
     * const dns = google.dns('v1beta2');
     * ```
     */ class Dns {
        context;
        changes;
        dnsKeys;
        managedZoneOperations;
        managedZones;
        policies;
        projects;
        resourceRecordSets;
        responsePolicies;
        responsePolicyRules;
        constructor(options, google){
            this.context = {
                _options: options || {},
                google
            };
            this.changes = new Resource$Changes(this.context);
            this.dnsKeys = new Resource$Dnskeys(this.context);
            this.managedZoneOperations = new Resource$Managedzoneoperations(this.context);
            this.managedZones = new Resource$Managedzones(this.context);
            this.policies = new Resource$Policies(this.context);
            this.projects = new Resource$Projects(this.context);
            this.resourceRecordSets = new Resource$Resourcerecordsets(this.context);
            this.responsePolicies = new Resource$Responsepolicies(this.context);
            this.responsePolicyRules = new Resource$Responsepolicyrules(this.context);
        }
    }
    dns_v1beta2.Dns = Dns;
    class Resource$Changes {
        context;
        constructor(context){
            this.context = context;
        }
        create(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://dns.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/dns/v1beta2/projects/{project}/managedZones/{managedZone}/changes').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'POST',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'project',
                    'managedZone'
                ],
                pathParams: [
                    'managedZone',
                    'project'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        get(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://dns.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/dns/v1beta2/projects/{project}/managedZones/{managedZone}/changes/{changeId}').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'GET',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'project',
                    'managedZone',
                    'changeId'
                ],
                pathParams: [
                    'changeId',
                    'managedZone',
                    'project'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        list(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://dns.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/dns/v1beta2/projects/{project}/managedZones/{managedZone}/changes').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'GET',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'project',
                    'managedZone'
                ],
                pathParams: [
                    'managedZone',
                    'project'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
    }
    dns_v1beta2.Resource$Changes = Resource$Changes;
    class Resource$Dnskeys {
        context;
        constructor(context){
            this.context = context;
        }
        get(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://dns.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/dns/v1beta2/projects/{project}/managedZones/{managedZone}/dnsKeys/{dnsKeyId}').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'GET',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'project',
                    'managedZone',
                    'dnsKeyId'
                ],
                pathParams: [
                    'dnsKeyId',
                    'managedZone',
                    'project'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        list(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://dns.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/dns/v1beta2/projects/{project}/managedZones/{managedZone}/dnsKeys').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'GET',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'project',
                    'managedZone'
                ],
                pathParams: [
                    'managedZone',
                    'project'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
    }
    dns_v1beta2.Resource$Dnskeys = Resource$Dnskeys;
    class Resource$Managedzoneoperations {
        context;
        constructor(context){
            this.context = context;
        }
        get(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://dns.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/dns/v1beta2/projects/{project}/managedZones/{managedZone}/operations/{operation}').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'GET',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'project',
                    'managedZone',
                    'operation'
                ],
                pathParams: [
                    'managedZone',
                    'operation',
                    'project'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        list(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://dns.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/dns/v1beta2/projects/{project}/managedZones/{managedZone}/operations').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'GET',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'project',
                    'managedZone'
                ],
                pathParams: [
                    'managedZone',
                    'project'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
    }
    dns_v1beta2.Resource$Managedzoneoperations = Resource$Managedzoneoperations;
    class Resource$Managedzones {
        context;
        constructor(context){
            this.context = context;
        }
        create(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://dns.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/dns/v1beta2/projects/{project}/managedZones').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'POST',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'project'
                ],
                pathParams: [
                    'project'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        delete(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://dns.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/dns/v1beta2/projects/{project}/managedZones/{managedZone}').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'DELETE',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'project',
                    'managedZone'
                ],
                pathParams: [
                    'managedZone',
                    'project'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        get(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://dns.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/dns/v1beta2/projects/{project}/managedZones/{managedZone}').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'GET',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'project',
                    'managedZone'
                ],
                pathParams: [
                    'managedZone',
                    'project'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        getIamPolicy(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://dns.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/dns/v1beta2/{+resource}:getIamPolicy').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'POST',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'resource'
                ],
                pathParams: [
                    'resource'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        list(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://dns.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/dns/v1beta2/projects/{project}/managedZones').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'GET',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'project'
                ],
                pathParams: [
                    'project'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        patch(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://dns.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/dns/v1beta2/projects/{project}/managedZones/{managedZone}').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'PATCH',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'project',
                    'managedZone'
                ],
                pathParams: [
                    'managedZone',
                    'project'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        setIamPolicy(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://dns.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/dns/v1beta2/{+resource}:setIamPolicy').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'POST',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'resource'
                ],
                pathParams: [
                    'resource'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        testIamPermissions(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://dns.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/dns/v1beta2/{+resource}:testIamPermissions').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'POST',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'resource'
                ],
                pathParams: [
                    'resource'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        update(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://dns.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/dns/v1beta2/projects/{project}/managedZones/{managedZone}').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'PUT',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'project',
                    'managedZone'
                ],
                pathParams: [
                    'managedZone',
                    'project'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
    }
    dns_v1beta2.Resource$Managedzones = Resource$Managedzones;
    class Resource$Policies {
        context;
        constructor(context){
            this.context = context;
        }
        create(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://dns.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/dns/v1beta2/projects/{project}/policies').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'POST',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'project'
                ],
                pathParams: [
                    'project'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        delete(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://dns.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/dns/v1beta2/projects/{project}/policies/{policy}').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'DELETE',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'project',
                    'policy'
                ],
                pathParams: [
                    'policy',
                    'project'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        get(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://dns.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/dns/v1beta2/projects/{project}/policies/{policy}').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'GET',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'project',
                    'policy'
                ],
                pathParams: [
                    'policy',
                    'project'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        list(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://dns.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/dns/v1beta2/projects/{project}/policies').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'GET',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'project'
                ],
                pathParams: [
                    'project'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        patch(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://dns.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/dns/v1beta2/projects/{project}/policies/{policy}').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'PATCH',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'project',
                    'policy'
                ],
                pathParams: [
                    'policy',
                    'project'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        update(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://dns.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/dns/v1beta2/projects/{project}/policies/{policy}').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'PUT',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'project',
                    'policy'
                ],
                pathParams: [
                    'policy',
                    'project'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
    }
    dns_v1beta2.Resource$Policies = Resource$Policies;
    class Resource$Projects {
        context;
        constructor(context){
            this.context = context;
        }
        get(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://dns.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/dns/v1beta2/projects/{project}').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'GET',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'project'
                ],
                pathParams: [
                    'project'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
    }
    dns_v1beta2.Resource$Projects = Resource$Projects;
    class Resource$Resourcerecordsets {
        context;
        constructor(context){
            this.context = context;
        }
        create(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://dns.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/dns/v1beta2/projects/{project}/managedZones/{managedZone}/rrsets').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'POST',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'project',
                    'managedZone'
                ],
                pathParams: [
                    'managedZone',
                    'project'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        delete(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://dns.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/dns/v1beta2/projects/{project}/managedZones/{managedZone}/rrsets/{name}/{type}').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'DELETE',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'project',
                    'managedZone',
                    'name',
                    'type'
                ],
                pathParams: [
                    'managedZone',
                    'name',
                    'project',
                    'type'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        get(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://dns.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/dns/v1beta2/projects/{project}/managedZones/{managedZone}/rrsets/{name}/{type}').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'GET',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'project',
                    'managedZone',
                    'name',
                    'type'
                ],
                pathParams: [
                    'managedZone',
                    'name',
                    'project',
                    'type'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        list(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://dns.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/dns/v1beta2/projects/{project}/managedZones/{managedZone}/rrsets').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'GET',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'project',
                    'managedZone'
                ],
                pathParams: [
                    'managedZone',
                    'project'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        patch(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://dns.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/dns/v1beta2/projects/{project}/managedZones/{managedZone}/rrsets/{name}/{type}').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'PATCH',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'project',
                    'managedZone',
                    'name',
                    'type'
                ],
                pathParams: [
                    'managedZone',
                    'name',
                    'project',
                    'type'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
    }
    dns_v1beta2.Resource$Resourcerecordsets = Resource$Resourcerecordsets;
    class Resource$Responsepolicies {
        context;
        constructor(context){
            this.context = context;
        }
        create(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://dns.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/dns/v1beta2/projects/{project}/responsePolicies').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'POST',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'project'
                ],
                pathParams: [
                    'project'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        delete(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://dns.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/dns/v1beta2/projects/{project}/responsePolicies/{responsePolicy}').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'DELETE',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'project',
                    'responsePolicy'
                ],
                pathParams: [
                    'project',
                    'responsePolicy'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        get(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://dns.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/dns/v1beta2/projects/{project}/responsePolicies/{responsePolicy}').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'GET',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'project',
                    'responsePolicy'
                ],
                pathParams: [
                    'project',
                    'responsePolicy'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        list(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://dns.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/dns/v1beta2/projects/{project}/responsePolicies').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'GET',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'project'
                ],
                pathParams: [
                    'project'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        patch(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://dns.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/dns/v1beta2/projects/{project}/responsePolicies/{responsePolicy}').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'PATCH',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'project',
                    'responsePolicy'
                ],
                pathParams: [
                    'project',
                    'responsePolicy'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        update(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://dns.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/dns/v1beta2/projects/{project}/responsePolicies/{responsePolicy}').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'PUT',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'project',
                    'responsePolicy'
                ],
                pathParams: [
                    'project',
                    'responsePolicy'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
    }
    dns_v1beta2.Resource$Responsepolicies = Resource$Responsepolicies;
    class Resource$Responsepolicyrules {
        context;
        constructor(context){
            this.context = context;
        }
        create(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://dns.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/dns/v1beta2/projects/{project}/responsePolicies/{responsePolicy}/rules').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'POST',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'project',
                    'responsePolicy'
                ],
                pathParams: [
                    'project',
                    'responsePolicy'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        delete(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://dns.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/dns/v1beta2/projects/{project}/responsePolicies/{responsePolicy}/rules/{responsePolicyRule}').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'DELETE',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'project',
                    'responsePolicy',
                    'responsePolicyRule'
                ],
                pathParams: [
                    'project',
                    'responsePolicy',
                    'responsePolicyRule'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        get(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://dns.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/dns/v1beta2/projects/{project}/responsePolicies/{responsePolicy}/rules/{responsePolicyRule}').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'GET',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'project',
                    'responsePolicy',
                    'responsePolicyRule'
                ],
                pathParams: [
                    'project',
                    'responsePolicy',
                    'responsePolicyRule'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        list(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://dns.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/dns/v1beta2/projects/{project}/responsePolicies/{responsePolicy}/rules').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'GET',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'project',
                    'responsePolicy'
                ],
                pathParams: [
                    'project',
                    'responsePolicy'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        patch(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://dns.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/dns/v1beta2/projects/{project}/responsePolicies/{responsePolicy}/rules/{responsePolicyRule}').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'PATCH',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'project',
                    'responsePolicy',
                    'responsePolicyRule'
                ],
                pathParams: [
                    'project',
                    'responsePolicy',
                    'responsePolicyRule'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        update(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://dns.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/dns/v1beta2/projects/{project}/responsePolicies/{responsePolicy}/rules/{responsePolicyRule}').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'PUT',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'project',
                    'responsePolicy',
                    'responsePolicyRule'
                ],
                pathParams: [
                    'project',
                    'responsePolicy',
                    'responsePolicyRule'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
    }
    dns_v1beta2.Resource$Responsepolicyrules = Resource$Responsepolicyrules;
})(dns_v1beta2 || (exports.dns_v1beta2 = dns_v1beta2 = {}));
}}),
"[project]/node_modules/googleapis/build/src/apis/dns/v2.js [app-rsc] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
// Copyright 2020 Google LLC
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//    http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.dns_v2 = void 0;
/* eslint-disable @typescript-eslint/no-explicit-any */ /* eslint-disable @typescript-eslint/class-name-casing */ /* eslint-disable @typescript-eslint/no-unused-vars */ /* eslint-disable @typescript-eslint/no-empty-interface */ /* eslint-disable @typescript-eslint/no-namespace */ /* eslint-disable no-irregular-whitespace */ const googleapis_common_1 = __turbopack_context__.r("[project]/node_modules/googleapis-common/build/src/index.js [app-rsc] (ecmascript)");
var dns_v2;
(function(dns_v2) {
    /**
     * Cloud DNS API
     *
     *
     *
     * @example
     * ```js
     * const {google} = require('googleapis');
     * const dns = google.dns('v2');
     * ```
     */ class Dns {
        context;
        changes;
        dnsKeys;
        managedZoneOperations;
        managedZones;
        policies;
        projects;
        resourceRecordSets;
        responsePolicies;
        responsePolicyRules;
        constructor(options, google){
            this.context = {
                _options: options || {},
                google
            };
            this.changes = new Resource$Changes(this.context);
            this.dnsKeys = new Resource$Dnskeys(this.context);
            this.managedZoneOperations = new Resource$Managedzoneoperations(this.context);
            this.managedZones = new Resource$Managedzones(this.context);
            this.policies = new Resource$Policies(this.context);
            this.projects = new Resource$Projects(this.context);
            this.resourceRecordSets = new Resource$Resourcerecordsets(this.context);
            this.responsePolicies = new Resource$Responsepolicies(this.context);
            this.responsePolicyRules = new Resource$Responsepolicyrules(this.context);
        }
    }
    dns_v2.Dns = Dns;
    class Resource$Changes {
        context;
        constructor(context){
            this.context = context;
        }
        create(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://dns.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/dns/v2/projects/{project}/locations/{location}/managedZones/{managedZone}/changes').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'POST'
                }, options),
                params,
                requiredParams: [
                    'project',
                    'location',
                    'managedZone'
                ],
                pathParams: [
                    'location',
                    'managedZone',
                    'project'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        get(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://dns.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/dns/v2/projects/{project}/locations/{location}/managedZones/{managedZone}/changes/{changeId}').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'GET'
                }, options),
                params,
                requiredParams: [
                    'project',
                    'location',
                    'managedZone',
                    'changeId'
                ],
                pathParams: [
                    'changeId',
                    'location',
                    'managedZone',
                    'project'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        list(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://dns.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/dns/v2/projects/{project}/locations/{location}/managedZones/{managedZone}/changes').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'GET'
                }, options),
                params,
                requiredParams: [
                    'project',
                    'location',
                    'managedZone'
                ],
                pathParams: [
                    'location',
                    'managedZone',
                    'project'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
    }
    dns_v2.Resource$Changes = Resource$Changes;
    class Resource$Dnskeys {
        context;
        constructor(context){
            this.context = context;
        }
        get(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://dns.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/dns/v2/projects/{project}/locations/{location}/managedZones/{managedZone}/dnsKeys/{dnsKeyId}').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'GET'
                }, options),
                params,
                requiredParams: [
                    'project',
                    'location',
                    'managedZone',
                    'dnsKeyId'
                ],
                pathParams: [
                    'dnsKeyId',
                    'location',
                    'managedZone',
                    'project'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        list(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://dns.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/dns/v2/projects/{project}/locations/{location}/managedZones/{managedZone}/dnsKeys').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'GET'
                }, options),
                params,
                requiredParams: [
                    'project',
                    'location',
                    'managedZone'
                ],
                pathParams: [
                    'location',
                    'managedZone',
                    'project'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
    }
    dns_v2.Resource$Dnskeys = Resource$Dnskeys;
    class Resource$Managedzoneoperations {
        context;
        constructor(context){
            this.context = context;
        }
        get(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://dns.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/dns/v2/projects/{project}/locations/{location}/managedZones/{managedZone}/operations/{operation}').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'GET'
                }, options),
                params,
                requiredParams: [
                    'project',
                    'location',
                    'managedZone',
                    'operation'
                ],
                pathParams: [
                    'location',
                    'managedZone',
                    'operation',
                    'project'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        list(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://dns.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/dns/v2/projects/{project}/locations/{location}/managedZones/{managedZone}/operations').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'GET'
                }, options),
                params,
                requiredParams: [
                    'project',
                    'location',
                    'managedZone'
                ],
                pathParams: [
                    'location',
                    'managedZone',
                    'project'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
    }
    dns_v2.Resource$Managedzoneoperations = Resource$Managedzoneoperations;
    class Resource$Managedzones {
        context;
        constructor(context){
            this.context = context;
        }
        create(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://dns.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/dns/v2/projects/{project}/locations/{location}/managedZones').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'POST'
                }, options),
                params,
                requiredParams: [
                    'project',
                    'location'
                ],
                pathParams: [
                    'location',
                    'project'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        delete(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://dns.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/dns/v2/projects/{project}/locations/{location}/managedZones/{managedZone}').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'DELETE'
                }, options),
                params,
                requiredParams: [
                    'project',
                    'location',
                    'managedZone'
                ],
                pathParams: [
                    'location',
                    'managedZone',
                    'project'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        get(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://dns.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/dns/v2/projects/{project}/locations/{location}/managedZones/{managedZone}').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'GET'
                }, options),
                params,
                requiredParams: [
                    'project',
                    'location',
                    'managedZone'
                ],
                pathParams: [
                    'location',
                    'managedZone',
                    'project'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        list(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://dns.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/dns/v2/projects/{project}/locations/{location}/managedZones').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'GET'
                }, options),
                params,
                requiredParams: [
                    'project',
                    'location'
                ],
                pathParams: [
                    'location',
                    'project'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        patch(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://dns.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/dns/v2/projects/{project}/locations/{location}/managedZones/{managedZone}').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'PATCH'
                }, options),
                params,
                requiredParams: [
                    'project',
                    'location',
                    'managedZone'
                ],
                pathParams: [
                    'location',
                    'managedZone',
                    'project'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        update(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://dns.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/dns/v2/projects/{project}/locations/{location}/managedZones/{managedZone}').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'PUT'
                }, options),
                params,
                requiredParams: [
                    'project',
                    'location',
                    'managedZone'
                ],
                pathParams: [
                    'location',
                    'managedZone',
                    'project'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
    }
    dns_v2.Resource$Managedzones = Resource$Managedzones;
    class Resource$Policies {
        context;
        constructor(context){
            this.context = context;
        }
        create(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://dns.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/dns/v2/projects/{project}/locations/{location}/policies').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'POST'
                }, options),
                params,
                requiredParams: [
                    'project',
                    'location'
                ],
                pathParams: [
                    'location',
                    'project'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        delete(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://dns.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/dns/v2/projects/{project}/locations/{location}/policies/{policy}').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'DELETE'
                }, options),
                params,
                requiredParams: [
                    'project',
                    'location',
                    'policy'
                ],
                pathParams: [
                    'location',
                    'policy',
                    'project'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        get(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://dns.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/dns/v2/projects/{project}/locations/{location}/policies/{policy}').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'GET'
                }, options),
                params,
                requiredParams: [
                    'project',
                    'location',
                    'policy'
                ],
                pathParams: [
                    'location',
                    'policy',
                    'project'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        list(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://dns.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/dns/v2/projects/{project}/locations/{location}/policies').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'GET'
                }, options),
                params,
                requiredParams: [
                    'project',
                    'location'
                ],
                pathParams: [
                    'location',
                    'project'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        patch(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://dns.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/dns/v2/projects/{project}/locations/{location}/policies/{policy}').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'PATCH'
                }, options),
                params,
                requiredParams: [
                    'project',
                    'location',
                    'policy'
                ],
                pathParams: [
                    'location',
                    'policy',
                    'project'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        update(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://dns.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/dns/v2/projects/{project}/locations/{location}/policies/{policy}').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'PUT'
                }, options),
                params,
                requiredParams: [
                    'project',
                    'location',
                    'policy'
                ],
                pathParams: [
                    'location',
                    'policy',
                    'project'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
    }
    dns_v2.Resource$Policies = Resource$Policies;
    class Resource$Projects {
        context;
        constructor(context){
            this.context = context;
        }
        get(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://dns.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/dns/v2/projects/{project}/locations/{location}').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'GET'
                }, options),
                params,
                requiredParams: [
                    'project',
                    'location'
                ],
                pathParams: [
                    'location',
                    'project'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
    }
    dns_v2.Resource$Projects = Resource$Projects;
    class Resource$Resourcerecordsets {
        context;
        constructor(context){
            this.context = context;
        }
        create(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://dns.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/dns/v2/projects/{project}/locations/{location}/managedZones/{managedZone}/rrsets').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'POST'
                }, options),
                params,
                requiredParams: [
                    'project',
                    'location',
                    'managedZone'
                ],
                pathParams: [
                    'location',
                    'managedZone',
                    'project'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        delete(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://dns.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/dns/v2/projects/{project}/locations/{location}/managedZones/{managedZone}/rrsets/{name}/{type}').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'DELETE'
                }, options),
                params,
                requiredParams: [
                    'project',
                    'location',
                    'managedZone',
                    'name',
                    'type'
                ],
                pathParams: [
                    'location',
                    'managedZone',
                    'name',
                    'project',
                    'type'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        get(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://dns.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/dns/v2/projects/{project}/locations/{location}/managedZones/{managedZone}/rrsets/{name}/{type}').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'GET'
                }, options),
                params,
                requiredParams: [
                    'project',
                    'location',
                    'managedZone',
                    'name',
                    'type'
                ],
                pathParams: [
                    'location',
                    'managedZone',
                    'name',
                    'project',
                    'type'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        list(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://dns.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/dns/v2/projects/{project}/locations/{location}/managedZones/{managedZone}/rrsets').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'GET'
                }, options),
                params,
                requiredParams: [
                    'project',
                    'location',
                    'managedZone'
                ],
                pathParams: [
                    'location',
                    'managedZone',
                    'project'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        patch(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://dns.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/dns/v2/projects/{project}/locations/{location}/managedZones/{managedZone}/rrsets/{name}/{type}').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'PATCH'
                }, options),
                params,
                requiredParams: [
                    'project',
                    'location',
                    'managedZone',
                    'name',
                    'type'
                ],
                pathParams: [
                    'location',
                    'managedZone',
                    'name',
                    'project',
                    'type'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
    }
    dns_v2.Resource$Resourcerecordsets = Resource$Resourcerecordsets;
    class Resource$Responsepolicies {
        context;
        constructor(context){
            this.context = context;
        }
        create(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://dns.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/dns/v2/projects/{project}/locations/{location}/responsePolicies').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'POST'
                }, options),
                params,
                requiredParams: [
                    'project',
                    'location'
                ],
                pathParams: [
                    'location',
                    'project'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        delete(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://dns.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/dns/v2/projects/{project}/locations/{location}/responsePolicies/{responsePolicy}').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'DELETE'
                }, options),
                params,
                requiredParams: [
                    'project',
                    'location',
                    'responsePolicy'
                ],
                pathParams: [
                    'location',
                    'project',
                    'responsePolicy'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        get(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://dns.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/dns/v2/projects/{project}/locations/{location}/responsePolicies/{responsePolicy}').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'GET'
                }, options),
                params,
                requiredParams: [
                    'project',
                    'location',
                    'responsePolicy'
                ],
                pathParams: [
                    'location',
                    'project',
                    'responsePolicy'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        list(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://dns.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/dns/v2/projects/{project}/locations/{location}/responsePolicies').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'GET'
                }, options),
                params,
                requiredParams: [
                    'project',
                    'location'
                ],
                pathParams: [
                    'location',
                    'project'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        patch(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://dns.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/dns/v2/projects/{project}/locations/{location}/responsePolicies/{responsePolicy}').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'PATCH'
                }, options),
                params,
                requiredParams: [
                    'project',
                    'location',
                    'responsePolicy'
                ],
                pathParams: [
                    'location',
                    'project',
                    'responsePolicy'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        update(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://dns.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/dns/v2/projects/{project}/locations/{location}/responsePolicies/{responsePolicy}').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'PUT'
                }, options),
                params,
                requiredParams: [
                    'project',
                    'location',
                    'responsePolicy'
                ],
                pathParams: [
                    'location',
                    'project',
                    'responsePolicy'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
    }
    dns_v2.Resource$Responsepolicies = Resource$Responsepolicies;
    class Resource$Responsepolicyrules {
        context;
        constructor(context){
            this.context = context;
        }
        create(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://dns.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/dns/v2/projects/{project}/locations/{location}/responsePolicies/{responsePolicy}/rules').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'POST'
                }, options),
                params,
                requiredParams: [
                    'project',
                    'location',
                    'responsePolicy'
                ],
                pathParams: [
                    'location',
                    'project',
                    'responsePolicy'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        delete(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://dns.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/dns/v2/projects/{project}/locations/{location}/responsePolicies/{responsePolicy}/rules/{responsePolicyRule}').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'DELETE'
                }, options),
                params,
                requiredParams: [
                    'project',
                    'location',
                    'responsePolicy',
                    'responsePolicyRule'
                ],
                pathParams: [
                    'location',
                    'project',
                    'responsePolicy',
                    'responsePolicyRule'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        get(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://dns.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/dns/v2/projects/{project}/locations/{location}/responsePolicies/{responsePolicy}/rules/{responsePolicyRule}').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'GET'
                }, options),
                params,
                requiredParams: [
                    'project',
                    'location',
                    'responsePolicy',
                    'responsePolicyRule'
                ],
                pathParams: [
                    'location',
                    'project',
                    'responsePolicy',
                    'responsePolicyRule'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        list(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://dns.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/dns/v2/projects/{project}/locations/{location}/responsePolicies/{responsePolicy}/rules').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'GET'
                }, options),
                params,
                requiredParams: [
                    'project',
                    'location',
                    'responsePolicy'
                ],
                pathParams: [
                    'location',
                    'project',
                    'responsePolicy'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        patch(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://dns.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/dns/v2/projects/{project}/locations/{location}/responsePolicies/{responsePolicy}/rules/{responsePolicyRule}').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'PATCH'
                }, options),
                params,
                requiredParams: [
                    'project',
                    'location',
                    'responsePolicy',
                    'responsePolicyRule'
                ],
                pathParams: [
                    'location',
                    'project',
                    'responsePolicy',
                    'responsePolicyRule'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        update(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://dns.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/dns/v2/projects/{project}/locations/{location}/responsePolicies/{responsePolicy}/rules/{responsePolicyRule}').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'PUT'
                }, options),
                params,
                requiredParams: [
                    'project',
                    'location',
                    'responsePolicy',
                    'responsePolicyRule'
                ],
                pathParams: [
                    'location',
                    'project',
                    'responsePolicy',
                    'responsePolicyRule'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
    }
    dns_v2.Resource$Responsepolicyrules = Resource$Responsepolicyrules;
})(dns_v2 || (exports.dns_v2 = dns_v2 = {}));
}}),
"[project]/node_modules/googleapis/build/src/apis/dns/v2beta1.js [app-rsc] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
// Copyright 2020 Google LLC
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//    http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.dns_v2beta1 = void 0;
/* eslint-disable @typescript-eslint/no-explicit-any */ /* eslint-disable @typescript-eslint/class-name-casing */ /* eslint-disable @typescript-eslint/no-unused-vars */ /* eslint-disable @typescript-eslint/no-empty-interface */ /* eslint-disable @typescript-eslint/no-namespace */ /* eslint-disable no-irregular-whitespace */ const googleapis_common_1 = __turbopack_context__.r("[project]/node_modules/googleapis-common/build/src/index.js [app-rsc] (ecmascript)");
var dns_v2beta1;
(function(dns_v2beta1) {
    /**
     * Cloud DNS API
     *
     *
     *
     * @example
     * const {google} = require('googleapis');
     * const dns = google.dns('v2beta1');
     *
     * @namespace dns
     * @type {Function}
     * @version v2beta1
     * @variation v2beta1
     * @param {object=} options Options for Dns
     */ class Dns {
        context;
        changes;
        dnsKeys;
        managedZoneOperations;
        managedZones;
        policies;
        projects;
        resourceRecordSets;
        constructor(options, google){
            this.context = {
                _options: options || {},
                google
            };
            this.changes = new Resource$Changes(this.context);
            this.dnsKeys = new Resource$Dnskeys(this.context);
            this.managedZoneOperations = new Resource$Managedzoneoperations(this.context);
            this.managedZones = new Resource$Managedzones(this.context);
            this.policies = new Resource$Policies(this.context);
            this.projects = new Resource$Projects(this.context);
            this.resourceRecordSets = new Resource$Resourcerecordsets(this.context);
        }
    }
    dns_v2beta1.Dns = Dns;
    class Resource$Changes {
        context;
        constructor(context){
            this.context = context;
        }
        create(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://dns.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/dns/v2beta1/projects/{project}/managedZones/{managedZone}/changes').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'POST'
                }, options),
                params,
                requiredParams: [
                    'project',
                    'managedZone'
                ],
                pathParams: [
                    'managedZone',
                    'project'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        get(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://dns.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/dns/v2beta1/projects/{project}/managedZones/{managedZone}/changes/{changeId}').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'GET'
                }, options),
                params,
                requiredParams: [
                    'project',
                    'managedZone',
                    'changeId'
                ],
                pathParams: [
                    'changeId',
                    'managedZone',
                    'project'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        list(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://dns.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/dns/v2beta1/projects/{project}/managedZones/{managedZone}/changes').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'GET'
                }, options),
                params,
                requiredParams: [
                    'project',
                    'managedZone'
                ],
                pathParams: [
                    'managedZone',
                    'project'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
    }
    dns_v2beta1.Resource$Changes = Resource$Changes;
    class Resource$Dnskeys {
        context;
        constructor(context){
            this.context = context;
        }
        get(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://dns.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/dns/v2beta1/projects/{project}/managedZones/{managedZone}/dnsKeys/{dnsKeyId}').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'GET'
                }, options),
                params,
                requiredParams: [
                    'project',
                    'managedZone',
                    'dnsKeyId'
                ],
                pathParams: [
                    'dnsKeyId',
                    'managedZone',
                    'project'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        list(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://dns.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/dns/v2beta1/projects/{project}/managedZones/{managedZone}/dnsKeys').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'GET'
                }, options),
                params,
                requiredParams: [
                    'project',
                    'managedZone'
                ],
                pathParams: [
                    'managedZone',
                    'project'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
    }
    dns_v2beta1.Resource$Dnskeys = Resource$Dnskeys;
    class Resource$Managedzoneoperations {
        context;
        constructor(context){
            this.context = context;
        }
        get(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://dns.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/dns/v2beta1/projects/{project}/managedZones/{managedZone}/operations/{operation}').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'GET'
                }, options),
                params,
                requiredParams: [
                    'project',
                    'managedZone',
                    'operation'
                ],
                pathParams: [
                    'managedZone',
                    'operation',
                    'project'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        list(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://dns.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/dns/v2beta1/projects/{project}/managedZones/{managedZone}/operations').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'GET'
                }, options),
                params,
                requiredParams: [
                    'project',
                    'managedZone'
                ],
                pathParams: [
                    'managedZone',
                    'project'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
    }
    dns_v2beta1.Resource$Managedzoneoperations = Resource$Managedzoneoperations;
    class Resource$Managedzones {
        context;
        constructor(context){
            this.context = context;
        }
        create(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://dns.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/dns/v2beta1/projects/{project}/managedZones').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'POST'
                }, options),
                params,
                requiredParams: [
                    'project'
                ],
                pathParams: [
                    'project'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        delete(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://dns.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/dns/v2beta1/projects/{project}/managedZones/{managedZone}').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'DELETE'
                }, options),
                params,
                requiredParams: [
                    'project',
                    'managedZone'
                ],
                pathParams: [
                    'managedZone',
                    'project'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        get(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://dns.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/dns/v2beta1/projects/{project}/managedZones/{managedZone}').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'GET'
                }, options),
                params,
                requiredParams: [
                    'project',
                    'managedZone'
                ],
                pathParams: [
                    'managedZone',
                    'project'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        list(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://dns.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/dns/v2beta1/projects/{project}/managedZones').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'GET'
                }, options),
                params,
                requiredParams: [
                    'project'
                ],
                pathParams: [
                    'project'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        patch(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://dns.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/dns/v2beta1/projects/{project}/managedZones/{managedZone}').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'PATCH'
                }, options),
                params,
                requiredParams: [
                    'project',
                    'managedZone'
                ],
                pathParams: [
                    'managedZone',
                    'project'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        update(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://dns.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/dns/v2beta1/projects/{project}/managedZones/{managedZone}').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'PUT'
                }, options),
                params,
                requiredParams: [
                    'project',
                    'managedZone'
                ],
                pathParams: [
                    'managedZone',
                    'project'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
    }
    dns_v2beta1.Resource$Managedzones = Resource$Managedzones;
    class Resource$Policies {
        context;
        constructor(context){
            this.context = context;
        }
        create(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://dns.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/dns/v2beta1/projects/{project}/policies').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'POST'
                }, options),
                params,
                requiredParams: [
                    'project'
                ],
                pathParams: [
                    'project'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        delete(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://dns.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/dns/v2beta1/projects/{project}/policies/{policy}').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'DELETE'
                }, options),
                params,
                requiredParams: [
                    'project',
                    'policy'
                ],
                pathParams: [
                    'policy',
                    'project'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        get(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://dns.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/dns/v2beta1/projects/{project}/policies/{policy}').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'GET'
                }, options),
                params,
                requiredParams: [
                    'project',
                    'policy'
                ],
                pathParams: [
                    'policy',
                    'project'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        list(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://dns.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/dns/v2beta1/projects/{project}/policies').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'GET'
                }, options),
                params,
                requiredParams: [
                    'project'
                ],
                pathParams: [
                    'project'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        patch(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://dns.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/dns/v2beta1/projects/{project}/policies/{policy}').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'PATCH'
                }, options),
                params,
                requiredParams: [
                    'project',
                    'policy'
                ],
                pathParams: [
                    'policy',
                    'project'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        update(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://dns.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/dns/v2beta1/projects/{project}/policies/{policy}').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'PUT'
                }, options),
                params,
                requiredParams: [
                    'project',
                    'policy'
                ],
                pathParams: [
                    'policy',
                    'project'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
    }
    dns_v2beta1.Resource$Policies = Resource$Policies;
    class Resource$Projects {
        context;
        constructor(context){
            this.context = context;
        }
        get(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://dns.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/dns/v2beta1/projects/{project}').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'GET'
                }, options),
                params,
                requiredParams: [
                    'project'
                ],
                pathParams: [
                    'project'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
    }
    dns_v2beta1.Resource$Projects = Resource$Projects;
    class Resource$Resourcerecordsets {
        context;
        constructor(context){
            this.context = context;
        }
        list(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://dns.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/dns/v2beta1/projects/{project}/managedZones/{managedZone}/rrsets').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'GET'
                }, options),
                params,
                requiredParams: [
                    'project',
                    'managedZone'
                ],
                pathParams: [
                    'managedZone',
                    'project'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
    }
    dns_v2beta1.Resource$Resourcerecordsets = Resource$Resourcerecordsets;
})(dns_v2beta1 || (exports.dns_v2beta1 = dns_v2beta1 = {}));
}}),
"[project]/node_modules/googleapis/build/src/apis/dns/index.js [app-rsc] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
// Copyright 2020 Google LLC
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//    http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.AuthPlus = exports.dns_v2beta1 = exports.dns_v2 = exports.dns_v1beta2 = exports.dns_v1 = exports.auth = exports.VERSIONS = void 0;
exports.dns = dns;
/*! THIS FILE IS AUTO-GENERATED */ const googleapis_common_1 = __turbopack_context__.r("[project]/node_modules/googleapis-common/build/src/index.js [app-rsc] (ecmascript)");
const v1_1 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/dns/v1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "dns_v1", {
    enumerable: true,
    get: function() {
        return v1_1.dns_v1;
    }
});
const v1beta2_1 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/dns/v1beta2.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "dns_v1beta2", {
    enumerable: true,
    get: function() {
        return v1beta2_1.dns_v1beta2;
    }
});
const v2_1 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/dns/v2.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "dns_v2", {
    enumerable: true,
    get: function() {
        return v2_1.dns_v2;
    }
});
const v2beta1_1 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/dns/v2beta1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "dns_v2beta1", {
    enumerable: true,
    get: function() {
        return v2beta1_1.dns_v2beta1;
    }
});
exports.VERSIONS = {
    v1: v1_1.dns_v1.Dns,
    v1beta2: v1beta2_1.dns_v1beta2.Dns,
    v2: v2_1.dns_v2.Dns,
    v2beta1: v2beta1_1.dns_v2beta1.Dns
};
function dns(versionOrOptions) {
    return (0, googleapis_common_1.getAPI)('dns', versionOrOptions, exports.VERSIONS, this);
}
const auth = new googleapis_common_1.AuthPlus();
exports.auth = auth;
var googleapis_common_2 = __turbopack_context__.r("[project]/node_modules/googleapis-common/build/src/index.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "AuthPlus", {
    enumerable: true,
    get: function() {
        return googleapis_common_2.AuthPlus;
    }
});
}}),

};

//# sourceMappingURL=node_modules_googleapis_build_src_apis_dns_b9cb5fb6._.js.map
{"version": 3, "sources": [], "sections": [{"offset": {"line": 111, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/AI_Projects/CRMcriadores/crm-interno/app/actions/sheetsActions.ts"], "sourcesContent": ["'use server';\n\nimport { google } from 'googleapis';\n\n// Configuração da autenticação\nconst getGoogleSheetsAuth = () => {\n  const credentials = {\n    type: 'service_account',\n    project_id: process.env.GOOGLE_PROJECT_ID,\n    private_key_id: process.env.GOOGLE_PRIVATE_KEY_ID,\n    private_key: process.env.GOOGLE_PRIVATE_KEY?.replace(/\\\\n/g, '\\n'),\n    client_email: process.env.GOOGLE_CLIENT_EMAIL,\n    client_id: process.env.GOOGLE_CLIENT_ID,\n    auth_uri: 'https://accounts.google.com/o/oauth2/auth',\n    token_uri: 'https://oauth2.googleapis.com/token',\n    auth_provider_x509_cert_url: 'https://www.googleapis.com/oauth2/v1/certs',\n    client_x509_cert_url: `https://www.googleapis.com/robot/v1/metadata/x509/${process.env.GOOGLE_CLIENT_EMAIL}`,\n  };\n\n  const auth = new google.auth.GoogleAuth({\n    credentials,\n    scopes: ['https://www.googleapis.com/auth/spreadsheets'],\n  });\n\n  return auth;\n};\n\n// Função para ler dados da planilha\nexport async function getData(sheetName: string) {\n  try {\n    const auth = getGoogleSheetsAuth();\n    const sheets = google.sheets({ version: 'v4', auth });\n    \n    const spreadsheetId = process.env.GOOGLE_SPREADSHEET_ID;\n    \n    if (!spreadsheetId) {\n      throw new Error('GOOGLE_SPREADSHEET_ID não está configurado');\n    }\n\n    const response = await sheets.spreadsheets.values.get({\n      spreadsheetId,\n      range: `${sheetName}!A:Z`, // Lê todas as colunas de A até Z\n    });\n\n    return response.data.values || [];\n  } catch (error) {\n    console.error('Erro ao ler dados da planilha:', error);\n    throw new Error('Falha ao ler dados da planilha');\n  }\n}\n\n// Função para adicionar dados à planilha\nexport async function appendData(sheetName: string, rowData: any[]) {\n  try {\n    const auth = getGoogleSheetsAuth();\n    const sheets = google.sheets({ version: 'v4', auth });\n    \n    const spreadsheetId = process.env.GOOGLE_SPREADSHEET_ID;\n    \n    if (!spreadsheetId) {\n      throw new Error('GOOGLE_SPREADSHEET_ID não está configurado');\n    }\n\n    const response = await sheets.spreadsheets.values.append({\n      spreadsheetId,\n      range: `${sheetName}!A:Z`,\n      valueInputOption: 'USER_ENTERED',\n      requestBody: {\n        values: [rowData],\n      },\n    });\n\n    return response.data;\n  } catch (error) {\n    console.error('Erro ao adicionar dados à planilha:', error);\n    throw new Error('Falha ao adicionar dados à planilha');\n  }\n}\n\n// Função para atualizar dados na planilha\nexport async function updateData(sheetName: string, range: string, rowData: any[]) {\n  try {\n    const auth = getGoogleSheetsAuth();\n    const sheets = google.sheets({ version: 'v4', auth });\n    \n    const spreadsheetId = process.env.GOOGLE_SPREADSHEET_ID;\n    \n    if (!spreadsheetId) {\n      throw new Error('GOOGLE_SPREADSHEET_ID não está configurado');\n    }\n\n    const response = await sheets.spreadsheets.values.update({\n      spreadsheetId,\n      range: `${sheetName}!${range}`,\n      valueInputOption: 'USER_ENTERED',\n      requestBody: {\n        values: [rowData],\n      },\n    });\n\n    return response.data;\n  } catch (error) {\n    console.error('Erro ao atualizar dados da planilha:', error);\n    throw new Error('Falha ao atualizar dados da planilha');\n  }\n}\n"], "names": [], "mappings": ";;;;;;;AAEA;;;;;AAEA,+BAA+B;AAC/B,MAAM,sBAAsB;IAC1B,MAAM,cAAc;QAClB,MAAM;QACN,YAAY,QAAQ,GAAG,CAAC,iBAAiB;QACzC,gBAAgB,QAAQ,GAAG,CAAC,qBAAqB;QACjD,aAAa,QAAQ,GAAG,CAAC,kBAAkB,EAAE,QAAQ,QAAQ;QAC7D,cAAc,QAAQ,GAAG,CAAC,mBAAmB;QAC7C,WAAW,QAAQ,GAAG,CAAC,gBAAgB;QACvC,UAAU;QACV,WAAW;QACX,6BAA6B;QAC7B,sBAAsB,CAAC,kDAAkD,EAAE,QAAQ,GAAG,CAAC,mBAAmB,EAAE;IAC9G;IAEA,MAAM,OAAO,IAAI,mJAAA,CAAA,SAAM,CAAC,IAAI,CAAC,UAAU,CAAC;QACtC;QACA,QAAQ;YAAC;SAA+C;IAC1D;IAEA,OAAO;AACT;AAGO,eAAe,QAAQ,SAAiB;IAC7C,IAAI;QACF,MAAM,OAAO;QACb,MAAM,SAAS,mJAAA,CAAA,SAAM,CAAC,MAAM,CAAC;YAAE,SAAS;YAAM;QAAK;QAEnD,MAAM,gBAAgB,QAAQ,GAAG,CAAC,qBAAqB;QAEvD,IAAI,CAAC,eAAe;YAClB,MAAM,IAAI,MAAM;QAClB;QAEA,MAAM,WAAW,MAAM,OAAO,YAAY,CAAC,MAAM,CAAC,GAAG,CAAC;YACpD;YACA,OAAO,GAAG,UAAU,IAAI,CAAC;QAC3B;QAEA,OAAO,SAAS,IAAI,CAAC,MAAM,IAAI,EAAE;IACnC,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,kCAAkC;QAChD,MAAM,IAAI,MAAM;IAClB;AACF;AAGO,eAAe,WAAW,SAAiB,EAAE,OAAc;IAChE,IAAI;QACF,MAAM,OAAO;QACb,MAAM,SAAS,mJAAA,CAAA,SAAM,CAAC,MAAM,CAAC;YAAE,SAAS;YAAM;QAAK;QAEnD,MAAM,gBAAgB,QAAQ,GAAG,CAAC,qBAAqB;QAEvD,IAAI,CAAC,eAAe;YAClB,MAAM,IAAI,MAAM;QAClB;QAEA,MAAM,WAAW,MAAM,OAAO,YAAY,CAAC,MAAM,CAAC,MAAM,CAAC;YACvD;YACA,OAAO,GAAG,UAAU,IAAI,CAAC;YACzB,kBAAkB;YAClB,aAAa;gBACX,QAAQ;oBAAC;iBAAQ;YACnB;QACF;QAEA,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,uCAAuC;QACrD,MAAM,IAAI,MAAM;IAClB;AACF;AAGO,eAAe,WAAW,SAAiB,EAAE,KAAa,EAAE,OAAc;IAC/E,IAAI;QACF,MAAM,OAAO;QACb,MAAM,SAAS,mJAAA,CAAA,SAAM,CAAC,MAAM,CAAC;YAAE,SAAS;YAAM;QAAK;QAEnD,MAAM,gBAAgB,QAAQ,GAAG,CAAC,qBAAqB;QAEvD,IAAI,CAAC,eAAe;YAClB,MAAM,IAAI,MAAM;QAClB;QAEA,MAAM,WAAW,MAAM,OAAO,YAAY,CAAC,MAAM,CAAC,MAAM,CAAC;YACvD;YACA,OAAO,GAAG,UAAU,CAAC,EAAE,OAAO;YAC9B,kBAAkB;YAClB,aAAa;gBACX,QAAQ;oBAAC;iBAAQ;YACnB;QACF;QAEA,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,wCAAwC;QACtD,MAAM,IAAI,MAAM;IAClB;AACF;;;IA7EsB;IAwBA;IA4BA;;AApDA,+OAAA;AAwBA,+OAAA;AA4BA,+OAAA", "debugId": null}}, {"offset": {"line": 235, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/AI_Projects/CRMcriadores/crm-interno/.next-internal/server/app/%28dashboard%29/dashboard/campaigns/page/actions.js%20%28server%20actions%20loader%29"], "sourcesContent": ["export {getData as '40416fe263e60966356356bb294165af55bf27c98c'} from 'ACTIONS_MODULE0'\nexport {appendData as '60b2397e35e4d22eb574fd297096247fb0b426b814'} from 'ACTIONS_MODULE0'\nexport {updateData as '700091b6dbbe7aa4a45146966c103d91060aef6152'} from 'ACTIONS_MODULE0'\n"], "names": [], "mappings": ";AAAA", "debugId": null}}, {"offset": {"line": 299, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/AI_Projects/CRMcriadores/crm-interno/lib/utils.ts"], "sourcesContent": ["/**\n * Transforma dados de array de arrays (formato do Google Sheets) \n * em array de objetos JSON usando a primeira linha como cabeçalhos\n */\nexport function transformData(data: any[][]): Record<string, any>[] {\n  if (!data || data.length === 0) {\n    return [];\n  }\n\n  // A primeira linha contém os cabeçalhos\n  const headers = data[0];\n  \n  // As linhas restantes contêm os dados\n  const rows = data.slice(1);\n\n  return rows.map((row) => {\n    const obj: Record<string, any> = {};\n    \n    headers.forEach((header, index) => {\n      // Usa o cabeçalho como chave e o valor da linha correspondente\n      obj[header] = row[index] || '';\n    });\n\n    return obj;\n  });\n}\n\n/**\n * Converte um objeto em array de valores na ordem dos cabeçalhos fornecidos\n */\nexport function objectToRowData(obj: Record<string, any>, headers: string[]): any[] {\n  return headers.map(header => obj[header] || '');\n}\n\n/**\n * Valida se os dados têm a estrutura esperada\n */\nexport function validateSheetData(data: any[][]): boolean {\n  return Array.isArray(data) && data.length > 0 && Array.isArray(data[0]);\n}\n\n/**\n * Limpa e normaliza strings vindas do Google Sheets\n */\nexport function cleanSheetValue(value: any): string {\n  if (value === null || value === undefined) {\n    return '';\n  }\n  \n  return String(value).trim();\n}\n\n/**\n * Converte valores de string para tipos apropriados\n */\nexport function parseSheetValue(value: string, type: 'string' | 'number' | 'boolean' | 'date' = 'string'): any {\n  const cleanValue = cleanSheetValue(value);\n  \n  if (cleanValue === '') {\n    return type === 'number' ? 0 : type === 'boolean' ? false : '';\n  }\n\n  switch (type) {\n    case 'number':\n      const num = parseFloat(cleanValue);\n      return isNaN(num) ? 0 : num;\n    \n    case 'boolean':\n      return cleanValue.toLowerCase() === 'true' || cleanValue === '1';\n    \n    case 'date':\n      const date = new Date(cleanValue);\n      return isNaN(date.getTime()) ? null : date;\n    \n    default:\n      return cleanValue;\n  }\n}\n\n/**\n * Formata dados para exibição\n */\nexport function formatDisplayValue(value: any, type: 'currency' | 'percentage' | 'date' | 'number' | 'text' = 'text'): string {\n  if (value === null || value === undefined || value === '') {\n    return '-';\n  }\n\n  switch (type) {\n    case 'currency':\n      const numValue = typeof value === 'number' ? value : parseFloat(value);\n      return isNaN(numValue) ? '-' : new Intl.NumberFormat('pt-BR', {\n        style: 'currency',\n        currency: 'BRL'\n      }).format(numValue);\n    \n    case 'percentage':\n      const pctValue = typeof value === 'number' ? value : parseFloat(value);\n      return isNaN(pctValue) ? '-' : `${pctValue.toFixed(1)}%`;\n    \n    case 'date':\n      const date = value instanceof Date ? value : new Date(value);\n      return isNaN(date.getTime()) ? '-' : date.toLocaleDateString('pt-BR');\n    \n    case 'number':\n      const numberValue = typeof value === 'number' ? value : parseFloat(value);\n      return isNaN(numberValue) ? '-' : new Intl.NumberFormat('pt-BR').format(numberValue);\n    \n    default:\n      return String(value);\n  }\n}\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;;;;AACM,SAAS,cAAc,IAAa;IACzC,IAAI,CAAC,QAAQ,KAAK,MAAM,KAAK,GAAG;QAC9B,OAAO,EAAE;IACX;IAEA,wCAAwC;IACxC,MAAM,UAAU,IAAI,CAAC,EAAE;IAEvB,sCAAsC;IACtC,MAAM,OAAO,KAAK,KAAK,CAAC;IAExB,OAAO,KAAK,GAAG,CAAC,CAAC;QACf,MAAM,MAA2B,CAAC;QAElC,QAAQ,OAAO,CAAC,CAAC,QAAQ;YACvB,+DAA+D;YAC/D,GAAG,CAAC,OAAO,GAAG,GAAG,CAAC,MAAM,IAAI;QAC9B;QAEA,OAAO;IACT;AACF;AAKO,SAAS,gBAAgB,GAAwB,EAAE,OAAiB;IACzE,OAAO,QAAQ,GAAG,CAAC,CAAA,SAAU,GAAG,CAAC,OAAO,IAAI;AAC9C;AAKO,SAAS,kBAAkB,IAAa;IAC7C,OAAO,MAAM,OAAO,CAAC,SAAS,KAAK,MAAM,GAAG,KAAK,MAAM,OAAO,CAAC,IAAI,CAAC,EAAE;AACxE;AAKO,SAAS,gBAAgB,KAAU;IACxC,IAAI,UAAU,QAAQ,UAAU,WAAW;QACzC,OAAO;IACT;IAEA,OAAO,OAAO,OAAO,IAAI;AAC3B;AAKO,SAAS,gBAAgB,KAAa,EAAE,OAAiD,QAAQ;IACtG,MAAM,aAAa,gBAAgB;IAEnC,IAAI,eAAe,IAAI;QACrB,OAAO,SAAS,WAAW,IAAI,SAAS,YAAY,QAAQ;IAC9D;IAEA,OAAQ;QACN,KAAK;YACH,MAAM,MAAM,WAAW;YACvB,OAAO,MAAM,OAAO,IAAI;QAE1B,KAAK;YACH,OAAO,WAAW,WAAW,OAAO,UAAU,eAAe;QAE/D,KAAK;YACH,MAAM,OAAO,IAAI,KAAK;YACtB,OAAO,MAAM,KAAK,OAAO,MAAM,OAAO;QAExC;YACE,OAAO;IACX;AACF;AAKO,SAAS,mBAAmB,KAAU,EAAE,OAA+D,MAAM;IAClH,IAAI,UAAU,QAAQ,UAAU,aAAa,UAAU,IAAI;QACzD,OAAO;IACT;IAEA,OAAQ;QACN,KAAK;YACH,MAAM,WAAW,OAAO,UAAU,WAAW,QAAQ,WAAW;YAChE,OAAO,MAAM,YAAY,MAAM,IAAI,KAAK,YAAY,CAAC,SAAS;gBAC5D,OAAO;gBACP,UAAU;YACZ,GAAG,MAAM,CAAC;QAEZ,KAAK;YACH,MAAM,WAAW,OAAO,UAAU,WAAW,QAAQ,WAAW;YAChE,OAAO,MAAM,YAAY,MAAM,GAAG,SAAS,OAAO,CAAC,GAAG,CAAC,CAAC;QAE1D,KAAK;YACH,MAAM,OAAO,iBAAiB,OAAO,QAAQ,IAAI,KAAK;YACtD,OAAO,MAAM,KAAK,OAAO,MAAM,MAAM,KAAK,kBAAkB,CAAC;QAE/D,KAAK;YACH,MAAM,cAAc,OAAO,UAAU,WAAW,QAAQ,WAAW;YACnE,OAAO,MAAM,eAAe,MAAM,IAAI,KAAK,YAAY,CAAC,SAAS,MAAM,CAAC;QAE1E;YACE,OAAO,OAAO;IAClB;AACF", "debugId": null}}, {"offset": {"line": 387, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/AI_Projects/CRMcriadores/crm-interno/components/CampaignCard.tsx"], "sourcesContent": ["import React from 'react';\n\ninterface CampaignCardProps {\n  campaignTitle: string;\n  brief: string;\n  startDate: string;\n  endDate: string;\n  status: string;\n}\n\nexport default function CampaignCard({\n  campaignTitle,\n  brief,\n  startDate,\n  endDate,\n  status\n}: CampaignCardProps) {\n  // Função para determinar a cor do badge baseado no status\n  const getStatusColor = (status: string): string => {\n    const statusColors: Record<string, string> = {\n      'Ativa': 'bg-green-100 text-green-800 border-green-200',\n      'Planejamento': 'bg-blue-100 text-blue-800 border-blue-200',\n      'Em Aprovação': 'bg-yellow-100 text-yellow-800 border-yellow-200',\n      'Pausada': 'bg-orange-100 text-orange-800 border-orange-200',\n      'Finalizada': 'bg-gray-100 text-gray-800 border-gray-200',\n      'Cancelada': 'bg-red-100 text-red-800 border-red-200',\n    };\n    \n    return statusColors[status] || 'bg-gray-100 text-gray-800 border-gray-200';\n  };\n\n  // Função para determinar o ícone baseado no status\n  const getStatusIcon = (status: string): string => {\n    const statusIcons: Record<string, string> = {\n      'Ativa': '🚀',\n      'Planejamento': '📋',\n      'Em Aprovação': '⏳',\n      'Pausada': '⏸️',\n      'Finalizada': '✅',\n      'Cancelada': '❌',\n    };\n    \n    return statusIcons[status] || '📊';\n  };\n\n  // Função para formatar data\n  const formatDate = (dateString: string): string => {\n    try {\n      const date = new Date(dateString);\n      return date.toLocaleDateString('pt-BR', {\n        day: '2-digit',\n        month: '2-digit',\n        year: 'numeric'\n      });\n    } catch {\n      return dateString;\n    }\n  };\n\n  // Função para calcular duração da campanha\n  const calculateDuration = (start: string, end: string): string => {\n    try {\n      const startDate = new Date(start);\n      const endDate = new Date(end);\n      const diffTime = Math.abs(endDate.getTime() - startDate.getTime());\n      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));\n      \n      if (diffDays === 1) return '1 dia';\n      if (diffDays < 30) return `${diffDays} dias`;\n      if (diffDays < 365) return `${Math.round(diffDays / 30)} meses`;\n      return `${Math.round(diffDays / 365)} anos`;\n    } catch {\n      return 'N/A';\n    }\n  };\n\n  return (\n    <div className=\"bg-surface border border-outline-variant rounded-2xl p-6 shadow-sm hover:shadow-md transition-shadow\">\n      {/* Cabeçalho */}\n      <div className=\"mb-4\">\n        <div className=\"flex items-start justify-between mb-3\">\n          <h3 className=\"text-xl font-semibold text-on-surface flex-1 mr-3\">\n            {campaignTitle}\n          </h3>\n          \n          {/* Badge de status */}\n          <div className=\"flex items-center\">\n            <span className=\"text-lg mr-2\">\n              {getStatusIcon(status)}\n            </span>\n            <span \n              className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium border ${getStatusColor(status)}`}\n            >\n              {status}\n            </span>\n          </div>\n        </div>\n      </div>\n\n      {/* Brief da campanha */}\n      <div className=\"mb-4\">\n        <p className=\"text-sm text-on-surface-variant leading-relaxed line-clamp-3\">\n          {brief}\n        </p>\n      </div>\n\n      {/* Informações de período */}\n      <div className=\"bg-surface-container rounded-xl p-4 mb-4\">\n        <div className=\"grid grid-cols-1 sm:grid-cols-3 gap-4 text-sm\">\n          <div>\n            <span className=\"text-on-surface-variant font-medium block mb-1\">\n              Início:\n            </span>\n            <span className=\"text-on-surface\">\n              {formatDate(startDate)}\n            </span>\n          </div>\n          \n          <div>\n            <span className=\"text-on-surface-variant font-medium block mb-1\">\n              Fim:\n            </span>\n            <span className=\"text-on-surface\">\n              {formatDate(endDate)}\n            </span>\n          </div>\n          \n          <div>\n            <span className=\"text-on-surface-variant font-medium block mb-1\">\n              Duração:\n            </span>\n            <span className=\"text-on-surface\">\n              {calculateDuration(startDate, endDate)}\n            </span>\n          </div>\n        </div>\n      </div>\n\n      {/* Footer com indicadores */}\n      <div className=\"flex items-center justify-between pt-4 border-t border-outline-variant\">\n        <div className=\"flex items-center text-xs text-on-surface-variant\">\n          <div className=\"w-1.5 h-1.5 bg-primary rounded-full mr-2\"></div>\n          <span>Campanha de Marketing</span>\n        </div>\n        \n        {/* Indicador de urgência baseado no status e datas */}\n        <div className=\"flex items-center\">\n          {status === 'Ativa' && (\n            <span className=\"text-xs text-green-600 font-medium\">\n              🟢 Em execução\n            </span>\n          )}\n          {status === 'Em Aprovação' && (\n            <span className=\"text-xs text-yellow-600 font-medium\">\n              ⏳ Aguardando\n            </span>\n          )}\n          {status === 'Planejamento' && (\n            <span className=\"text-xs text-blue-600 font-medium\">\n              📋 Em preparação\n            </span>\n          )}\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAUe,SAAS,aAAa,EACnC,aAAa,EACb,KAAK,EACL,SAAS,EACT,OAAO,EACP,MAAM,EACY;IAClB,0DAA0D;IAC1D,MAAM,iBAAiB,CAAC;QACtB,MAAM,eAAuC;YAC3C,SAAS;YACT,gBAAgB;YAChB,gBAAgB;YAChB,WAAW;YACX,cAAc;YACd,aAAa;QACf;QAEA,OAAO,YAAY,CAAC,OAAO,IAAI;IACjC;IAEA,mDAAmD;IACnD,MAAM,gBAAgB,CAAC;QACrB,MAAM,cAAsC;YAC1C,SAAS;YACT,gBAAgB;YAChB,gBAAgB;YAChB,WAAW;YACX,cAAc;YACd,aAAa;QACf;QAEA,OAAO,WAAW,CAAC,OAAO,IAAI;IAChC;IAEA,4BAA4B;IAC5B,MAAM,aAAa,CAAC;QAClB,IAAI;YACF,MAAM,OAAO,IAAI,KAAK;YACtB,OAAO,KAAK,kBAAkB,CAAC,SAAS;gBACtC,KAAK;gBACL,OAAO;gBACP,MAAM;YACR;QACF,EAAE,OAAM;YACN,OAAO;QACT;IACF;IAEA,2CAA2C;IAC3C,MAAM,oBAAoB,CAAC,OAAe;QACxC,IAAI;YACF,MAAM,YAAY,IAAI,KAAK;YAC3B,MAAM,UAAU,IAAI,KAAK;YACzB,MAAM,WAAW,KAAK,GAAG,CAAC,QAAQ,OAAO,KAAK,UAAU,OAAO;YAC/D,MAAM,WAAW,KAAK,IAAI,CAAC,WAAW,CAAC,OAAO,KAAK,KAAK,EAAE;YAE1D,IAAI,aAAa,GAAG,OAAO;YAC3B,IAAI,WAAW,IAAI,OAAO,GAAG,SAAS,KAAK,CAAC;YAC5C,IAAI,WAAW,KAAK,OAAO,GAAG,KAAK,KAAK,CAAC,WAAW,IAAI,MAAM,CAAC;YAC/D,OAAO,GAAG,KAAK,KAAK,CAAC,WAAW,KAAK,KAAK,CAAC;QAC7C,EAAE,OAAM;YACN,OAAO;QACT;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCACX;;;;;;sCAIH,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAK,WAAU;8CACb,cAAc;;;;;;8CAEjB,8OAAC;oCACC,WAAW,CAAC,2EAA2E,EAAE,eAAe,SAAS;8CAEhH;;;;;;;;;;;;;;;;;;;;;;;0BAOT,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAE,WAAU;8BACV;;;;;;;;;;;0BAKL,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;;8CACC,8OAAC;oCAAK,WAAU;8CAAiD;;;;;;8CAGjE,8OAAC;oCAAK,WAAU;8CACb,WAAW;;;;;;;;;;;;sCAIhB,8OAAC;;8CACC,8OAAC;oCAAK,WAAU;8CAAiD;;;;;;8CAGjE,8OAAC;oCAAK,WAAU;8CACb,WAAW;;;;;;;;;;;;sCAIhB,8OAAC;;8CACC,8OAAC;oCAAK,WAAU;8CAAiD;;;;;;8CAGjE,8OAAC;oCAAK,WAAU;8CACb,kBAAkB,WAAW;;;;;;;;;;;;;;;;;;;;;;;0BAOtC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;;;;;0CACf,8OAAC;0CAAK;;;;;;;;;;;;kCAIR,8OAAC;wBAAI,WAAU;;4BACZ,WAAW,yBACV,8OAAC;gCAAK,WAAU;0CAAqC;;;;;;4BAItD,WAAW,gCACV,8OAAC;gCAAK,WAAU;0CAAsC;;;;;;4BAIvD,WAAW,gCACV,8OAAC;gCAAK,WAAU;0CAAoC;;;;;;;;;;;;;;;;;;;;;;;;AAQhE", "debugId": null}}, {"offset": {"line": 678, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/AI_Projects/CRMcriadores/crm-interno/app/%28dashboard%29/dashboard/campaigns/page.tsx"], "sourcesContent": ["import React from 'react';\nimport { getData } from '@/app/actions/sheetsActions';\nimport { transformData } from '@/lib/utils';\nimport CampaignCard from '@/components/CampaignCard';\n\n// Dados de exemplo para demonstração (serão substituídos pelos dados do Google Sheets)\nconst mockCampaigns = [\n  {\n    campaignTitle: 'Campanha Verão 2024',\n    brief: 'Campanha focada em produtos de verão, destacando roupas leves e acessórios para a estação. Público-alvo: jovens de 18-35 anos.',\n    startDate: '2024-01-15',\n    endDate: '2024-03-15',\n    status: 'Ativa'\n  },\n  {\n    campaignTitle: 'Black Friday Especial',\n    brief: 'Promoção especial para Black Friday com descontos de até 70% em produtos selecionados. Foco em conversão e volume de vendas.',\n    startDate: '2024-11-20',\n    endDate: '2024-11-30',\n    status: 'Planejamento'\n  },\n  {\n    campaignTitle: 'Lançamento Produto X',\n    brief: 'Campanha de lançamento do novo produto X, incluindo teasers, reviews de influenciadores e eventos de lançamento.',\n    startDate: '2024-02-01',\n    endDate: '2024-02-28',\n    status: 'Em Aprovação'\n  }\n];\n\nexport default async function CampaignsPage() {\n  let campaigns = mockCampaigns;\n\n  // Tenta buscar dados do Google Sheets, mas usa dados mock se falhar\n  try {\n    const rawData = await getData('Campaigns');\n    if (rawData && rawData.length > 0) {\n      const transformedData = transformData(rawData);\n\n      // Mapeia os dados transformados para o formato esperado pelo componente\n      campaigns = transformedData.map((item: any) => ({\n        campaignTitle: item.campaignTitle || item.Título || item['Título da Campanha'] || 'Campanha sem título',\n        brief: item.brief || item.Briefing || item.Descrição || 'Descrição não informada',\n        startDate: item.startDate || item.Início || item['Data de Início'] || new Date().toISOString().split('T')[0],\n        endDate: item.endDate || item.Fim || item['Data de Fim'] || new Date().toISOString().split('T')[0],\n        status: item.status || item.Status || item.Situação || 'Planejamento'\n      }));\n    }\n  } catch (error) {\n    console.log('Usando dados de exemplo - Google Sheets não configurado ainda');\n  }\n\n  return (\n    <div>\n      <div className=\"mb-6\">\n        <h1 className=\"text-3xl font-bold text-on-surface mb-2\">Campanhas</h1>\n        <p className=\"text-on-surface-variant\">\n          Gerencie suas campanhas de marketing e acompanhe o progresso\n        </p>\n      </div>\n\n      <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\n        {campaigns.map((campaign, index) => (\n          <CampaignCard\n            key={index}\n            campaignTitle={campaign.campaignTitle}\n            brief={campaign.brief}\n            startDate={campaign.startDate}\n            endDate={campaign.endDate}\n            status={campaign.status}\n          />\n        ))}\n      </div>\n\n      {campaigns.length === 0 && (\n        <div className=\"text-center py-12\">\n          <div className=\"text-4xl mb-4\">📢</div>\n          <h3 className=\"text-lg font-medium text-on-surface mb-2\">\n            Nenhuma campanha encontrada\n          </h3>\n          <p className=\"text-sm text-on-surface-variant\">\n            Configure o Google Sheets para ver os dados das campanhas.\n          </p>\n        </div>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;;;;;AAEA,uFAAuF;AACvF,MAAM,gBAAgB;IACpB;QACE,eAAe;QACf,OAAO;QACP,WAAW;QACX,SAAS;QACT,QAAQ;IACV;IACA;QACE,eAAe;QACf,OAAO;QACP,WAAW;QACX,SAAS;QACT,QAAQ;IACV;IACA;QACE,eAAe;QACf,OAAO;QACP,WAAW;QACX,SAAS;QACT,QAAQ;IACV;CACD;AAEc,eAAe;IAC5B,IAAI,YAAY;IAEhB,oEAAoE;IACpE,IAAI;QACF,MAAM,UAAU,MAAM,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD,EAAE;QAC9B,IAAI,WAAW,QAAQ,MAAM,GAAG,GAAG;YACjC,MAAM,kBAAkB,CAAA,GAAA,4GAAA,CAAA,gBAAa,AAAD,EAAE;YAEtC,wEAAwE;YACxE,YAAY,gBAAgB,GAAG,CAAC,CAAC,OAAc,CAAC;oBAC9C,eAAe,KAAK,aAAa,IAAI,KAAK,MAAM,IAAI,IAAI,CAAC,qBAAqB,IAAI;oBAClF,OAAO,KAAK,KAAK,IAAI,KAAK,QAAQ,IAAI,KAAK,SAAS,IAAI;oBACxD,WAAW,KAAK,SAAS,IAAI,KAAK,MAAM,IAAI,IAAI,CAAC,iBAAiB,IAAI,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;oBAC5G,SAAS,KAAK,OAAO,IAAI,KAAK,GAAG,IAAI,IAAI,CAAC,cAAc,IAAI,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;oBAClG,QAAQ,KAAK,MAAM,IAAI,KAAK,MAAM,IAAI,KAAK,QAAQ,IAAI;gBACzD,CAAC;QACH;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,GAAG,CAAC;IACd;IAEA,qBACE,8OAAC;;0BACC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAA0C;;;;;;kCACxD,8OAAC;wBAAE,WAAU;kCAA0B;;;;;;;;;;;;0BAKzC,8OAAC;gBAAI,WAAU;0BACZ,UAAU,GAAG,CAAC,CAAC,UAAU,sBACxB,8OAAC,2HAAA,CAAA,UAAY;wBAEX,eAAe,SAAS,aAAa;wBACrC,OAAO,SAAS,KAAK;wBACrB,WAAW,SAAS,SAAS;wBAC7B,SAAS,SAAS,OAAO;wBACzB,QAAQ,SAAS,MAAM;uBALlB;;;;;;;;;;YAUV,UAAU,MAAM,KAAK,mBACpB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCAAgB;;;;;;kCAC/B,8OAAC;wBAAG,WAAU;kCAA2C;;;;;;kCAGzD,8OAAC;wBAAE,WAAU;kCAAkC;;;;;;;;;;;;;;;;;;AAOzD", "debugId": null}}]}
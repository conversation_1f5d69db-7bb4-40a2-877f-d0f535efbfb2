{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/AI_Projects/CRMcriadores/crm-interno/node_modules/googleapis/build/src/apis/tagmanager/v1.js"], "sourcesContent": ["\"use strict\";\n// Copyright 2020 Google LLC\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//    http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.tagmanager_v1 = void 0;\n/* eslint-disable @typescript-eslint/no-explicit-any */\n/* eslint-disable @typescript-eslint/no-unused-vars */\n/* eslint-disable @typescript-eslint/no-empty-interface */\n/* eslint-disable @typescript-eslint/no-namespace */\n/* eslint-disable no-irregular-whitespace */\nconst googleapis_common_1 = require(\"googleapis-common\");\nvar tagmanager_v1;\n(function (tagmanager_v1) {\n    /**\n     * Tag Manager API\n     *\n     * This API allows clients to access and modify container and tag configuration.\n     *\n     * @example\n     * ```js\n     * const {google} = require('googleapis');\n     * const tagmanager = google.tagmanager('v1');\n     * ```\n     */\n    class Tagmanager {\n        context;\n        accounts;\n        constructor(options, google) {\n            this.context = {\n                _options: options || {},\n                google,\n            };\n            this.accounts = new Resource$Accounts(this.context);\n        }\n    }\n    tagmanager_v1.Tagmanager = Tagmanager;\n    class Resource$Accounts {\n        context;\n        containers;\n        permissions;\n        constructor(context) {\n            this.context = context;\n            this.containers = new Resource$Accounts$Containers(this.context);\n            this.permissions = new Resource$Accounts$Permissions(this.context);\n        }\n        get(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback || {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://tagmanager.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/tagmanager/v1/accounts/{accountId}').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'GET',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['accountId'],\n                pathParams: ['accountId'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        list(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback || {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://tagmanager.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/tagmanager/v1/accounts').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'GET',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: [],\n                pathParams: [],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        update(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback || {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://tagmanager.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/tagmanager/v1/accounts/{accountId}').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'PUT',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['accountId'],\n                pathParams: ['accountId'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n    }\n    tagmanager_v1.Resource$Accounts = Resource$Accounts;\n    class Resource$Accounts$Containers {\n        context;\n        environments;\n        folders;\n        move_folders;\n        reauthorize_environments;\n        tags;\n        triggers;\n        variables;\n        versions;\n        constructor(context) {\n            this.context = context;\n            this.environments = new Resource$Accounts$Containers$Environments(this.context);\n            this.folders = new Resource$Accounts$Containers$Folders(this.context);\n            this.move_folders = new Resource$Accounts$Containers$Move_folders(this.context);\n            this.reauthorize_environments =\n                new Resource$Accounts$Containers$Reauthorize_environments(this.context);\n            this.tags = new Resource$Accounts$Containers$Tags(this.context);\n            this.triggers = new Resource$Accounts$Containers$Triggers(this.context);\n            this.variables = new Resource$Accounts$Containers$Variables(this.context);\n            this.versions = new Resource$Accounts$Containers$Versions(this.context);\n        }\n        create(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://tagmanager.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/tagmanager/v1/accounts/{accountId}/containers').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'POST',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['accountId'],\n                pathParams: ['accountId'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        delete(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://tagmanager.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl +\n                        '/tagmanager/v1/accounts/{accountId}/containers/{containerId}').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'DELETE',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['accountId', 'containerId'],\n                pathParams: ['accountId', 'containerId'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        get(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://tagmanager.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl +\n                        '/tagmanager/v1/accounts/{accountId}/containers/{containerId}').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'GET',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['accountId', 'containerId'],\n                pathParams: ['accountId', 'containerId'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        list(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://tagmanager.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/tagmanager/v1/accounts/{accountId}/containers').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'GET',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['accountId'],\n                pathParams: ['accountId'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        update(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://tagmanager.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl +\n                        '/tagmanager/v1/accounts/{accountId}/containers/{containerId}').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'PUT',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['accountId', 'containerId'],\n                pathParams: ['accountId', 'containerId'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n    }\n    tagmanager_v1.Resource$Accounts$Containers = Resource$Accounts$Containers;\n    class Resource$Accounts$Containers$Environments {\n        context;\n        constructor(context) {\n            this.context = context;\n        }\n        create(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://tagmanager.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl +\n                        '/tagmanager/v1/accounts/{accountId}/containers/{containerId}/environments').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'POST',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['accountId', 'containerId'],\n                pathParams: ['accountId', 'containerId'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        delete(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://tagmanager.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl +\n                        '/tagmanager/v1/accounts/{accountId}/containers/{containerId}/environments/{environmentId}').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'DELETE',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['accountId', 'containerId', 'environmentId'],\n                pathParams: ['accountId', 'containerId', 'environmentId'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        get(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://tagmanager.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl +\n                        '/tagmanager/v1/accounts/{accountId}/containers/{containerId}/environments/{environmentId}').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'GET',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['accountId', 'containerId', 'environmentId'],\n                pathParams: ['accountId', 'containerId', 'environmentId'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        list(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://tagmanager.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl +\n                        '/tagmanager/v1/accounts/{accountId}/containers/{containerId}/environments').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'GET',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['accountId', 'containerId'],\n                pathParams: ['accountId', 'containerId'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        update(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://tagmanager.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl +\n                        '/tagmanager/v1/accounts/{accountId}/containers/{containerId}/environments/{environmentId}').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'PUT',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['accountId', 'containerId', 'environmentId'],\n                pathParams: ['accountId', 'containerId', 'environmentId'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n    }\n    tagmanager_v1.Resource$Accounts$Containers$Environments = Resource$Accounts$Containers$Environments;\n    class Resource$Accounts$Containers$Folders {\n        context;\n        entities;\n        constructor(context) {\n            this.context = context;\n            this.entities = new Resource$Accounts$Containers$Folders$Entities(this.context);\n        }\n        create(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://tagmanager.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl +\n                        '/tagmanager/v1/accounts/{accountId}/containers/{containerId}/folders').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'POST',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['accountId', 'containerId'],\n                pathParams: ['accountId', 'containerId'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        delete(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://tagmanager.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl +\n                        '/tagmanager/v1/accounts/{accountId}/containers/{containerId}/folders/{folderId}').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'DELETE',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['accountId', 'containerId', 'folderId'],\n                pathParams: ['accountId', 'containerId', 'folderId'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        get(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://tagmanager.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl +\n                        '/tagmanager/v1/accounts/{accountId}/containers/{containerId}/folders/{folderId}').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'GET',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['accountId', 'containerId', 'folderId'],\n                pathParams: ['accountId', 'containerId', 'folderId'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        list(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://tagmanager.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl +\n                        '/tagmanager/v1/accounts/{accountId}/containers/{containerId}/folders').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'GET',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['accountId', 'containerId'],\n                pathParams: ['accountId', 'containerId'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        update(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://tagmanager.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl +\n                        '/tagmanager/v1/accounts/{accountId}/containers/{containerId}/folders/{folderId}').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'PUT',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['accountId', 'containerId', 'folderId'],\n                pathParams: ['accountId', 'containerId', 'folderId'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n    }\n    tagmanager_v1.Resource$Accounts$Containers$Folders = Resource$Accounts$Containers$Folders;\n    class Resource$Accounts$Containers$Folders$Entities {\n        context;\n        constructor(context) {\n            this.context = context;\n        }\n        list(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params =\n                    {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://tagmanager.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl +\n                        '/tagmanager/v1/accounts/{accountId}/containers/{containerId}/folders/{folderId}/entities').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'GET',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['accountId', 'containerId', 'folderId'],\n                pathParams: ['accountId', 'containerId', 'folderId'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n    }\n    tagmanager_v1.Resource$Accounts$Containers$Folders$Entities = Resource$Accounts$Containers$Folders$Entities;\n    class Resource$Accounts$Containers$Move_folders {\n        context;\n        constructor(context) {\n            this.context = context;\n        }\n        update(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://tagmanager.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl +\n                        '/tagmanager/v1/accounts/{accountId}/containers/{containerId}/move_folders/{folderId}').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'PUT',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['accountId', 'containerId', 'folderId'],\n                pathParams: ['accountId', 'containerId', 'folderId'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n    }\n    tagmanager_v1.Resource$Accounts$Containers$Move_folders = Resource$Accounts$Containers$Move_folders;\n    class Resource$Accounts$Containers$Reauthorize_environments {\n        context;\n        constructor(context) {\n            this.context = context;\n        }\n        update(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params =\n                    {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://tagmanager.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl +\n                        '/tagmanager/v1/accounts/{accountId}/containers/{containerId}/reauthorize_environments/{environmentId}').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'PUT',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['accountId', 'containerId', 'environmentId'],\n                pathParams: ['accountId', 'containerId', 'environmentId'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n    }\n    tagmanager_v1.Resource$Accounts$Containers$Reauthorize_environments = Resource$Accounts$Containers$Reauthorize_environments;\n    class Resource$Accounts$Containers$Tags {\n        context;\n        constructor(context) {\n            this.context = context;\n        }\n        create(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://tagmanager.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl +\n                        '/tagmanager/v1/accounts/{accountId}/containers/{containerId}/tags').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'POST',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['accountId', 'containerId'],\n                pathParams: ['accountId', 'containerId'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        delete(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://tagmanager.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl +\n                        '/tagmanager/v1/accounts/{accountId}/containers/{containerId}/tags/{tagId}').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'DELETE',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['accountId', 'containerId', 'tagId'],\n                pathParams: ['accountId', 'containerId', 'tagId'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        get(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://tagmanager.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl +\n                        '/tagmanager/v1/accounts/{accountId}/containers/{containerId}/tags/{tagId}').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'GET',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['accountId', 'containerId', 'tagId'],\n                pathParams: ['accountId', 'containerId', 'tagId'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        list(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://tagmanager.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl +\n                        '/tagmanager/v1/accounts/{accountId}/containers/{containerId}/tags').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'GET',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['accountId', 'containerId'],\n                pathParams: ['accountId', 'containerId'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        update(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://tagmanager.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl +\n                        '/tagmanager/v1/accounts/{accountId}/containers/{containerId}/tags/{tagId}').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'PUT',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['accountId', 'containerId', 'tagId'],\n                pathParams: ['accountId', 'containerId', 'tagId'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n    }\n    tagmanager_v1.Resource$Accounts$Containers$Tags = Resource$Accounts$Containers$Tags;\n    class Resource$Accounts$Containers$Triggers {\n        context;\n        constructor(context) {\n            this.context = context;\n        }\n        create(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://tagmanager.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl +\n                        '/tagmanager/v1/accounts/{accountId}/containers/{containerId}/triggers').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'POST',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['accountId', 'containerId'],\n                pathParams: ['accountId', 'containerId'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        delete(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://tagmanager.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl +\n                        '/tagmanager/v1/accounts/{accountId}/containers/{containerId}/triggers/{triggerId}').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'DELETE',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['accountId', 'containerId', 'triggerId'],\n                pathParams: ['accountId', 'containerId', 'triggerId'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        get(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://tagmanager.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl +\n                        '/tagmanager/v1/accounts/{accountId}/containers/{containerId}/triggers/{triggerId}').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'GET',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['accountId', 'containerId', 'triggerId'],\n                pathParams: ['accountId', 'containerId', 'triggerId'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        list(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://tagmanager.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl +\n                        '/tagmanager/v1/accounts/{accountId}/containers/{containerId}/triggers').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'GET',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['accountId', 'containerId'],\n                pathParams: ['accountId', 'containerId'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        update(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://tagmanager.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl +\n                        '/tagmanager/v1/accounts/{accountId}/containers/{containerId}/triggers/{triggerId}').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'PUT',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['accountId', 'containerId', 'triggerId'],\n                pathParams: ['accountId', 'containerId', 'triggerId'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n    }\n    tagmanager_v1.Resource$Accounts$Containers$Triggers = Resource$Accounts$Containers$Triggers;\n    class Resource$Accounts$Containers$Variables {\n        context;\n        constructor(context) {\n            this.context = context;\n        }\n        create(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://tagmanager.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl +\n                        '/tagmanager/v1/accounts/{accountId}/containers/{containerId}/variables').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'POST',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['accountId', 'containerId'],\n                pathParams: ['accountId', 'containerId'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        delete(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://tagmanager.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl +\n                        '/tagmanager/v1/accounts/{accountId}/containers/{containerId}/variables/{variableId}').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'DELETE',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['accountId', 'containerId', 'variableId'],\n                pathParams: ['accountId', 'containerId', 'variableId'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        get(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://tagmanager.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl +\n                        '/tagmanager/v1/accounts/{accountId}/containers/{containerId}/variables/{variableId}').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'GET',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['accountId', 'containerId', 'variableId'],\n                pathParams: ['accountId', 'containerId', 'variableId'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        list(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://tagmanager.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl +\n                        '/tagmanager/v1/accounts/{accountId}/containers/{containerId}/variables').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'GET',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['accountId', 'containerId'],\n                pathParams: ['accountId', 'containerId'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        update(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://tagmanager.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl +\n                        '/tagmanager/v1/accounts/{accountId}/containers/{containerId}/variables/{variableId}').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'PUT',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['accountId', 'containerId', 'variableId'],\n                pathParams: ['accountId', 'containerId', 'variableId'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n    }\n    tagmanager_v1.Resource$Accounts$Containers$Variables = Resource$Accounts$Containers$Variables;\n    class Resource$Accounts$Containers$Versions {\n        context;\n        constructor(context) {\n            this.context = context;\n        }\n        create(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://tagmanager.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl +\n                        '/tagmanager/v1/accounts/{accountId}/containers/{containerId}/versions').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'POST',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['accountId', 'containerId'],\n                pathParams: ['accountId', 'containerId'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        delete(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://tagmanager.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl +\n                        '/tagmanager/v1/accounts/{accountId}/containers/{containerId}/versions/{containerVersionId}').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'DELETE',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['accountId', 'containerId', 'containerVersionId'],\n                pathParams: ['accountId', 'containerId', 'containerVersionId'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        get(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://tagmanager.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl +\n                        '/tagmanager/v1/accounts/{accountId}/containers/{containerId}/versions/{containerVersionId}').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'GET',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['accountId', 'containerId', 'containerVersionId'],\n                pathParams: ['accountId', 'containerId', 'containerVersionId'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        list(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://tagmanager.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl +\n                        '/tagmanager/v1/accounts/{accountId}/containers/{containerId}/versions').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'GET',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['accountId', 'containerId'],\n                pathParams: ['accountId', 'containerId'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        publish(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://tagmanager.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl +\n                        '/tagmanager/v1/accounts/{accountId}/containers/{containerId}/versions/{containerVersionId}/publish').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'POST',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['accountId', 'containerId', 'containerVersionId'],\n                pathParams: ['accountId', 'containerId', 'containerVersionId'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        restore(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://tagmanager.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl +\n                        '/tagmanager/v1/accounts/{accountId}/containers/{containerId}/versions/{containerVersionId}/restore').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'POST',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['accountId', 'containerId', 'containerVersionId'],\n                pathParams: ['accountId', 'containerId', 'containerVersionId'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        undelete(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://tagmanager.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl +\n                        '/tagmanager/v1/accounts/{accountId}/containers/{containerId}/versions/{containerVersionId}/undelete').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'POST',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['accountId', 'containerId', 'containerVersionId'],\n                pathParams: ['accountId', 'containerId', 'containerVersionId'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        update(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://tagmanager.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl +\n                        '/tagmanager/v1/accounts/{accountId}/containers/{containerId}/versions/{containerVersionId}').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'PUT',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['accountId', 'containerId', 'containerVersionId'],\n                pathParams: ['accountId', 'containerId', 'containerVersionId'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n    }\n    tagmanager_v1.Resource$Accounts$Containers$Versions = Resource$Accounts$Containers$Versions;\n    class Resource$Accounts$Permissions {\n        context;\n        constructor(context) {\n            this.context = context;\n        }\n        create(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://tagmanager.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/tagmanager/v1/accounts/{accountId}/permissions').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'POST',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['accountId'],\n                pathParams: ['accountId'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        delete(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://tagmanager.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl +\n                        '/tagmanager/v1/accounts/{accountId}/permissions/{permissionId}').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'DELETE',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['accountId', 'permissionId'],\n                pathParams: ['accountId', 'permissionId'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        get(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://tagmanager.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl +\n                        '/tagmanager/v1/accounts/{accountId}/permissions/{permissionId}').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'GET',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['accountId', 'permissionId'],\n                pathParams: ['accountId', 'permissionId'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        list(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://tagmanager.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/tagmanager/v1/accounts/{accountId}/permissions').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'GET',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['accountId'],\n                pathParams: ['accountId'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        update(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://tagmanager.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl +\n                        '/tagmanager/v1/accounts/{accountId}/permissions/{permissionId}').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'PUT',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['accountId', 'permissionId'],\n                pathParams: ['accountId', 'permissionId'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n    }\n    tagmanager_v1.Resource$Accounts$Permissions = Resource$Accounts$Permissions;\n})(tagmanager_v1 || (exports.tagmanager_v1 = tagmanager_v1 = {}));\n"], "names": [], "mappings": "AAAA;AACA,4BAA4B;AAC5B,kEAAkE;AAClE,mEAAmE;AACnE,0CAA0C;AAC1C,EAAE;AACF,gDAAgD;AAChD,EAAE;AACF,sEAAsE;AACtE,oEAAoE;AACpE,2EAA2E;AAC3E,sEAAsE;AACtE,iCAAiC;AACjC,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,QAAQ,aAAa,GAAG,KAAK;AAC7B,qDAAqD,GACrD,oDAAoD,GACpD,wDAAwD,GACxD,kDAAkD,GAClD,0CAA0C,GAC1C,MAAM;AACN,IAAI;AACJ,CAAC,SAAU,aAAa;IACpB;;;;;;;;;;KAUC,GACD,MAAM;QACF,QAAQ;QACR,SAAS;QACT,YAAY,OAAO,EAAE,MAAM,CAAE;YACzB,IAAI,CAAC,OAAO,GAAG;gBACX,UAAU,WAAW,CAAC;gBACtB;YACJ;YACA,IAAI,CAAC,QAAQ,GAAG,IAAI,kBAAkB,IAAI,CAAC,OAAO;QACtD;IACJ;IACA,cAAc,UAAU,GAAG;IAC3B,MAAM;QACF,QAAQ;QACR,WAAW;QACX,YAAY;QACZ,YAAY,OAAO,CAAE;YACjB,IAAI,CAAC,OAAO,GAAG;YACf,IAAI,CAAC,UAAU,GAAG,IAAI,6BAA6B,IAAI,CAAC,OAAO;YAC/D,IAAI,CAAC,WAAW,GAAG,IAAI,8BAA8B,IAAI,CAAC,OAAO;QACrE;QACA,IAAI,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAC/C,IAAI,SAAU,oBAAoB,CAAC;YACnC,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,qCAAqC,EAAE,OAAO,CAAC,gBAAgB;oBAC/E,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAY;gBAC7B,YAAY;oBAAC;iBAAY;gBACzB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,KAAK,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAChD,IAAI,SAAU,oBAAoB,CAAC;YACnC,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,yBAAyB,EAAE,OAAO,CAAC,gBAAgB;oBACnE,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB,EAAE;gBAClB,YAAY,EAAE;gBACd,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,OAAO,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAClD,IAAI,SAAU,oBAAoB,CAAC;YACnC,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,qCAAqC,EAAE,OAAO,CAAC,gBAAgB;oBAC/E,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAY;gBAC7B,YAAY;oBAAC;iBAAY;gBACzB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;IACJ;IACA,cAAc,iBAAiB,GAAG;IAClC,MAAM;QACF,QAAQ;QACR,aAAa;QACb,QAAQ;QACR,aAAa;QACb,yBAAyB;QACzB,KAAK;QACL,SAAS;QACT,UAAU;QACV,SAAS;QACT,YAAY,OAAO,CAAE;YACjB,IAAI,CAAC,OAAO,GAAG;YACf,IAAI,CAAC,YAAY,GAAG,IAAI,0CAA0C,IAAI,CAAC,OAAO;YAC9E,IAAI,CAAC,OAAO,GAAG,IAAI,qCAAqC,IAAI,CAAC,OAAO;YACpE,IAAI,CAAC,YAAY,GAAG,IAAI,0CAA0C,IAAI,CAAC,OAAO;YAC9E,IAAI,CAAC,wBAAwB,GACzB,IAAI,sDAAsD,IAAI,CAAC,OAAO;YAC1E,IAAI,CAAC,IAAI,GAAG,IAAI,kCAAkC,IAAI,CAAC,OAAO;YAC9D,IAAI,CAAC,QAAQ,GAAG,IAAI,sCAAsC,IAAI,CAAC,OAAO;YACtE,IAAI,CAAC,SAAS,GAAG,IAAI,uCAAuC,IAAI,CAAC,OAAO;YACxE,IAAI,CAAC,QAAQ,GAAG,IAAI,sCAAsC,IAAI,CAAC,OAAO;QAC1E;QACA,OAAO,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAClD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,gDAAgD,EAAE,OAAO,CAAC,gBAAgB;oBAC1F,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAY;gBAC7B,YAAY;oBAAC;iBAAY;gBACzB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,OAAO,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAClD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UACF,8DAA8D,EAAE,OAAO,CAAC,gBAAgB;oBAC5F,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;oBAAa;iBAAc;gBAC5C,YAAY;oBAAC;oBAAa;iBAAc;gBACxC,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,IAAI,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAC/C,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UACF,8DAA8D,EAAE,OAAO,CAAC,gBAAgB;oBAC5F,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;oBAAa;iBAAc;gBAC5C,YAAY;oBAAC;oBAAa;iBAAc;gBACxC,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,KAAK,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAChD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,gDAAgD,EAAE,OAAO,CAAC,gBAAgB;oBAC1F,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAY;gBAC7B,YAAY;oBAAC;iBAAY;gBACzB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,OAAO,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAClD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UACF,8DAA8D,EAAE,OAAO,CAAC,gBAAgB;oBAC5F,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;oBAAa;iBAAc;gBAC5C,YAAY;oBAAC;oBAAa;iBAAc;gBACxC,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;IACJ;IACA,cAAc,4BAA4B,GAAG;IAC7C,MAAM;QACF,QAAQ;QACR,YAAY,OAAO,CAAE;YACjB,IAAI,CAAC,OAAO,GAAG;QACnB;QACA,OAAO,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAClD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UACF,2EAA2E,EAAE,OAAO,CAAC,gBAAgB;oBACzG,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;oBAAa;iBAAc;gBAC5C,YAAY;oBAAC;oBAAa;iBAAc;gBACxC,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,OAAO,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAClD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UACF,2FAA2F,EAAE,OAAO,CAAC,gBAAgB;oBACzH,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;oBAAa;oBAAe;iBAAgB;gBAC7D,YAAY;oBAAC;oBAAa;oBAAe;iBAAgB;gBACzD,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,IAAI,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAC/C,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UACF,2FAA2F,EAAE,OAAO,CAAC,gBAAgB;oBACzH,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;oBAAa;oBAAe;iBAAgB;gBAC7D,YAAY;oBAAC;oBAAa;oBAAe;iBAAgB;gBACzD,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,KAAK,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAChD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UACF,2EAA2E,EAAE,OAAO,CAAC,gBAAgB;oBACzG,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;oBAAa;iBAAc;gBAC5C,YAAY;oBAAC;oBAAa;iBAAc;gBACxC,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,OAAO,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAClD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UACF,2FAA2F,EAAE,OAAO,CAAC,gBAAgB;oBACzH,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;oBAAa;oBAAe;iBAAgB;gBAC7D,YAAY;oBAAC;oBAAa;oBAAe;iBAAgB;gBACzD,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;IACJ;IACA,cAAc,yCAAyC,GAAG;IAC1D,MAAM;QACF,QAAQ;QACR,SAAS;QACT,YAAY,OAAO,CAAE;YACjB,IAAI,CAAC,OAAO,GAAG;YACf,IAAI,CAAC,QAAQ,GAAG,IAAI,8CAA8C,IAAI,CAAC,OAAO;QAClF;QACA,OAAO,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAClD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UACF,sEAAsE,EAAE,OAAO,CAAC,gBAAgB;oBACpG,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;oBAAa;iBAAc;gBAC5C,YAAY;oBAAC;oBAAa;iBAAc;gBACxC,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,OAAO,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAClD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UACF,iFAAiF,EAAE,OAAO,CAAC,gBAAgB;oBAC/G,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;oBAAa;oBAAe;iBAAW;gBACxD,YAAY;oBAAC;oBAAa;oBAAe;iBAAW;gBACpD,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,IAAI,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAC/C,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UACF,iFAAiF,EAAE,OAAO,CAAC,gBAAgB;oBAC/G,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;oBAAa;oBAAe;iBAAW;gBACxD,YAAY;oBAAC;oBAAa;oBAAe;iBAAW;gBACpD,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,KAAK,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAChD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UACF,sEAAsE,EAAE,OAAO,CAAC,gBAAgB;oBACpG,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;oBAAa;iBAAc;gBAC5C,YAAY;oBAAC;oBAAa;iBAAc;gBACxC,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,OAAO,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAClD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UACF,iFAAiF,EAAE,OAAO,CAAC,gBAAgB;oBAC/G,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;oBAAa;oBAAe;iBAAW;gBACxD,YAAY;oBAAC;oBAAa;oBAAe;iBAAW;gBACpD,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;IACJ;IACA,cAAc,oCAAoC,GAAG;IACrD,MAAM;QACF,QAAQ;QACR,YAAY,OAAO,CAAE;YACjB,IAAI,CAAC,OAAO,GAAG;QACnB;QACA,KAAK,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAChD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SACI,CAAC;gBACL,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UACF,0FAA0F,EAAE,OAAO,CAAC,gBAAgB;oBACxH,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;oBAAa;oBAAe;iBAAW;gBACxD,YAAY;oBAAC;oBAAa;oBAAe;iBAAW;gBACpD,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;IACJ;IACA,cAAc,6CAA6C,GAAG;IAC9D,MAAM;QACF,QAAQ;QACR,YAAY,OAAO,CAAE;YACjB,IAAI,CAAC,OAAO,GAAG;QACnB;QACA,OAAO,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAClD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UACF,sFAAsF,EAAE,OAAO,CAAC,gBAAgB;oBACpH,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;oBAAa;oBAAe;iBAAW;gBACxD,YAAY;oBAAC;oBAAa;oBAAe;iBAAW;gBACpD,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;IACJ;IACA,cAAc,yCAAyC,GAAG;IAC1D,MAAM;QACF,QAAQ;QACR,YAAY,OAAO,CAAE;YACjB,IAAI,CAAC,OAAO,GAAG;QACnB;QACA,OAAO,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAClD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SACI,CAAC;gBACL,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UACF,uGAAuG,EAAE,OAAO,CAAC,gBAAgB;oBACrI,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;oBAAa;oBAAe;iBAAgB;gBAC7D,YAAY;oBAAC;oBAAa;oBAAe;iBAAgB;gBACzD,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;IACJ;IACA,cAAc,qDAAqD,GAAG;IACtE,MAAM;QACF,QAAQ;QACR,YAAY,OAAO,CAAE;YACjB,IAAI,CAAC,OAAO,GAAG;QACnB;QACA,OAAO,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAClD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UACF,mEAAmE,EAAE,OAAO,CAAC,gBAAgB;oBACjG,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;oBAAa;iBAAc;gBAC5C,YAAY;oBAAC;oBAAa;iBAAc;gBACxC,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,OAAO,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAClD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UACF,2EAA2E,EAAE,OAAO,CAAC,gBAAgB;oBACzG,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;oBAAa;oBAAe;iBAAQ;gBACrD,YAAY;oBAAC;oBAAa;oBAAe;iBAAQ;gBACjD,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,IAAI,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAC/C,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UACF,2EAA2E,EAAE,OAAO,CAAC,gBAAgB;oBACzG,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;oBAAa;oBAAe;iBAAQ;gBACrD,YAAY;oBAAC;oBAAa;oBAAe;iBAAQ;gBACjD,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,KAAK,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAChD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UACF,mEAAmE,EAAE,OAAO,CAAC,gBAAgB;oBACjG,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;oBAAa;iBAAc;gBAC5C,YAAY;oBAAC;oBAAa;iBAAc;gBACxC,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,OAAO,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAClD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UACF,2EAA2E,EAAE,OAAO,CAAC,gBAAgB;oBACzG,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;oBAAa;oBAAe;iBAAQ;gBACrD,YAAY;oBAAC;oBAAa;oBAAe;iBAAQ;gBACjD,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;IACJ;IACA,cAAc,iCAAiC,GAAG;IAClD,MAAM;QACF,QAAQ;QACR,YAAY,OAAO,CAAE;YACjB,IAAI,CAAC,OAAO,GAAG;QACnB;QACA,OAAO,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAClD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UACF,uEAAuE,EAAE,OAAO,CAAC,gBAAgB;oBACrG,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;oBAAa;iBAAc;gBAC5C,YAAY;oBAAC;oBAAa;iBAAc;gBACxC,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,OAAO,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAClD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UACF,mFAAmF,EAAE,OAAO,CAAC,gBAAgB;oBACjH,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;oBAAa;oBAAe;iBAAY;gBACzD,YAAY;oBAAC;oBAAa;oBAAe;iBAAY;gBACrD,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,IAAI,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAC/C,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UACF,mFAAmF,EAAE,OAAO,CAAC,gBAAgB;oBACjH,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;oBAAa;oBAAe;iBAAY;gBACzD,YAAY;oBAAC;oBAAa;oBAAe;iBAAY;gBACrD,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,KAAK,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAChD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UACF,uEAAuE,EAAE,OAAO,CAAC,gBAAgB;oBACrG,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;oBAAa;iBAAc;gBAC5C,YAAY;oBAAC;oBAAa;iBAAc;gBACxC,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,OAAO,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAClD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UACF,mFAAmF,EAAE,OAAO,CAAC,gBAAgB;oBACjH,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;oBAAa;oBAAe;iBAAY;gBACzD,YAAY;oBAAC;oBAAa;oBAAe;iBAAY;gBACrD,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;IACJ;IACA,cAAc,qCAAqC,GAAG;IACtD,MAAM;QACF,QAAQ;QACR,YAAY,OAAO,CAAE;YACjB,IAAI,CAAC,OAAO,GAAG;QACnB;QACA,OAAO,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAClD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UACF,wEAAwE,EAAE,OAAO,CAAC,gBAAgB;oBACtG,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;oBAAa;iBAAc;gBAC5C,YAAY;oBAAC;oBAAa;iBAAc;gBACxC,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,OAAO,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAClD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UACF,qFAAqF,EAAE,OAAO,CAAC,gBAAgB;oBACnH,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;oBAAa;oBAAe;iBAAa;gBAC1D,YAAY;oBAAC;oBAAa;oBAAe;iBAAa;gBACtD,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,IAAI,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAC/C,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UACF,qFAAqF,EAAE,OAAO,CAAC,gBAAgB;oBACnH,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;oBAAa;oBAAe;iBAAa;gBAC1D,YAAY;oBAAC;oBAAa;oBAAe;iBAAa;gBACtD,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,KAAK,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAChD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UACF,wEAAwE,EAAE,OAAO,CAAC,gBAAgB;oBACtG,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;oBAAa;iBAAc;gBAC5C,YAAY;oBAAC;oBAAa;iBAAc;gBACxC,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,OAAO,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAClD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UACF,qFAAqF,EAAE,OAAO,CAAC,gBAAgB;oBACnH,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;oBAAa;oBAAe;iBAAa;gBAC1D,YAAY;oBAAC;oBAAa;oBAAe;iBAAa;gBACtD,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;IACJ;IACA,cAAc,sCAAsC,GAAG;IACvD,MAAM;QACF,QAAQ;QACR,YAAY,OAAO,CAAE;YACjB,IAAI,CAAC,OAAO,GAAG;QACnB;QACA,OAAO,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAClD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UACF,uEAAuE,EAAE,OAAO,CAAC,gBAAgB;oBACrG,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;oBAAa;iBAAc;gBAC5C,YAAY;oBAAC;oBAAa;iBAAc;gBACxC,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,OAAO,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAClD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UACF,4FAA4F,EAAE,OAAO,CAAC,gBAAgB;oBAC1H,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;oBAAa;oBAAe;iBAAqB;gBAClE,YAAY;oBAAC;oBAAa;oBAAe;iBAAqB;gBAC9D,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,IAAI,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAC/C,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UACF,4FAA4F,EAAE,OAAO,CAAC,gBAAgB;oBAC1H,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;oBAAa;oBAAe;iBAAqB;gBAClE,YAAY;oBAAC;oBAAa;oBAAe;iBAAqB;gBAC9D,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,KAAK,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAChD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UACF,uEAAuE,EAAE,OAAO,CAAC,gBAAgB;oBACrG,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;oBAAa;iBAAc;gBAC5C,YAAY;oBAAC;oBAAa;iBAAc;gBACxC,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,QAAQ,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YACnD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UACF,oGAAoG,EAAE,OAAO,CAAC,gBAAgB;oBAClI,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;oBAAa;oBAAe;iBAAqB;gBAClE,YAAY;oBAAC;oBAAa;oBAAe;iBAAqB;gBAC9D,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,QAAQ,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YACnD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UACF,oGAAoG,EAAE,OAAO,CAAC,gBAAgB;oBAClI,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;oBAAa;oBAAe;iBAAqB;gBAClE,YAAY;oBAAC;oBAAa;oBAAe;iBAAqB;gBAC9D,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,SAAS,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YACpD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UACF,qGAAqG,EAAE,OAAO,CAAC,gBAAgB;oBACnI,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;oBAAa;oBAAe;iBAAqB;gBAClE,YAAY;oBAAC;oBAAa;oBAAe;iBAAqB;gBAC9D,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,OAAO,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAClD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UACF,4FAA4F,EAAE,OAAO,CAAC,gBAAgB;oBAC1H,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;oBAAa;oBAAe;iBAAqB;gBAClE,YAAY;oBAAC;oBAAa;oBAAe;iBAAqB;gBAC9D,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;IACJ;IACA,cAAc,qCAAqC,GAAG;IACtD,MAAM;QACF,QAAQ;QACR,YAAY,OAAO,CAAE;YACjB,IAAI,CAAC,OAAO,GAAG;QACnB;QACA,OAAO,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAClD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,iDAAiD,EAAE,OAAO,CAAC,gBAAgB;oBAC3F,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAY;gBAC7B,YAAY;oBAAC;iBAAY;gBACzB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,OAAO,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAClD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UACF,gEAAgE,EAAE,OAAO,CAAC,gBAAgB;oBAC9F,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;oBAAa;iBAAe;gBAC7C,YAAY;oBAAC;oBAAa;iBAAe;gBACzC,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,IAAI,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAC/C,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UACF,gEAAgE,EAAE,OAAO,CAAC,gBAAgB;oBAC9F,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;oBAAa;iBAAe;gBAC7C,YAAY;oBAAC;oBAAa;iBAAe;gBACzC,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,KAAK,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAChD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,iDAAiD,EAAE,OAAO,CAAC,gBAAgB;oBAC3F,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAY;gBAC7B,YAAY;oBAAC;iBAAY;gBACzB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,OAAO,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAClD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UACF,gEAAgE,EAAE,OAAO,CAAC,gBAAgB;oBAC9F,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;oBAAa;iBAAe;gBAC7C,YAAY;oBAAC;oBAAa;iBAAe;gBACzC,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;IACJ;IACA,cAAc,6BAA6B,GAAG;AAClD,CAAC,EAAE,iBAAiB,CAAC,QAAQ,aAAa,GAAG,gBAAgB,CAAC,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1954, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/AI_Projects/CRMcriadores/crm-interno/node_modules/googleapis/build/src/apis/tagmanager/v2.js"], "sourcesContent": ["\"use strict\";\n// Copyright 2020 Google LLC\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//    http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.tagmanager_v2 = void 0;\n/* eslint-disable @typescript-eslint/no-explicit-any */\n/* eslint-disable @typescript-eslint/no-unused-vars */\n/* eslint-disable @typescript-eslint/no-empty-interface */\n/* eslint-disable @typescript-eslint/no-namespace */\n/* eslint-disable no-irregular-whitespace */\nconst googleapis_common_1 = require(\"googleapis-common\");\nvar tagmanager_v2;\n(function (tagmanager_v2) {\n    /**\n     * Tag Manager API\n     *\n     * This API allows clients to access and modify container and tag configuration.\n     *\n     * @example\n     * ```js\n     * const {google} = require('googleapis');\n     * const tagmanager = google.tagmanager('v2');\n     * ```\n     */\n    class Tagmanager {\n        context;\n        accounts;\n        constructor(options, google) {\n            this.context = {\n                _options: options || {},\n                google,\n            };\n            this.accounts = new Resource$Accounts(this.context);\n        }\n    }\n    tagmanager_v2.Tagmanager = Tagmanager;\n    class Resource$Accounts {\n        context;\n        containers;\n        user_permissions;\n        constructor(context) {\n            this.context = context;\n            this.containers = new Resource$Accounts$Containers(this.context);\n            this.user_permissions = new Resource$Accounts$User_permissions(this.context);\n        }\n        get(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback || {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://tagmanager.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/tagmanager/v2/{+path}').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'GET',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['path'],\n                pathParams: ['path'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        list(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback || {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://tagmanager.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/tagmanager/v2/accounts').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'GET',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: [],\n                pathParams: [],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        update(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback || {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://tagmanager.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/tagmanager/v2/{+path}').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'PUT',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['path'],\n                pathParams: ['path'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n    }\n    tagmanager_v2.Resource$Accounts = Resource$Accounts;\n    class Resource$Accounts$Containers {\n        context;\n        destinations;\n        environments;\n        versions;\n        version_headers;\n        workspaces;\n        constructor(context) {\n            this.context = context;\n            this.destinations = new Resource$Accounts$Containers$Destinations(this.context);\n            this.environments = new Resource$Accounts$Containers$Environments(this.context);\n            this.versions = new Resource$Accounts$Containers$Versions(this.context);\n            this.version_headers = new Resource$Accounts$Containers$Version_headers(this.context);\n            this.workspaces = new Resource$Accounts$Containers$Workspaces(this.context);\n        }\n        combine(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://tagmanager.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/tagmanager/v2/{+path}:combine').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'POST',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['path'],\n                pathParams: ['path'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        create(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://tagmanager.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/tagmanager/v2/{+parent}/containers').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'POST',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['parent'],\n                pathParams: ['parent'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        delete(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://tagmanager.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/tagmanager/v2/{+path}').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'DELETE',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['path'],\n                pathParams: ['path'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        get(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://tagmanager.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/tagmanager/v2/{+path}').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'GET',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['path'],\n                pathParams: ['path'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        list(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://tagmanager.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/tagmanager/v2/{+parent}/containers').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'GET',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['parent'],\n                pathParams: ['parent'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        lookup(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://tagmanager.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/tagmanager/v2/accounts/containers:lookup').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'GET',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: [],\n                pathParams: [],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        move_tag_id(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://tagmanager.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/tagmanager/v2/{+path}:move_tag_id').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'POST',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['path'],\n                pathParams: ['path'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        snippet(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://tagmanager.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/tagmanager/v2/{+path}:snippet').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'GET',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['path'],\n                pathParams: ['path'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        update(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://tagmanager.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/tagmanager/v2/{+path}').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'PUT',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['path'],\n                pathParams: ['path'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n    }\n    tagmanager_v2.Resource$Accounts$Containers = Resource$Accounts$Containers;\n    class Resource$Accounts$Containers$Destinations {\n        context;\n        constructor(context) {\n            this.context = context;\n        }\n        get(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://tagmanager.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/tagmanager/v2/{+path}').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'GET',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['path'],\n                pathParams: ['path'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        link(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://tagmanager.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/tagmanager/v2/{+parent}/destinations:link').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'POST',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['parent'],\n                pathParams: ['parent'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        list(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://tagmanager.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/tagmanager/v2/{+parent}/destinations').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'GET',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['parent'],\n                pathParams: ['parent'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n    }\n    tagmanager_v2.Resource$Accounts$Containers$Destinations = Resource$Accounts$Containers$Destinations;\n    class Resource$Accounts$Containers$Environments {\n        context;\n        constructor(context) {\n            this.context = context;\n        }\n        create(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://tagmanager.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/tagmanager/v2/{+parent}/environments').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'POST',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['parent'],\n                pathParams: ['parent'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        delete(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://tagmanager.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/tagmanager/v2/{+path}').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'DELETE',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['path'],\n                pathParams: ['path'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        get(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://tagmanager.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/tagmanager/v2/{+path}').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'GET',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['path'],\n                pathParams: ['path'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        list(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://tagmanager.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/tagmanager/v2/{+parent}/environments').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'GET',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['parent'],\n                pathParams: ['parent'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        reauthorize(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params =\n                    {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://tagmanager.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/tagmanager/v2/{+path}:reauthorize').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'POST',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['path'],\n                pathParams: ['path'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        update(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://tagmanager.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/tagmanager/v2/{+path}').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'PUT',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['path'],\n                pathParams: ['path'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n    }\n    tagmanager_v2.Resource$Accounts$Containers$Environments = Resource$Accounts$Containers$Environments;\n    class Resource$Accounts$Containers$Versions {\n        context;\n        constructor(context) {\n            this.context = context;\n        }\n        delete(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://tagmanager.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/tagmanager/v2/{+path}').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'DELETE',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['path'],\n                pathParams: ['path'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        get(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://tagmanager.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/tagmanager/v2/{+path}').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'GET',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['path'],\n                pathParams: ['path'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        live(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://tagmanager.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/tagmanager/v2/{+parent}/versions:live').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'GET',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['parent'],\n                pathParams: ['parent'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        publish(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://tagmanager.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/tagmanager/v2/{+path}:publish').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'POST',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['path'],\n                pathParams: ['path'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        set_latest(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://tagmanager.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/tagmanager/v2/{+path}:set_latest').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'POST',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['path'],\n                pathParams: ['path'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        undelete(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://tagmanager.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/tagmanager/v2/{+path}:undelete').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'POST',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['path'],\n                pathParams: ['path'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        update(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://tagmanager.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/tagmanager/v2/{+path}').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'PUT',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['path'],\n                pathParams: ['path'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n    }\n    tagmanager_v2.Resource$Accounts$Containers$Versions = Resource$Accounts$Containers$Versions;\n    class Resource$Accounts$Containers$Version_headers {\n        context;\n        constructor(context) {\n            this.context = context;\n        }\n        latest(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params =\n                    {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://tagmanager.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/tagmanager/v2/{+parent}/version_headers:latest').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'GET',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['parent'],\n                pathParams: ['parent'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        list(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://tagmanager.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/tagmanager/v2/{+parent}/version_headers').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'GET',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['parent'],\n                pathParams: ['parent'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n    }\n    tagmanager_v2.Resource$Accounts$Containers$Version_headers = Resource$Accounts$Containers$Version_headers;\n    class Resource$Accounts$Containers$Workspaces {\n        context;\n        built_in_variables;\n        clients;\n        folders;\n        gtag_config;\n        tags;\n        templates;\n        transformations;\n        triggers;\n        variables;\n        zones;\n        constructor(context) {\n            this.context = context;\n            this.built_in_variables =\n                new Resource$Accounts$Containers$Workspaces$Built_in_variables(this.context);\n            this.clients = new Resource$Accounts$Containers$Workspaces$Clients(this.context);\n            this.folders = new Resource$Accounts$Containers$Workspaces$Folders(this.context);\n            this.gtag_config =\n                new Resource$Accounts$Containers$Workspaces$Gtag_config(this.context);\n            this.tags = new Resource$Accounts$Containers$Workspaces$Tags(this.context);\n            this.templates = new Resource$Accounts$Containers$Workspaces$Templates(this.context);\n            this.transformations =\n                new Resource$Accounts$Containers$Workspaces$Transformations(this.context);\n            this.triggers = new Resource$Accounts$Containers$Workspaces$Triggers(this.context);\n            this.variables = new Resource$Accounts$Containers$Workspaces$Variables(this.context);\n            this.zones = new Resource$Accounts$Containers$Workspaces$Zones(this.context);\n        }\n        create(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://tagmanager.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/tagmanager/v2/{+parent}/workspaces').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'POST',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['parent'],\n                pathParams: ['parent'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        create_version(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params =\n                    {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://tagmanager.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/tagmanager/v2/{+path}:create_version').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'POST',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['path'],\n                pathParams: ['path'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        delete(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://tagmanager.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/tagmanager/v2/{+path}').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'DELETE',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['path'],\n                pathParams: ['path'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        get(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://tagmanager.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/tagmanager/v2/{+path}').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'GET',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['path'],\n                pathParams: ['path'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        getStatus(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://tagmanager.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/tagmanager/v2/{+path}/status').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'GET',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['path'],\n                pathParams: ['path'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        list(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://tagmanager.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/tagmanager/v2/{+parent}/workspaces').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'GET',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['parent'],\n                pathParams: ['parent'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        quick_preview(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params =\n                    {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://tagmanager.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/tagmanager/v2/{+path}:quick_preview').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'POST',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['path'],\n                pathParams: ['path'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        resolve_conflict(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params =\n                    {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://tagmanager.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/tagmanager/v2/{+path}:resolve_conflict').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'POST',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['path'],\n                pathParams: ['path'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        sync(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://tagmanager.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/tagmanager/v2/{+path}:sync').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'POST',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['path'],\n                pathParams: ['path'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        update(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://tagmanager.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/tagmanager/v2/{+path}').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'PUT',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['path'],\n                pathParams: ['path'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n    }\n    tagmanager_v2.Resource$Accounts$Containers$Workspaces = Resource$Accounts$Containers$Workspaces;\n    class Resource$Accounts$Containers$Workspaces$Built_in_variables {\n        context;\n        constructor(context) {\n            this.context = context;\n        }\n        create(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params =\n                    {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://tagmanager.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/tagmanager/v2/{+parent}/built_in_variables').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'POST',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['parent'],\n                pathParams: ['parent'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        delete(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params =\n                    {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://tagmanager.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/tagmanager/v2/{+path}').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'DELETE',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['path'],\n                pathParams: ['path'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        list(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params =\n                    {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://tagmanager.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/tagmanager/v2/{+parent}/built_in_variables').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'GET',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['parent'],\n                pathParams: ['parent'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        revert(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params =\n                    {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://tagmanager.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/tagmanager/v2/{+path}/built_in_variables:revert').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'POST',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['path'],\n                pathParams: ['path'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n    }\n    tagmanager_v2.Resource$Accounts$Containers$Workspaces$Built_in_variables = Resource$Accounts$Containers$Workspaces$Built_in_variables;\n    class Resource$Accounts$Containers$Workspaces$Clients {\n        context;\n        constructor(context) {\n            this.context = context;\n        }\n        create(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params =\n                    {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://tagmanager.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/tagmanager/v2/{+parent}/clients').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'POST',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['parent'],\n                pathParams: ['parent'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        delete(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params =\n                    {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://tagmanager.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/tagmanager/v2/{+path}').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'DELETE',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['path'],\n                pathParams: ['path'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        get(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params =\n                    {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://tagmanager.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/tagmanager/v2/{+path}').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'GET',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['path'],\n                pathParams: ['path'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        list(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params =\n                    {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://tagmanager.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/tagmanager/v2/{+parent}/clients').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'GET',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['parent'],\n                pathParams: ['parent'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        revert(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params =\n                    {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://tagmanager.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/tagmanager/v2/{+path}:revert').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'POST',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['path'],\n                pathParams: ['path'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        update(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params =\n                    {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://tagmanager.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/tagmanager/v2/{+path}').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'PUT',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['path'],\n                pathParams: ['path'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n    }\n    tagmanager_v2.Resource$Accounts$Containers$Workspaces$Clients = Resource$Accounts$Containers$Workspaces$Clients;\n    class Resource$Accounts$Containers$Workspaces$Folders {\n        context;\n        constructor(context) {\n            this.context = context;\n        }\n        create(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params =\n                    {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://tagmanager.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/tagmanager/v2/{+parent}/folders').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'POST',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['parent'],\n                pathParams: ['parent'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        delete(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params =\n                    {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://tagmanager.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/tagmanager/v2/{+path}').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'DELETE',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['path'],\n                pathParams: ['path'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        entities(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params =\n                    {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://tagmanager.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/tagmanager/v2/{+path}:entities').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'POST',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['path'],\n                pathParams: ['path'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        get(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params =\n                    {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://tagmanager.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/tagmanager/v2/{+path}').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'GET',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['path'],\n                pathParams: ['path'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        list(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params =\n                    {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://tagmanager.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/tagmanager/v2/{+parent}/folders').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'GET',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['parent'],\n                pathParams: ['parent'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        move_entities_to_folder(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params =\n                    {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://tagmanager.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/tagmanager/v2/{+path}:move_entities_to_folder').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'POST',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['path'],\n                pathParams: ['path'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        revert(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params =\n                    {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://tagmanager.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/tagmanager/v2/{+path}:revert').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'POST',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['path'],\n                pathParams: ['path'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        update(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params =\n                    {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://tagmanager.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/tagmanager/v2/{+path}').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'PUT',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['path'],\n                pathParams: ['path'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n    }\n    tagmanager_v2.Resource$Accounts$Containers$Workspaces$Folders = Resource$Accounts$Containers$Workspaces$Folders;\n    class Resource$Accounts$Containers$Workspaces$Gtag_config {\n        context;\n        constructor(context) {\n            this.context = context;\n        }\n        create(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params =\n                    {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://tagmanager.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/tagmanager/v2/{+parent}/gtag_config').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'POST',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['parent'],\n                pathParams: ['parent'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        delete(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params =\n                    {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://tagmanager.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/tagmanager/v2/{+path}').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'DELETE',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['path'],\n                pathParams: ['path'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        get(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params =\n                    {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://tagmanager.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/tagmanager/v2/{+path}').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'GET',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['path'],\n                pathParams: ['path'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        list(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params =\n                    {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://tagmanager.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/tagmanager/v2/{+parent}/gtag_config').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'GET',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['parent'],\n                pathParams: ['parent'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        update(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params =\n                    {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://tagmanager.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/tagmanager/v2/{+path}').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'PUT',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['path'],\n                pathParams: ['path'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n    }\n    tagmanager_v2.Resource$Accounts$Containers$Workspaces$Gtag_config = Resource$Accounts$Containers$Workspaces$Gtag_config;\n    class Resource$Accounts$Containers$Workspaces$Tags {\n        context;\n        constructor(context) {\n            this.context = context;\n        }\n        create(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params =\n                    {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://tagmanager.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/tagmanager/v2/{+parent}/tags').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'POST',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['parent'],\n                pathParams: ['parent'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        delete(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params =\n                    {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://tagmanager.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/tagmanager/v2/{+path}').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'DELETE',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['path'],\n                pathParams: ['path'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        get(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://tagmanager.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/tagmanager/v2/{+path}').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'GET',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['path'],\n                pathParams: ['path'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        list(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://tagmanager.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/tagmanager/v2/{+parent}/tags').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'GET',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['parent'],\n                pathParams: ['parent'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        revert(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params =\n                    {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://tagmanager.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/tagmanager/v2/{+path}:revert').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'POST',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['path'],\n                pathParams: ['path'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        update(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params =\n                    {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://tagmanager.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/tagmanager/v2/{+path}').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'PUT',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['path'],\n                pathParams: ['path'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n    }\n    tagmanager_v2.Resource$Accounts$Containers$Workspaces$Tags = Resource$Accounts$Containers$Workspaces$Tags;\n    class Resource$Accounts$Containers$Workspaces$Templates {\n        context;\n        constructor(context) {\n            this.context = context;\n        }\n        create(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params =\n                    {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://tagmanager.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/tagmanager/v2/{+parent}/templates').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'POST',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['parent'],\n                pathParams: ['parent'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        delete(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params =\n                    {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://tagmanager.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/tagmanager/v2/{+path}').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'DELETE',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['path'],\n                pathParams: ['path'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        get(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params =\n                    {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://tagmanager.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/tagmanager/v2/{+path}').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'GET',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['path'],\n                pathParams: ['path'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        import_from_gallery(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params =\n                    {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://tagmanager.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/tagmanager/v2/{+parent}/templates:import_from_gallery').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'POST',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['parent'],\n                pathParams: ['parent'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        list(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params =\n                    {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://tagmanager.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/tagmanager/v2/{+parent}/templates').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'GET',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['parent'],\n                pathParams: ['parent'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        revert(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params =\n                    {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://tagmanager.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/tagmanager/v2/{+path}:revert').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'POST',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['path'],\n                pathParams: ['path'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        update(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params =\n                    {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://tagmanager.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/tagmanager/v2/{+path}').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'PUT',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['path'],\n                pathParams: ['path'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n    }\n    tagmanager_v2.Resource$Accounts$Containers$Workspaces$Templates = Resource$Accounts$Containers$Workspaces$Templates;\n    class Resource$Accounts$Containers$Workspaces$Transformations {\n        context;\n        constructor(context) {\n            this.context = context;\n        }\n        create(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params =\n                    {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://tagmanager.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/tagmanager/v2/{+parent}/transformations').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'POST',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['parent'],\n                pathParams: ['parent'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        delete(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params =\n                    {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://tagmanager.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/tagmanager/v2/{+path}').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'DELETE',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['path'],\n                pathParams: ['path'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        get(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params =\n                    {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://tagmanager.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/tagmanager/v2/{+path}').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'GET',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['path'],\n                pathParams: ['path'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        list(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params =\n                    {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://tagmanager.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/tagmanager/v2/{+parent}/transformations').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'GET',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['parent'],\n                pathParams: ['parent'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        revert(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params =\n                    {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://tagmanager.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/tagmanager/v2/{+path}:revert').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'POST',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['path'],\n                pathParams: ['path'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        update(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params =\n                    {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://tagmanager.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/tagmanager/v2/{+path}').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'PUT',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['path'],\n                pathParams: ['path'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n    }\n    tagmanager_v2.Resource$Accounts$Containers$Workspaces$Transformations = Resource$Accounts$Containers$Workspaces$Transformations;\n    class Resource$Accounts$Containers$Workspaces$Triggers {\n        context;\n        constructor(context) {\n            this.context = context;\n        }\n        create(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params =\n                    {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://tagmanager.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/tagmanager/v2/{+parent}/triggers').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'POST',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['parent'],\n                pathParams: ['parent'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        delete(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params =\n                    {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://tagmanager.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/tagmanager/v2/{+path}').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'DELETE',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['path'],\n                pathParams: ['path'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        get(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params =\n                    {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://tagmanager.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/tagmanager/v2/{+path}').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'GET',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['path'],\n                pathParams: ['path'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        list(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params =\n                    {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://tagmanager.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/tagmanager/v2/{+parent}/triggers').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'GET',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['parent'],\n                pathParams: ['parent'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        revert(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params =\n                    {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://tagmanager.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/tagmanager/v2/{+path}:revert').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'POST',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['path'],\n                pathParams: ['path'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        update(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params =\n                    {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://tagmanager.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/tagmanager/v2/{+path}').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'PUT',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['path'],\n                pathParams: ['path'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n    }\n    tagmanager_v2.Resource$Accounts$Containers$Workspaces$Triggers = Resource$Accounts$Containers$Workspaces$Triggers;\n    class Resource$Accounts$Containers$Workspaces$Variables {\n        context;\n        constructor(context) {\n            this.context = context;\n        }\n        create(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params =\n                    {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://tagmanager.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/tagmanager/v2/{+parent}/variables').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'POST',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['parent'],\n                pathParams: ['parent'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        delete(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params =\n                    {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://tagmanager.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/tagmanager/v2/{+path}').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'DELETE',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['path'],\n                pathParams: ['path'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        get(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params =\n                    {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://tagmanager.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/tagmanager/v2/{+path}').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'GET',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['path'],\n                pathParams: ['path'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        list(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params =\n                    {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://tagmanager.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/tagmanager/v2/{+parent}/variables').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'GET',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['parent'],\n                pathParams: ['parent'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        revert(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params =\n                    {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://tagmanager.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/tagmanager/v2/{+path}:revert').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'POST',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['path'],\n                pathParams: ['path'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        update(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params =\n                    {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://tagmanager.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/tagmanager/v2/{+path}').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'PUT',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['path'],\n                pathParams: ['path'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n    }\n    tagmanager_v2.Resource$Accounts$Containers$Workspaces$Variables = Resource$Accounts$Containers$Workspaces$Variables;\n    class Resource$Accounts$Containers$Workspaces$Zones {\n        context;\n        constructor(context) {\n            this.context = context;\n        }\n        create(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params =\n                    {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://tagmanager.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/tagmanager/v2/{+parent}/zones').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'POST',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['parent'],\n                pathParams: ['parent'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        delete(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params =\n                    {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://tagmanager.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/tagmanager/v2/{+path}').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'DELETE',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['path'],\n                pathParams: ['path'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        get(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://tagmanager.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/tagmanager/v2/{+path}').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'GET',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['path'],\n                pathParams: ['path'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        list(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params =\n                    {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://tagmanager.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/tagmanager/v2/{+parent}/zones').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'GET',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['parent'],\n                pathParams: ['parent'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        revert(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params =\n                    {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://tagmanager.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/tagmanager/v2/{+path}:revert').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'POST',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['path'],\n                pathParams: ['path'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        update(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params =\n                    {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://tagmanager.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/tagmanager/v2/{+path}').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'PUT',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['path'],\n                pathParams: ['path'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n    }\n    tagmanager_v2.Resource$Accounts$Containers$Workspaces$Zones = Resource$Accounts$Containers$Workspaces$Zones;\n    class Resource$Accounts$User_permissions {\n        context;\n        constructor(context) {\n            this.context = context;\n        }\n        create(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://tagmanager.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/tagmanager/v2/{+parent}/user_permissions').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'POST',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['parent'],\n                pathParams: ['parent'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        delete(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://tagmanager.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/tagmanager/v2/{+path}').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'DELETE',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['path'],\n                pathParams: ['path'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        get(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://tagmanager.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/tagmanager/v2/{+path}').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'GET',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['path'],\n                pathParams: ['path'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        list(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://tagmanager.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/tagmanager/v2/{+parent}/user_permissions').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'GET',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['parent'],\n                pathParams: ['parent'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        update(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://tagmanager.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/tagmanager/v2/{+path}').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'PUT',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['path'],\n                pathParams: ['path'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n    }\n    tagmanager_v2.Resource$Accounts$User_permissions = Resource$Accounts$User_permissions;\n})(tagmanager_v2 || (exports.tagmanager_v2 = tagmanager_v2 = {}));\n"], "names": [], "mappings": "AAAA;AACA,4BAA4B;AAC5B,kEAAkE;AAClE,mEAAmE;AACnE,0CAA0C;AAC1C,EAAE;AACF,gDAAgD;AAChD,EAAE;AACF,sEAAsE;AACtE,oEAAoE;AACpE,2EAA2E;AAC3E,sEAAsE;AACtE,iCAAiC;AACjC,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,QAAQ,aAAa,GAAG,KAAK;AAC7B,qDAAqD,GACrD,oDAAoD,GACpD,wDAAwD,GACxD,kDAAkD,GAClD,0CAA0C,GAC1C,MAAM;AACN,IAAI;AACJ,CAAC,SAAU,aAAa;IACpB;;;;;;;;;;KAUC,GACD,MAAM;QACF,QAAQ;QACR,SAAS;QACT,YAAY,OAAO,EAAE,MAAM,CAAE;YACzB,IAAI,CAAC,OAAO,GAAG;gBACX,UAAU,WAAW,CAAC;gBACtB;YACJ;YACA,IAAI,CAAC,QAAQ,GAAG,IAAI,kBAAkB,IAAI,CAAC,OAAO;QACtD;IACJ;IACA,cAAc,UAAU,GAAG;IAC3B,MAAM;QACF,QAAQ;QACR,WAAW;QACX,iBAAiB;QACjB,YAAY,OAAO,CAAE;YACjB,IAAI,CAAC,OAAO,GAAG;YACf,IAAI,CAAC,UAAU,GAAG,IAAI,6BAA6B,IAAI,CAAC,OAAO;YAC/D,IAAI,CAAC,gBAAgB,GAAG,IAAI,mCAAmC,IAAI,CAAC,OAAO;QAC/E;QACA,IAAI,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAC/C,IAAI,SAAU,oBAAoB,CAAC;YACnC,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,wBAAwB,EAAE,OAAO,CAAC,gBAAgB;oBAClE,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAO;gBACxB,YAAY;oBAAC;iBAAO;gBACpB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,KAAK,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAChD,IAAI,SAAU,oBAAoB,CAAC;YACnC,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,yBAAyB,EAAE,OAAO,CAAC,gBAAgB;oBACnE,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB,EAAE;gBAClB,YAAY,EAAE;gBACd,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,OAAO,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAClD,IAAI,SAAU,oBAAoB,CAAC;YACnC,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,wBAAwB,EAAE,OAAO,CAAC,gBAAgB;oBAClE,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAO;gBACxB,YAAY;oBAAC;iBAAO;gBACpB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;IACJ;IACA,cAAc,iBAAiB,GAAG;IAClC,MAAM;QACF,QAAQ;QACR,aAAa;QACb,aAAa;QACb,SAAS;QACT,gBAAgB;QAChB,WAAW;QACX,YAAY,OAAO,CAAE;YACjB,IAAI,CAAC,OAAO,GAAG;YACf,IAAI,CAAC,YAAY,GAAG,IAAI,0CAA0C,IAAI,CAAC,OAAO;YAC9E,IAAI,CAAC,YAAY,GAAG,IAAI,0CAA0C,IAAI,CAAC,OAAO;YAC9E,IAAI,CAAC,QAAQ,GAAG,IAAI,sCAAsC,IAAI,CAAC,OAAO;YACtE,IAAI,CAAC,eAAe,GAAG,IAAI,6CAA6C,IAAI,CAAC,OAAO;YACpF,IAAI,CAAC,UAAU,GAAG,IAAI,wCAAwC,IAAI,CAAC,OAAO;QAC9E;QACA,QAAQ,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YACnD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,gCAAgC,EAAE,OAAO,CAAC,gBAAgB;oBAC1E,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAO;gBACxB,YAAY;oBAAC;iBAAO;gBACpB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,OAAO,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAClD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,qCAAqC,EAAE,OAAO,CAAC,gBAAgB;oBAC/E,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAS;gBAC1B,YAAY;oBAAC;iBAAS;gBACtB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,OAAO,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAClD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,wBAAwB,EAAE,OAAO,CAAC,gBAAgB;oBAClE,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAO;gBACxB,YAAY;oBAAC;iBAAO;gBACpB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,IAAI,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAC/C,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,wBAAwB,EAAE,OAAO,CAAC,gBAAgB;oBAClE,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAO;gBACxB,YAAY;oBAAC;iBAAO;gBACpB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,KAAK,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAChD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,qCAAqC,EAAE,OAAO,CAAC,gBAAgB;oBAC/E,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAS;gBAC1B,YAAY;oBAAC;iBAAS;gBACtB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,OAAO,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAClD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,2CAA2C,EAAE,OAAO,CAAC,gBAAgB;oBACrF,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB,EAAE;gBAClB,YAAY,EAAE;gBACd,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,YAAY,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YACvD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,oCAAoC,EAAE,OAAO,CAAC,gBAAgB;oBAC9E,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAO;gBACxB,YAAY;oBAAC;iBAAO;gBACpB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,QAAQ,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YACnD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,gCAAgC,EAAE,OAAO,CAAC,gBAAgB;oBAC1E,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAO;gBACxB,YAAY;oBAAC;iBAAO;gBACpB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,OAAO,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAClD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,wBAAwB,EAAE,OAAO,CAAC,gBAAgB;oBAClE,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAO;gBACxB,YAAY;oBAAC;iBAAO;gBACpB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;IACJ;IACA,cAAc,4BAA4B,GAAG;IAC7C,MAAM;QACF,QAAQ;QACR,YAAY,OAAO,CAAE;YACjB,IAAI,CAAC,OAAO,GAAG;QACnB;QACA,IAAI,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAC/C,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,wBAAwB,EAAE,OAAO,CAAC,gBAAgB;oBAClE,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAO;gBACxB,YAAY;oBAAC;iBAAO;gBACpB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,KAAK,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAChD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,4CAA4C,EAAE,OAAO,CAAC,gBAAgB;oBACtF,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAS;gBAC1B,YAAY;oBAAC;iBAAS;gBACtB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,KAAK,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAChD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,uCAAuC,EAAE,OAAO,CAAC,gBAAgB;oBACjF,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAS;gBAC1B,YAAY;oBAAC;iBAAS;gBACtB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;IACJ;IACA,cAAc,yCAAyC,GAAG;IAC1D,MAAM;QACF,QAAQ;QACR,YAAY,OAAO,CAAE;YACjB,IAAI,CAAC,OAAO,GAAG;QACnB;QACA,OAAO,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAClD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,uCAAuC,EAAE,OAAO,CAAC,gBAAgB;oBACjF,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAS;gBAC1B,YAAY;oBAAC;iBAAS;gBACtB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,OAAO,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAClD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,wBAAwB,EAAE,OAAO,CAAC,gBAAgB;oBAClE,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAO;gBACxB,YAAY;oBAAC;iBAAO;gBACpB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,IAAI,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAC/C,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,wBAAwB,EAAE,OAAO,CAAC,gBAAgB;oBAClE,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAO;gBACxB,YAAY;oBAAC;iBAAO;gBACpB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,KAAK,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAChD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,uCAAuC,EAAE,OAAO,CAAC,gBAAgB;oBACjF,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAS;gBAC1B,YAAY;oBAAC;iBAAS;gBACtB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,YAAY,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YACvD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SACI,CAAC;gBACL,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,oCAAoC,EAAE,OAAO,CAAC,gBAAgB;oBAC9E,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAO;gBACxB,YAAY;oBAAC;iBAAO;gBACpB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,OAAO,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAClD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,wBAAwB,EAAE,OAAO,CAAC,gBAAgB;oBAClE,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAO;gBACxB,YAAY;oBAAC;iBAAO;gBACpB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;IACJ;IACA,cAAc,yCAAyC,GAAG;IAC1D,MAAM;QACF,QAAQ;QACR,YAAY,OAAO,CAAE;YACjB,IAAI,CAAC,OAAO,GAAG;QACnB;QACA,OAAO,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAClD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,wBAAwB,EAAE,OAAO,CAAC,gBAAgB;oBAClE,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAO;gBACxB,YAAY;oBAAC;iBAAO;gBACpB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,IAAI,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAC/C,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,wBAAwB,EAAE,OAAO,CAAC,gBAAgB;oBAClE,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAO;gBACxB,YAAY;oBAAC;iBAAO;gBACpB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,KAAK,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAChD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,wCAAwC,EAAE,OAAO,CAAC,gBAAgB;oBAClF,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAS;gBAC1B,YAAY;oBAAC;iBAAS;gBACtB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,QAAQ,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YACnD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,gCAAgC,EAAE,OAAO,CAAC,gBAAgB;oBAC1E,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAO;gBACxB,YAAY;oBAAC;iBAAO;gBACpB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,WAAW,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YACtD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,mCAAmC,EAAE,OAAO,CAAC,gBAAgB;oBAC7E,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAO;gBACxB,YAAY;oBAAC;iBAAO;gBACpB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,SAAS,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YACpD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,iCAAiC,EAAE,OAAO,CAAC,gBAAgB;oBAC3E,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAO;gBACxB,YAAY;oBAAC;iBAAO;gBACpB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,OAAO,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAClD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,wBAAwB,EAAE,OAAO,CAAC,gBAAgB;oBAClE,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAO;gBACxB,YAAY;oBAAC;iBAAO;gBACpB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;IACJ;IACA,cAAc,qCAAqC,GAAG;IACtD,MAAM;QACF,QAAQ;QACR,YAAY,OAAO,CAAE;YACjB,IAAI,CAAC,OAAO,GAAG;QACnB;QACA,OAAO,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAClD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SACI,CAAC;gBACL,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,iDAAiD,EAAE,OAAO,CAAC,gBAAgB;oBAC3F,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAS;gBAC1B,YAAY;oBAAC;iBAAS;gBACtB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,KAAK,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAChD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,0CAA0C,EAAE,OAAO,CAAC,gBAAgB;oBACpF,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAS;gBAC1B,YAAY;oBAAC;iBAAS;gBACtB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;IACJ;IACA,cAAc,4CAA4C,GAAG;IAC7D,MAAM;QACF,QAAQ;QACR,mBAAmB;QACnB,QAAQ;QACR,QAAQ;QACR,YAAY;QACZ,KAAK;QACL,UAAU;QACV,gBAAgB;QAChB,SAAS;QACT,UAAU;QACV,MAAM;QACN,YAAY,OAAO,CAAE;YACjB,IAAI,CAAC,OAAO,GAAG;YACf,IAAI,CAAC,kBAAkB,GACnB,IAAI,2DAA2D,IAAI,CAAC,OAAO;YAC/E,IAAI,CAAC,OAAO,GAAG,IAAI,gDAAgD,IAAI,CAAC,OAAO;YAC/E,IAAI,CAAC,OAAO,GAAG,IAAI,gDAAgD,IAAI,CAAC,OAAO;YAC/E,IAAI,CAAC,WAAW,GACZ,IAAI,oDAAoD,IAAI,CAAC,OAAO;YACxE,IAAI,CAAC,IAAI,GAAG,IAAI,6CAA6C,IAAI,CAAC,OAAO;YACzE,IAAI,CAAC,SAAS,GAAG,IAAI,kDAAkD,IAAI,CAAC,OAAO;YACnF,IAAI,CAAC,eAAe,GAChB,IAAI,wDAAwD,IAAI,CAAC,OAAO;YAC5E,IAAI,CAAC,QAAQ,GAAG,IAAI,iDAAiD,IAAI,CAAC,OAAO;YACjF,IAAI,CAAC,SAAS,GAAG,IAAI,kDAAkD,IAAI,CAAC,OAAO;YACnF,IAAI,CAAC,KAAK,GAAG,IAAI,8CAA8C,IAAI,CAAC,OAAO;QAC/E;QACA,OAAO,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAClD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,qCAAqC,EAAE,OAAO,CAAC,gBAAgB;oBAC/E,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAS;gBAC1B,YAAY;oBAAC;iBAAS;gBACtB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,eAAe,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAC1D,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SACI,CAAC;gBACL,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,uCAAuC,EAAE,OAAO,CAAC,gBAAgB;oBACjF,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAO;gBACxB,YAAY;oBAAC;iBAAO;gBACpB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,OAAO,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAClD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,wBAAwB,EAAE,OAAO,CAAC,gBAAgB;oBAClE,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAO;gBACxB,YAAY;oBAAC;iBAAO;gBACpB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,IAAI,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAC/C,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,wBAAwB,EAAE,OAAO,CAAC,gBAAgB;oBAClE,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAO;gBACxB,YAAY;oBAAC;iBAAO;gBACpB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,UAAU,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YACrD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,+BAA+B,EAAE,OAAO,CAAC,gBAAgB;oBACzE,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAO;gBACxB,YAAY;oBAAC;iBAAO;gBACpB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,KAAK,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAChD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,qCAAqC,EAAE,OAAO,CAAC,gBAAgB;oBAC/E,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAS;gBAC1B,YAAY;oBAAC;iBAAS;gBACtB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,cAAc,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YACzD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SACI,CAAC;gBACL,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,sCAAsC,EAAE,OAAO,CAAC,gBAAgB;oBAChF,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAO;gBACxB,YAAY;oBAAC;iBAAO;gBACpB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,iBAAiB,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAC5D,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SACI,CAAC;gBACL,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,yCAAyC,EAAE,OAAO,CAAC,gBAAgB;oBACnF,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAO;gBACxB,YAAY;oBAAC;iBAAO;gBACpB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,KAAK,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAChD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,6BAA6B,EAAE,OAAO,CAAC,gBAAgB;oBACvE,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAO;gBACxB,YAAY;oBAAC;iBAAO;gBACpB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,OAAO,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAClD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,wBAAwB,EAAE,OAAO,CAAC,gBAAgB;oBAClE,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAO;gBACxB,YAAY;oBAAC;iBAAO;gBACpB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;IACJ;IACA,cAAc,uCAAuC,GAAG;IACxD,MAAM;QACF,QAAQ;QACR,YAAY,OAAO,CAAE;YACjB,IAAI,CAAC,OAAO,GAAG;QACnB;QACA,OAAO,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAClD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SACI,CAAC;gBACL,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,6CAA6C,EAAE,OAAO,CAAC,gBAAgB;oBACvF,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAS;gBAC1B,YAAY;oBAAC;iBAAS;gBACtB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,OAAO,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAClD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SACI,CAAC;gBACL,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,wBAAwB,EAAE,OAAO,CAAC,gBAAgB;oBAClE,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAO;gBACxB,YAAY;oBAAC;iBAAO;gBACpB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,KAAK,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAChD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SACI,CAAC;gBACL,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,6CAA6C,EAAE,OAAO,CAAC,gBAAgB;oBACvF,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAS;gBAC1B,YAAY;oBAAC;iBAAS;gBACtB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,OAAO,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAClD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SACI,CAAC;gBACL,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,kDAAkD,EAAE,OAAO,CAAC,gBAAgB;oBAC5F,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAO;gBACxB,YAAY;oBAAC;iBAAO;gBACpB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;IACJ;IACA,cAAc,0DAA0D,GAAG;IAC3E,MAAM;QACF,QAAQ;QACR,YAAY,OAAO,CAAE;YACjB,IAAI,CAAC,OAAO,GAAG;QACnB;QACA,OAAO,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAClD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SACI,CAAC;gBACL,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,kCAAkC,EAAE,OAAO,CAAC,gBAAgB;oBAC5E,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAS;gBAC1B,YAAY;oBAAC;iBAAS;gBACtB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,OAAO,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAClD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SACI,CAAC;gBACL,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,wBAAwB,EAAE,OAAO,CAAC,gBAAgB;oBAClE,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAO;gBACxB,YAAY;oBAAC;iBAAO;gBACpB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,IAAI,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAC/C,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SACI,CAAC;gBACL,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,wBAAwB,EAAE,OAAO,CAAC,gBAAgB;oBAClE,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAO;gBACxB,YAAY;oBAAC;iBAAO;gBACpB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,KAAK,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAChD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SACI,CAAC;gBACL,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,kCAAkC,EAAE,OAAO,CAAC,gBAAgB;oBAC5E,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAS;gBAC1B,YAAY;oBAAC;iBAAS;gBACtB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,OAAO,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAClD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SACI,CAAC;gBACL,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,+BAA+B,EAAE,OAAO,CAAC,gBAAgB;oBACzE,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAO;gBACxB,YAAY;oBAAC;iBAAO;gBACpB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,OAAO,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAClD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SACI,CAAC;gBACL,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,wBAAwB,EAAE,OAAO,CAAC,gBAAgB;oBAClE,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAO;gBACxB,YAAY;oBAAC;iBAAO;gBACpB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;IACJ;IACA,cAAc,+CAA+C,GAAG;IAChE,MAAM;QACF,QAAQ;QACR,YAAY,OAAO,CAAE;YACjB,IAAI,CAAC,OAAO,GAAG;QACnB;QACA,OAAO,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAClD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SACI,CAAC;gBACL,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,kCAAkC,EAAE,OAAO,CAAC,gBAAgB;oBAC5E,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAS;gBAC1B,YAAY;oBAAC;iBAAS;gBACtB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,OAAO,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAClD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SACI,CAAC;gBACL,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,wBAAwB,EAAE,OAAO,CAAC,gBAAgB;oBAClE,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAO;gBACxB,YAAY;oBAAC;iBAAO;gBACpB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,SAAS,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YACpD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SACI,CAAC;gBACL,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,iCAAiC,EAAE,OAAO,CAAC,gBAAgB;oBAC3E,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAO;gBACxB,YAAY;oBAAC;iBAAO;gBACpB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,IAAI,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAC/C,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SACI,CAAC;gBACL,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,wBAAwB,EAAE,OAAO,CAAC,gBAAgB;oBAClE,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAO;gBACxB,YAAY;oBAAC;iBAAO;gBACpB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,KAAK,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAChD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SACI,CAAC;gBACL,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,kCAAkC,EAAE,OAAO,CAAC,gBAAgB;oBAC5E,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAS;gBAC1B,YAAY;oBAAC;iBAAS;gBACtB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,wBAAwB,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YACnE,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SACI,CAAC;gBACL,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,gDAAgD,EAAE,OAAO,CAAC,gBAAgB;oBAC1F,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAO;gBACxB,YAAY;oBAAC;iBAAO;gBACpB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,OAAO,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAClD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SACI,CAAC;gBACL,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,+BAA+B,EAAE,OAAO,CAAC,gBAAgB;oBACzE,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAO;gBACxB,YAAY;oBAAC;iBAAO;gBACpB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,OAAO,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAClD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SACI,CAAC;gBACL,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,wBAAwB,EAAE,OAAO,CAAC,gBAAgB;oBAClE,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAO;gBACxB,YAAY;oBAAC;iBAAO;gBACpB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;IACJ;IACA,cAAc,+CAA+C,GAAG;IAChE,MAAM;QACF,QAAQ;QACR,YAAY,OAAO,CAAE;YACjB,IAAI,CAAC,OAAO,GAAG;QACnB;QACA,OAAO,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAClD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SACI,CAAC;gBACL,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,sCAAsC,EAAE,OAAO,CAAC,gBAAgB;oBAChF,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAS;gBAC1B,YAAY;oBAAC;iBAAS;gBACtB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,OAAO,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAClD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SACI,CAAC;gBACL,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,wBAAwB,EAAE,OAAO,CAAC,gBAAgB;oBAClE,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAO;gBACxB,YAAY;oBAAC;iBAAO;gBACpB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,IAAI,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAC/C,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SACI,CAAC;gBACL,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,wBAAwB,EAAE,OAAO,CAAC,gBAAgB;oBAClE,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAO;gBACxB,YAAY;oBAAC;iBAAO;gBACpB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,KAAK,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAChD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SACI,CAAC;gBACL,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,sCAAsC,EAAE,OAAO,CAAC,gBAAgB;oBAChF,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAS;gBAC1B,YAAY;oBAAC;iBAAS;gBACtB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,OAAO,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAClD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SACI,CAAC;gBACL,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,wBAAwB,EAAE,OAAO,CAAC,gBAAgB;oBAClE,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAO;gBACxB,YAAY;oBAAC;iBAAO;gBACpB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;IACJ;IACA,cAAc,mDAAmD,GAAG;IACpE,MAAM;QACF,QAAQ;QACR,YAAY,OAAO,CAAE;YACjB,IAAI,CAAC,OAAO,GAAG;QACnB;QACA,OAAO,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAClD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SACI,CAAC;gBACL,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,+BAA+B,EAAE,OAAO,CAAC,gBAAgB;oBACzE,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAS;gBAC1B,YAAY;oBAAC;iBAAS;gBACtB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,OAAO,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAClD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SACI,CAAC;gBACL,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,wBAAwB,EAAE,OAAO,CAAC,gBAAgB;oBAClE,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAO;gBACxB,YAAY;oBAAC;iBAAO;gBACpB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,IAAI,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAC/C,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,wBAAwB,EAAE,OAAO,CAAC,gBAAgB;oBAClE,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAO;gBACxB,YAAY;oBAAC;iBAAO;gBACpB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,KAAK,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAChD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,+BAA+B,EAAE,OAAO,CAAC,gBAAgB;oBACzE,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAS;gBAC1B,YAAY;oBAAC;iBAAS;gBACtB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,OAAO,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAClD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SACI,CAAC;gBACL,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,+BAA+B,EAAE,OAAO,CAAC,gBAAgB;oBACzE,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAO;gBACxB,YAAY;oBAAC;iBAAO;gBACpB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,OAAO,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAClD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SACI,CAAC;gBACL,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,wBAAwB,EAAE,OAAO,CAAC,gBAAgB;oBAClE,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAO;gBACxB,YAAY;oBAAC;iBAAO;gBACpB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;IACJ;IACA,cAAc,4CAA4C,GAAG;IAC7D,MAAM;QACF,QAAQ;QACR,YAAY,OAAO,CAAE;YACjB,IAAI,CAAC,OAAO,GAAG;QACnB;QACA,OAAO,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAClD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SACI,CAAC;gBACL,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,oCAAoC,EAAE,OAAO,CAAC,gBAAgB;oBAC9E,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAS;gBAC1B,YAAY;oBAAC;iBAAS;gBACtB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,OAAO,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAClD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SACI,CAAC;gBACL,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,wBAAwB,EAAE,OAAO,CAAC,gBAAgB;oBAClE,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAO;gBACxB,YAAY;oBAAC;iBAAO;gBACpB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,IAAI,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAC/C,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SACI,CAAC;gBACL,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,wBAAwB,EAAE,OAAO,CAAC,gBAAgB;oBAClE,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAO;gBACxB,YAAY;oBAAC;iBAAO;gBACpB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,oBAAoB,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAC/D,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SACI,CAAC;gBACL,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,wDAAwD,EAAE,OAAO,CAAC,gBAAgB;oBAClG,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAS;gBAC1B,YAAY;oBAAC;iBAAS;gBACtB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,KAAK,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAChD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SACI,CAAC;gBACL,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,oCAAoC,EAAE,OAAO,CAAC,gBAAgB;oBAC9E,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAS;gBAC1B,YAAY;oBAAC;iBAAS;gBACtB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,OAAO,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAClD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SACI,CAAC;gBACL,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,+BAA+B,EAAE,OAAO,CAAC,gBAAgB;oBACzE,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAO;gBACxB,YAAY;oBAAC;iBAAO;gBACpB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,OAAO,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAClD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SACI,CAAC;gBACL,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,wBAAwB,EAAE,OAAO,CAAC,gBAAgB;oBAClE,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAO;gBACxB,YAAY;oBAAC;iBAAO;gBACpB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;IACJ;IACA,cAAc,iDAAiD,GAAG;IAClE,MAAM;QACF,QAAQ;QACR,YAAY,OAAO,CAAE;YACjB,IAAI,CAAC,OAAO,GAAG;QACnB;QACA,OAAO,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAClD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SACI,CAAC;gBACL,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,0CAA0C,EAAE,OAAO,CAAC,gBAAgB;oBACpF,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAS;gBAC1B,YAAY;oBAAC;iBAAS;gBACtB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,OAAO,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAClD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SACI,CAAC;gBACL,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,wBAAwB,EAAE,OAAO,CAAC,gBAAgB;oBAClE,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAO;gBACxB,YAAY;oBAAC;iBAAO;gBACpB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,IAAI,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAC/C,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SACI,CAAC;gBACL,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,wBAAwB,EAAE,OAAO,CAAC,gBAAgB;oBAClE,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAO;gBACxB,YAAY;oBAAC;iBAAO;gBACpB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,KAAK,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAChD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SACI,CAAC;gBACL,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,0CAA0C,EAAE,OAAO,CAAC,gBAAgB;oBACpF,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAS;gBAC1B,YAAY;oBAAC;iBAAS;gBACtB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,OAAO,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAClD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SACI,CAAC;gBACL,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,+BAA+B,EAAE,OAAO,CAAC,gBAAgB;oBACzE,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAO;gBACxB,YAAY;oBAAC;iBAAO;gBACpB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,OAAO,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAClD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SACI,CAAC;gBACL,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,wBAAwB,EAAE,OAAO,CAAC,gBAAgB;oBAClE,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAO;gBACxB,YAAY;oBAAC;iBAAO;gBACpB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;IACJ;IACA,cAAc,uDAAuD,GAAG;IACxE,MAAM;QACF,QAAQ;QACR,YAAY,OAAO,CAAE;YACjB,IAAI,CAAC,OAAO,GAAG;QACnB;QACA,OAAO,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAClD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SACI,CAAC;gBACL,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,mCAAmC,EAAE,OAAO,CAAC,gBAAgB;oBAC7E,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAS;gBAC1B,YAAY;oBAAC;iBAAS;gBACtB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,OAAO,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAClD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SACI,CAAC;gBACL,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,wBAAwB,EAAE,OAAO,CAAC,gBAAgB;oBAClE,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAO;gBACxB,YAAY;oBAAC;iBAAO;gBACpB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,IAAI,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAC/C,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SACI,CAAC;gBACL,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,wBAAwB,EAAE,OAAO,CAAC,gBAAgB;oBAClE,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAO;gBACxB,YAAY;oBAAC;iBAAO;gBACpB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,KAAK,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAChD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SACI,CAAC;gBACL,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,mCAAmC,EAAE,OAAO,CAAC,gBAAgB;oBAC7E,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAS;gBAC1B,YAAY;oBAAC;iBAAS;gBACtB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,OAAO,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAClD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SACI,CAAC;gBACL,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,+BAA+B,EAAE,OAAO,CAAC,gBAAgB;oBACzE,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAO;gBACxB,YAAY;oBAAC;iBAAO;gBACpB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,OAAO,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAClD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SACI,CAAC;gBACL,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,wBAAwB,EAAE,OAAO,CAAC,gBAAgB;oBAClE,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAO;gBACxB,YAAY;oBAAC;iBAAO;gBACpB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;IACJ;IACA,cAAc,gDAAgD,GAAG;IACjE,MAAM;QACF,QAAQ;QACR,YAAY,OAAO,CAAE;YACjB,IAAI,CAAC,OAAO,GAAG;QACnB;QACA,OAAO,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAClD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SACI,CAAC;gBACL,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,oCAAoC,EAAE,OAAO,CAAC,gBAAgB;oBAC9E,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAS;gBAC1B,YAAY;oBAAC;iBAAS;gBACtB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,OAAO,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAClD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SACI,CAAC;gBACL,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,wBAAwB,EAAE,OAAO,CAAC,gBAAgB;oBAClE,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAO;gBACxB,YAAY;oBAAC;iBAAO;gBACpB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,IAAI,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAC/C,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SACI,CAAC;gBACL,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,wBAAwB,EAAE,OAAO,CAAC,gBAAgB;oBAClE,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAO;gBACxB,YAAY;oBAAC;iBAAO;gBACpB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,KAAK,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAChD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SACI,CAAC;gBACL,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,oCAAoC,EAAE,OAAO,CAAC,gBAAgB;oBAC9E,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAS;gBAC1B,YAAY;oBAAC;iBAAS;gBACtB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,OAAO,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAClD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SACI,CAAC;gBACL,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,+BAA+B,EAAE,OAAO,CAAC,gBAAgB;oBACzE,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAO;gBACxB,YAAY;oBAAC;iBAAO;gBACpB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,OAAO,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAClD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SACI,CAAC;gBACL,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,wBAAwB,EAAE,OAAO,CAAC,gBAAgB;oBAClE,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAO;gBACxB,YAAY;oBAAC;iBAAO;gBACpB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;IACJ;IACA,cAAc,iDAAiD,GAAG;IAClE,MAAM;QACF,QAAQ;QACR,YAAY,OAAO,CAAE;YACjB,IAAI,CAAC,OAAO,GAAG;QACnB;QACA,OAAO,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAClD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SACI,CAAC;gBACL,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,gCAAgC,EAAE,OAAO,CAAC,gBAAgB;oBAC1E,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAS;gBAC1B,YAAY;oBAAC;iBAAS;gBACtB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,OAAO,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAClD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SACI,CAAC;gBACL,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,wBAAwB,EAAE,OAAO,CAAC,gBAAgB;oBAClE,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAO;gBACxB,YAAY;oBAAC;iBAAO;gBACpB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,IAAI,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAC/C,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,wBAAwB,EAAE,OAAO,CAAC,gBAAgB;oBAClE,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAO;gBACxB,YAAY;oBAAC;iBAAO;gBACpB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,KAAK,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAChD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SACI,CAAC;gBACL,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,gCAAgC,EAAE,OAAO,CAAC,gBAAgB;oBAC1E,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAS;gBAC1B,YAAY;oBAAC;iBAAS;gBACtB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,OAAO,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAClD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SACI,CAAC;gBACL,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,+BAA+B,EAAE,OAAO,CAAC,gBAAgB;oBACzE,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAO;gBACxB,YAAY;oBAAC;iBAAO;gBACpB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,OAAO,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAClD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SACI,CAAC;gBACL,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,wBAAwB,EAAE,OAAO,CAAC,gBAAgB;oBAClE,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAO;gBACxB,YAAY;oBAAC;iBAAO;gBACpB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;IACJ;IACA,cAAc,6CAA6C,GAAG;IAC9D,MAAM;QACF,QAAQ;QACR,YAAY,OAAO,CAAE;YACjB,IAAI,CAAC,OAAO,GAAG;QACnB;QACA,OAAO,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAClD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,2CAA2C,EAAE,OAAO,CAAC,gBAAgB;oBACrF,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAS;gBAC1B,YAAY;oBAAC;iBAAS;gBACtB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,OAAO,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAClD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,wBAAwB,EAAE,OAAO,CAAC,gBAAgB;oBAClE,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAO;gBACxB,YAAY;oBAAC;iBAAO;gBACpB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,IAAI,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAC/C,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,wBAAwB,EAAE,OAAO,CAAC,gBAAgB;oBAClE,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAO;gBACxB,YAAY;oBAAC;iBAAO;gBACpB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,KAAK,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAChD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,2CAA2C,EAAE,OAAO,CAAC,gBAAgB;oBACrF,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAS;gBAC1B,YAAY;oBAAC;iBAAS;gBACtB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,OAAO,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAClD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,wBAAwB,EAAE,OAAO,CAAC,gBAAgB;oBAClE,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAO;gBACxB,YAAY;oBAAC;iBAAO;gBACpB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;IACJ;IACA,cAAc,kCAAkC,GAAG;AACvD,CAAC,EAAE,iBAAiB,CAAC,QAAQ,aAAa,GAAG,gBAAgB,CAAC,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5724, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/AI_Projects/CRMcriadores/crm-interno/node_modules/googleapis/build/src/apis/tagmanager/index.js"], "sourcesContent": ["\"use strict\";\n// Copyright 2020 Google LLC\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//    http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.AuthPlus = exports.tagmanager_v2 = exports.tagmanager_v1 = exports.auth = exports.VERSIONS = void 0;\nexports.tagmanager = tagmanager;\n/*! THIS FILE IS AUTO-GENERATED */\nconst googleapis_common_1 = require(\"googleapis-common\");\nconst v1_1 = require(\"./v1\");\nObject.defineProperty(exports, \"tagmanager_v1\", { enumerable: true, get: function () { return v1_1.tagmanager_v1; } });\nconst v2_1 = require(\"./v2\");\nObject.defineProperty(exports, \"tagmanager_v2\", { enumerable: true, get: function () { return v2_1.tagmanager_v2; } });\nexports.VERSIONS = {\n    v1: v1_1.tagmanager_v1.Tagmanager,\n    v2: v2_1.tagmanager_v2.Tagmanager,\n};\nfunction tagmanager(versionOrOptions) {\n    return (0, googleapis_common_1.getAPI)('tagmanager', versionOrOptions, exports.VERSIONS, this);\n}\nconst auth = new googleapis_common_1.AuthPlus();\nexports.auth = auth;\nvar googleapis_common_2 = require(\"googleapis-common\");\nObject.defineProperty(exports, \"AuthPlus\", { enumerable: true, get: function () { return googleapis_common_2.AuthPlus; } });\n"], "names": [], "mappings": "AAAA;AACA,4BAA4B;AAC5B,kEAAkE;AAClE,mEAAmE;AACnE,0CAA0C;AAC1C,EAAE;AACF,gDAAgD;AAChD,EAAE;AACF,sEAAsE;AACtE,oEAAoE;AACpE,2EAA2E;AAC3E,sEAAsE;AACtE,iCAAiC;AACjC,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,QAAQ,QAAQ,GAAG,QAAQ,aAAa,GAAG,QAAQ,aAAa,GAAG,QAAQ,IAAI,GAAG,QAAQ,QAAQ,GAAG,KAAK;AAC1G,QAAQ,UAAU,GAAG;AACrB,gCAAgC,GAChC,MAAM;AACN,MAAM;AACN,OAAO,cAAc,CAAC,SAAS,iBAAiB;IAAE,YAAY;IAAM,KAAK;QAAc,OAAO,KAAK,aAAa;IAAE;AAAE;AACpH,MAAM;AACN,OAAO,cAAc,CAAC,SAAS,iBAAiB;IAAE,YAAY;IAAM,KAAK;QAAc,OAAO,KAAK,aAAa;IAAE;AAAE;AACpH,QAAQ,QAAQ,GAAG;IACf,IAAI,KAAK,aAAa,CAAC,UAAU;IACjC,IAAI,KAAK,aAAa,CAAC,UAAU;AACrC;AACA,SAAS,WAAW,gBAAgB;IAChC,OAAO,CAAC,GAAG,oBAAoB,MAAM,EAAE,cAAc,kBAAkB,QAAQ,QAAQ,EAAE,IAAI;AACjG;AACA,MAAM,OAAO,IAAI,oBAAoB,QAAQ;AAC7C,QAAQ,IAAI,GAAG;AACf,IAAI;AACJ,OAAO,cAAc,CAAC,SAAS,YAAY;IAAE,YAAY;IAAM,KAAK;QAAc,OAAO,oBAAoB,QAAQ;IAAE;AAAE", "ignoreList": [0], "debugId": null}}]}
{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/AI_Projects/CRMcriadores/crm-interno/app/actions/calendarActions.ts"], "sourcesContent": ["'use server';\n\nimport { google } from 'googleapis';\nimport { JWT } from 'google-auth-library';\n\n// Configuração de autenticação do Google Calendar\nfunction getGoogleCalendarAuth() {\n  const credentials = {\n    client_email: process.env.GOOGLE_CLIENT_EMAIL,\n    private_key: process.env.GOOGLE_PRIVATE_KEY?.replace(/\\\\n/g, '\\n'),\n  };\n\n  if (!credentials.client_email || !credentials.private_key) {\n    throw new Error('Credenciais do Google Calendar não configuradas');\n  }\n\n  return new JWT({\n    email: credentials.client_email,\n    key: credentials.private_key,\n    scopes: ['https://www.googleapis.com/auth/calendar'],\n  });\n}\n\n// Interface para evento do calendário\ninterface CalendarEvent {\n  summary: string;\n  description?: string;\n  startDateTime: string;\n  endDateTime: string;\n  attendees?: string[];\n  location?: string;\n}\n\n// Função para criar evento no Google Calendar\nexport async function createCalendarEvent(event: CalendarEvent) {\n  try {\n    const auth = getGoogleCalendarAuth();\n    const calendar = google.calendar({ version: 'v3', auth });\n    \n    const calendarId = process.env.GOOGLE_CALENDAR_ID || 'primary';\n    \n    if (!calendarId || calendarId === 'primary') {\n      console.log('Simulando criação de evento no calendário:', event.summary);\n      return { \n        success: true, \n        message: 'Evento simulado criado com sucesso',\n        eventId: `simulated-${Date.now()}`\n      };\n    }\n\n    const calendarEvent = {\n      summary: event.summary,\n      description: event.description,\n      start: {\n        dateTime: event.startDateTime,\n        timeZone: 'America/Sao_Paulo',\n      },\n      end: {\n        dateTime: event.endDateTime,\n        timeZone: 'America/Sao_Paulo',\n      },\n      attendees: event.attendees?.map(email => ({ email })),\n      location: event.location,\n      reminders: {\n        useDefault: false,\n        overrides: [\n          { method: 'email', minutes: 24 * 60 }, // 1 dia antes\n          { method: 'popup', minutes: 60 }, // 1 hora antes\n        ],\n      },\n    };\n\n    const response = await calendar.events.insert({\n      calendarId,\n      requestBody: calendarEvent,\n    });\n\n    return { \n      success: true, \n      data: response.data,\n      eventId: response.data.id \n    };\n  } catch (error) {\n    console.error('Erro ao criar evento no calendário:', error);\n    throw new Error('Falha ao criar evento no calendário');\n  }\n}\n\n// Função para atualizar evento no Google Calendar\nexport async function updateCalendarEvent(eventId: string, event: Partial<CalendarEvent>) {\n  try {\n    const auth = getGoogleCalendarAuth();\n    const calendar = google.calendar({ version: 'v3', auth });\n    \n    const calendarId = process.env.GOOGLE_CALENDAR_ID || 'primary';\n    \n    if (!calendarId || calendarId === 'primary') {\n      console.log('Simulando atualização de evento no calendário:', eventId);\n      return { \n        success: true, \n        message: 'Evento simulado atualizado com sucesso' \n      };\n    }\n\n    const updateData: any = {};\n    \n    if (event.summary) updateData.summary = event.summary;\n    if (event.description) updateData.description = event.description;\n    if (event.startDateTime) {\n      updateData.start = {\n        dateTime: event.startDateTime,\n        timeZone: 'America/Sao_Paulo',\n      };\n    }\n    if (event.endDateTime) {\n      updateData.end = {\n        dateTime: event.endDateTime,\n        timeZone: 'America/Sao_Paulo',\n      };\n    }\n    if (event.attendees) {\n      updateData.attendees = event.attendees.map(email => ({ email }));\n    }\n    if (event.location) updateData.location = event.location;\n\n    const response = await calendar.events.update({\n      calendarId,\n      eventId,\n      requestBody: updateData,\n    });\n\n    return { success: true, data: response.data };\n  } catch (error) {\n    console.error('Erro ao atualizar evento no calendário:', error);\n    throw new Error('Falha ao atualizar evento no calendário');\n  }\n}\n\n// Função para deletar evento do Google Calendar\nexport async function deleteCalendarEvent(eventId: string) {\n  try {\n    const auth = getGoogleCalendarAuth();\n    const calendar = google.calendar({ version: 'v3', auth });\n    \n    const calendarId = process.env.GOOGLE_CALENDAR_ID || 'primary';\n    \n    if (!calendarId || calendarId === 'primary') {\n      console.log('Simulando exclusão de evento no calendário:', eventId);\n      return { \n        success: true, \n        message: 'Evento simulado excluído com sucesso' \n      };\n    }\n\n    await calendar.events.delete({\n      calendarId,\n      eventId,\n    });\n\n    return { success: true };\n  } catch (error) {\n    console.error('Erro ao deletar evento no calendário:', error);\n    throw new Error('Falha ao deletar evento do calendário');\n  }\n}\n\n// Função para criar agendamento automático quando negócio entra na fase \"Agendamentos\"\nexport async function createSchedulingEvent(businessData: any) {\n  try {\n    const startDate = new Date();\n    startDate.setDate(startDate.getDate() + 2); // 2 dias a partir de hoje\n    startDate.setHours(14, 0, 0, 0); // 14:00\n\n    const endDate = new Date(startDate);\n    endDate.setHours(15, 0, 0, 0); // 15:00\n\n    const event: CalendarEvent = {\n      summary: `Agendamento: ${businessData.businessName}`,\n      description: `\nAgendamento para coordenação com criadores\n        \nNegócio: ${businessData.businessName}\nValor: R$ ${(businessData.value / 1000).toFixed(0)}K\nCriadores: ${businessData.creators?.length || 0}\n        \nPróxima ação: ${businessData.nextAction}\n        \nDescrição: ${businessData.description}\n      `.trim(),\n      startDateTime: startDate.toISOString(),\n      endDateTime: endDate.toISOString(),\n      location: 'Reunião Online',\n      attendees: businessData.creators?.map((creator: any) => creator.email).filter(Boolean) || []\n    };\n\n    const result = await createCalendarEvent(event);\n    \n    console.log(`Evento criado para ${businessData.businessName}:`, result.eventId);\n    \n    return result;\n  } catch (error) {\n    console.error('Erro ao criar agendamento automático:', error);\n    throw error;\n  }\n}\n\n// Função para listar eventos do calendário\nexport async function listCalendarEvents(timeMin?: string, timeMax?: string) {\n  try {\n    const auth = getGoogleCalendarAuth();\n    const calendar = google.calendar({ version: 'v3', auth });\n    \n    const calendarId = process.env.GOOGLE_CALENDAR_ID || 'primary';\n    \n    if (!calendarId || calendarId === 'primary') {\n      console.log('Simulando listagem de eventos do calendário');\n      return { \n        success: true, \n        events: [\n          {\n            id: 'simulated-1',\n            summary: 'Agendamento: Loja de Roupas Fashion',\n            start: { dateTime: new Date().toISOString() },\n            end: { dateTime: new Date(Date.now() + 3600000).toISOString() }\n          }\n        ]\n      };\n    }\n\n    const response = await calendar.events.list({\n      calendarId,\n      timeMin: timeMin || new Date().toISOString(),\n      timeMax: timeMax,\n      maxResults: 50,\n      singleEvents: true,\n      orderBy: 'startTime',\n    });\n\n    return { \n      success: true, \n      events: response.data.items || [] \n    };\n  } catch (error) {\n    console.error('Erro ao listar eventos do calendário:', error);\n    throw new Error('Falha ao listar eventos do calendário');\n  }\n}\n"], "names": [], "mappings": ";;;;;;;;;AAEA;AACA;;;;;;AAEA,kDAAkD;AAClD,SAAS;IACP,MAAM,cAAc;QAClB,cAAc,QAAQ,GAAG,CAAC,mBAAmB;QAC7C,aAAa,QAAQ,GAAG,CAAC,kBAAkB,EAAE,QAAQ,QAAQ;IAC/D;IAEA,IAAI,CAAC,YAAY,YAAY,IAAI,CAAC,YAAY,WAAW,EAAE;QACzD,MAAM,IAAI,MAAM;IAClB;IAEA,OAAO,IAAI,kKAAA,CAAA,MAAG,CAAC;QACb,OAAO,YAAY,YAAY;QAC/B,KAAK,YAAY,WAAW;QAC5B,QAAQ;YAAC;SAA2C;IACtD;AACF;AAaO,eAAe,oBAAoB,KAAoB;IAC5D,IAAI;QACF,MAAM,OAAO;QACb,MAAM,WAAW,mJAAA,CAAA,SAAM,CAAC,QAAQ,CAAC;YAAE,SAAS;YAAM;QAAK;QAEvD,MAAM,aAAa,QAAQ,GAAG,CAAC,kBAAkB,IAAI;QAErD,IAAI,CAAC,cAAc,eAAe,WAAW;YAC3C,QAAQ,GAAG,CAAC,8CAA8C,MAAM,OAAO;YACvE,OAAO;gBACL,SAAS;gBACT,SAAS;gBACT,SAAS,CAAC,UAAU,EAAE,KAAK,GAAG,IAAI;YACpC;QACF;QAEA,MAAM,gBAAgB;YACpB,SAAS,MAAM,OAAO;YACtB,aAAa,MAAM,WAAW;YAC9B,OAAO;gBACL,UAAU,MAAM,aAAa;gBAC7B,UAAU;YACZ;YACA,KAAK;gBACH,UAAU,MAAM,WAAW;gBAC3B,UAAU;YACZ;YACA,WAAW,MAAM,SAAS,EAAE,IAAI,CAAA,QAAS,CAAC;oBAAE;gBAAM,CAAC;YACnD,UAAU,MAAM,QAAQ;YACxB,WAAW;gBACT,YAAY;gBACZ,WAAW;oBACT;wBAAE,QAAQ;wBAAS,SAAS,KAAK;oBAAG;oBACpC;wBAAE,QAAQ;wBAAS,SAAS;oBAAG;iBAChC;YACH;QACF;QAEA,MAAM,WAAW,MAAM,SAAS,MAAM,CAAC,MAAM,CAAC;YAC5C;YACA,aAAa;QACf;QAEA,OAAO;YACL,SAAS;YACT,MAAM,SAAS,IAAI;YACnB,SAAS,SAAS,IAAI,CAAC,EAAE;QAC3B;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,uCAAuC;QACrD,MAAM,IAAI,MAAM;IAClB;AACF;AAGO,eAAe,oBAAoB,OAAe,EAAE,KAA6B;IACtF,IAAI;QACF,MAAM,OAAO;QACb,MAAM,WAAW,mJAAA,CAAA,SAAM,CAAC,QAAQ,CAAC;YAAE,SAAS;YAAM;QAAK;QAEvD,MAAM,aAAa,QAAQ,GAAG,CAAC,kBAAkB,IAAI;QAErD,IAAI,CAAC,cAAc,eAAe,WAAW;YAC3C,QAAQ,GAAG,CAAC,kDAAkD;YAC9D,OAAO;gBACL,SAAS;gBACT,SAAS;YACX;QACF;QAEA,MAAM,aAAkB,CAAC;QAEzB,IAAI,MAAM,OAAO,EAAE,WAAW,OAAO,GAAG,MAAM,OAAO;QACrD,IAAI,MAAM,WAAW,EAAE,WAAW,WAAW,GAAG,MAAM,WAAW;QACjE,IAAI,MAAM,aAAa,EAAE;YACvB,WAAW,KAAK,GAAG;gBACjB,UAAU,MAAM,aAAa;gBAC7B,UAAU;YACZ;QACF;QACA,IAAI,MAAM,WAAW,EAAE;YACrB,WAAW,GAAG,GAAG;gBACf,UAAU,MAAM,WAAW;gBAC3B,UAAU;YACZ;QACF;QACA,IAAI,MAAM,SAAS,EAAE;YACnB,WAAW,SAAS,GAAG,MAAM,SAAS,CAAC,GAAG,CAAC,CAAA,QAAS,CAAC;oBAAE;gBAAM,CAAC;QAChE;QACA,IAAI,MAAM,QAAQ,EAAE,WAAW,QAAQ,GAAG,MAAM,QAAQ;QAExD,MAAM,WAAW,MAAM,SAAS,MAAM,CAAC,MAAM,CAAC;YAC5C;YACA;YACA,aAAa;QACf;QAEA,OAAO;YAAE,SAAS;YAAM,MAAM,SAAS,IAAI;QAAC;IAC9C,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,2CAA2C;QACzD,MAAM,IAAI,MAAM;IAClB;AACF;AAGO,eAAe,oBAAoB,OAAe;IACvD,IAAI;QACF,MAAM,OAAO;QACb,MAAM,WAAW,mJAAA,CAAA,SAAM,CAAC,QAAQ,CAAC;YAAE,SAAS;YAAM;QAAK;QAEvD,MAAM,aAAa,QAAQ,GAAG,CAAC,kBAAkB,IAAI;QAErD,IAAI,CAAC,cAAc,eAAe,WAAW;YAC3C,QAAQ,GAAG,CAAC,+CAA+C;YAC3D,OAAO;gBACL,SAAS;gBACT,SAAS;YACX;QACF;QAEA,MAAM,SAAS,MAAM,CAAC,MAAM,CAAC;YAC3B;YACA;QACF;QAEA,OAAO;YAAE,SAAS;QAAK;IACzB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,yCAAyC;QACvD,MAAM,IAAI,MAAM;IAClB;AACF;AAGO,eAAe,sBAAsB,YAAiB;IAC3D,IAAI;QACF,MAAM,YAAY,IAAI;QACtB,UAAU,OAAO,CAAC,UAAU,OAAO,KAAK,IAAI,0BAA0B;QACtE,UAAU,QAAQ,CAAC,IAAI,GAAG,GAAG,IAAI,QAAQ;QAEzC,MAAM,UAAU,IAAI,KAAK;QACzB,QAAQ,QAAQ,CAAC,IAAI,GAAG,GAAG,IAAI,QAAQ;QAEvC,MAAM,QAAuB;YAC3B,SAAS,CAAC,aAAa,EAAE,aAAa,YAAY,EAAE;YACpD,aAAa,CAAC;;;SAGX,EAAE,aAAa,YAAY,CAAC;UAC3B,EAAE,CAAC,aAAa,KAAK,GAAG,IAAI,EAAE,OAAO,CAAC,GAAG;WACxC,EAAE,aAAa,QAAQ,EAAE,UAAU,EAAE;;cAElC,EAAE,aAAa,UAAU,CAAC;;WAE7B,EAAE,aAAa,WAAW,CAAC;MAChC,CAAC,CAAC,IAAI;YACN,eAAe,UAAU,WAAW;YACpC,aAAa,QAAQ,WAAW;YAChC,UAAU;YACV,WAAW,aAAa,QAAQ,EAAE,IAAI,CAAC,UAAiB,QAAQ,KAAK,EAAE,OAAO,YAAY,EAAE;QAC9F;QAEA,MAAM,SAAS,MAAM,oBAAoB;QAEzC,QAAQ,GAAG,CAAC,CAAC,mBAAmB,EAAE,aAAa,YAAY,CAAC,CAAC,CAAC,EAAE,OAAO,OAAO;QAE9E,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,yCAAyC;QACvD,MAAM;IACR;AACF;AAGO,eAAe,mBAAmB,OAAgB,EAAE,OAAgB;IACzE,IAAI;QACF,MAAM,OAAO;QACb,MAAM,WAAW,mJAAA,CAAA,SAAM,CAAC,QAAQ,CAAC;YAAE,SAAS;YAAM;QAAK;QAEvD,MAAM,aAAa,QAAQ,GAAG,CAAC,kBAAkB,IAAI;QAErD,IAAI,CAAC,cAAc,eAAe,WAAW;YAC3C,QAAQ,GAAG,CAAC;YACZ,OAAO;gBACL,SAAS;gBACT,QAAQ;oBACN;wBACE,IAAI;wBACJ,SAAS;wBACT,OAAO;4BAAE,UAAU,IAAI,OAAO,WAAW;wBAAG;wBAC5C,KAAK;4BAAE,UAAU,IAAI,KAAK,KAAK,GAAG,KAAK,SAAS,WAAW;wBAAG;oBAChE;iBACD;YACH;QACF;QAEA,MAAM,WAAW,MAAM,SAAS,MAAM,CAAC,IAAI,CAAC;YAC1C;YACA,SAAS,WAAW,IAAI,OAAO,WAAW;YAC1C,SAAS;YACT,YAAY;YACZ,cAAc;YACd,SAAS;QACX;QAEA,OAAO;YACL,SAAS;YACT,QAAQ,SAAS,IAAI,CAAC,KAAK,IAAI,EAAE;QACnC;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,yCAAyC;QACvD,MAAM,IAAI,MAAM;IAClB;AACF;;;IApNsB;IAuDA;IAkDA;IA4BA;IAwCA;;AA7KA,+OAAA;AAuDA,+OAAA;AAkDA,+OAAA;AA4BA,+OAAA;AAwCA,+OAAA", "debugId": null}}]}
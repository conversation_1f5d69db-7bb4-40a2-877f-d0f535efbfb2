{"version": 3, "sources": [], "sections": [{"offset": {"line": 111, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/AI_Projects/CRMcriadores/crm-interno/app/actions/sheetsActions.ts"], "sourcesContent": ["'use server';\n\nimport { google } from 'googleapis';\n\n// Configuração da autenticação\nconst getGoogleSheetsAuth = () => {\n  const credentials = {\n    type: 'service_account',\n    project_id: process.env.GOOGLE_PROJECT_ID,\n    private_key_id: process.env.GOOGLE_PRIVATE_KEY_ID,\n    private_key: process.env.GOOGLE_PRIVATE_KEY?.replace(/\\\\n/g, '\\n'),\n    client_email: process.env.GOOGLE_CLIENT_EMAIL,\n    client_id: process.env.GOOGLE_CLIENT_ID,\n    auth_uri: 'https://accounts.google.com/o/oauth2/auth',\n    token_uri: 'https://oauth2.googleapis.com/token',\n    auth_provider_x509_cert_url: 'https://www.googleapis.com/oauth2/v1/certs',\n    client_x509_cert_url: `https://www.googleapis.com/robot/v1/metadata/x509/${process.env.GOOGLE_CLIENT_EMAIL}`,\n  };\n\n  const auth = new google.auth.GoogleAuth({\n    credentials,\n    scopes: ['https://www.googleapis.com/auth/spreadsheets'],\n  });\n\n  return auth;\n};\n\n// Função para ler dados da planilha\nexport async function getData(sheetName: string) {\n  try {\n    const auth = getGoogleSheetsAuth();\n    const sheets = google.sheets({ version: 'v4', auth });\n    \n    const spreadsheetId = process.env.GOOGLE_SPREADSHEET_ID;\n    \n    if (!spreadsheetId) {\n      throw new Error('GOOGLE_SPREADSHEET_ID não está configurado');\n    }\n\n    const response = await sheets.spreadsheets.values.get({\n      spreadsheetId,\n      range: `${sheetName}!A:Z`, // Lê todas as colunas de A até Z\n    });\n\n    return response.data.values || [];\n  } catch (error) {\n    console.error('Erro ao ler dados da planilha:', error);\n    throw new Error('Falha ao ler dados da planilha');\n  }\n}\n\n// Função para adicionar dados à planilha\nexport async function appendData(sheetName: string, rowData: any[]) {\n  try {\n    const auth = getGoogleSheetsAuth();\n    const sheets = google.sheets({ version: 'v4', auth });\n    \n    const spreadsheetId = process.env.GOOGLE_SPREADSHEET_ID;\n    \n    if (!spreadsheetId) {\n      throw new Error('GOOGLE_SPREADSHEET_ID não está configurado');\n    }\n\n    const response = await sheets.spreadsheets.values.append({\n      spreadsheetId,\n      range: `${sheetName}!A:Z`,\n      valueInputOption: 'USER_ENTERED',\n      requestBody: {\n        values: [rowData],\n      },\n    });\n\n    return response.data;\n  } catch (error) {\n    console.error('Erro ao adicionar dados à planilha:', error);\n    throw new Error('Falha ao adicionar dados à planilha');\n  }\n}\n\n// Função para atualizar dados na planilha\nexport async function updateData(sheetName: string, range: string, rowData: any[]) {\n  try {\n    const auth = getGoogleSheetsAuth();\n    const sheets = google.sheets({ version: 'v4', auth });\n\n    const spreadsheetId = process.env.GOOGLE_SPREADSHEET_ID;\n\n    if (!spreadsheetId) {\n      throw new Error('GOOGLE_SPREADSHEET_ID não está configurado');\n    }\n\n    const response = await sheets.spreadsheets.values.update({\n      spreadsheetId,\n      range: `${sheetName}!${range}`,\n      valueInputOption: 'USER_ENTERED',\n      requestBody: {\n        values: [rowData],\n      },\n    });\n\n    return response.data;\n  } catch (error) {\n    console.error('Erro ao atualizar dados da planilha:', error);\n    throw new Error('Falha ao atualizar dados da planilha');\n  }\n}\n\n// Função específica para atualizar o estágio da jornada de um negócio\nexport async function updateBusinessStage(businessId: string, newStage: string) {\n  try {\n    // Se Google Sheets não estiver configurado, simula sucesso\n    const spreadsheetId = process.env.GOOGLE_SPREADSHEET_ID;\n\n    if (!spreadsheetId) {\n      console.log(`Simulando atualização: Negócio ${businessId} movido para ${newStage}`);\n      return { success: true, message: 'Atualização simulada com sucesso' };\n    }\n\n    const auth = getGoogleSheetsAuth();\n    const sheets = google.sheets({ version: 'v4', auth });\n\n    // Primeiro, busca todos os dados para encontrar a linha do negócio\n    const allData = await sheets.spreadsheets.values.get({\n      spreadsheetId,\n      range: 'Businesses!A:Z',\n    });\n\n    const rows = allData.data.values || [];\n    if (rows.length === 0) {\n      throw new Error('Nenhum dado encontrado na planilha');\n    }\n\n    // Encontra a linha do negócio pelo ID\n    const headers = rows[0];\n    const idColumnIndex = headers.findIndex((header: string) =>\n      header.toLowerCase().includes('id')\n    );\n    const stageColumnIndex = headers.findIndex((header: string) =>\n      header.toLowerCase().includes('stage') || header.toLowerCase().includes('estágio')\n    );\n\n    if (idColumnIndex === -1 || stageColumnIndex === -1) {\n      throw new Error('Colunas ID ou Stage não encontradas');\n    }\n\n    // Procura pela linha do negócio\n    let targetRowIndex = -1;\n    for (let i = 1; i < rows.length; i++) {\n      if (rows[i][idColumnIndex] === businessId) {\n        targetRowIndex = i + 1; // +1 porque as linhas do Sheets são 1-indexed\n        break;\n      }\n    }\n\n    if (targetRowIndex === -1) {\n      throw new Error(`Negócio com ID ${businessId} não encontrado`);\n    }\n\n    // Atualiza apenas a célula do estágio\n    const stageColumn = String.fromCharCode(65 + stageColumnIndex); // Converte índice para letra (A, B, C...)\n    const range = `${stageColumn}${targetRowIndex}`;\n\n    const response = await sheets.spreadsheets.values.update({\n      spreadsheetId,\n      range: `Businesses!${range}`,\n      valueInputOption: 'USER_ENTERED',\n      requestBody: {\n        values: [[newStage]],\n      },\n    });\n\n    return { success: true, data: response.data };\n  } catch (error) {\n    console.error('Erro ao atualizar estágio do negócio:', error);\n    throw new Error('Falha ao atualizar estágio do negócio');\n  }\n}\n"], "names": [], "mappings": ";;;;;;;;AAEA;;;;;AAEA,+BAA+B;AAC/B,MAAM,sBAAsB;IAC1B,MAAM,cAAc;QAClB,MAAM;QACN,YAAY,QAAQ,GAAG,CAAC,iBAAiB;QACzC,gBAAgB,QAAQ,GAAG,CAAC,qBAAqB;QACjD,aAAa,QAAQ,GAAG,CAAC,kBAAkB,EAAE,QAAQ,QAAQ;QAC7D,cAAc,QAAQ,GAAG,CAAC,mBAAmB;QAC7C,WAAW,QAAQ,GAAG,CAAC,gBAAgB;QACvC,UAAU;QACV,WAAW;QACX,6BAA6B;QAC7B,sBAAsB,CAAC,kDAAkD,EAAE,QAAQ,GAAG,CAAC,mBAAmB,EAAE;IAC9G;IAEA,MAAM,OAAO,IAAI,mJAAA,CAAA,SAAM,CAAC,IAAI,CAAC,UAAU,CAAC;QACtC;QACA,QAAQ;YAAC;SAA+C;IAC1D;IAEA,OAAO;AACT;AAGO,eAAe,QAAQ,SAAiB;IAC7C,IAAI;QACF,MAAM,OAAO;QACb,MAAM,SAAS,mJAAA,CAAA,SAAM,CAAC,MAAM,CAAC;YAAE,SAAS;YAAM;QAAK;QAEnD,MAAM,gBAAgB,QAAQ,GAAG,CAAC,qBAAqB;QAEvD,IAAI,CAAC,eAAe;YAClB,MAAM,IAAI,MAAM;QAClB;QAEA,MAAM,WAAW,MAAM,OAAO,YAAY,CAAC,MAAM,CAAC,GAAG,CAAC;YACpD;YACA,OAAO,GAAG,UAAU,IAAI,CAAC;QAC3B;QAEA,OAAO,SAAS,IAAI,CAAC,MAAM,IAAI,EAAE;IACnC,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,kCAAkC;QAChD,MAAM,IAAI,MAAM;IAClB;AACF;AAGO,eAAe,WAAW,SAAiB,EAAE,OAAc;IAChE,IAAI;QACF,MAAM,OAAO;QACb,MAAM,SAAS,mJAAA,CAAA,SAAM,CAAC,MAAM,CAAC;YAAE,SAAS;YAAM;QAAK;QAEnD,MAAM,gBAAgB,QAAQ,GAAG,CAAC,qBAAqB;QAEvD,IAAI,CAAC,eAAe;YAClB,MAAM,IAAI,MAAM;QAClB;QAEA,MAAM,WAAW,MAAM,OAAO,YAAY,CAAC,MAAM,CAAC,MAAM,CAAC;YACvD;YACA,OAAO,GAAG,UAAU,IAAI,CAAC;YACzB,kBAAkB;YAClB,aAAa;gBACX,QAAQ;oBAAC;iBAAQ;YACnB;QACF;QAEA,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,uCAAuC;QACrD,MAAM,IAAI,MAAM;IAClB;AACF;AAGO,eAAe,WAAW,SAAiB,EAAE,KAAa,EAAE,OAAc;IAC/E,IAAI;QACF,MAAM,OAAO;QACb,MAAM,SAAS,mJAAA,CAAA,SAAM,CAAC,MAAM,CAAC;YAAE,SAAS;YAAM;QAAK;QAEnD,MAAM,gBAAgB,QAAQ,GAAG,CAAC,qBAAqB;QAEvD,IAAI,CAAC,eAAe;YAClB,MAAM,IAAI,MAAM;QAClB;QAEA,MAAM,WAAW,MAAM,OAAO,YAAY,CAAC,MAAM,CAAC,MAAM,CAAC;YACvD;YACA,OAAO,GAAG,UAAU,CAAC,EAAE,OAAO;YAC9B,kBAAkB;YAClB,aAAa;gBACX,QAAQ;oBAAC;iBAAQ;YACnB;QACF;QAEA,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,wCAAwC;QACtD,MAAM,IAAI,MAAM;IAClB;AACF;AAGO,eAAe,oBAAoB,UAAkB,EAAE,QAAgB;IAC5E,IAAI;QACF,2DAA2D;QAC3D,MAAM,gBAAgB,QAAQ,GAAG,CAAC,qBAAqB;QAEvD,IAAI,CAAC,eAAe;YAClB,QAAQ,GAAG,CAAC,CAAC,+BAA+B,EAAE,WAAW,aAAa,EAAE,UAAU;YAClF,OAAO;gBAAE,SAAS;gBAAM,SAAS;YAAmC;QACtE;QAEA,MAAM,OAAO;QACb,MAAM,SAAS,mJAAA,CAAA,SAAM,CAAC,MAAM,CAAC;YAAE,SAAS;YAAM;QAAK;QAEnD,mEAAmE;QACnE,MAAM,UAAU,MAAM,OAAO,YAAY,CAAC,MAAM,CAAC,GAAG,CAAC;YACnD;YACA,OAAO;QACT;QAEA,MAAM,OAAO,QAAQ,IAAI,CAAC,MAAM,IAAI,EAAE;QACtC,IAAI,KAAK,MAAM,KAAK,GAAG;YACrB,MAAM,IAAI,MAAM;QAClB;QAEA,sCAAsC;QACtC,MAAM,UAAU,IAAI,CAAC,EAAE;QACvB,MAAM,gBAAgB,QAAQ,SAAS,CAAC,CAAC,SACvC,OAAO,WAAW,GAAG,QAAQ,CAAC;QAEhC,MAAM,mBAAmB,QAAQ,SAAS,CAAC,CAAC,SAC1C,OAAO,WAAW,GAAG,QAAQ,CAAC,YAAY,OAAO,WAAW,GAAG,QAAQ,CAAC;QAG1E,IAAI,kBAAkB,CAAC,KAAK,qBAAqB,CAAC,GAAG;YACnD,MAAM,IAAI,MAAM;QAClB;QAEA,gCAAgC;QAChC,IAAI,iBAAiB,CAAC;QACtB,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,MAAM,EAAE,IAAK;YACpC,IAAI,IAAI,CAAC,EAAE,CAAC,cAAc,KAAK,YAAY;gBACzC,iBAAiB,IAAI,GAAG,8CAA8C;gBACtE;YACF;QACF;QAEA,IAAI,mBAAmB,CAAC,GAAG;YACzB,MAAM,IAAI,MAAM,CAAC,eAAe,EAAE,WAAW,eAAe,CAAC;QAC/D;QAEA,sCAAsC;QACtC,MAAM,cAAc,OAAO,YAAY,CAAC,KAAK,mBAAmB,0CAA0C;QAC1G,MAAM,QAAQ,GAAG,cAAc,gBAAgB;QAE/C,MAAM,WAAW,MAAM,OAAO,YAAY,CAAC,MAAM,CAAC,MAAM,CAAC;YACvD;YACA,OAAO,CAAC,WAAW,EAAE,OAAO;YAC5B,kBAAkB;YAClB,aAAa;gBACX,QAAQ;oBAAC;wBAAC;qBAAS;iBAAC;YACtB;QACF;QAEA,OAAO;YAAE,SAAS;YAAM,MAAM,SAAS,IAAI;QAAC;IAC9C,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,yCAAyC;QACvD,MAAM,IAAI,MAAM;IAClB;AACF;;;IApJsB;IAwBA;IA4BA;IA4BA;;AAhFA,+OAAA;AAwBA,+OAAA;AA4BA,+OAAA;AA4BA,+OAAA", "debugId": null}}, {"offset": {"line": 305, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/AI_Projects/CRMcriadores/crm-interno/.next-internal/server/app/%28dashboard%29/jornada/page/actions.js%20%28server%20actions%20loader%29"], "sourcesContent": ["export {getData as '40416fe263e60966356356bb294165af55bf27c98c'} from 'ACTIONS_MODULE0'\nexport {updateBusinessStage as '60c2e3b2963d1dbcaed9ada159154eae380101e11a'} from 'ACTIONS_MODULE0'\n"], "names": [], "mappings": ";AAAA", "debugId": null}}, {"offset": {"line": 366, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/AI_Projects/CRMcriadores/crm-interno/app/%28dashboard%29/jornada/page.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/app/(dashboard)/jornada/page.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/app/(dashboard)/jornada/page.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAoS,GACjU,kEACA", "debugId": null}}, {"offset": {"line": 380, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/AI_Projects/CRMcriadores/crm-interno/app/%28dashboard%29/jornada/page.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/app/(dashboard)/jornada/page.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/app/(dashboard)/jornada/page.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAgR,GAC7S,8CACA", "debugId": null}}, {"offset": {"line": 394, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}]}
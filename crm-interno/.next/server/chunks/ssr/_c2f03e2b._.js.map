{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/AI_Projects/CRMcriadores/crm-interno/app/actions/sheetsActions.ts"], "sourcesContent": ["'use server';\n\nimport { google } from 'googleapis';\n\n// Configuração da autenticação\nconst getGoogleSheetsAuth = () => {\n  const credentials = {\n    type: 'service_account',\n    project_id: process.env.GOOGLE_PROJECT_ID,\n    private_key_id: process.env.GOOGLE_PRIVATE_KEY_ID,\n    private_key: process.env.GOOGLE_PRIVATE_KEY?.replace(/\\\\n/g, '\\n'),\n    client_email: process.env.GOOGLE_CLIENT_EMAIL,\n    client_id: process.env.GOOGLE_CLIENT_ID,\n    auth_uri: 'https://accounts.google.com/o/oauth2/auth',\n    token_uri: 'https://oauth2.googleapis.com/token',\n    auth_provider_x509_cert_url: 'https://www.googleapis.com/oauth2/v1/certs',\n    client_x509_cert_url: `https://www.googleapis.com/robot/v1/metadata/x509/${process.env.GOOGLE_CLIENT_EMAIL}`,\n  };\n\n  const auth = new google.auth.GoogleAuth({\n    credentials,\n    scopes: ['https://www.googleapis.com/auth/spreadsheets'],\n  });\n\n  return auth;\n};\n\n// Função para ler dados da planilha\nexport async function getData(sheetName: string) {\n  try {\n    const auth = getGoogleSheetsAuth();\n    const sheets = google.sheets({ version: 'v4', auth });\n    \n    const spreadsheetId = process.env.GOOGLE_SPREADSHEET_ID;\n    \n    if (!spreadsheetId) {\n      throw new Error('GOOGLE_SPREADSHEET_ID não está configurado');\n    }\n\n    const response = await sheets.spreadsheets.values.get({\n      spreadsheetId,\n      range: `${sheetName}!A:Z`, // Lê todas as colunas de A até Z\n    });\n\n    return response.data.values || [];\n  } catch (error) {\n    console.error('Erro ao ler dados da planilha:', error);\n    throw new Error('Falha ao ler dados da planilha');\n  }\n}\n\n// Função para adicionar dados à planilha\nexport async function appendData(sheetName: string, rowData: any[]) {\n  try {\n    const auth = getGoogleSheetsAuth();\n    const sheets = google.sheets({ version: 'v4', auth });\n    \n    const spreadsheetId = process.env.GOOGLE_SPREADSHEET_ID;\n    \n    if (!spreadsheetId) {\n      throw new Error('GOOGLE_SPREADSHEET_ID não está configurado');\n    }\n\n    const response = await sheets.spreadsheets.values.append({\n      spreadsheetId,\n      range: `${sheetName}!A:Z`,\n      valueInputOption: 'USER_ENTERED',\n      requestBody: {\n        values: [rowData],\n      },\n    });\n\n    return response.data;\n  } catch (error) {\n    console.error('Erro ao adicionar dados à planilha:', error);\n    throw new Error('Falha ao adicionar dados à planilha');\n  }\n}\n\n// Função para atualizar dados na planilha\nexport async function updateData(sheetName: string, range: string, rowData: any[]) {\n  try {\n    const auth = getGoogleSheetsAuth();\n    const sheets = google.sheets({ version: 'v4', auth });\n\n    const spreadsheetId = process.env.GOOGLE_SPREADSHEET_ID;\n\n    if (!spreadsheetId) {\n      throw new Error('GOOGLE_SPREADSHEET_ID não está configurado');\n    }\n\n    const response = await sheets.spreadsheets.values.update({\n      spreadsheetId,\n      range: `${sheetName}!${range}`,\n      valueInputOption: 'USER_ENTERED',\n      requestBody: {\n        values: [rowData],\n      },\n    });\n\n    return response.data;\n  } catch (error) {\n    console.error('Erro ao atualizar dados da planilha:', error);\n    throw new Error('Falha ao atualizar dados da planilha');\n  }\n}\n\n// Função específica para atualizar o estágio da jornada de um negócio\nexport async function updateBusinessStage(businessId: string, newStage: string, businessData?: any) {\n  try {\n    // Se Google Sheets não estiver configurado, simula sucesso\n    const spreadsheetId = process.env.GOOGLE_SPREADSHEET_ID;\n\n    if (!spreadsheetId) {\n      console.log(`Simulando atualização: Negócio ${businessId} movido para ${newStage}`);\n      return { success: true, message: 'Atualização simulada com sucesso' };\n    }\n\n    const auth = getGoogleSheetsAuth();\n    const sheets = google.sheets({ version: 'v4', auth });\n\n    // Primeiro, busca todos os dados para encontrar a linha do negócio\n    const allData = await sheets.spreadsheets.values.get({\n      spreadsheetId,\n      range: 'Businesses!A:Z',\n    });\n\n    const rows = allData.data.values || [];\n    if (rows.length === 0) {\n      throw new Error('Nenhum dado encontrado na planilha');\n    }\n\n    // Encontra a linha do negócio pelo ID\n    const headers = rows[0];\n    const idColumnIndex = headers.findIndex((header: string) =>\n      header.toLowerCase().includes('id')\n    );\n    const stageColumnIndex = headers.findIndex((header: string) =>\n      header.toLowerCase().includes('stage') || header.toLowerCase().includes('estágio')\n    );\n\n    if (idColumnIndex === -1 || stageColumnIndex === -1) {\n      throw new Error('Colunas ID ou Stage não encontradas');\n    }\n\n    // Procura pela linha do negócio\n    let targetRowIndex = -1;\n    for (let i = 1; i < rows.length; i++) {\n      if (rows[i][idColumnIndex] === businessId) {\n        targetRowIndex = i + 1; // +1 porque as linhas do Sheets são 1-indexed\n        break;\n      }\n    }\n\n    if (targetRowIndex === -1) {\n      throw new Error(`Negócio com ID ${businessId} não encontrado`);\n    }\n\n    // Atualiza apenas a célula do estágio\n    const stageColumn = String.fromCharCode(65 + stageColumnIndex); // Converte índice para letra (A, B, C...)\n    const range = `${stageColumn}${targetRowIndex}`;\n\n    const response = await sheets.spreadsheets.values.update({\n      spreadsheetId,\n      range: `Businesses!${range}`,\n      valueInputOption: 'USER_ENTERED',\n      requestBody: {\n        values: [[newStage]],\n      },\n    });\n\n    // Se o novo estágio for \"Agendamentos\", criar evento no calendário\n    if (newStage === 'Agendamentos' && businessData) {\n      try {\n        const { createSchedulingEvent } = await import('./calendarActions');\n        await createSchedulingEvent(businessData);\n        console.log(`Evento de agendamento criado para ${businessData.businessName}`);\n      } catch (calendarError) {\n        console.error('Erro ao criar evento no calendário:', calendarError);\n        // Não falha a operação principal se o calendário falhar\n      }\n    }\n\n    return { success: true, data: response.data };\n  } catch (error) {\n    console.error('Erro ao atualizar estágio do negócio:', error);\n    throw new Error('Falha ao atualizar estágio do negócio');\n  }\n}\n"], "names": [], "mappings": ";;;;;;IA4BsB,UAAA,WAAA,GAAA,CAAA,GAAA,sNAAA,CAAA,wBAAA,EAAA,8CAAA,sNAAA,CAAA,aAAA,EAAA,KAAA,GAAA,sNAAA,CAAA,mBAAA,EAAA", "debugId": null}}, {"offset": {"line": 20, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/AI_Projects/CRMcriadores/crm-interno/app/actions/sheetsActions.ts"], "sourcesContent": ["'use server';\n\nimport { google } from 'googleapis';\n\n// Configuração da autenticação\nconst getGoogleSheetsAuth = () => {\n  const credentials = {\n    type: 'service_account',\n    project_id: process.env.GOOGLE_PROJECT_ID,\n    private_key_id: process.env.GOOGLE_PRIVATE_KEY_ID,\n    private_key: process.env.GOOGLE_PRIVATE_KEY?.replace(/\\\\n/g, '\\n'),\n    client_email: process.env.GOOGLE_CLIENT_EMAIL,\n    client_id: process.env.GOOGLE_CLIENT_ID,\n    auth_uri: 'https://accounts.google.com/o/oauth2/auth',\n    token_uri: 'https://oauth2.googleapis.com/token',\n    auth_provider_x509_cert_url: 'https://www.googleapis.com/oauth2/v1/certs',\n    client_x509_cert_url: `https://www.googleapis.com/robot/v1/metadata/x509/${process.env.GOOGLE_CLIENT_EMAIL}`,\n  };\n\n  const auth = new google.auth.GoogleAuth({\n    credentials,\n    scopes: ['https://www.googleapis.com/auth/spreadsheets'],\n  });\n\n  return auth;\n};\n\n// Função para ler dados da planilha\nexport async function getData(sheetName: string) {\n  try {\n    const auth = getGoogleSheetsAuth();\n    const sheets = google.sheets({ version: 'v4', auth });\n    \n    const spreadsheetId = process.env.GOOGLE_SPREADSHEET_ID;\n    \n    if (!spreadsheetId) {\n      throw new Error('GOOGLE_SPREADSHEET_ID não está configurado');\n    }\n\n    const response = await sheets.spreadsheets.values.get({\n      spreadsheetId,\n      range: `${sheetName}!A:Z`, // Lê todas as colunas de A até Z\n    });\n\n    return response.data.values || [];\n  } catch (error) {\n    console.error('Erro ao ler dados da planilha:', error);\n    throw new Error('Falha ao ler dados da planilha');\n  }\n}\n\n// Função para adicionar dados à planilha\nexport async function appendData(sheetName: string, rowData: any[]) {\n  try {\n    const auth = getGoogleSheetsAuth();\n    const sheets = google.sheets({ version: 'v4', auth });\n    \n    const spreadsheetId = process.env.GOOGLE_SPREADSHEET_ID;\n    \n    if (!spreadsheetId) {\n      throw new Error('GOOGLE_SPREADSHEET_ID não está configurado');\n    }\n\n    const response = await sheets.spreadsheets.values.append({\n      spreadsheetId,\n      range: `${sheetName}!A:Z`,\n      valueInputOption: 'USER_ENTERED',\n      requestBody: {\n        values: [rowData],\n      },\n    });\n\n    return response.data;\n  } catch (error) {\n    console.error('Erro ao adicionar dados à planilha:', error);\n    throw new Error('Falha ao adicionar dados à planilha');\n  }\n}\n\n// Função para atualizar dados na planilha\nexport async function updateData(sheetName: string, range: string, rowData: any[]) {\n  try {\n    const auth = getGoogleSheetsAuth();\n    const sheets = google.sheets({ version: 'v4', auth });\n\n    const spreadsheetId = process.env.GOOGLE_SPREADSHEET_ID;\n\n    if (!spreadsheetId) {\n      throw new Error('GOOGLE_SPREADSHEET_ID não está configurado');\n    }\n\n    const response = await sheets.spreadsheets.values.update({\n      spreadsheetId,\n      range: `${sheetName}!${range}`,\n      valueInputOption: 'USER_ENTERED',\n      requestBody: {\n        values: [rowData],\n      },\n    });\n\n    return response.data;\n  } catch (error) {\n    console.error('Erro ao atualizar dados da planilha:', error);\n    throw new Error('Falha ao atualizar dados da planilha');\n  }\n}\n\n// Função específica para atualizar o estágio da jornada de um negócio\nexport async function updateBusinessStage(businessId: string, newStage: string, businessData?: any) {\n  try {\n    // Se Google Sheets não estiver configurado, simula sucesso\n    const spreadsheetId = process.env.GOOGLE_SPREADSHEET_ID;\n\n    if (!spreadsheetId) {\n      console.log(`Simulando atualização: Negócio ${businessId} movido para ${newStage}`);\n      return { success: true, message: 'Atualização simulada com sucesso' };\n    }\n\n    const auth = getGoogleSheetsAuth();\n    const sheets = google.sheets({ version: 'v4', auth });\n\n    // Primeiro, busca todos os dados para encontrar a linha do negócio\n    const allData = await sheets.spreadsheets.values.get({\n      spreadsheetId,\n      range: 'Businesses!A:Z',\n    });\n\n    const rows = allData.data.values || [];\n    if (rows.length === 0) {\n      throw new Error('Nenhum dado encontrado na planilha');\n    }\n\n    // Encontra a linha do negócio pelo ID\n    const headers = rows[0];\n    const idColumnIndex = headers.findIndex((header: string) =>\n      header.toLowerCase().includes('id')\n    );\n    const stageColumnIndex = headers.findIndex((header: string) =>\n      header.toLowerCase().includes('stage') || header.toLowerCase().includes('estágio')\n    );\n\n    if (idColumnIndex === -1 || stageColumnIndex === -1) {\n      throw new Error('Colunas ID ou Stage não encontradas');\n    }\n\n    // Procura pela linha do negócio\n    let targetRowIndex = -1;\n    for (let i = 1; i < rows.length; i++) {\n      if (rows[i][idColumnIndex] === businessId) {\n        targetRowIndex = i + 1; // +1 porque as linhas do Sheets são 1-indexed\n        break;\n      }\n    }\n\n    if (targetRowIndex === -1) {\n      throw new Error(`Negócio com ID ${businessId} não encontrado`);\n    }\n\n    // Atualiza apenas a célula do estágio\n    const stageColumn = String.fromCharCode(65 + stageColumnIndex); // Converte índice para letra (A, B, C...)\n    const range = `${stageColumn}${targetRowIndex}`;\n\n    const response = await sheets.spreadsheets.values.update({\n      spreadsheetId,\n      range: `Businesses!${range}`,\n      valueInputOption: 'USER_ENTERED',\n      requestBody: {\n        values: [[newStage]],\n      },\n    });\n\n    // Se o novo estágio for \"Agendamentos\", criar evento no calendário\n    if (newStage === 'Agendamentos' && businessData) {\n      try {\n        const { createSchedulingEvent } = await import('./calendarActions');\n        await createSchedulingEvent(businessData);\n        console.log(`Evento de agendamento criado para ${businessData.businessName}`);\n      } catch (calendarError) {\n        console.error('Erro ao criar evento no calendário:', calendarError);\n        // Não falha a operação principal se o calendário falhar\n      }\n    }\n\n    return { success: true, data: response.data };\n  } catch (error) {\n    console.error('Erro ao atualizar estágio do negócio:', error);\n    throw new Error('Falha ao atualizar estágio do negócio');\n  }\n}\n"], "names": [], "mappings": ";;;;;;IA4GsB,sBAAA,WAAA,GAAA,CAAA,GAAA,sNAAA,CAAA,wBAAA,EAAA,8CAAA,sNAAA,CAAA,aAAA,EAAA,KAAA,GAAA,sNAAA,CAAA,mBAAA,EAAA", "debugId": null}}, {"offset": {"line": 33, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/AI_Projects/CRMcriadores/crm-interno/lib/utils.ts"], "sourcesContent": ["/**\n * Transforma dados de array de arrays (formato do Google Sheets) \n * em array de objetos JSON usando a primeira linha como cabeçalhos\n */\nexport function transformData(data: any[][]): Record<string, any>[] {\n  if (!data || data.length === 0) {\n    return [];\n  }\n\n  // A primeira linha contém os cabeçalhos\n  const headers = data[0];\n  \n  // As linhas restantes contêm os dados\n  const rows = data.slice(1);\n\n  return rows.map((row) => {\n    const obj: Record<string, any> = {};\n    \n    headers.forEach((header, index) => {\n      // Usa o cabeçalho como chave e o valor da linha correspondente\n      obj[header] = row[index] || '';\n    });\n\n    return obj;\n  });\n}\n\n/**\n * Converte um objeto em array de valores na ordem dos cabeçalhos fornecidos\n */\nexport function objectToRowData(obj: Record<string, any>, headers: string[]): any[] {\n  return headers.map(header => obj[header] || '');\n}\n\n/**\n * Valida se os dados têm a estrutura esperada\n */\nexport function validateSheetData(data: any[][]): boolean {\n  return Array.isArray(data) && data.length > 0 && Array.isArray(data[0]);\n}\n\n/**\n * Limpa e normaliza strings vindas do Google Sheets\n */\nexport function cleanSheetValue(value: any): string {\n  if (value === null || value === undefined) {\n    return '';\n  }\n  \n  return String(value).trim();\n}\n\n/**\n * Converte valores de string para tipos apropriados\n */\nexport function parseSheetValue(value: string, type: 'string' | 'number' | 'boolean' | 'date' = 'string'): any {\n  const cleanValue = cleanSheetValue(value);\n  \n  if (cleanValue === '') {\n    return type === 'number' ? 0 : type === 'boolean' ? false : '';\n  }\n\n  switch (type) {\n    case 'number':\n      const num = parseFloat(cleanValue);\n      return isNaN(num) ? 0 : num;\n    \n    case 'boolean':\n      return cleanValue.toLowerCase() === 'true' || cleanValue === '1';\n    \n    case 'date':\n      const date = new Date(cleanValue);\n      return isNaN(date.getTime()) ? null : date;\n    \n    default:\n      return cleanValue;\n  }\n}\n\n/**\n * Formata dados para exibição\n */\nexport function formatDisplayValue(value: any, type: 'currency' | 'percentage' | 'date' | 'number' | 'text' = 'text'): string {\n  if (value === null || value === undefined || value === '') {\n    return '-';\n  }\n\n  switch (type) {\n    case 'currency':\n      const numValue = typeof value === 'number' ? value : parseFloat(value);\n      return isNaN(numValue) ? '-' : new Intl.NumberFormat('pt-BR', {\n        style: 'currency',\n        currency: 'BRL'\n      }).format(numValue);\n    \n    case 'percentage':\n      const pctValue = typeof value === 'number' ? value : parseFloat(value);\n      return isNaN(pctValue) ? '-' : `${pctValue.toFixed(1)}%`;\n    \n    case 'date':\n      const date = value instanceof Date ? value : new Date(value);\n      return isNaN(date.getTime()) ? '-' : date.toLocaleDateString('pt-BR');\n    \n    case 'number':\n      const numberValue = typeof value === 'number' ? value : parseFloat(value);\n      return isNaN(numberValue) ? '-' : new Intl.NumberFormat('pt-BR').format(numberValue);\n    \n    default:\n      return String(value);\n  }\n}\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;;;;AACM,SAAS,cAAc,IAAa;IACzC,IAAI,CAAC,QAAQ,KAAK,MAAM,KAAK,GAAG;QAC9B,OAAO,EAAE;IACX;IAEA,wCAAwC;IACxC,MAAM,UAAU,IAAI,CAAC,EAAE;IAEvB,sCAAsC;IACtC,MAAM,OAAO,KAAK,KAAK,CAAC;IAExB,OAAO,KAAK,GAAG,CAAC,CAAC;QACf,MAAM,MAA2B,CAAC;QAElC,QAAQ,OAAO,CAAC,CAAC,QAAQ;YACvB,+DAA+D;YAC/D,GAAG,CAAC,OAAO,GAAG,GAAG,CAAC,MAAM,IAAI;QAC9B;QAEA,OAAO;IACT;AACF;AAKO,SAAS,gBAAgB,GAAwB,EAAE,OAAiB;IACzE,OAAO,QAAQ,GAAG,CAAC,CAAA,SAAU,GAAG,CAAC,OAAO,IAAI;AAC9C;AAKO,SAAS,kBAAkB,IAAa;IAC7C,OAAO,MAAM,OAAO,CAAC,SAAS,KAAK,MAAM,GAAG,KAAK,MAAM,OAAO,CAAC,IAAI,CAAC,EAAE;AACxE;AAKO,SAAS,gBAAgB,KAAU;IACxC,IAAI,UAAU,QAAQ,UAAU,WAAW;QACzC,OAAO;IACT;IAEA,OAAO,OAAO,OAAO,IAAI;AAC3B;AAKO,SAAS,gBAAgB,KAAa,EAAE,OAAiD,QAAQ;IACtG,MAAM,aAAa,gBAAgB;IAEnC,IAAI,eAAe,IAAI;QACrB,OAAO,SAAS,WAAW,IAAI,SAAS,YAAY,QAAQ;IAC9D;IAEA,OAAQ;QACN,KAAK;YACH,MAAM,MAAM,WAAW;YACvB,OAAO,MAAM,OAAO,IAAI;QAE1B,KAAK;YACH,OAAO,WAAW,WAAW,OAAO,UAAU,eAAe;QAE/D,KAAK;YACH,MAAM,OAAO,IAAI,KAAK;YACtB,OAAO,MAAM,KAAK,OAAO,MAAM,OAAO;QAExC;YACE,OAAO;IACX;AACF;AAKO,SAAS,mBAAmB,KAAU,EAAE,OAA+D,MAAM;IAClH,IAAI,UAAU,QAAQ,UAAU,aAAa,UAAU,IAAI;QACzD,OAAO;IACT;IAEA,OAAQ;QACN,KAAK;YACH,MAAM,WAAW,OAAO,UAAU,WAAW,QAAQ,WAAW;YAChE,OAAO,MAAM,YAAY,MAAM,IAAI,KAAK,YAAY,CAAC,SAAS;gBAC5D,OAAO;gBACP,UAAU;YACZ,GAAG,MAAM,CAAC;QAEZ,KAAK;YACH,MAAM,WAAW,OAAO,UAAU,WAAW,QAAQ,WAAW;YAChE,OAAO,MAAM,YAAY,MAAM,GAAG,SAAS,OAAO,CAAC,GAAG,CAAC,CAAC;QAE1D,KAAK;YACH,MAAM,OAAO,iBAAiB,OAAO,QAAQ,IAAI,KAAK;YACtD,OAAO,MAAM,KAAK,OAAO,MAAM,MAAM,KAAK,kBAAkB,CAAC;QAE/D,KAAK;YACH,MAAM,cAAc,OAAO,UAAU,WAAW,QAAQ,WAAW;YACnE,OAAO,MAAM,eAAe,MAAM,IAAI,KAAK,YAAY,CAAC,SAAS,MAAM,CAAC;QAE1E;YACE,OAAO,OAAO;IAClB;AACF", "debugId": null}}, {"offset": {"line": 121, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/AI_Projects/CRMcriadores/crm-interno/components/BusinessDetailModal.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport Image from 'next/image';\n\ninterface Creator {\n  name: string;\n  username: string;\n  followers: number;\n  engagementRate: number;\n}\n\ninterface Campaign {\n  title: string;\n  status: string;\n  startDate: string;\n  endDate: string;\n}\n\ninterface Business {\n  id: number;\n  businessName: string;\n  journeyStage: string;\n  nextAction: string;\n  contactDate: string;\n  value: number;\n  description: string;\n  creators: Creator[];\n  campaigns: Campaign[];\n}\n\ninterface BusinessDetailModalProps {\n  business: Business | null;\n  isOpen: boolean;\n  onClose: () => void;\n}\n\nexport default function BusinessDetailModal({ business, isOpen, onClose }: BusinessDetailModalProps) {\n  if (!isOpen || !business) return null;\n\n  const formatFollowers = (count: number): string => {\n    if (count >= 1000000) {\n      return `${(count / 1000000).toFixed(1)}M`;\n    } else if (count >= 1000) {\n      return `${(count / 1000).toFixed(1)}K`;\n    }\n    return count.toString();\n  };\n\n  const getStatusColor = (status: string): string => {\n    const statusColors: Record<string, string> = {\n      'Ativa': 'bg-green-100 text-green-800',\n      'Planejamento': 'bg-blue-100 text-blue-800',\n      'Em Aprovação': 'bg-yellow-100 text-yellow-800',\n      'Pausada': 'bg-orange-100 text-orange-800',\n      'Finalizada': 'bg-gray-100 text-gray-800',\n      'Cancelada': 'bg-red-100 text-red-800',\n    };\n    return statusColors[status] || 'bg-gray-100 text-gray-800';\n  };\n\n  return (\n    <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\">\n      <div className=\"bg-surface rounded-2xl max-w-4xl w-full max-h-[90vh] overflow-y-auto shadow-2xl\">\n        {/* Header */}\n        <div className=\"sticky top-0 bg-surface border-b border-outline-variant p-6 flex items-center justify-between\">\n          <div>\n            <h2 className=\"text-2xl font-bold text-on-surface\">{business.businessName}</h2>\n            <p className=\"text-on-surface-variant mt-1\">Detalhes do Projeto</p>\n          </div>\n          <button\n            onClick={onClose}\n            className=\"p-2 hover:bg-surface-container rounded-full transition-colors\"\n          >\n            <span className=\"text-2xl\">✕</span>\n          </button>\n        </div>\n\n        {/* Content */}\n        <div className=\"p-6 space-y-6\">\n          {/* Informações Gerais */}\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n            <div className=\"card-elevated p-4\">\n              <h3 className=\"font-semibold text-on-surface mb-3\">📊 Informações do Projeto</h3>\n              <div className=\"space-y-3\">\n                <div>\n                  <span className=\"text-sm text-on-surface-variant\">Fase Atual:</span>\n                  <p className=\"font-medium text-on-surface\">{business.journeyStage}</p>\n                </div>\n                <div>\n                  <span className=\"text-sm text-on-surface-variant\">Valor do Projeto:</span>\n                  <p className=\"font-medium text-primary text-lg\">\n                    R$ {business.value.toLocaleString('pt-BR')}\n                  </p>\n                </div>\n                <div>\n                  <span className=\"text-sm text-on-surface-variant\">Data de Contato:</span>\n                  <p className=\"font-medium text-on-surface\">\n                    {new Date(business.contactDate).toLocaleDateString('pt-BR')}\n                  </p>\n                </div>\n              </div>\n            </div>\n\n            <div className=\"card-elevated p-4\">\n              <h3 className=\"font-semibold text-on-surface mb-3\">🎯 Próxima Ação</h3>\n              <p className=\"text-on-surface\">{business.nextAction}</p>\n            </div>\n          </div>\n\n          {/* Descrição */}\n          <div className=\"card-elevated p-4\">\n            <h3 className=\"font-semibold text-on-surface mb-3\">📝 Descrição do Projeto</h3>\n            <p className=\"text-on-surface leading-relaxed\">{business.description}</p>\n          </div>\n\n          {/* Influenciadores */}\n          <div className=\"card-elevated p-4\">\n            <h3 className=\"font-semibold text-on-surface mb-4\">\n              👥 Influenciadores Contratados ({business.creators.length})\n            </h3>\n            \n            {business.creators.length > 0 ? (\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                {business.creators.map((creator, index) => (\n                  <div key={index} className=\"bg-surface-container rounded-lg p-4\">\n                    <div className=\"flex items-center space-x-3\">\n                      <div className=\"relative w-12 h-12\">\n                        <Image\n                          src=\"/placeholder-avatar.svg\"\n                          alt={`Avatar de ${creator.name}`}\n                          fill\n                          className=\"rounded-full object-cover\"\n                          sizes=\"48px\"\n                        />\n                      </div>\n                      <div className=\"flex-1\">\n                        <h4 className=\"font-medium text-on-surface\">{creator.name}</h4>\n                        <p className=\"text-sm text-on-surface-variant\">@{creator.username}</p>\n                      </div>\n                    </div>\n                    \n                    <div className=\"grid grid-cols-2 gap-4 mt-3\">\n                      <div className=\"text-center\">\n                        <div className=\"text-lg font-bold text-primary\">\n                          {formatFollowers(creator.followers)}\n                        </div>\n                        <div className=\"text-xs text-on-surface-variant\">Seguidores</div>\n                      </div>\n                      <div className=\"text-center\">\n                        <div className=\"text-lg font-bold text-secondary\">\n                          {creator.engagementRate.toFixed(1)}%\n                        </div>\n                        <div className=\"text-xs text-on-surface-variant\">Engajamento</div>\n                      </div>\n                    </div>\n                  </div>\n                ))}\n              </div>\n            ) : (\n              <div className=\"text-center py-8 text-on-surface-variant\">\n                <div className=\"text-4xl mb-2\">👥</div>\n                <p>Nenhum influenciador contratado ainda</p>\n              </div>\n            )}\n          </div>\n\n          {/* Campanhas */}\n          <div className=\"card-elevated p-4\">\n            <h3 className=\"font-semibold text-on-surface mb-4\">\n              📢 Campanhas Relacionadas ({business.campaigns.length})\n            </h3>\n            \n            {business.campaigns.length > 0 ? (\n              <div className=\"space-y-3\">\n                {business.campaigns.map((campaign, index) => (\n                  <div key={index} className=\"bg-surface-container rounded-lg p-4\">\n                    <div className=\"flex items-center justify-between mb-2\">\n                      <h4 className=\"font-medium text-on-surface\">{campaign.title}</h4>\n                      <span className={`px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(campaign.status)}`}>\n                        {campaign.status}\n                      </span>\n                    </div>\n                    <div className=\"grid grid-cols-2 gap-4 text-sm\">\n                      <div>\n                        <span className=\"text-on-surface-variant\">Início:</span>\n                        <span className=\"ml-2 text-on-surface\">\n                          {new Date(campaign.startDate).toLocaleDateString('pt-BR')}\n                        </span>\n                      </div>\n                      <div>\n                        <span className=\"text-on-surface-variant\">Fim:</span>\n                        <span className=\"ml-2 text-on-surface\">\n                          {new Date(campaign.endDate).toLocaleDateString('pt-BR')}\n                        </span>\n                      </div>\n                    </div>\n                  </div>\n                ))}\n              </div>\n            ) : (\n              <div className=\"text-center py-8 text-on-surface-variant\">\n                <div className=\"text-4xl mb-2\">📢</div>\n                <p>Nenhuma campanha criada ainda</p>\n              </div>\n            )}\n          </div>\n        </div>\n\n        {/* Footer */}\n        <div className=\"sticky bottom-0 bg-surface border-t border-outline-variant p-6 flex justify-end space-x-3\">\n          <button onClick={onClose} className=\"btn-outlined\">\n            Fechar\n          </button>\n          <button className=\"btn-primary\">\n            <span className=\"mr-2\">✏️</span>\n            Editar Projeto\n          </button>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAGA;AAHA;;;AAqCe,SAAS,oBAAoB,EAAE,QAAQ,EAAE,MAAM,EAAE,OAAO,EAA4B;IACjG,IAAI,CAAC,UAAU,CAAC,UAAU,OAAO;IAEjC,MAAM,kBAAkB,CAAC;QACvB,IAAI,SAAS,SAAS;YACpB,OAAO,GAAG,CAAC,QAAQ,OAAO,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC;QAC3C,OAAO,IAAI,SAAS,MAAM;YACxB,OAAO,GAAG,CAAC,QAAQ,IAAI,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC;QACxC;QACA,OAAO,MAAM,QAAQ;IACvB;IAEA,MAAM,iBAAiB,CAAC;QACtB,MAAM,eAAuC;YAC3C,SAAS;YACT,gBAAgB;YAChB,gBAAgB;YAChB,WAAW;YACX,cAAc;YACd,aAAa;QACf;QACA,OAAO,YAAY,CAAC,OAAO,IAAI;IACjC;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAAsC,SAAS,YAAY;;;;;;8CACzE,8OAAC;oCAAE,WAAU;8CAA+B;;;;;;;;;;;;sCAE9C,8OAAC;4BACC,SAAS;4BACT,WAAU;sCAEV,cAAA,8OAAC;gCAAK,WAAU;0CAAW;;;;;;;;;;;;;;;;;8BAK/B,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAqC;;;;;;sDACnD,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;;sEACC,8OAAC;4DAAK,WAAU;sEAAkC;;;;;;sEAClD,8OAAC;4DAAE,WAAU;sEAA+B,SAAS,YAAY;;;;;;;;;;;;8DAEnE,8OAAC;;sEACC,8OAAC;4DAAK,WAAU;sEAAkC;;;;;;sEAClD,8OAAC;4DAAE,WAAU;;gEAAmC;gEAC1C,SAAS,KAAK,CAAC,cAAc,CAAC;;;;;;;;;;;;;8DAGtC,8OAAC;;sEACC,8OAAC;4DAAK,WAAU;sEAAkC;;;;;;sEAClD,8OAAC;4DAAE,WAAU;sEACV,IAAI,KAAK,SAAS,WAAW,EAAE,kBAAkB,CAAC;;;;;;;;;;;;;;;;;;;;;;;;8CAM3D,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAqC;;;;;;sDACnD,8OAAC;4CAAE,WAAU;sDAAmB,SAAS,UAAU;;;;;;;;;;;;;;;;;;sCAKvD,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAqC;;;;;;8CACnD,8OAAC;oCAAE,WAAU;8CAAmC,SAAS,WAAW;;;;;;;;;;;;sCAItE,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;;wCAAqC;wCAChB,SAAS,QAAQ,CAAC,MAAM;wCAAC;;;;;;;gCAG3D,SAAS,QAAQ,CAAC,MAAM,GAAG,kBAC1B,8OAAC;oCAAI,WAAU;8CACZ,SAAS,QAAQ,CAAC,GAAG,CAAC,CAAC,SAAS,sBAC/B,8OAAC;4CAAgB,WAAU;;8DACzB,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC,6HAAA,CAAA,UAAK;gEACJ,KAAI;gEACJ,KAAK,CAAC,UAAU,EAAE,QAAQ,IAAI,EAAE;gEAChC,IAAI;gEACJ,WAAU;gEACV,OAAM;;;;;;;;;;;sEAGV,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAG,WAAU;8EAA+B,QAAQ,IAAI;;;;;;8EACzD,8OAAC;oEAAE,WAAU;;wEAAkC;wEAAE,QAAQ,QAAQ;;;;;;;;;;;;;;;;;;;8DAIrE,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;8EACZ,gBAAgB,QAAQ,SAAS;;;;;;8EAEpC,8OAAC;oEAAI,WAAU;8EAAkC;;;;;;;;;;;;sEAEnD,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;;wEACZ,QAAQ,cAAc,CAAC,OAAO,CAAC;wEAAG;;;;;;;8EAErC,8OAAC;oEAAI,WAAU;8EAAkC;;;;;;;;;;;;;;;;;;;2CA5B7C;;;;;;;;;yDAmCd,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDAAgB;;;;;;sDAC/B,8OAAC;sDAAE;;;;;;;;;;;;;;;;;;sCAMT,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;;wCAAqC;wCACrB,SAAS,SAAS,CAAC,MAAM;wCAAC;;;;;;;gCAGvD,SAAS,SAAS,CAAC,MAAM,GAAG,kBAC3B,8OAAC;oCAAI,WAAU;8CACZ,SAAS,SAAS,CAAC,GAAG,CAAC,CAAC,UAAU,sBACjC,8OAAC;4CAAgB,WAAU;;8DACzB,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAG,WAAU;sEAA+B,SAAS,KAAK;;;;;;sEAC3D,8OAAC;4DAAK,WAAW,CAAC,2CAA2C,EAAE,eAAe,SAAS,MAAM,GAAG;sEAC7F,SAAS,MAAM;;;;;;;;;;;;8DAGpB,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;;8EACC,8OAAC;oEAAK,WAAU;8EAA0B;;;;;;8EAC1C,8OAAC;oEAAK,WAAU;8EACb,IAAI,KAAK,SAAS,SAAS,EAAE,kBAAkB,CAAC;;;;;;;;;;;;sEAGrD,8OAAC;;8EACC,8OAAC;oEAAK,WAAU;8EAA0B;;;;;;8EAC1C,8OAAC;oEAAK,WAAU;8EACb,IAAI,KAAK,SAAS,OAAO,EAAE,kBAAkB,CAAC;;;;;;;;;;;;;;;;;;;2CAjB7C;;;;;;;;;yDAyBd,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDAAgB;;;;;;sDAC/B,8OAAC;sDAAE;;;;;;;;;;;;;;;;;;;;;;;;8BAOX,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAO,SAAS;4BAAS,WAAU;sCAAe;;;;;;sCAGnD,8OAAC;4BAAO,WAAU;;8CAChB,8OAAC;oCAAK,WAAU;8CAAO;;;;;;gCAAS;;;;;;;;;;;;;;;;;;;;;;;;AAO5C", "debugId": null}}, {"offset": {"line": 746, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/AI_Projects/CRMcriadores/crm-interno/components/DraggableBusinessCard.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { useSortable } from '@dnd-kit/sortable';\nimport { CSS } from '@dnd-kit/utilities';\n\ninterface Business {\n  id: number;\n  businessName: string;\n  journeyStage: string;\n  nextAction: string;\n  contactDate: string;\n  value: number;\n  description: string;\n  creators: any[];\n  campaigns: any[];\n}\n\ninterface DraggableBusinessCardProps {\n  business: Business;\n  onClick: () => void;\n  isDragging?: boolean;\n}\n\nexport default function DraggableBusinessCard({ \n  business, \n  onClick, \n  isDragging = false \n}: DraggableBusinessCardProps) {\n  const {\n    attributes,\n    listeners,\n    setNodeRef,\n    transform,\n    transition,\n    isDragging: isSortableDragging,\n  } = useSortable({\n    id: business.id.toString(),\n  });\n\n  const style = {\n    transform: CSS.Transform.toString(transform),\n    transition,\n    opacity: isSortableDragging ? 0.5 : 1,\n  };\n\n  return (\n    <div\n      ref={setNodeRef}\n      style={style}\n      {...attributes}\n      {...listeners}\n      className={`\n        bg-surface-container rounded-lg p-4 transition-all duration-200 cursor-grab active:cursor-grabbing\n        border-l-4 border-primary hover:shadow-md\n        ${isSortableDragging ? 'shadow-lg scale-105 rotate-2' : ''}\n        ${isDragging ? 'opacity-50' : ''}\n      `}\n      onClick={(e) => {\n        // Só chama onClick se não estiver arrastando\n        if (!isSortableDragging) {\n          onClick();\n        }\n      }}\n    >\n      {/* Header com nome do negócio */}\n      <div className=\"flex items-start justify-between mb-3\">\n        <h4 className=\"font-semibold text-on-surface flex-1 mr-2\">\n          {business.businessName}\n        </h4>\n        \n        {/* Indicador de drag */}\n        <div className=\"text-on-surface-variant opacity-50\">\n          <svg width=\"16\" height=\"16\" viewBox=\"0 0 16 16\" fill=\"currentColor\">\n            <circle cx=\"3\" cy=\"3\" r=\"1\"/>\n            <circle cx=\"8\" cy=\"3\" r=\"1\"/>\n            <circle cx=\"13\" cy=\"3\" r=\"1\"/>\n            <circle cx=\"3\" cy=\"8\" r=\"1\"/>\n            <circle cx=\"8\" cy=\"8\" r=\"1\"/>\n            <circle cx=\"13\" cy=\"8\" r=\"1\"/>\n            <circle cx=\"3\" cy=\"13\" r=\"1\"/>\n            <circle cx=\"8\" cy=\"13\" r=\"1\"/>\n            <circle cx=\"13\" cy=\"13\" r=\"1\"/>\n          </svg>\n        </div>\n      </div>\n      \n      {/* Próxima ação */}\n      <p className=\"text-sm text-on-surface-variant mb-3 line-clamp-2\">\n        {business.nextAction}\n      </p>\n      \n      {/* Influenciadores */}\n      <div className=\"flex items-center mb-3\">\n        <span className=\"text-xs text-on-surface-variant mr-2\">👥</span>\n        <span className=\"text-sm font-medium text-secondary\">\n          {business.creators.length} influenciador{business.creators.length !== 1 ? 'es' : ''}\n        </span>\n      </div>\n      \n      {/* Footer com data e valor */}\n      <div className=\"flex items-center justify-between text-sm\">\n        <span className=\"text-on-surface-variant\">\n          {new Date(business.contactDate).toLocaleDateString('pt-BR')}\n        </span>\n        <span className=\"font-bold text-primary\">\n          R$ {(business.value / 1000).toFixed(0)}K\n        </span>\n      </div>\n      \n      {/* Indicador de clique para detalhes */}\n      <div className=\"mt-2 text-xs text-on-surface-variant opacity-70\">\n        Arraste para mover • Clique para detalhes\n      </div>\n    </div>\n  );\n}\n\n// Componente para o overlay durante o drag\nexport function BusinessCardOverlay({ business }: { business: Business }) {\n  return (\n    <div className=\"bg-surface-container rounded-lg p-4 shadow-2xl border-l-4 border-primary rotate-3 scale-105\">\n      <h4 className=\"font-semibold text-on-surface mb-2\">\n        {business.businessName}\n      </h4>\n      <p className=\"text-sm text-on-surface-variant mb-3 line-clamp-2\">\n        {business.nextAction}\n      </p>\n      <div className=\"flex items-center justify-between text-sm\">\n        <span className=\"text-on-surface-variant\">\n          👥 {business.creators.length} influenciador{business.creators.length !== 1 ? 'es' : ''}\n        </span>\n        <span className=\"font-bold text-primary\">\n          R$ {(business.value / 1000).toFixed(0)}K\n        </span>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAGA;AACA;AAJA;;;;AAwBe,SAAS,sBAAsB,EAC5C,QAAQ,EACR,OAAO,EACP,aAAa,KAAK,EACS;IAC3B,MAAM,EACJ,UAAU,EACV,SAAS,EACT,UAAU,EACV,SAAS,EACT,UAAU,EACV,YAAY,kBAAkB,EAC/B,GAAG,CAAA,GAAA,mKAAA,CAAA,cAAW,AAAD,EAAE;QACd,IAAI,SAAS,EAAE,CAAC,QAAQ;IAC1B;IAEA,MAAM,QAAQ;QACZ,WAAW,qKAAA,CAAA,MAAG,CAAC,SAAS,CAAC,QAAQ,CAAC;QAClC;QACA,SAAS,qBAAqB,MAAM;IACtC;IAEA,qBACE,8OAAC;QACC,KAAK;QACL,OAAO;QACN,GAAG,UAAU;QACb,GAAG,SAAS;QACb,WAAW,CAAC;;;QAGV,EAAE,qBAAqB,iCAAiC,GAAG;QAC3D,EAAE,aAAa,eAAe,GAAG;MACnC,CAAC;QACD,SAAS,CAAC;YACR,6CAA6C;YAC7C,IAAI,CAAC,oBAAoB;gBACvB;YACF;QACF;;0BAGA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCACX,SAAS,YAAY;;;;;;kCAIxB,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,OAAM;4BAAK,QAAO;4BAAK,SAAQ;4BAAY,MAAK;;8CACnD,8OAAC;oCAAO,IAAG;oCAAI,IAAG;oCAAI,GAAE;;;;;;8CACxB,8OAAC;oCAAO,IAAG;oCAAI,IAAG;oCAAI,GAAE;;;;;;8CACxB,8OAAC;oCAAO,IAAG;oCAAK,IAAG;oCAAI,GAAE;;;;;;8CACzB,8OAAC;oCAAO,IAAG;oCAAI,IAAG;oCAAI,GAAE;;;;;;8CACxB,8OAAC;oCAAO,IAAG;oCAAI,IAAG;oCAAI,GAAE;;;;;;8CACxB,8OAAC;oCAAO,IAAG;oCAAK,IAAG;oCAAI,GAAE;;;;;;8CACzB,8OAAC;oCAAO,IAAG;oCAAI,IAAG;oCAAK,GAAE;;;;;;8CACzB,8OAAC;oCAAO,IAAG;oCAAI,IAAG;oCAAK,GAAE;;;;;;8CACzB,8OAAC;oCAAO,IAAG;oCAAK,IAAG;oCAAK,GAAE;;;;;;;;;;;;;;;;;;;;;;;0BAMhC,8OAAC;gBAAE,WAAU;0BACV,SAAS,UAAU;;;;;;0BAItB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAK,WAAU;kCAAuC;;;;;;kCACvD,8OAAC;wBAAK,WAAU;;4BACb,SAAS,QAAQ,CAAC,MAAM;4BAAC;4BAAe,SAAS,QAAQ,CAAC,MAAM,KAAK,IAAI,OAAO;;;;;;;;;;;;;0BAKrF,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAK,WAAU;kCACb,IAAI,KAAK,SAAS,WAAW,EAAE,kBAAkB,CAAC;;;;;;kCAErD,8OAAC;wBAAK,WAAU;;4BAAyB;4BACnC,CAAC,SAAS,KAAK,GAAG,IAAI,EAAE,OAAO,CAAC;4BAAG;;;;;;;;;;;;;0BAK3C,8OAAC;gBAAI,WAAU;0BAAkD;;;;;;;;;;;;AAKvE;AAGO,SAAS,oBAAoB,EAAE,QAAQ,EAA0B;IACtE,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAG,WAAU;0BACX,SAAS,YAAY;;;;;;0BAExB,8OAAC;gBAAE,WAAU;0BACV,SAAS,UAAU;;;;;;0BAEtB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAK,WAAU;;4BAA0B;4BACpC,SAAS,QAAQ,CAAC,MAAM;4BAAC;4BAAe,SAAS,QAAQ,CAAC,MAAM,KAAK,IAAI,OAAO;;;;;;;kCAEtF,8OAAC;wBAAK,WAAU;;4BAAyB;4BACnC,CAAC,SAAS,KAAK,GAAG,IAAI,EAAE,OAAO,CAAC;4BAAG;;;;;;;;;;;;;;;;;;;AAKjD", "debugId": null}}, {"offset": {"line": 1049, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/AI_Projects/CRMcriadores/crm-interno/components/DroppableColumn.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { useDroppable } from '@dnd-kit/core';\nimport { SortableContext, verticalListSortingStrategy } from '@dnd-kit/sortable';\nimport DraggableBusinessCard from './DraggableBusinessCard';\n\ninterface Business {\n  id: number;\n  businessName: string;\n  journeyStage: string;\n  nextAction: string;\n  contactDate: string;\n  value: number;\n  description: string;\n  creators: any[];\n  campaigns: any[];\n}\n\ninterface DroppableColumnProps {\n  id: string;\n  title: string;\n  icon: string;\n  businesses: Business[];\n  totalValue: number;\n  onBusinessClick: (business: Business) => void;\n  isUpdating?: boolean;\n}\n\nexport default function DroppableColumn({\n  id,\n  title,\n  icon,\n  businesses,\n  totalValue,\n  onBusinessClick,\n  isUpdating = false\n}: DroppableColumnProps) {\n  const { setNodeRef, isOver } = useDroppable({\n    id,\n  });\n\n  const businessIds = businesses.map(b => b.id.toString());\n\n  return (\n    <div\n      ref={setNodeRef}\n      className={`\n        card-elevated p-6 min-h-96 transition-all duration-200\n        ${isOver ? 'bg-primary-container border-2 border-primary border-dashed' : ''}\n        ${isUpdating ? 'opacity-75' : ''}\n      `}\n    >\n      {/* Header da coluna */}\n      <div className=\"mb-6\">\n        <div className=\"flex items-center justify-between mb-3\">\n          <div className=\"flex items-center\">\n            <span className=\"text-2xl mr-3\">{icon}</span>\n            <h3 className=\"text-lg font-semibold text-on-surface\">{title}</h3>\n          </div>\n          <span className=\"text-sm bg-surface-container px-3 py-1 rounded-full font-medium\">\n            {businesses.length}\n          </span>\n        </div>\n        \n        <div className=\"text-sm text-on-surface-variant\">\n          Total: R$ {(totalValue / 1000).toFixed(0)}K\n        </div>\n        \n        {/* Indicador de drop zone */}\n        {isOver && (\n          <div className=\"mt-3 text-sm text-primary font-medium animate-pulse\">\n            ↓ Solte aqui para mover\n          </div>\n        )}\n      </div>\n\n      {/* Lista de negócios com contexto sortable */}\n      <SortableContext items={businessIds} strategy={verticalListSortingStrategy}>\n        <div className=\"space-y-4\">\n          {businesses.map((business) => (\n            <DraggableBusinessCard\n              key={business.id}\n              business={business}\n              onClick={() => onBusinessClick(business)}\n            />\n          ))}\n          \n          {businesses.length === 0 && (\n            <div className=\"text-center py-12 text-on-surface-variant\">\n              <div className=\"text-4xl mb-3\">{icon}</div>\n              <p className=\"text-sm\">Nenhum negócio nesta fase</p>\n              <p className=\"text-xs mt-1 opacity-70\">\n                {isOver ? 'Solte aqui para adicionar' : 'Arraste projetos para cá'}\n              </p>\n            </div>\n          )}\n        </div>\n      </SortableContext>\n      \n      {/* Loading indicator */}\n      {isUpdating && (\n        <div className=\"absolute inset-0 bg-surface bg-opacity-50 flex items-center justify-center rounded-lg\">\n          <div className=\"flex items-center space-x-2 text-primary\">\n            <div className=\"animate-spin rounded-full h-4 w-4 border-2 border-primary border-t-transparent\"></div>\n            <span className=\"text-sm font-medium\">Atualizando...</span>\n          </div>\n        </div>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AALA;;;;;AA6Be,SAAS,gBAAgB,EACtC,EAAE,EACF,KAAK,EACL,IAAI,EACJ,UAAU,EACV,UAAU,EACV,eAAe,EACf,aAAa,KAAK,EACG;IACrB,MAAM,EAAE,UAAU,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,2JAAA,CAAA,eAAY,AAAD,EAAE;QAC1C;IACF;IAEA,MAAM,cAAc,WAAW,GAAG,CAAC,CAAA,IAAK,EAAE,EAAE,CAAC,QAAQ;IAErD,qBACE,8OAAC;QACC,KAAK;QACL,WAAW,CAAC;;QAEV,EAAE,SAAS,+DAA+D,GAAG;QAC7E,EAAE,aAAa,eAAe,GAAG;MACnC,CAAC;;0BAGD,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAK,WAAU;kDAAiB;;;;;;kDACjC,8OAAC;wCAAG,WAAU;kDAAyC;;;;;;;;;;;;0CAEzD,8OAAC;gCAAK,WAAU;0CACb,WAAW,MAAM;;;;;;;;;;;;kCAItB,8OAAC;wBAAI,WAAU;;4BAAkC;4BACpC,CAAC,aAAa,IAAI,EAAE,OAAO,CAAC;4BAAG;;;;;;;oBAI3C,wBACC,8OAAC;wBAAI,WAAU;kCAAsD;;;;;;;;;;;;0BAOzE,8OAAC,mKAAA,CAAA,kBAAe;gBAAC,OAAO;gBAAa,UAAU,mKAAA,CAAA,8BAA2B;0BACxE,cAAA,8OAAC;oBAAI,WAAU;;wBACZ,WAAW,GAAG,CAAC,CAAC,yBACf,8OAAC,oIAAA,CAAA,UAAqB;gCAEpB,UAAU;gCACV,SAAS,IAAM,gBAAgB;+BAF1B,SAAS,EAAE;;;;;wBAMnB,WAAW,MAAM,KAAK,mBACrB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CAAiB;;;;;;8CAChC,8OAAC;oCAAE,WAAU;8CAAU;;;;;;8CACvB,8OAAC;oCAAE,WAAU;8CACV,SAAS,8BAA8B;;;;;;;;;;;;;;;;;;;;;;;YAQjD,4BACC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;;;;;sCACf,8OAAC;4BAAK,WAAU;sCAAsB;;;;;;;;;;;;;;;;;;;;;;;AAMlD", "debugId": null}}, {"offset": {"line": 1247, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/AI_Projects/CRMcriadores/crm-interno/components/Toast.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect } from 'react';\n\ninterface ToastProps {\n  message: string;\n  type: 'success' | 'error' | 'info';\n  isVisible: boolean;\n  onClose: () => void;\n  duration?: number;\n}\n\nexport default function Toast({ \n  message, \n  type, \n  isVisible, \n  onClose, \n  duration = 3000 \n}: ToastProps) {\n  useEffect(() => {\n    if (isVisible) {\n      const timer = setTimeout(() => {\n        onClose();\n      }, duration);\n\n      return () => clearTimeout(timer);\n    }\n  }, [isVisible, duration, onClose]);\n\n  if (!isVisible) return null;\n\n  const getToastStyles = () => {\n    switch (type) {\n      case 'success':\n        return 'bg-green-100 text-green-800 border-green-200';\n      case 'error':\n        return 'bg-red-100 text-red-800 border-red-200';\n      case 'info':\n        return 'bg-blue-100 text-blue-800 border-blue-200';\n      default:\n        return 'bg-surface-container text-on-surface border-outline-variant';\n    }\n  };\n\n  const getIcon = () => {\n    switch (type) {\n      case 'success':\n        return '✅';\n      case 'error':\n        return '❌';\n      case 'info':\n        return 'ℹ️';\n      default:\n        return '📢';\n    }\n  };\n\n  return (\n    <div className=\"fixed top-4 right-4 z-50 animate-in slide-in-from-right duration-300\">\n      <div className={`\n        flex items-center space-x-3 px-4 py-3 rounded-lg border shadow-lg max-w-sm\n        ${getToastStyles()}\n      `}>\n        <span className=\"text-lg\">{getIcon()}</span>\n        <span className=\"flex-1 text-sm font-medium\">{message}</span>\n        <button\n          onClick={onClose}\n          className=\"text-lg hover:opacity-70 transition-opacity\"\n        >\n          ×\n        </button>\n      </div>\n    </div>\n  );\n}\n\n// Hook para usar toast\nexport function useToast() {\n  const [toast, setToast] = useState<{\n    message: string;\n    type: 'success' | 'error' | 'info';\n    isVisible: boolean;\n  }>({\n    message: '',\n    type: 'info',\n    isVisible: false,\n  });\n\n  const showToast = (message: string, type: 'success' | 'error' | 'info' = 'info') => {\n    setToast({\n      message,\n      type,\n      isVisible: true,\n    });\n  };\n\n  const hideToast = () => {\n    setToast(prev => ({ ...prev, isVisible: false }));\n  };\n\n  const ToastComponent = () => (\n    <Toast\n      message={toast.message}\n      type={toast.type}\n      isVisible={toast.isVisible}\n      onClose={hideToast}\n    />\n  );\n\n  return {\n    showToast,\n    hideToast,\n    ToastComponent,\n  };\n}\n"], "names": [], "mappings": ";;;;;AAEA;AAFA;;;AAYe,SAAS,MAAM,EAC5B,OAAO,EACP,IAAI,EACJ,SAAS,EACT,OAAO,EACP,WAAW,IAAI,EACJ;IACX,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,WAAW;YACb,MAAM,QAAQ,WAAW;gBACvB;YACF,GAAG;YAEH,OAAO,IAAM,aAAa;QAC5B;IACF,GAAG;QAAC;QAAW;QAAU;KAAQ;IAEjC,IAAI,CAAC,WAAW,OAAO;IAEvB,MAAM,iBAAiB;QACrB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,UAAU;QACd,OAAQ;YACN,KAAK;g<PERSON><PERSON>,OAAO;YAC<PERSON>,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAW,CAAC;;QAEf,EAAE,iBAAiB;MACrB,CAAC;;8BACC,8OAAC;oBAAK,WAAU;8BAAW;;;;;;8BAC3B,8OAAC;oBAAK,WAAU;8BAA8B;;;;;;8BAC9C,8OAAC;oBACC,SAAS;oBACT,WAAU;8BACX;;;;;;;;;;;;;;;;;AAMT;AAGO,SAAS;IACd,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAI9B;QACD,SAAS;QACT,MAAM;QACN,WAAW;IACb;IAEA,MAAM,YAAY,CAAC,SAAiB,OAAqC,MAAM;QAC7E,SAAS;YACP;YACA;YACA,WAAW;QACb;IACF;IAEA,MAAM,YAAY;QAChB,SAAS,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,WAAW;YAAM,CAAC;IACjD;IAEA,MAAM,iBAAiB,kBACrB,8OAAC;YACC,SAAS,MAAM,OAAO;YACtB,MAAM,MAAM,IAAI;YAChB,WAAW,MAAM,SAAS;YAC1B,SAAS;;;;;;IAIb,OAAO;QACL;QACA;QACA;IACF;AACF", "debugId": null}}, {"offset": {"line": 1380, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/AI_Projects/CRMcriadores/crm-interno/app/actions/calendarActions.ts"], "sourcesContent": ["'use server';\n\nimport { google } from 'googleapis';\nimport { JWT } from 'google-auth-library';\n\n// Configuração de autenticação do Google Calendar\nfunction getGoogleCalendarAuth() {\n  const credentials = {\n    client_email: process.env.GOOGLE_CLIENT_EMAIL,\n    private_key: process.env.GOOGLE_PRIVATE_KEY?.replace(/\\\\n/g, '\\n'),\n  };\n\n  if (!credentials.client_email || !credentials.private_key) {\n    throw new Error('Credenciais do Google Calendar não configuradas');\n  }\n\n  return new JWT({\n    email: credentials.client_email,\n    key: credentials.private_key,\n    scopes: ['https://www.googleapis.com/auth/calendar'],\n  });\n}\n\n// Interface para evento do calendário\ninterface CalendarEvent {\n  summary: string;\n  description?: string;\n  startDateTime: string;\n  endDateTime: string;\n  attendees?: string[];\n  location?: string;\n}\n\n// Função para criar evento no Google Calendar\nexport async function createCalendarEvent(event: CalendarEvent) {\n  try {\n    const auth = getGoogleCalendarAuth();\n    const calendar = google.calendar({ version: 'v3', auth });\n    \n    const calendarId = process.env.GOOGLE_CALENDAR_ID || 'primary';\n    \n    if (!calendarId || calendarId === 'primary') {\n      console.log('Simulando criação de evento no calendário:', event.summary);\n      return { \n        success: true, \n        message: 'Evento simulado criado com sucesso',\n        eventId: `simulated-${Date.now()}`\n      };\n    }\n\n    const calendarEvent = {\n      summary: event.summary,\n      description: event.description,\n      start: {\n        dateTime: event.startDateTime,\n        timeZone: 'America/Sao_Paulo',\n      },\n      end: {\n        dateTime: event.endDateTime,\n        timeZone: 'America/Sao_Paulo',\n      },\n      attendees: event.attendees?.map(email => ({ email })),\n      location: event.location,\n      reminders: {\n        useDefault: false,\n        overrides: [\n          { method: 'email', minutes: 24 * 60 }, // 1 dia antes\n          { method: 'popup', minutes: 60 }, // 1 hora antes\n        ],\n      },\n    };\n\n    const response = await calendar.events.insert({\n      calendarId,\n      requestBody: calendarEvent,\n    });\n\n    return { \n      success: true, \n      data: response.data,\n      eventId: response.data.id \n    };\n  } catch (error) {\n    console.error('Erro ao criar evento no calendário:', error);\n    throw new Error('Falha ao criar evento no calendário');\n  }\n}\n\n// Função para atualizar evento no Google Calendar\nexport async function updateCalendarEvent(eventId: string, event: Partial<CalendarEvent>) {\n  try {\n    const auth = getGoogleCalendarAuth();\n    const calendar = google.calendar({ version: 'v3', auth });\n    \n    const calendarId = process.env.GOOGLE_CALENDAR_ID || 'primary';\n    \n    if (!calendarId || calendarId === 'primary') {\n      console.log('Simulando atualização de evento no calendário:', eventId);\n      return { \n        success: true, \n        message: 'Evento simulado atualizado com sucesso' \n      };\n    }\n\n    const updateData: any = {};\n    \n    if (event.summary) updateData.summary = event.summary;\n    if (event.description) updateData.description = event.description;\n    if (event.startDateTime) {\n      updateData.start = {\n        dateTime: event.startDateTime,\n        timeZone: 'America/Sao_Paulo',\n      };\n    }\n    if (event.endDateTime) {\n      updateData.end = {\n        dateTime: event.endDateTime,\n        timeZone: 'America/Sao_Paulo',\n      };\n    }\n    if (event.attendees) {\n      updateData.attendees = event.attendees.map(email => ({ email }));\n    }\n    if (event.location) updateData.location = event.location;\n\n    const response = await calendar.events.update({\n      calendarId,\n      eventId,\n      requestBody: updateData,\n    });\n\n    return { success: true, data: response.data };\n  } catch (error) {\n    console.error('Erro ao atualizar evento no calendário:', error);\n    throw new Error('Falha ao atualizar evento no calendário');\n  }\n}\n\n// Função para deletar evento do Google Calendar\nexport async function deleteCalendarEvent(eventId: string) {\n  try {\n    const auth = getGoogleCalendarAuth();\n    const calendar = google.calendar({ version: 'v3', auth });\n    \n    const calendarId = process.env.GOOGLE_CALENDAR_ID || 'primary';\n    \n    if (!calendarId || calendarId === 'primary') {\n      console.log('Simulando exclusão de evento no calendário:', eventId);\n      return { \n        success: true, \n        message: 'Evento simulado excluído com sucesso' \n      };\n    }\n\n    await calendar.events.delete({\n      calendarId,\n      eventId,\n    });\n\n    return { success: true };\n  } catch (error) {\n    console.error('Erro ao deletar evento no calendário:', error);\n    throw new Error('Falha ao deletar evento do calendário');\n  }\n}\n\n// Função para criar agendamento automático quando negócio entra na fase \"Agendamentos\"\nexport async function createSchedulingEvent(businessData: any) {\n  try {\n    const startDate = new Date();\n    startDate.setDate(startDate.getDate() + 2); // 2 dias a partir de hoje\n    startDate.setHours(14, 0, 0, 0); // 14:00\n\n    const endDate = new Date(startDate);\n    endDate.setHours(15, 0, 0, 0); // 15:00\n\n    const event: CalendarEvent = {\n      summary: `Agendamento: ${businessData.businessName}`,\n      description: `\nAgendamento para coordenação com criadores\n        \nNegócio: ${businessData.businessName}\nValor: R$ ${(businessData.value / 1000).toFixed(0)}K\nCriadores: ${businessData.creators?.length || 0}\n        \nPróxima ação: ${businessData.nextAction}\n        \nDescrição: ${businessData.description}\n      `.trim(),\n      startDateTime: startDate.toISOString(),\n      endDateTime: endDate.toISOString(),\n      location: 'Reunião Online',\n      attendees: businessData.creators?.map((creator: any) => creator.email).filter(Boolean) || []\n    };\n\n    const result = await createCalendarEvent(event);\n    \n    console.log(`Evento criado para ${businessData.businessName}:`, result.eventId);\n    \n    return result;\n  } catch (error) {\n    console.error('Erro ao criar agendamento automático:', error);\n    throw error;\n  }\n}\n\n// Função para listar eventos do calendário\nexport async function listCalendarEvents(timeMin?: string, timeMax?: string) {\n  try {\n    const auth = getGoogleCalendarAuth();\n    const calendar = google.calendar({ version: 'v3', auth });\n    \n    const calendarId = process.env.GOOGLE_CALENDAR_ID || 'primary';\n    \n    if (!calendarId || calendarId === 'primary') {\n      console.log('Simulando listagem de eventos do calendário');\n      return { \n        success: true, \n        events: [\n          {\n            id: 'simulated-1',\n            summary: 'Agendamento: Loja de Roupas Fashion',\n            start: { dateTime: new Date().toISOString() },\n            end: { dateTime: new Date(Date.now() + 3600000).toISOString() }\n          }\n        ]\n      };\n    }\n\n    const response = await calendar.events.list({\n      calendarId,\n      timeMin: timeMin || new Date().toISOString(),\n      timeMax: timeMax,\n      maxResults: 50,\n      singleEvents: true,\n      orderBy: 'startTime',\n    });\n\n    return { \n      success: true, \n      events: response.data.items || [] \n    };\n  } catch (error) {\n    console.error('Erro ao listar eventos do calendário:', error);\n    throw new Error('Falha ao listar eventos do calendário');\n  }\n}\n"], "names": [], "mappings": ";;;;;;IA+MsB,qBAAA,WAAA,GAAA,CAAA,GAAA,sNAAA,CAAA,wBAAA,EAAA,8CAAA,sNAAA,CAAA,aAAA,EAAA,KAAA,GAAA,sNAAA,CAAA,mBAAA,EAAA", "debugId": null}}, {"offset": {"line": 1393, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/AI_Projects/CRMcriadores/crm-interno/components/CalendarWidget.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect } from 'react';\nimport { listCalendarEvents } from '@/app/actions/calendarActions';\n\ninterface CalendarEvent {\n  id: string;\n  summary: string;\n  start: { dateTime: string };\n  end: { dateTime: string };\n  description?: string;\n  location?: string;\n}\n\nexport default function CalendarWidget() {\n  const [events, setEvents] = useState<CalendarEvent[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n\n  useEffect(() => {\n    loadEvents();\n  }, []);\n\n  const loadEvents = async () => {\n    try {\n      setLoading(true);\n      const result = await listCalendarEvents();\n      if (result.success) {\n        setEvents(result.events);\n      }\n    } catch (err) {\n      setError('Erro ao carregar eventos do calendário');\n      console.error('Erro ao carregar eventos:', err);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const formatDate = (dateString: string) => {\n    const date = new Date(dateString);\n    return date.toLocaleDateString('pt-BR', {\n      day: '2-digit',\n      month: '2-digit',\n      year: 'numeric',\n    });\n  };\n\n  const formatTime = (dateString: string) => {\n    const date = new Date(dateString);\n    return date.toLocaleTimeString('pt-BR', {\n      hour: '2-digit',\n      minute: '2-digit',\n    });\n  };\n\n  const isToday = (dateString: string) => {\n    const eventDate = new Date(dateString);\n    const today = new Date();\n    return eventDate.toDateString() === today.toDateString();\n  };\n\n  const isTomorrow = (dateString: string) => {\n    const eventDate = new Date(dateString);\n    const tomorrow = new Date();\n    tomorrow.setDate(tomorrow.getDate() + 1);\n    return eventDate.toDateString() === tomorrow.toDateString();\n  };\n\n  const getDateLabel = (dateString: string) => {\n    if (isToday(dateString)) return 'Hoje';\n    if (isTomorrow(dateString)) return 'Amanhã';\n    return formatDate(dateString);\n  };\n\n  if (loading) {\n    return (\n      <div className=\"card-elevated p-6\">\n        <h3 className=\"text-lg font-semibold text-on-surface mb-4 flex items-center\">\n          <span className=\"text-xl mr-2\">📅</span>\n          Próximos Agendamentos\n        </h3>\n        <div className=\"flex items-center justify-center py-8\">\n          <div className=\"animate-spin rounded-full h-8 w-8 border-2 border-primary border-t-transparent\"></div>\n          <span className=\"ml-3 text-on-surface-variant\">Carregando eventos...</span>\n        </div>\n      </div>\n    );\n  }\n\n  if (error) {\n    return (\n      <div className=\"card-elevated p-6\">\n        <h3 className=\"text-lg font-semibold text-on-surface mb-4 flex items-center\">\n          <span className=\"text-xl mr-2\">📅</span>\n          Próximos Agendamentos\n        </h3>\n        <div className=\"text-center py-8\">\n          <div className=\"text-4xl mb-3\">⚠️</div>\n          <p className=\"text-sm text-on-surface-variant\">{error}</p>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"card-elevated p-6\">\n      <div className=\"flex items-center justify-between mb-4\">\n        <h3 className=\"text-lg font-semibold text-on-surface flex items-center\">\n          <span className=\"text-xl mr-2\">📅</span>\n          Próximos Agendamentos\n        </h3>\n        <button \n          onClick={loadEvents}\n          className=\"text-sm text-primary hover:text-primary-dark transition-colors\"\n        >\n          🔄 Atualizar\n        </button>\n      </div>\n\n      {events.length === 0 ? (\n        <div className=\"text-center py-8\">\n          <div className=\"text-4xl mb-3\">📅</div>\n          <p className=\"text-sm text-on-surface-variant mb-2\">\n            Nenhum agendamento encontrado\n          </p>\n          <p className=\"text-xs text-on-surface-variant\">\n            Mova negócios para \"Agendamentos\" para criar eventos automaticamente\n          </p>\n        </div>\n      ) : (\n        <div className=\"space-y-3\">\n          {events.slice(0, 5).map((event) => (\n            <div \n              key={event.id} \n              className=\"bg-surface-container rounded-lg p-4 hover:shadow-sm transition-shadow\"\n            >\n              <div className=\"flex items-start justify-between mb-2\">\n                <h4 className=\"font-medium text-on-surface text-sm line-clamp-1\">\n                  {event.summary}\n                </h4>\n                <span className={`text-xs px-2 py-1 rounded-full font-medium ${\n                  isToday(event.start.dateTime) \n                    ? 'bg-primary text-on-primary' \n                    : isTomorrow(event.start.dateTime)\n                    ? 'bg-secondary text-on-secondary'\n                    : 'bg-surface-container-high text-on-surface-variant'\n                }`}>\n                  {getDateLabel(event.start.dateTime)}\n                </span>\n              </div>\n\n              <div className=\"flex items-center text-xs text-on-surface-variant mb-2\">\n                <span className=\"mr-3\">\n                  🕐 {formatTime(event.start.dateTime)} - {formatTime(event.end.dateTime)}\n                </span>\n                {event.location && (\n                  <span>\n                    📍 {event.location}\n                  </span>\n                )}\n              </div>\n\n              {event.description && (\n                <p className=\"text-xs text-on-surface-variant line-clamp-2\">\n                  {event.description}\n                </p>\n              )}\n            </div>\n          ))}\n\n          {events.length > 5 && (\n            <div className=\"text-center pt-2\">\n              <span className=\"text-xs text-on-surface-variant\">\n                +{events.length - 5} mais eventos\n              </span>\n            </div>\n          )}\n        </div>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAce,SAAS;IACtB,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAmB,EAAE;IACxD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAElD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG,EAAE;IAEL,MAAM,aAAa;QACjB,IAAI;YACF,WAAW;YACX,MAAM,SAAS,MAAM,CAAA,GAAA,sJAAA,CAAA,qBAAkB,AAAD;YACtC,IAAI,OAAO,OAAO,EAAE;gBAClB,UAAU,OAAO,MAAM;YACzB;QACF,EAAE,OAAO,KAAK;YACZ,SAAS;YACT,QAAQ,KAAK,CAAC,6BAA6B;QAC7C,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,aAAa,CAAC;QAClB,MAAM,OAAO,IAAI,KAAK;QACtB,OAAO,KAAK,kBAAkB,CAAC,SAAS;YACtC,KAAK;YACL,OAAO;YACP,MAAM;QACR;IACF;IAEA,MAAM,aAAa,CAAC;QAClB,MAAM,OAAO,IAAI,KAAK;QACtB,OAAO,KAAK,kBAAkB,CAAC,SAAS;YACtC,MAAM;YACN,QAAQ;QACV;IACF;IAEA,MAAM,UAAU,CAAC;QACf,MAAM,YAAY,IAAI,KAAK;QAC3B,MAAM,QAAQ,IAAI;QAClB,OAAO,UAAU,YAAY,OAAO,MAAM,YAAY;IACxD;IAEA,MAAM,aAAa,CAAC;QAClB,MAAM,YAAY,IAAI,KAAK;QAC3B,MAAM,WAAW,IAAI;QACrB,SAAS,OAAO,CAAC,SAAS,OAAO,KAAK;QACtC,OAAO,UAAU,YAAY,OAAO,SAAS,YAAY;IAC3D;IAEA,MAAM,eAAe,CAAC;QACpB,IAAI,QAAQ,aAAa,OAAO;QAChC,IAAI,WAAW,aAAa,OAAO;QACnC,OAAO,WAAW;IACpB;IAEA,IAAI,SAAS;QACX,qBACE,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAG,WAAU;;sCACZ,8OAAC;4BAAK,WAAU;sCAAe;;;;;;wBAAS;;;;;;;8BAG1C,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;;;;;sCACf,8OAAC;4BAAK,WAAU;sCAA+B;;;;;;;;;;;;;;;;;;IAIvD;IAEA,IAAI,OAAO;QACT,qBACE,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAG,WAAU;;sCACZ,8OAAC;4BAAK,WAAU;sCAAe;;;;;;wBAAS;;;;;;;8BAG1C,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCAAgB;;;;;;sCAC/B,8OAAC;4BAAE,WAAU;sCAAmC;;;;;;;;;;;;;;;;;;IAIxD;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;;0CACZ,8OAAC;gCAAK,WAAU;0CAAe;;;;;;4BAAS;;;;;;;kCAG1C,8OAAC;wBACC,SAAS;wBACT,WAAU;kCACX;;;;;;;;;;;;YAKF,OAAO,MAAM,KAAK,kBACjB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCAAgB;;;;;;kCAC/B,8OAAC;wBAAE,WAAU;kCAAuC;;;;;;kCAGpD,8OAAC;wBAAE,WAAU;kCAAkC;;;;;;;;;;;qCAKjD,8OAAC;gBAAI,WAAU;;oBACZ,OAAO,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,sBACvB,8OAAC;4BAEC,WAAU;;8CAEV,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDACX,MAAM,OAAO;;;;;;sDAEhB,8OAAC;4CAAK,WAAW,CAAC,2CAA2C,EAC3D,QAAQ,MAAM,KAAK,CAAC,QAAQ,IACxB,+BACA,WAAW,MAAM,KAAK,CAAC,QAAQ,IAC/B,mCACA,qDACJ;sDACC,aAAa,MAAM,KAAK,CAAC,QAAQ;;;;;;;;;;;;8CAItC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAK,WAAU;;gDAAO;gDACjB,WAAW,MAAM,KAAK,CAAC,QAAQ;gDAAE;gDAAI,WAAW,MAAM,GAAG,CAAC,QAAQ;;;;;;;wCAEvE,MAAM,QAAQ,kBACb,8OAAC;;gDAAK;gDACA,MAAM,QAAQ;;;;;;;;;;;;;gCAKvB,MAAM,WAAW,kBAChB,8OAAC;oCAAE,WAAU;8CACV,MAAM,WAAW;;;;;;;2BA/BjB,MAAM,EAAE;;;;;oBAqChB,OAAO,MAAM,GAAG,mBACf,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAK,WAAU;;gCAAkC;gCAC9C,OAAO,MAAM,GAAG;gCAAE;;;;;;;;;;;;;;;;;;;;;;;;AAQpC", "debugId": null}}, {"offset": {"line": 1748, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/AI_Projects/CRMcriadores/crm-interno/app/%28dashboard%29/jornada/page.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect, useId } from 'react';\nimport { getData, updateBusinessStage } from '@/app/actions/sheetsActions';\nimport { transformData } from '@/lib/utils';\nimport BusinessDetailModal from '@/components/BusinessDetailModal';\nimport {\n  DndContext,\n  DragEndEvent,\n  DragOverlay,\n  DragStartEvent,\n  PointerSensor,\n  useSensor,\n  useSensors,\n  closestCorners,\n} from '@dnd-kit/core';\nimport {\n  SortableContext,\n  verticalListSortingStrategy,\n} from '@dnd-kit/sortable';\nimport DraggableBusinessCard, { BusinessCardOverlay } from '@/components/DraggableBusinessCard';\nimport DroppableColumn from '@/components/DroppableColumn';\nimport { useToast } from '@/components/Toast';\nimport CalendarWidget from '@/components/CalendarWidget';\n\n// Dados de exemplo para demonstração (serão substituídos pelos dados do Google Sheets)\nconst mockBusinesses = [\n  {\n    id: 1,\n    businessName: 'Loja de Roupas Fashion',\n    journeyStage: 'Agendamentos',\n    nextAction: 'Agendar sessões de fotos com influenciadores',\n    contactDate: '2024-01-15',\n    value: 15000,\n    description: 'Campanha de verão focada em roupas casuais para jovens de 18-30 anos',\n    creators: [\n      { name: 'Ana Silva', username: 'anasilva', followers: 125000, engagementRate: 4.2 },\n      { name: 'Carlos Santos', username: 'carlossantos', followers: 89000, engagementRate: 6.8 }\n    ],\n    campaigns: [\n      { title: 'Campanha Verão 2024', status: 'Ativa', startDate: '2024-01-15', endDate: '2024-03-15' }\n    ]\n  },\n  {\n    id: 2,\n    businessName: 'Restaurante Gourmet',\n    journeyStage: 'Reunião Briefing',\n    nextAction: 'Definir estratégia de conteúdo gastronômico',\n    contactDate: '2024-01-10',\n    value: 8000,\n    description: 'Divulgação de pratos especiais e experiência gastronômica única',\n    creators: [\n      { name: 'Maria Oliveira', username: 'mariaoliveira', followers: 234000, engagementRate: 3.1 }\n    ],\n    campaigns: []\n  },\n  {\n    id: 3,\n    businessName: 'Academia Fitness Plus',\n    journeyStage: 'Entrega Final',\n    nextAction: 'Finalizar edição dos vídeos de treino',\n    contactDate: '2024-01-20',\n    value: 25000,\n    description: 'Campanha de motivação fitness com foco em resultados reais',\n    creators: [\n      { name: 'João Fitness', username: 'joaofitness', followers: 156000, engagementRate: 5.4 },\n      { name: 'Carla Strong', username: 'carlastrong', followers: 98000, engagementRate: 7.2 },\n      { name: 'Pedro Muscle', username: 'pedromuscle', followers: 67000, engagementRate: 4.8 }\n    ],\n    campaigns: [\n      { title: 'Transformação 90 Dias', status: 'Ativa', startDate: '2024-01-01', endDate: '2024-03-31' }\n    ]\n  },\n  {\n    id: 4,\n    businessName: 'Clínica de Estética',\n    journeyStage: 'Reunião Briefing',\n    nextAction: 'Alinhar diretrizes de comunicação sobre procedimentos',\n    contactDate: '2024-01-12',\n    value: 12000,\n    description: 'Divulgação de tratamentos estéticos com foco em naturalidade',\n    creators: [\n      { name: 'Bella Beauty', username: 'bellabeauty', followers: 189000, engagementRate: 6.1 }\n    ],\n    campaigns: []\n  },\n  {\n    id: 5,\n    businessName: 'Loja de Eletrônicos',\n    journeyStage: 'Agendamentos',\n    nextAction: 'Coordenar reviews de produtos com tech creators',\n    contactDate: '2024-01-08',\n    value: 18000,\n    description: 'Reviews autênticos de gadgets e eletrônicos inovadores',\n    creators: [\n      { name: 'Tech Master', username: 'techmaster', followers: 145000, engagementRate: 5.9 },\n      { name: 'Gamer Pro', username: 'gamerpro', followers: 203000, engagementRate: 4.5 }\n    ],\n    campaigns: [\n      { title: 'Tech Reviews 2024', status: 'Planejamento', startDate: '2024-02-01', endDate: '2024-04-30' }\n    ]\n  }\n];\n\n// Definir as fases da jornada (3 fases principais)\nconst journeyStages = [\n  { id: 'Reunião Briefing', label: 'Reunião Briefing', color: 'bg-blue-100 text-blue-800', icon: '📋' },\n  { id: 'Agendamentos', label: 'Agendamentos', color: 'bg-yellow-100 text-yellow-800', icon: '📅' },\n  { id: 'Entrega Final', label: 'Entrega Final', color: 'bg-green-100 text-green-800', icon: '✅' }\n];\n\nexport default function JornadaPage() {\n  const [businesses, setBusinesses] = useState(mockBusinesses);\n  const [selectedBusiness, setSelectedBusiness] = useState<any>(null);\n  const [isModalOpen, setIsModalOpen] = useState(false);\n  const [activeId, setActiveId] = useState<string | null>(null);\n  const [isUpdating, setIsUpdating] = useState(false);\n  const [isClient, setIsClient] = useState(false);\n  const { showToast, ToastComponent } = useToast();\n  const dndId = useId();\n\n  // Configurar sensores para drag & drop\n  const sensors = useSensors(\n    useSensor(PointerSensor, {\n      activationConstraint: {\n        distance: 8, // Mínimo de 8px de movimento para ativar o drag\n      },\n    })\n  );\n\n  // Controlar hidratação para evitar erros de SSR\n  useEffect(() => {\n    setIsClient(true);\n  }, []);\n\n  // Carregar dados do Google Sheets\n  React.useEffect(() => {\n    async function loadData() {\n      try {\n        const rawData = await getData('Businesses');\n        if (rawData && rawData.length > 0) {\n          const transformedData = transformData(rawData);\n\n          // Mapeia os dados transformados para o formato esperado pelo componente\n          const businessesData = transformedData.map((item: any) => ({\n            id: item.id || Math.random(),\n            businessName: item.businessName || item.Nome || item['Nome do Negócio'] || 'Negócio não informado',\n            journeyStage: item.journeyStage || item.Estágio || item['Estágio da Jornada'] || 'Reunião Briefing',\n            nextAction: item.nextAction || item.Ação || item['Próxima Ação'] || 'Definir próxima ação',\n            contactDate: item.contactDate || item.Data || item['Data de Contato'] || new Date().toISOString().split('T')[0],\n            value: parseInt(item.value || item.Valor || item['Valor do Negócio'] || '0'),\n            description: item.description || item.Descrição || 'Descrição não informada',\n            creators: JSON.parse(item.creators || '[]'),\n            campaigns: JSON.parse(item.campaigns || '[]')\n          }));\n          setBusinesses(businessesData);\n        }\n      } catch (error) {\n        console.log('Usando dados de exemplo - Google Sheets não configurado ainda');\n      }\n    }\n\n    loadData();\n  }, []);\n\n  const handleBusinessClick = (business: any) => {\n    setSelectedBusiness(business);\n    setIsModalOpen(true);\n  };\n\n  const handleCloseModal = () => {\n    setIsModalOpen(false);\n    setSelectedBusiness(null);\n  };\n\n  // Função chamada quando o drag inicia\n  const handleDragStart = (event: DragStartEvent) => {\n    setActiveId(event.active.id as string);\n  };\n\n  // Função chamada quando o drag termina\n  const handleDragEnd = async (event: DragEndEvent) => {\n    const { active, over } = event;\n    setActiveId(null);\n\n    if (!over) return;\n\n    const businessId = active.id as string;\n    const newStage = over.id as string;\n\n    // Encontra o negócio que está sendo movido\n    const business = businesses.find(b => b.id.toString() === businessId);\n    if (!business || business.journeyStage === newStage) return;\n\n    // Atualiza o estado local imediatamente para feedback visual\n    setBusinesses(prev =>\n      prev.map(b =>\n        b.id.toString() === businessId\n          ? { ...b, journeyStage: newStage }\n          : b\n      )\n    );\n\n    // Atualiza no banco de dados\n    setIsUpdating(true);\n    try {\n      await updateBusinessStage(businessId, newStage, business);\n      console.log(`Negócio ${business.businessName} movido para ${newStage}`);\n\n      // Notificação especial para agendamentos\n      if (newStage === 'Agendamentos') {\n        showToast(`${business.businessName} movido para Agendamentos. Evento criado no calendário!`, 'success');\n      } else {\n        showToast(`${business.businessName} movido para ${newStage}`, 'success');\n      }\n\n    } catch (error) {\n      console.error('Erro ao atualizar estágio:', error);\n\n      // Reverte a mudança local em caso de erro\n      setBusinesses(prev =>\n        prev.map(b =>\n          b.id.toString() === businessId\n            ? { ...b, journeyStage: business.journeyStage }\n            : b\n        )\n      );\n\n      // Notificação de erro\n      showToast('Erro ao mover negócio. Tente novamente.', 'error');\n\n    } finally {\n      setIsUpdating(false);\n    }\n  };\n\n  // Encontra o negócio que está sendo arrastado para o overlay\n  const activeBusiness = activeId ? businesses.find(b => b.id.toString() === activeId) : null;\n\n\n\n  // Agrupar negócios por fase da jornada\n  const businessesByStage = journeyStages.map(stage => ({\n    ...stage,\n    businesses: businesses.filter(business => business.journeyStage === stage.id),\n    totalValue: businesses\n      .filter(business => business.journeyStage === stage.id)\n      .reduce((sum, business) => sum + business.value, 0)\n  }));\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header com estatísticas gerais */}\n      <div className=\"grid grid-cols-1 md:grid-cols-4 gap-4\">\n        <div className=\"card-elevated p-4\">\n          <div className=\"flex items-center justify-between\">\n            <div>\n              <p className=\"text-sm text-on-surface-variant\">Total de Negócios</p>\n              <p className=\"text-2xl font-bold text-on-surface\">{businesses.length}</p>\n            </div>\n            <div className=\"text-2xl\">🏢</div>\n          </div>\n        </div>\n        \n        <div className=\"card-elevated p-4\">\n          <div className=\"flex items-center justify-between\">\n            <div>\n              <p className=\"text-sm text-on-surface-variant\">Valor Total</p>\n              <p className=\"text-2xl font-bold text-primary\">\n                R$ {(businesses.reduce((sum, b) => sum + b.value, 0) / 1000).toFixed(0)}K\n              </p>\n            </div>\n            <div className=\"text-2xl\">💰</div>\n          </div>\n        </div>\n        \n        <div className=\"card-elevated p-4\">\n          <div className=\"flex items-center justify-between\">\n            <div>\n              <p className=\"text-sm text-on-surface-variant\">Em Fechamento</p>\n              <p className=\"text-2xl font-bold text-green-600\">\n                {businesses.filter(b => b.journeyStage === 'Fechamento').length}\n              </p>\n            </div>\n            <div className=\"text-2xl\">🎯</div>\n          </div>\n        </div>\n        \n        <div className=\"card-elevated p-4\">\n          <div className=\"flex items-center justify-between\">\n            <div>\n              <p className=\"text-sm text-on-surface-variant\">Taxa Conversão</p>\n              <p className=\"text-2xl font-bold text-secondary\">\n                {businesses.length > 0 ? Math.round((businesses.filter(b => b.journeyStage === 'Pós-venda').length / businesses.length) * 100) : 0}%\n              </p>\n            </div>\n            <div className=\"text-2xl\">📈</div>\n          </div>\n        </div>\n      </div>\n\n      {/* Layout Principal */}\n      <div className=\"grid grid-cols-1 xl:grid-cols-4 gap-6\">\n        {/* Kanban da Jornada com Drag & Drop */}\n        <div className=\"xl:col-span-3\">\n          {isClient ? (\n            <DndContext\n              id={dndId}\n              sensors={sensors}\n              collisionDetection={closestCorners}\n              onDragStart={handleDragStart}\n              onDragEnd={handleDragEnd}\n            >\n              <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-6\">\n                {businessesByStage.map((stage) => (\n                  <DroppableColumn\n                    key={stage.id}\n                    id={stage.id}\n                    title={stage.label}\n                    icon={stage.icon}\n                    businesses={stage.businesses}\n                    totalValue={stage.totalValue}\n                    onBusinessClick={handleBusinessClick}\n                    isUpdating={isUpdating}\n                  />\n                ))}\n              </div>\n\n              {/* Overlay para mostrar o item sendo arrastado */}\n              <DragOverlay>\n                {activeBusiness ? (\n                  <BusinessCardOverlay business={activeBusiness} />\n                ) : null}\n              </DragOverlay>\n            </DndContext>\n          ) : (\n            <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-6\">\n              {businessesByStage.map((stage) => (\n                <div key={stage.id} className=\"card-elevated p-6 min-h-96\">\n                  <div className=\"mb-6\">\n                    <div className=\"flex items-center justify-between mb-3\">\n                      <div className=\"flex items-center\">\n                        <span className=\"text-2xl mr-3\">{stage.icon}</span>\n                        <h3 className=\"text-lg font-semibold text-on-surface\">{stage.label}</h3>\n                      </div>\n                      <span className=\"text-sm bg-surface-container px-3 py-1 rounded-full font-medium\">\n                        {stage.businesses.length}\n                      </span>\n                    </div>\n                    <div className=\"text-sm text-on-surface-variant\">\n                      Total: R$ {(stage.totalValue / 1000).toFixed(0)}K\n                    </div>\n                  </div>\n                  <div className=\"space-y-4\">\n                    {stage.businesses.map((business) => (\n                      <div\n                        key={business.id}\n                        className=\"bg-surface-container rounded-lg p-4 border-l-4 border-primary cursor-pointer hover:shadow-md transition-all duration-200\"\n                        onClick={() => handleBusinessClick(business)}\n                      >\n                        <h4 className=\"font-semibold text-on-surface mb-2\">{business.businessName}</h4>\n                        <p className=\"text-sm text-on-surface-variant mb-3\">{business.nextAction}</p>\n                        <div className=\"flex items-center justify-between text-sm\">\n                          <span className=\"text-on-surface-variant\">\n                            👥 {business.creators.length} influenciador{business.creators.length !== 1 ? 'es' : ''}\n                          </span>\n                          <span className=\"font-bold text-primary\">\n                            R$ {(business.value / 1000).toFixed(0)}K\n                          </span>\n                        </div>\n                      </div>\n                    ))}\n                  </div>\n                </div>\n              ))}\n            </div>\n          )}\n        </div>\n\n        {/* Sidebar com Calendário */}\n        <div className=\"xl:col-span-1\">\n          <CalendarWidget />\n        </div>\n      </div>\n\n      {/* Modal de Detalhes */}\n      <BusinessDetailModal\n        business={selectedBusiness}\n        isOpen={isModalOpen}\n        onClose={handleCloseModal}\n      />\n\n      {/* Toast Notifications */}\n      <ToastComponent />\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AACA;AACA;AAcA;AACA;AACA;AACA;AAvBA;;;;;;;;;;;AAyBA,uFAAuF;AACvF,MAAM,iBAAiB;IACrB;QACE,IAAI;QACJ,cAAc;QACd,cAAc;QACd,YAAY;QACZ,aAAa;QACb,OAAO;QACP,aAAa;QACb,UAAU;YACR;gBAAE,MAAM;gBAAa,UAAU;gBAAY,WAAW;gBAAQ,gBAAgB;YAAI;YAClF;gBAAE,MAAM;gBAAiB,UAAU;gBAAgB,WAAW;gBAAO,gBAAgB;YAAI;SAC1F;QACD,WAAW;YACT;gBAAE,OAAO;gBAAuB,QAAQ;gBAAS,WAAW;gBAAc,SAAS;YAAa;SACjG;IACH;IACA;QACE,IAAI;QACJ,cAAc;QACd,cAAc;QACd,YAAY;QACZ,aAAa;QACb,OAAO;QACP,aAAa;QACb,UAAU;YACR;gBAAE,MAAM;gBAAkB,UAAU;gBAAiB,WAAW;gBAAQ,gBAAgB;YAAI;SAC7F;QACD,WAAW,EAAE;IACf;IACA;QACE,IAAI;QACJ,cAAc;QACd,cAAc;QACd,YAAY;QACZ,aAAa;QACb,OAAO;QACP,aAAa;QACb,UAAU;YACR;gBAAE,MAAM;gBAAgB,UAAU;gBAAe,WAAW;gBAAQ,gBAAgB;YAAI;YACxF;gBAAE,MAAM;gBAAgB,UAAU;gBAAe,WAAW;gBAAO,gBAAgB;YAAI;YACvF;gBAAE,MAAM;gBAAgB,UAAU;gBAAe,WAAW;gBAAO,gBAAgB;YAAI;SACxF;QACD,WAAW;YACT;gBAAE,OAAO;gBAAyB,QAAQ;gBAAS,WAAW;gBAAc,SAAS;YAAa;SACnG;IACH;IACA;QACE,IAAI;QACJ,cAAc;QACd,cAAc;QACd,YAAY;QACZ,aAAa;QACb,OAAO;QACP,aAAa;QACb,UAAU;YACR;gBAAE,MAAM;gBAAgB,UAAU;gBAAe,WAAW;gBAAQ,gBAAgB;YAAI;SACzF;QACD,WAAW,EAAE;IACf;IACA;QACE,IAAI;QACJ,cAAc;QACd,cAAc;QACd,YAAY;QACZ,aAAa;QACb,OAAO;QACP,aAAa;QACb,UAAU;YACR;gBAAE,MAAM;gBAAe,UAAU;gBAAc,WAAW;gBAAQ,gBAAgB;YAAI;YACtF;gBAAE,MAAM;gBAAa,UAAU;gBAAY,WAAW;gBAAQ,gBAAgB;YAAI;SACnF;QACD,WAAW;YACT;gBAAE,OAAO;gBAAqB,QAAQ;gBAAgB,WAAW;gBAAc,SAAS;YAAa;SACtG;IACH;CACD;AAED,mDAAmD;AACnD,MAAM,gBAAgB;IACpB;QAAE,IAAI;QAAoB,OAAO;QAAoB,OAAO;QAA6B,MAAM;IAAK;IACpG;QAAE,IAAI;QAAgB,OAAO;QAAgB,OAAO;QAAiC,MAAM;IAAK;IAChG;QAAE,IAAI;QAAiB,OAAO;QAAiB,OAAO;QAA+B,MAAM;IAAI;CAChG;AAEc,SAAS;IACtB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAO;IAC9D,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IACxD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,EAAE,SAAS,EAAE,cAAc,EAAE,GAAG,CAAA,GAAA,oHAAA,CAAA,WAAQ,AAAD;IAC7C,MAAM,QAAQ,CAAA,GAAA,qMAAA,CAAA,QAAK,AAAD;IAElB,uCAAuC;IACvC,MAAM,UAAU,CAAA,GAAA,2JAAA,CAAA,aAAU,AAAD,EACvB,CAAA,GAAA,2JAAA,CAAA,YAAS,AAAD,EAAE,2JAAA,CAAA,gBAAa,EAAE;QACvB,sBAAsB;YACpB,UAAU;QACZ;IACF;IAGF,gDAAgD;IAChD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,YAAY;IACd,GAAG,EAAE;IAEL,kCAAkC;IAClC,qMAAA,CAAA,UAAK,CAAC,SAAS,CAAC;QACd,eAAe;YACb,IAAI;gBACF,MAAM,UAAU,MAAM,CAAA,GAAA,sJAAA,CAAA,UAAO,AAAD,EAAE;gBAC9B,IAAI,WAAW,QAAQ,MAAM,GAAG,GAAG;oBACjC,MAAM,kBAAkB,CAAA,GAAA,4GAAA,CAAA,gBAAa,AAAD,EAAE;oBAEtC,wEAAwE;oBACxE,MAAM,iBAAiB,gBAAgB,GAAG,CAAC,CAAC,OAAc,CAAC;4BACzD,IAAI,KAAK,EAAE,IAAI,KAAK,MAAM;4BAC1B,cAAc,KAAK,YAAY,IAAI,KAAK,IAAI,IAAI,IAAI,CAAC,kBAAkB,IAAI;4BAC3E,cAAc,KAAK,YAAY,IAAI,KAAK,OAAO,IAAI,IAAI,CAAC,qBAAqB,IAAI;4BACjF,YAAY,KAAK,UAAU,IAAI,KAAK,IAAI,IAAI,IAAI,CAAC,eAAe,IAAI;4BACpE,aAAa,KAAK,WAAW,IAAI,KAAK,IAAI,IAAI,IAAI,CAAC,kBAAkB,IAAI,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;4BAC/G,OAAO,SAAS,KAAK,KAAK,IAAI,KAAK,KAAK,IAAI,IAAI,CAAC,mBAAmB,IAAI;4BACxE,aAAa,KAAK,WAAW,IAAI,KAAK,SAAS,IAAI;4BACnD,UAAU,KAAK,KAAK,CAAC,KAAK,QAAQ,IAAI;4BACtC,WAAW,KAAK,KAAK,CAAC,KAAK,SAAS,IAAI;wBAC1C,CAAC;oBACD,cAAc;gBAChB;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,GAAG,CAAC;YACd;QACF;QAEA;IACF,GAAG,EAAE;IAEL,MAAM,sBAAsB,CAAC;QAC3B,oBAAoB;QACpB,eAAe;IACjB;IAEA,MAAM,mBAAmB;QACvB,eAAe;QACf,oBAAoB;IACtB;IAEA,sCAAsC;IACtC,MAAM,kBAAkB,CAAC;QACvB,YAAY,MAAM,MAAM,CAAC,EAAE;IAC7B;IAEA,uCAAuC;IACvC,MAAM,gBAAgB,OAAO;QAC3B,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,GAAG;QACzB,YAAY;QAEZ,IAAI,CAAC,MAAM;QAEX,MAAM,aAAa,OAAO,EAAE;QAC5B,MAAM,WAAW,KAAK,EAAE;QAExB,2CAA2C;QAC3C,MAAM,WAAW,WAAW,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,CAAC,QAAQ,OAAO;QAC1D,IAAI,CAAC,YAAY,SAAS,YAAY,KAAK,UAAU;QAErD,6DAA6D;QAC7D,cAAc,CAAA,OACZ,KAAK,GAAG,CAAC,CAAA,IACP,EAAE,EAAE,CAAC,QAAQ,OAAO,aAChB;oBAAE,GAAG,CAAC;oBAAE,cAAc;gBAAS,IAC/B;QAIR,6BAA6B;QAC7B,cAAc;QACd,IAAI;YACF,MAAM,CAAA,GAAA,sJAAA,CAAA,sBAAmB,AAAD,EAAE,YAAY,UAAU;YAChD,QAAQ,GAAG,CAAC,CAAC,QAAQ,EAAE,SAAS,YAAY,CAAC,aAAa,EAAE,UAAU;YAEtE,yCAAyC;YACzC,IAAI,aAAa,gBAAgB;gBAC/B,UAAU,GAAG,SAAS,YAAY,CAAC,uDAAuD,CAAC,EAAE;YAC/F,OAAO;gBACL,UAAU,GAAG,SAAS,YAAY,CAAC,aAAa,EAAE,UAAU,EAAE;YAChE;QAEF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,8BAA8B;YAE5C,0CAA0C;YAC1C,cAAc,CAAA,OACZ,KAAK,GAAG,CAAC,CAAA,IACP,EAAE,EAAE,CAAC,QAAQ,OAAO,aAChB;wBAAE,GAAG,CAAC;wBAAE,cAAc,SAAS,YAAY;oBAAC,IAC5C;YAIR,sBAAsB;YACtB,UAAU,2CAA2C;QAEvD,SAAU;YACR,cAAc;QAChB;IACF;IAEA,6DAA6D;IAC7D,MAAM,iBAAiB,WAAW,WAAW,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,CAAC,QAAQ,OAAO,YAAY;IAIvF,uCAAuC;IACvC,MAAM,oBAAoB,cAAc,GAAG,CAAC,CAAA,QAAS,CAAC;YACpD,GAAG,KAAK;YACR,YAAY,WAAW,MAAM,CAAC,CAAA,WAAY,SAAS,YAAY,KAAK,MAAM,EAAE;YAC5E,YAAY,WACT,MAAM,CAAC,CAAA,WAAY,SAAS,YAAY,KAAK,MAAM,EAAE,EACrD,MAAM,CAAC,CAAC,KAAK,WAAa,MAAM,SAAS,KAAK,EAAE;QACrD,CAAC;IAED,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;sDACC,8OAAC;4CAAE,WAAU;sDAAkC;;;;;;sDAC/C,8OAAC;4CAAE,WAAU;sDAAsC,WAAW,MAAM;;;;;;;;;;;;8CAEtE,8OAAC;oCAAI,WAAU;8CAAW;;;;;;;;;;;;;;;;;kCAI9B,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;sDACC,8OAAC;4CAAE,WAAU;sDAAkC;;;;;;sDAC/C,8OAAC;4CAAE,WAAU;;gDAAkC;gDACzC,CAAC,WAAW,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,EAAE,KAAK,EAAE,KAAK,IAAI,EAAE,OAAO,CAAC;gDAAG;;;;;;;;;;;;;8CAG5E,8OAAC;oCAAI,WAAU;8CAAW;;;;;;;;;;;;;;;;;kCAI9B,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;sDACC,8OAAC;4CAAE,WAAU;sDAAkC;;;;;;sDAC/C,8OAAC;4CAAE,WAAU;sDACV,WAAW,MAAM,CAAC,CAAA,IAAK,EAAE,YAAY,KAAK,cAAc,MAAM;;;;;;;;;;;;8CAGnE,8OAAC;oCAAI,WAAU;8CAAW;;;;;;;;;;;;;;;;;kCAI9B,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;sDACC,8OAAC;4CAAE,WAAU;sDAAkC;;;;;;sDAC/C,8OAAC;4CAAE,WAAU;;gDACV,WAAW,MAAM,GAAG,IAAI,KAAK,KAAK,CAAC,AAAC,WAAW,MAAM,CAAC,CAAA,IAAK,EAAE,YAAY,KAAK,aAAa,MAAM,GAAG,WAAW,MAAM,GAAI,OAAO;gDAAE;;;;;;;;;;;;;8CAGvI,8OAAC;oCAAI,WAAU;8CAAW;;;;;;;;;;;;;;;;;;;;;;;0BAMhC,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;kCACZ,yBACC,8OAAC,2JAAA,CAAA,aAAU;4BACT,IAAI;4BACJ,SAAS;4BACT,oBAAoB,2JAAA,CAAA,iBAAc;4BAClC,aAAa;4BACb,WAAW;;8CAEX,8OAAC;oCAAI,WAAU;8CACZ,kBAAkB,GAAG,CAAC,CAAC,sBACtB,8OAAC,8HAAA,CAAA,UAAe;4CAEd,IAAI,MAAM,EAAE;4CACZ,OAAO,MAAM,KAAK;4CAClB,MAAM,MAAM,IAAI;4CAChB,YAAY,MAAM,UAAU;4CAC5B,YAAY,MAAM,UAAU;4CAC5B,iBAAiB;4CACjB,YAAY;2CAPP,MAAM,EAAE;;;;;;;;;;8CAanB,8OAAC,2JAAA,CAAA,cAAW;8CACT,+BACC,8OAAC,oIAAA,CAAA,sBAAmB;wCAAC,UAAU;;;;;+CAC7B;;;;;;;;;;;iDAIR,8OAAC;4BAAI,WAAU;sCACZ,kBAAkB,GAAG,CAAC,CAAC,sBACtB,8OAAC;oCAAmB,WAAU;;sDAC5B,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAK,WAAU;8EAAiB,MAAM,IAAI;;;;;;8EAC3C,8OAAC;oEAAG,WAAU;8EAAyC,MAAM,KAAK;;;;;;;;;;;;sEAEpE,8OAAC;4DAAK,WAAU;sEACb,MAAM,UAAU,CAAC,MAAM;;;;;;;;;;;;8DAG5B,8OAAC;oDAAI,WAAU;;wDAAkC;wDACpC,CAAC,MAAM,UAAU,GAAG,IAAI,EAAE,OAAO,CAAC;wDAAG;;;;;;;;;;;;;sDAGpD,8OAAC;4CAAI,WAAU;sDACZ,MAAM,UAAU,CAAC,GAAG,CAAC,CAAC,yBACrB,8OAAC;oDAEC,WAAU;oDACV,SAAS,IAAM,oBAAoB;;sEAEnC,8OAAC;4DAAG,WAAU;sEAAsC,SAAS,YAAY;;;;;;sEACzE,8OAAC;4DAAE,WAAU;sEAAwC,SAAS,UAAU;;;;;;sEACxE,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAK,WAAU;;wEAA0B;wEACpC,SAAS,QAAQ,CAAC,MAAM;wEAAC;wEAAe,SAAS,QAAQ,CAAC,MAAM,KAAK,IAAI,OAAO;;;;;;;8EAEtF,8OAAC;oEAAK,WAAU;;wEAAyB;wEACnC,CAAC,SAAS,KAAK,GAAG,IAAI,EAAE,OAAO,CAAC;wEAAG;;;;;;;;;;;;;;mDAXtC,SAAS,EAAE;;;;;;;;;;;mCAlBd,MAAM,EAAE;;;;;;;;;;;;;;;kCA0C1B,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,6HAAA,CAAA,UAAc;;;;;;;;;;;;;;;;0BAKnB,8OAAC,kIAAA,CAAA,UAAmB;gBAClB,UAAU;gBACV,QAAQ;gBACR,SAAS;;;;;;0BAIX,8OAAC;;;;;;;;;;;AAGP", "debugId": null}}]}
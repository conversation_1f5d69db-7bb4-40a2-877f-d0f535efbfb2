{"version": 3, "sources": [], "sections": [{"offset": {"line": 111, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/AI_Projects/CRMcriadores/crm-interno/app/actions/sheetsActions.ts"], "sourcesContent": ["'use server';\n\nimport { google } from 'googleapis';\n\n// Configuração da autenticação\nconst getGoogleSheetsAuth = () => {\n  const credentials = {\n    type: 'service_account',\n    project_id: process.env.GOOGLE_PROJECT_ID,\n    private_key_id: process.env.GOOGLE_PRIVATE_KEY_ID,\n    private_key: process.env.GOOGLE_PRIVATE_KEY?.replace(/\\\\n/g, '\\n'),\n    client_email: process.env.GOOGLE_CLIENT_EMAIL,\n    client_id: process.env.GOOGLE_CLIENT_ID,\n    auth_uri: 'https://accounts.google.com/o/oauth2/auth',\n    token_uri: 'https://oauth2.googleapis.com/token',\n    auth_provider_x509_cert_url: 'https://www.googleapis.com/oauth2/v1/certs',\n    client_x509_cert_url: `https://www.googleapis.com/robot/v1/metadata/x509/${process.env.GOOGLE_CLIENT_EMAIL}`,\n  };\n\n  const auth = new google.auth.GoogleAuth({\n    credentials,\n    scopes: ['https://www.googleapis.com/auth/spreadsheets'],\n  });\n\n  return auth;\n};\n\n// Função para ler dados da planilha\nexport async function getData(sheetName: string) {\n  try {\n    const auth = getGoogleSheetsAuth();\n    const sheets = google.sheets({ version: 'v4', auth });\n    \n    const spreadsheetId = process.env.GOOGLE_SPREADSHEET_ID;\n    \n    if (!spreadsheetId) {\n      throw new Error('GOOGLE_SPREADSHEET_ID não está configurado');\n    }\n\n    const response = await sheets.spreadsheets.values.get({\n      spreadsheetId,\n      range: `${sheetName}!A:Z`, // Lê todas as colunas de A até Z\n    });\n\n    return response.data.values || [];\n  } catch (error) {\n    console.error('Erro ao ler dados da planilha:', error);\n    throw new Error('Falha ao ler dados da planilha');\n  }\n}\n\n// Função para adicionar dados à planilha\nexport async function appendData(sheetName: string, rowData: any[]) {\n  try {\n    const auth = getGoogleSheetsAuth();\n    const sheets = google.sheets({ version: 'v4', auth });\n    \n    const spreadsheetId = process.env.GOOGLE_SPREADSHEET_ID;\n    \n    if (!spreadsheetId) {\n      throw new Error('GOOGLE_SPREADSHEET_ID não está configurado');\n    }\n\n    const response = await sheets.spreadsheets.values.append({\n      spreadsheetId,\n      range: `${sheetName}!A:Z`,\n      valueInputOption: 'USER_ENTERED',\n      requestBody: {\n        values: [rowData],\n      },\n    });\n\n    return response.data;\n  } catch (error) {\n    console.error('Erro ao adicionar dados à planilha:', error);\n    throw new Error('Falha ao adicionar dados à planilha');\n  }\n}\n\n// Função para atualizar dados na planilha\nexport async function updateData(sheetName: string, range: string, rowData: any[]) {\n  try {\n    const auth = getGoogleSheetsAuth();\n    const sheets = google.sheets({ version: 'v4', auth });\n\n    const spreadsheetId = process.env.GOOGLE_SPREADSHEET_ID;\n\n    if (!spreadsheetId) {\n      throw new Error('GOOGLE_SPREADSHEET_ID não está configurado');\n    }\n\n    const response = await sheets.spreadsheets.values.update({\n      spreadsheetId,\n      range: `${sheetName}!${range}`,\n      valueInputOption: 'USER_ENTERED',\n      requestBody: {\n        values: [rowData],\n      },\n    });\n\n    return response.data;\n  } catch (error) {\n    console.error('Erro ao atualizar dados da planilha:', error);\n    throw new Error('Falha ao atualizar dados da planilha');\n  }\n}\n\n// Função específica para atualizar o estágio da jornada de um negócio\nexport async function updateBusinessStage(businessId: string, newStage: string) {\n  try {\n    // Se Google Sheets não estiver configurado, simula sucesso\n    const spreadsheetId = process.env.GOOGLE_SPREADSHEET_ID;\n\n    if (!spreadsheetId) {\n      console.log(`Simulando atualização: Negócio ${businessId} movido para ${newStage}`);\n      return { success: true, message: 'Atualização simulada com sucesso' };\n    }\n\n    const auth = getGoogleSheetsAuth();\n    const sheets = google.sheets({ version: 'v4', auth });\n\n    // Primeiro, busca todos os dados para encontrar a linha do negócio\n    const allData = await sheets.spreadsheets.values.get({\n      spreadsheetId,\n      range: 'Businesses!A:Z',\n    });\n\n    const rows = allData.data.values || [];\n    if (rows.length === 0) {\n      throw new Error('Nenhum dado encontrado na planilha');\n    }\n\n    // Encontra a linha do negócio pelo ID\n    const headers = rows[0];\n    const idColumnIndex = headers.findIndex((header: string) =>\n      header.toLowerCase().includes('id')\n    );\n    const stageColumnIndex = headers.findIndex((header: string) =>\n      header.toLowerCase().includes('stage') || header.toLowerCase().includes('estágio')\n    );\n\n    if (idColumnIndex === -1 || stageColumnIndex === -1) {\n      throw new Error('Colunas ID ou Stage não encontradas');\n    }\n\n    // Procura pela linha do negócio\n    let targetRowIndex = -1;\n    for (let i = 1; i < rows.length; i++) {\n      if (rows[i][idColumnIndex] === businessId) {\n        targetRowIndex = i + 1; // +1 porque as linhas do Sheets são 1-indexed\n        break;\n      }\n    }\n\n    if (targetRowIndex === -1) {\n      throw new Error(`Negócio com ID ${businessId} não encontrado`);\n    }\n\n    // Atualiza apenas a célula do estágio\n    const stageColumn = String.fromCharCode(65 + stageColumnIndex); // Converte índice para letra (A, B, C...)\n    const range = `${stageColumn}${targetRowIndex}`;\n\n    const response = await sheets.spreadsheets.values.update({\n      spreadsheetId,\n      range: `Businesses!${range}`,\n      valueInputOption: 'USER_ENTERED',\n      requestBody: {\n        values: [[newStage]],\n      },\n    });\n\n    return { success: true, data: response.data };\n  } catch (error) {\n    console.error('Erro ao atualizar estágio do negócio:', error);\n    throw new Error('Falha ao atualizar estágio do negócio');\n  }\n}\n"], "names": [], "mappings": ";;;;;;;;AAEA;;;;;AAEA,+BAA+B;AAC/B,MAAM,sBAAsB;IAC1B,MAAM,cAAc;QAClB,MAAM;QACN,YAAY,QAAQ,GAAG,CAAC,iBAAiB;QACzC,gBAAgB,QAAQ,GAAG,CAAC,qBAAqB;QACjD,aAAa,QAAQ,GAAG,CAAC,kBAAkB,EAAE,QAAQ,QAAQ;QAC7D,cAAc,QAAQ,GAAG,CAAC,mBAAmB;QAC7C,WAAW,QAAQ,GAAG,CAAC,gBAAgB;QACvC,UAAU;QACV,WAAW;QACX,6BAA6B;QAC7B,sBAAsB,CAAC,kDAAkD,EAAE,QAAQ,GAAG,CAAC,mBAAmB,EAAE;IAC9G;IAEA,MAAM,OAAO,IAAI,mJAAA,CAAA,SAAM,CAAC,IAAI,CAAC,UAAU,CAAC;QACtC;QACA,QAAQ;YAAC;SAA+C;IAC1D;IAEA,OAAO;AACT;AAGO,eAAe,QAAQ,SAAiB;IAC7C,IAAI;QACF,MAAM,OAAO;QACb,MAAM,SAAS,mJAAA,CAAA,SAAM,CAAC,MAAM,CAAC;YAAE,SAAS;YAAM;QAAK;QAEnD,MAAM,gBAAgB,QAAQ,GAAG,CAAC,qBAAqB;QAEvD,IAAI,CAAC,eAAe;YAClB,MAAM,IAAI,MAAM;QAClB;QAEA,MAAM,WAAW,MAAM,OAAO,YAAY,CAAC,MAAM,CAAC,GAAG,CAAC;YACpD;YACA,OAAO,GAAG,UAAU,IAAI,CAAC;QAC3B;QAEA,OAAO,SAAS,IAAI,CAAC,MAAM,IAAI,EAAE;IACnC,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,kCAAkC;QAChD,MAAM,IAAI,MAAM;IAClB;AACF;AAGO,eAAe,WAAW,SAAiB,EAAE,OAAc;IAChE,IAAI;QACF,MAAM,OAAO;QACb,MAAM,SAAS,mJAAA,CAAA,SAAM,CAAC,MAAM,CAAC;YAAE,SAAS;YAAM;QAAK;QAEnD,MAAM,gBAAgB,QAAQ,GAAG,CAAC,qBAAqB;QAEvD,IAAI,CAAC,eAAe;YAClB,MAAM,IAAI,MAAM;QAClB;QAEA,MAAM,WAAW,MAAM,OAAO,YAAY,CAAC,MAAM,CAAC,MAAM,CAAC;YACvD;YACA,OAAO,GAAG,UAAU,IAAI,CAAC;YACzB,kBAAkB;YAClB,aAAa;gBACX,QAAQ;oBAAC;iBAAQ;YACnB;QACF;QAEA,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,uCAAuC;QACrD,MAAM,IAAI,MAAM;IAClB;AACF;AAGO,eAAe,WAAW,SAAiB,EAAE,KAAa,EAAE,OAAc;IAC/E,IAAI;QACF,MAAM,OAAO;QACb,MAAM,SAAS,mJAAA,CAAA,SAAM,CAAC,MAAM,CAAC;YAAE,SAAS;YAAM;QAAK;QAEnD,MAAM,gBAAgB,QAAQ,GAAG,CAAC,qBAAqB;QAEvD,IAAI,CAAC,eAAe;YAClB,MAAM,IAAI,MAAM;QAClB;QAEA,MAAM,WAAW,MAAM,OAAO,YAAY,CAAC,MAAM,CAAC,MAAM,CAAC;YACvD;YACA,OAAO,GAAG,UAAU,CAAC,EAAE,OAAO;YAC9B,kBAAkB;YAClB,aAAa;gBACX,QAAQ;oBAAC;iBAAQ;YACnB;QACF;QAEA,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,wCAAwC;QACtD,MAAM,IAAI,MAAM;IAClB;AACF;AAGO,eAAe,oBAAoB,UAAkB,EAAE,QAAgB;IAC5E,IAAI;QACF,2DAA2D;QAC3D,MAAM,gBAAgB,QAAQ,GAAG,CAAC,qBAAqB;QAEvD,IAAI,CAAC,eAAe;YAClB,QAAQ,GAAG,CAAC,CAAC,+BAA+B,EAAE,WAAW,aAAa,EAAE,UAAU;YAClF,OAAO;gBAAE,SAAS;gBAAM,SAAS;YAAmC;QACtE;QAEA,MAAM,OAAO;QACb,MAAM,SAAS,mJAAA,CAAA,SAAM,CAAC,MAAM,CAAC;YAAE,SAAS;YAAM;QAAK;QAEnD,mEAAmE;QACnE,MAAM,UAAU,MAAM,OAAO,YAAY,CAAC,MAAM,CAAC,GAAG,CAAC;YACnD;YACA,OAAO;QACT;QAEA,MAAM,OAAO,QAAQ,IAAI,CAAC,MAAM,IAAI,EAAE;QACtC,IAAI,KAAK,MAAM,KAAK,GAAG;YACrB,MAAM,IAAI,MAAM;QAClB;QAEA,sCAAsC;QACtC,MAAM,UAAU,IAAI,CAAC,EAAE;QACvB,MAAM,gBAAgB,QAAQ,SAAS,CAAC,CAAC,SACvC,OAAO,WAAW,GAAG,QAAQ,CAAC;QAEhC,MAAM,mBAAmB,QAAQ,SAAS,CAAC,CAAC,SAC1C,OAAO,WAAW,GAAG,QAAQ,CAAC,YAAY,OAAO,WAAW,GAAG,QAAQ,CAAC;QAG1E,IAAI,kBAAkB,CAAC,KAAK,qBAAqB,CAAC,GAAG;YACnD,MAAM,IAAI,MAAM;QAClB;QAEA,gCAAgC;QAChC,IAAI,iBAAiB,CAAC;QACtB,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,MAAM,EAAE,IAAK;YACpC,IAAI,IAAI,CAAC,EAAE,CAAC,cAAc,KAAK,YAAY;gBACzC,iBAAiB,IAAI,GAAG,8CAA8C;gBACtE;YACF;QACF;QAEA,IAAI,mBAAmB,CAAC,GAAG;YACzB,MAAM,IAAI,MAAM,CAAC,eAAe,EAAE,WAAW,eAAe,CAAC;QAC/D;QAEA,sCAAsC;QACtC,MAAM,cAAc,OAAO,YAAY,CAAC,KAAK,mBAAmB,0CAA0C;QAC1G,MAAM,QAAQ,GAAG,cAAc,gBAAgB;QAE/C,MAAM,WAAW,MAAM,OAAO,YAAY,CAAC,MAAM,CAAC,MAAM,CAAC;YACvD;YACA,OAAO,CAAC,WAAW,EAAE,OAAO;YAC5B,kBAAkB;YAClB,aAAa;gBACX,QAAQ;oBAAC;wBAAC;qBAAS;iBAAC;YACtB;QACF;QAEA,OAAO;YAAE,SAAS;YAAM,MAAM,SAAS,IAAI;QAAC;IAC9C,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,yCAAyC;QACvD,MAAM,IAAI,MAAM;IAClB;AACF;;;IApJsB;IAwBA;IA4BA;IA4BA;;AAhFA,+OAAA;AAwBA,+OAAA;AA4BA,+OAAA;AA4BA,+OAAA", "debugId": null}}, {"offset": {"line": 305, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/AI_Projects/CRMcriadores/crm-interno/.next-internal/server/app/%28dashboard%29/influencers/page/actions.js%20%28server%20actions%20loader%29"], "sourcesContent": ["export {getData as '40416fe263e60966356356bb294165af55bf27c98c'} from 'ACTIONS_MODULE0'\nexport {appendData as '60b2397e35e4d22eb574fd297096247fb0b426b814'} from 'ACTIONS_MODULE0'\nexport {updateBusinessStage as '60c2e3b2963d1dbcaed9ada159154eae380101e11a'} from 'ACTIONS_MODULE0'\nexport {updateData as '700091b6dbbe7aa4a45146966c103d91060aef6152'} from 'ACTIONS_MODULE0'\n"], "names": [], "mappings": ";AAAA", "debugId": null}}, {"offset": {"line": 372, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/AI_Projects/CRMcriadores/crm-interno/lib/utils.ts"], "sourcesContent": ["/**\n * Transforma dados de array de arrays (formato do Google Sheets) \n * em array de objetos JSON usando a primeira linha como cabeçalhos\n */\nexport function transformData(data: any[][]): Record<string, any>[] {\n  if (!data || data.length === 0) {\n    return [];\n  }\n\n  // A primeira linha contém os cabeçalhos\n  const headers = data[0];\n  \n  // As linhas restantes contêm os dados\n  const rows = data.slice(1);\n\n  return rows.map((row) => {\n    const obj: Record<string, any> = {};\n    \n    headers.forEach((header, index) => {\n      // Usa o cabeçalho como chave e o valor da linha correspondente\n      obj[header] = row[index] || '';\n    });\n\n    return obj;\n  });\n}\n\n/**\n * Converte um objeto em array de valores na ordem dos cabeçalhos fornecidos\n */\nexport function objectToRowData(obj: Record<string, any>, headers: string[]): any[] {\n  return headers.map(header => obj[header] || '');\n}\n\n/**\n * Valida se os dados têm a estrutura esperada\n */\nexport function validateSheetData(data: any[][]): boolean {\n  return Array.isArray(data) && data.length > 0 && Array.isArray(data[0]);\n}\n\n/**\n * Limpa e normaliza strings vindas do Google Sheets\n */\nexport function cleanSheetValue(value: any): string {\n  if (value === null || value === undefined) {\n    return '';\n  }\n  \n  return String(value).trim();\n}\n\n/**\n * Converte valores de string para tipos apropriados\n */\nexport function parseSheetValue(value: string, type: 'string' | 'number' | 'boolean' | 'date' = 'string'): any {\n  const cleanValue = cleanSheetValue(value);\n  \n  if (cleanValue === '') {\n    return type === 'number' ? 0 : type === 'boolean' ? false : '';\n  }\n\n  switch (type) {\n    case 'number':\n      const num = parseFloat(cleanValue);\n      return isNaN(num) ? 0 : num;\n    \n    case 'boolean':\n      return cleanValue.toLowerCase() === 'true' || cleanValue === '1';\n    \n    case 'date':\n      const date = new Date(cleanValue);\n      return isNaN(date.getTime()) ? null : date;\n    \n    default:\n      return cleanValue;\n  }\n}\n\n/**\n * Formata dados para exibição\n */\nexport function formatDisplayValue(value: any, type: 'currency' | 'percentage' | 'date' | 'number' | 'text' = 'text'): string {\n  if (value === null || value === undefined || value === '') {\n    return '-';\n  }\n\n  switch (type) {\n    case 'currency':\n      const numValue = typeof value === 'number' ? value : parseFloat(value);\n      return isNaN(numValue) ? '-' : new Intl.NumberFormat('pt-BR', {\n        style: 'currency',\n        currency: 'BRL'\n      }).format(numValue);\n    \n    case 'percentage':\n      const pctValue = typeof value === 'number' ? value : parseFloat(value);\n      return isNaN(pctValue) ? '-' : `${pctValue.toFixed(1)}%`;\n    \n    case 'date':\n      const date = value instanceof Date ? value : new Date(value);\n      return isNaN(date.getTime()) ? '-' : date.toLocaleDateString('pt-BR');\n    \n    case 'number':\n      const numberValue = typeof value === 'number' ? value : parseFloat(value);\n      return isNaN(numberValue) ? '-' : new Intl.NumberFormat('pt-BR').format(numberValue);\n    \n    default:\n      return String(value);\n  }\n}\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;;;;AACM,SAAS,cAAc,IAAa;IACzC,IAAI,CAAC,QAAQ,KAAK,MAAM,KAAK,GAAG;QAC9B,OAAO,EAAE;IACX;IAEA,wCAAwC;IACxC,MAAM,UAAU,IAAI,CAAC,EAAE;IAEvB,sCAAsC;IACtC,MAAM,OAAO,KAAK,KAAK,CAAC;IAExB,OAAO,KAAK,GAAG,CAAC,CAAC;QACf,MAAM,MAA2B,CAAC;QAElC,QAAQ,OAAO,CAAC,CAAC,QAAQ;YACvB,+DAA+D;YAC/D,GAAG,CAAC,OAAO,GAAG,GAAG,CAAC,MAAM,IAAI;QAC9B;QAEA,OAAO;IACT;AACF;AAKO,SAAS,gBAAgB,GAAwB,EAAE,OAAiB;IACzE,OAAO,QAAQ,GAAG,CAAC,CAAA,SAAU,GAAG,CAAC,OAAO,IAAI;AAC9C;AAKO,SAAS,kBAAkB,IAAa;IAC7C,OAAO,MAAM,OAAO,CAAC,SAAS,KAAK,MAAM,GAAG,KAAK,MAAM,OAAO,CAAC,IAAI,CAAC,EAAE;AACxE;AAKO,SAAS,gBAAgB,KAAU;IACxC,IAAI,UAAU,QAAQ,UAAU,WAAW;QACzC,OAAO;IACT;IAEA,OAAO,OAAO,OAAO,IAAI;AAC3B;AAKO,SAAS,gBAAgB,KAAa,EAAE,OAAiD,QAAQ;IACtG,MAAM,aAAa,gBAAgB;IAEnC,IAAI,eAAe,IAAI;QACrB,OAAO,SAAS,WAAW,IAAI,SAAS,YAAY,QAAQ;IAC9D;IAEA,OAAQ;QACN,KAAK;YACH,MAAM,MAAM,WAAW;YACvB,OAAO,MAAM,OAAO,IAAI;QAE1B,KAAK;YACH,OAAO,WAAW,WAAW,OAAO,UAAU,eAAe;QAE/D,KAAK;YACH,MAAM,OAAO,IAAI,KAAK;YACtB,OAAO,MAAM,KAAK,OAAO,MAAM,OAAO;QAExC;YACE,OAAO;IACX;AACF;AAKO,SAAS,mBAAmB,KAAU,EAAE,OAA+D,MAAM;IAClH,IAAI,UAAU,QAAQ,UAAU,aAAa,UAAU,IAAI;QACzD,OAAO;IACT;IAEA,OAAQ;QACN,KAAK;YACH,MAAM,WAAW,OAAO,UAAU,WAAW,QAAQ,WAAW;YAChE,OAAO,MAAM,YAAY,MAAM,IAAI,KAAK,YAAY,CAAC,SAAS;gBAC5D,OAAO;gBACP,UAAU;YACZ,GAAG,MAAM,CAAC;QAEZ,KAAK;YACH,MAAM,WAAW,OAAO,UAAU,WAAW,QAAQ,WAAW;YAChE,OAAO,MAAM,YAAY,MAAM,GAAG,SAAS,OAAO,CAAC,GAAG,CAAC,CAAC;QAE1D,KAAK;YACH,MAAM,OAAO,iBAAiB,OAAO,QAAQ,IAAI,KAAK;YACtD,OAAO,MAAM,KAAK,OAAO,MAAM,MAAM,KAAK,kBAAkB,CAAC;QAE/D,KAAK;YACH,MAAM,cAAc,OAAO,UAAU,WAAW,QAAQ,WAAW;YACnE,OAAO,MAAM,eAAe,MAAM,IAAI,KAAK,YAAY,CAAC,SAAS,MAAM,CAAC;QAE1E;YACE,OAAO,OAAO;IAClB;AACF", "debugId": null}}, {"offset": {"line": 460, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/AI_Projects/CRMcriadores/crm-interno/components/CreatorCard.tsx"], "sourcesContent": ["import React from 'react';\nimport Image from 'next/image';\n\ninterface CreatorCardProps {\n  avatarUrl: string;\n  name: string;\n  username: string;\n  followers: number;\n  engagementRate: number;\n  businessName?: string;\n}\n\nexport default function CreatorCard({\n  avatarUrl,\n  name,\n  username,\n  followers,\n  engagementRate,\n  businessName\n}: CreatorCardProps) {\n  // Função para formatar número de seguidores\n  const formatFollowers = (count: number): string => {\n    if (count >= 1000000) {\n      return `${(count / 1000000).toFixed(1)}M`;\n    } else if (count >= 1000) {\n      return `${(count / 1000).toFixed(1)}K`;\n    }\n    return count.toString();\n  };\n\n  return (\n    <div className=\"card-elevated p-6 hover:shadow-lg transition-all duration-200\">\n      {/* Avatar e informações principais */}\n      <div className=\"flex flex-col items-center text-center mb-4\">\n        <div className=\"relative w-16 h-16 mb-3\">\n          <Image\n            src={avatarUrl || '/placeholder-avatar.svg'}\n            alt={`Avatar de ${name}`}\n            fill\n            className=\"rounded-full object-cover\"\n            sizes=\"64px\"\n          />\n        </div>\n\n        <h3 className=\"text-lg font-semibold text-on-surface mb-1\">\n          {name}\n        </h3>\n\n        <p className=\"text-sm text-on-surface-variant mb-1\">\n          @{username}\n        </p>\n\n        {businessName && (\n          <p className=\"text-xs text-primary mb-3 font-medium\">\n            🏢 {businessName}\n          </p>\n        )}\n      </div>\n\n      {/* Métricas */}\n      <div className=\"grid grid-cols-2 gap-4 mb-4\">\n        <div className=\"text-center bg-surface-container rounded-lg p-3\">\n          <div className=\"text-xl font-bold text-primary mb-1\">\n            {formatFollowers(followers)}\n          </div>\n          <div className=\"text-xs text-on-surface-variant uppercase tracking-wide\">\n            Seguidores\n          </div>\n        </div>\n\n        <div className=\"text-center bg-surface-container rounded-lg p-3\">\n          <div className=\"text-xl font-bold text-secondary mb-1\">\n            {engagementRate.toFixed(1)}%\n          </div>\n          <div className=\"text-xs text-on-surface-variant uppercase tracking-wide\">\n            Engajamento\n          </div>\n        </div>\n      </div>\n\n      {/* Indicador de status de engajamento */}\n      <div className=\"flex items-center justify-center\">\n        <div\n          className={`w-2 h-2 rounded-full mr-2 ${\n            engagementRate >= 5\n              ? 'bg-green-500'\n              : engagementRate >= 2\n              ? 'bg-yellow-500'\n              : 'bg-red-500'\n          }`}\n        />\n        <span className=\"text-xs text-on-surface-variant\">\n          {engagementRate >= 5\n            ? 'Alto engajamento'\n            : engagementRate >= 2\n            ? 'Engajamento médio'\n            : 'Baixo engajamento'\n          }\n        </span>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AACA;;;AAWe,SAAS,YAAY,EAClC,SAAS,EACT,IAAI,EACJ,QAAQ,EACR,SAAS,EACT,cAAc,EACd,YAAY,EACK;IACjB,4CAA4C;IAC5C,MAAM,kBAAkB,CAAC;QACvB,IAAI,SAAS,SAAS;YACpB,OAAO,GAAG,CAAC,QAAQ,OAAO,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC;QAC3C,OAAO,IAAI,SAAS,MAAM;YACxB,OAAO,GAAG,CAAC,QAAQ,IAAI,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC;QACxC;QACA,OAAO,MAAM,QAAQ;IACvB;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,6HAAA,CAAA,UAAK;4BACJ,KAAK,aAAa;4BAClB,KAAK,CAAC,UAAU,EAAE,MAAM;4BACxB,IAAI;4BACJ,WAAU;4BACV,OAAM;;;;;;;;;;;kCAIV,8OAAC;wBAAG,WAAU;kCACX;;;;;;kCAGH,8OAAC;wBAAE,WAAU;;4BAAuC;4BAChD;;;;;;;oBAGH,8BACC,8OAAC;wBAAE,WAAU;;4BAAwC;4BAC/C;;;;;;;;;;;;;0BAMV,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACZ,gBAAgB;;;;;;0CAEnB,8OAAC;gCAAI,WAAU;0CAA0D;;;;;;;;;;;;kCAK3E,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;oCACZ,eAAe,OAAO,CAAC;oCAAG;;;;;;;0CAE7B,8OAAC;gCAAI,WAAU;0CAA0D;;;;;;;;;;;;;;;;;;0BAO7E,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBACC,WAAW,CAAC,0BAA0B,EACpC,kBAAkB,IACd,iBACA,kBAAkB,IAClB,kBACA,cACJ;;;;;;kCAEJ,8OAAC;wBAAK,WAAU;kCACb,kBAAkB,IACf,qBACA,kBAAkB,IAClB,sBACA;;;;;;;;;;;;;;;;;;AAMd", "debugId": null}}, {"offset": {"line": 636, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/AI_Projects/CRMcriadores/crm-interno/app/%28dashboard%29/influencers/page.tsx"], "sourcesContent": ["import React from 'react';\nimport { getData } from '@/app/actions/sheetsActions';\nimport { transformData } from '@/lib/utils';\nimport CreatorCard from '@/components/CreatorCard';\n\n// Dados de criadores extraídos dos negócios (integração completa)\nconst getAllCreators = () => {\n  const mockBusinesses = [\n    {\n      creators: [\n        { name: '<PERSON>', username: 'an<PERSON><PERSON><PERSON>', followers: 125000, engagementRate: 4.2, businessName: 'Loja de Roupas Fashion' },\n        { name: '<PERSON>', username: 'carlossantos', followers: 89000, engagementRate: 6.8, businessName: '<PERSON>ja de Roupas Fashion' }\n      ]\n    },\n    {\n      creators: [\n        { name: '<PERSON>', username: 'ma<PERSON><PERSON><PERSON><PERSON>', followers: 234000, engagementRate: 3.1, businessName: 'Restaurante Gourmet' }\n      ]\n    },\n    {\n      creators: [\n        { name: '<PERSON>', username: 'joaofitness', followers: 156000, engagementRate: 5.4, businessName: 'Academia Fitness Plus' },\n        { name: '<PERSON>', username: 'carlastrong', followers: 98000, engagementRate: 7.2, businessName: 'Academia Fitness Plus' },\n        { name: '<PERSON>', username: 'pedromuscle', followers: 67000, engagementRate: 4.8, businessName: 'Academia Fitness Plus' }\n      ]\n    },\n    {\n      creators: [\n        { name: 'Bella Beauty', username: 'bellabeauty', followers: 189000, engagementRate: 6.1, businessName: 'Clínica de Estética' }\n      ]\n    },\n    {\n      creators: [\n        { name: 'Tech Master', username: 'techmaster', followers: 145000, engagementRate: 5.9, businessName: 'Loja de Eletrônicos' },\n        { name: 'Gamer Pro', username: 'gamerpro', followers: 203000, engagementRate: 4.5, businessName: 'Loja de Eletrônicos' }\n      ]\n    }\n  ];\n\n  // Extrair todos os criadores de todos os negócios\n  const allCreators = mockBusinesses.flatMap(business =>\n    business.creators.map(creator => ({\n      ...creator,\n      avatarUrl: '/placeholder-avatar.svg'\n    }))\n  );\n\n  return allCreators;\n};\n\nconst mockCreators = getAllCreators();\n\nexport default async function CreatorsPage() {\n  let creators = mockCreators;\n\n  // Tenta buscar dados do Google Sheets, mas usa dados mock se falhar\n  try {\n    const rawData = await getData('Creators');\n    if (rawData && rawData.length > 0) {\n      const transformedData = transformData(rawData);\n\n      // Mapeia os dados transformados para o formato esperado pelo componente\n      creators = transformedData.map((item: any) => ({\n        avatarUrl: item.avatarUrl || '/placeholder-avatar.svg',\n        name: item.name || item.Nome || 'Nome não informado',\n        username: item.username || item.Username || 'username',\n        followers: parseInt(item.followers || item.Seguidores || '0'),\n        engagementRate: parseFloat(item.engagementRate || item.Engajamento || '0'),\n        businessName: item.businessName || item.Negocio || ''\n      }));\n    }\n  } catch (error) {\n    console.log('Usando dados de exemplo - Google Sheets não configurado ainda');\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Stats Cards */}\n      <div className=\"grid grid-cols-1 md:grid-cols-4 gap-4\">\n        <div className=\"card-elevated p-4\">\n          <div className=\"flex items-center justify-between\">\n            <div>\n              <p className=\"text-sm text-on-surface-variant\">Total</p>\n              <p className=\"text-2xl font-bold text-on-surface\">{creators.length}</p>\n            </div>\n            <div className=\"text-2xl\">👥</div>\n          </div>\n        </div>\n\n        <div className=\"card-elevated p-4\">\n          <div className=\"flex items-center justify-between\">\n            <div>\n              <p className=\"text-sm text-on-surface-variant\">Alto Engajamento</p>\n              <p className=\"text-2xl font-bold text-green-600\">\n                {creators.filter(i => i.engagementRate >= 5).length}\n              </p>\n            </div>\n            <div className=\"text-2xl\">🔥</div>\n          </div>\n        </div>\n\n        <div className=\"card-elevated p-4\">\n          <div className=\"flex items-center justify-between\">\n            <div>\n              <p className=\"text-sm text-on-surface-variant\">Seguidores Totais</p>\n              <p className=\"text-2xl font-bold text-primary\">\n                {(creators.reduce((acc, i) => acc + i.followers, 0) / 1000000).toFixed(1)}M\n              </p>\n            </div>\n            <div className=\"text-2xl\">📊</div>\n          </div>\n        </div>\n\n        <div className=\"card-elevated p-4\">\n          <div className=\"flex items-center justify-between\">\n            <div>\n              <p className=\"text-sm text-on-surface-variant\">Engajamento Médio</p>\n              <p className=\"text-2xl font-bold text-secondary\">\n                {(creators.reduce((acc, i) => acc + i.engagementRate, 0) / creators.length || 0).toFixed(1)}%\n              </p>\n            </div>\n            <div className=\"text-2xl\">⚡</div>\n          </div>\n        </div>\n      </div>\n\n      {/* Creators Grid */}\n      <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\">\n        {creators.map((creator, index) => (\n          <CreatorCard\n            key={index}\n            avatarUrl={creator.avatarUrl}\n            name={creator.name}\n            username={creator.username}\n            followers={creator.followers}\n            engagementRate={creator.engagementRate}\n            businessName={creator.businessName}\n          />\n        ))}\n      </div>\n\n      {creators.length === 0 && (\n        <div className=\"text-center py-12\">\n          <div className=\"text-6xl mb-4\">👥</div>\n          <h3 className=\"text-xl font-medium text-on-surface mb-2\">\n            Nenhum criador encontrado\n          </h3>\n          <p className=\"text-on-surface-variant mb-6\">\n            Configure o Google Sheets para ver os dados dos criadores.\n          </p>\n          <button className=\"btn-primary\">\n            <span className=\"mr-2\">➕</span>\n            Adicionar Primeiro Criador\n          </button>\n        </div>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;;;;;AAEA,kEAAkE;AAClE,MAAM,iBAAiB;IACrB,MAAM,iBAAiB;QACrB;YACE,UAAU;gBACR;oBAAE,MAAM;oBAAa,UAAU;oBAAY,WAAW;oBAAQ,gBAAgB;oBAAK,cAAc;gBAAyB;gBAC1H;oBAAE,MAAM;oBAAiB,UAAU;oBAAgB,WAAW;oBAAO,gBAAgB;oBAAK,cAAc;gBAAyB;aAClI;QACH;QACA;YACE,UAAU;gBACR;oBAAE,MAAM;oBAAkB,UAAU;oBAAiB,WAAW;oBAAQ,gBAAgB;oBAAK,cAAc;gBAAsB;aAClI;QACH;QACA;YACE,UAAU;gBACR;oBAAE,MAAM;oBAAgB,UAAU;oBAAe,WAAW;oBAAQ,gBAAgB;oBAAK,cAAc;gBAAwB;gBAC/H;oBAAE,MAAM;oBAAgB,UAAU;oBAAe,WAAW;oBAAO,gBAAgB;oBAAK,cAAc;gBAAwB;gBAC9H;oBAAE,MAAM;oBAAgB,UAAU;oBAAe,WAAW;oBAAO,gBAAgB;oBAAK,cAAc;gBAAwB;aAC/H;QACH;QACA;YACE,UAAU;gBACR;oBAAE,MAAM;oBAAgB,UAAU;oBAAe,WAAW;oBAAQ,gBAAgB;oBAAK,cAAc;gBAAsB;aAC9H;QACH;QACA;YACE,UAAU;gBACR;oBAAE,MAAM;oBAAe,UAAU;oBAAc,WAAW;oBAAQ,gBAAgB;oBAAK,cAAc;gBAAsB;gBAC3H;oBAAE,MAAM;oBAAa,UAAU;oBAAY,WAAW;oBAAQ,gBAAgB;oBAAK,cAAc;gBAAsB;aACxH;QACH;KACD;IAED,kDAAkD;IAClD,MAAM,cAAc,eAAe,OAAO,CAAC,CAAA,WACzC,SAAS,QAAQ,CAAC,GAAG,CAAC,CAAA,UAAW,CAAC;gBAChC,GAAG,OAAO;gBACV,WAAW;YACb,CAAC;IAGH,OAAO;AACT;AAEA,MAAM,eAAe;AAEN,eAAe;IAC5B,IAAI,WAAW;IAEf,oEAAoE;IACpE,IAAI;QACF,MAAM,UAAU,MAAM,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD,EAAE;QAC9B,IAAI,WAAW,QAAQ,MAAM,GAAG,GAAG;YACjC,MAAM,kBAAkB,CAAA,GAAA,4GAAA,CAAA,gBAAa,AAAD,EAAE;YAEtC,wEAAwE;YACxE,WAAW,gBAAgB,GAAG,CAAC,CAAC,OAAc,CAAC;oBAC7C,WAAW,KAAK,SAAS,IAAI;oBAC7B,MAAM,KAAK,IAAI,IAAI,KAAK,IAAI,IAAI;oBAChC,UAAU,KAAK,QAAQ,IAAI,KAAK,QAAQ,IAAI;oBAC5C,WAAW,SAAS,KAAK,SAAS,IAAI,KAAK,UAAU,IAAI;oBACzD,gBAAgB,WAAW,KAAK,cAAc,IAAI,KAAK,WAAW,IAAI;oBACtE,cAAc,KAAK,YAAY,IAAI,KAAK,OAAO,IAAI;gBACrD,CAAC;QACH;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,GAAG,CAAC;IACd;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;sDACC,8OAAC;4CAAE,WAAU;sDAAkC;;;;;;sDAC/C,8OAAC;4CAAE,WAAU;sDAAsC,SAAS,MAAM;;;;;;;;;;;;8CAEpE,8OAAC;oCAAI,WAAU;8CAAW;;;;;;;;;;;;;;;;;kCAI9B,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;sDACC,8OAAC;4CAAE,WAAU;sDAAkC;;;;;;sDAC/C,8OAAC;4CAAE,WAAU;sDACV,SAAS,MAAM,CAAC,CAAA,IAAK,EAAE,cAAc,IAAI,GAAG,MAAM;;;;;;;;;;;;8CAGvD,8OAAC;oCAAI,WAAU;8CAAW;;;;;;;;;;;;;;;;;kCAI9B,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;sDACC,8OAAC;4CAAE,WAAU;sDAAkC;;;;;;sDAC/C,8OAAC;4CAAE,WAAU;;gDACV,CAAC,SAAS,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,EAAE,SAAS,EAAE,KAAK,OAAO,EAAE,OAAO,CAAC;gDAAG;;;;;;;;;;;;;8CAG9E,8OAAC;oCAAI,WAAU;8CAAW;;;;;;;;;;;;;;;;;kCAI9B,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;sDACC,8OAAC;4CAAE,WAAU;sDAAkC;;;;;;sDAC/C,8OAAC;4CAAE,WAAU;;gDACV,CAAC,SAAS,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,EAAE,cAAc,EAAE,KAAK,SAAS,MAAM,IAAI,CAAC,EAAE,OAAO,CAAC;gDAAG;;;;;;;;;;;;;8CAGhG,8OAAC;oCAAI,WAAU;8CAAW;;;;;;;;;;;;;;;;;;;;;;;0BAMhC,8OAAC;gBAAI,WAAU;0BACZ,SAAS,GAAG,CAAC,CAAC,SAAS,sBACtB,8OAAC,0HAAA,CAAA,UAAW;wBAEV,WAAW,QAAQ,SAAS;wBAC5B,MAAM,QAAQ,IAAI;wBAClB,UAAU,QAAQ,QAAQ;wBAC1B,WAAW,QAAQ,SAAS;wBAC5B,gBAAgB,QAAQ,cAAc;wBACtC,cAAc,QAAQ,YAAY;uBAN7B;;;;;;;;;;YAWV,SAAS,MAAM,KAAK,mBACnB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCAAgB;;;;;;kCAC/B,8OAAC;wBAAG,WAAU;kCAA2C;;;;;;kCAGzD,8OAAC;wBAAE,WAAU;kCAA+B;;;;;;kCAG5C,8OAAC;wBAAO,WAAU;;0CAChB,8OAAC;gCAAK,WAAU;0CAAO;;;;;;4BAAQ;;;;;;;;;;;;;;;;;;;AAO3C", "debugId": null}}]}
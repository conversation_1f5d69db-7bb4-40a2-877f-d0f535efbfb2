{"version": 3, "sources": [], "sections": [{"offset": {"line": 111, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/AI_Projects/CRMcriadores/crm-interno/app/actions/sheetsActions.ts"], "sourcesContent": ["'use server';\n\nimport { google } from 'googleapis';\n\n// Configuração da autenticação\nconst getGoogleSheetsAuth = () => {\n  const credentials = {\n    type: 'service_account',\n    project_id: process.env.GOOGLE_PROJECT_ID,\n    private_key_id: process.env.GOOGLE_PRIVATE_KEY_ID,\n    private_key: process.env.GOOGLE_PRIVATE_KEY?.replace(/\\\\n/g, '\\n'),\n    client_email: process.env.GOOGLE_CLIENT_EMAIL,\n    client_id: process.env.GOOGLE_CLIENT_ID,\n    auth_uri: 'https://accounts.google.com/o/oauth2/auth',\n    token_uri: 'https://oauth2.googleapis.com/token',\n    auth_provider_x509_cert_url: 'https://www.googleapis.com/oauth2/v1/certs',\n    client_x509_cert_url: `https://www.googleapis.com/robot/v1/metadata/x509/${process.env.GOOGLE_CLIENT_EMAIL}`,\n  };\n\n  const auth = new google.auth.GoogleAuth({\n    credentials,\n    scopes: ['https://www.googleapis.com/auth/spreadsheets'],\n  });\n\n  return auth;\n};\n\n// Função para ler dados da planilha\nexport async function getData(sheetName: string) {\n  try {\n    const auth = getGoogleSheetsAuth();\n    const sheets = google.sheets({ version: 'v4', auth });\n    \n    const spreadsheetId = process.env.GOOGLE_SPREADSHEET_ID;\n    \n    if (!spreadsheetId) {\n      throw new Error('GOOGLE_SPREADSHEET_ID não está configurado');\n    }\n\n    const response = await sheets.spreadsheets.values.get({\n      spreadsheetId,\n      range: `${sheetName}!A:Z`, // Lê todas as colunas de A até Z\n    });\n\n    return response.data.values || [];\n  } catch (error) {\n    console.error('Erro ao ler dados da planilha:', error);\n    throw new Error('Falha ao ler dados da planilha');\n  }\n}\n\n// Função para adicionar dados à planilha\nexport async function appendData(sheetName: string, rowData: any[]) {\n  try {\n    const auth = getGoogleSheetsAuth();\n    const sheets = google.sheets({ version: 'v4', auth });\n    \n    const spreadsheetId = process.env.GOOGLE_SPREADSHEET_ID;\n    \n    if (!spreadsheetId) {\n      throw new Error('GOOGLE_SPREADSHEET_ID não está configurado');\n    }\n\n    const response = await sheets.spreadsheets.values.append({\n      spreadsheetId,\n      range: `${sheetName}!A:Z`,\n      valueInputOption: 'USER_ENTERED',\n      requestBody: {\n        values: [rowData],\n      },\n    });\n\n    return response.data;\n  } catch (error) {\n    console.error('Erro ao adicionar dados à planilha:', error);\n    throw new Error('Falha ao adicionar dados à planilha');\n  }\n}\n\n// Função para atualizar dados na planilha\nexport async function updateData(sheetName: string, range: string, rowData: any[]) {\n  try {\n    const auth = getGoogleSheetsAuth();\n    const sheets = google.sheets({ version: 'v4', auth });\n    \n    const spreadsheetId = process.env.GOOGLE_SPREADSHEET_ID;\n    \n    if (!spreadsheetId) {\n      throw new Error('GOOGLE_SPREADSHEET_ID não está configurado');\n    }\n\n    const response = await sheets.spreadsheets.values.update({\n      spreadsheetId,\n      range: `${sheetName}!${range}`,\n      valueInputOption: 'USER_ENTERED',\n      requestBody: {\n        values: [rowData],\n      },\n    });\n\n    return response.data;\n  } catch (error) {\n    console.error('Erro ao atualizar dados da planilha:', error);\n    throw new Error('Falha ao atualizar dados da planilha');\n  }\n}\n"], "names": [], "mappings": ";;;;;;;AAEA;;;;;AAEA,+BAA+B;AAC/B,MAAM,sBAAsB;IAC1B,MAAM,cAAc;QAClB,MAAM;QACN,YAAY,QAAQ,GAAG,CAAC,iBAAiB;QACzC,gBAAgB,QAAQ,GAAG,CAAC,qBAAqB;QACjD,aAAa,QAAQ,GAAG,CAAC,kBAAkB,EAAE,QAAQ,QAAQ;QAC7D,cAAc,QAAQ,GAAG,CAAC,mBAAmB;QAC7C,WAAW,QAAQ,GAAG,CAAC,gBAAgB;QACvC,UAAU;QACV,WAAW;QACX,6BAA6B;QAC7B,sBAAsB,CAAC,kDAAkD,EAAE,QAAQ,GAAG,CAAC,mBAAmB,EAAE;IAC9G;IAEA,MAAM,OAAO,IAAI,mJAAA,CAAA,SAAM,CAAC,IAAI,CAAC,UAAU,CAAC;QACtC;QACA,QAAQ;YAAC;SAA+C;IAC1D;IAEA,OAAO;AACT;AAGO,eAAe,QAAQ,SAAiB;IAC7C,IAAI;QACF,MAAM,OAAO;QACb,MAAM,SAAS,mJAAA,CAAA,SAAM,CAAC,MAAM,CAAC;YAAE,SAAS;YAAM;QAAK;QAEnD,MAAM,gBAAgB,QAAQ,GAAG,CAAC,qBAAqB;QAEvD,IAAI,CAAC,eAAe;YAClB,MAAM,IAAI,MAAM;QAClB;QAEA,MAAM,WAAW,MAAM,OAAO,YAAY,CAAC,MAAM,CAAC,GAAG,CAAC;YACpD;YACA,OAAO,GAAG,UAAU,IAAI,CAAC;QAC3B;QAEA,OAAO,SAAS,IAAI,CAAC,MAAM,IAAI,EAAE;IACnC,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,kCAAkC;QAChD,MAAM,IAAI,MAAM;IAClB;AACF;AAGO,eAAe,WAAW,SAAiB,EAAE,OAAc;IAChE,IAAI;QACF,MAAM,OAAO;QACb,MAAM,SAAS,mJAAA,CAAA,SAAM,CAAC,MAAM,CAAC;YAAE,SAAS;YAAM;QAAK;QAEnD,MAAM,gBAAgB,QAAQ,GAAG,CAAC,qBAAqB;QAEvD,IAAI,CAAC,eAAe;YAClB,MAAM,IAAI,MAAM;QAClB;QAEA,MAAM,WAAW,MAAM,OAAO,YAAY,CAAC,MAAM,CAAC,MAAM,CAAC;YACvD;YACA,OAAO,GAAG,UAAU,IAAI,CAAC;YACzB,kBAAkB;YAClB,aAAa;gBACX,QAAQ;oBAAC;iBAAQ;YACnB;QACF;QAEA,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,uCAAuC;QACrD,MAAM,IAAI,MAAM;IAClB;AACF;AAGO,eAAe,WAAW,SAAiB,EAAE,KAAa,EAAE,OAAc;IAC/E,IAAI;QACF,MAAM,OAAO;QACb,MAAM,SAAS,mJAAA,CAAA,SAAM,CAAC,MAAM,CAAC;YAAE,SAAS;YAAM;QAAK;QAEnD,MAAM,gBAAgB,QAAQ,GAAG,CAAC,qBAAqB;QAEvD,IAAI,CAAC,eAAe;YAClB,MAAM,IAAI,MAAM;QAClB;QAEA,MAAM,WAAW,MAAM,OAAO,YAAY,CAAC,MAAM,CAAC,MAAM,CAAC;YACvD;YACA,OAAO,GAAG,UAAU,CAAC,EAAE,OAAO;YAC9B,kBAAkB;YAClB,aAAa;gBACX,QAAQ;oBAAC;iBAAQ;YACnB;QACF;QAEA,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,wCAAwC;QACtD,MAAM,IAAI,MAAM;IAClB;AACF;;;IA7EsB;IAwBA;IA4BA;;AApDA,+OAAA;AAwBA,+OAAA;AA4BA,+OAAA", "debugId": null}}, {"offset": {"line": 235, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/AI_Projects/CRMcriadores/crm-interno/.next-internal/server/app/%28dashboard%29/jornada/page/actions.js%20%28server%20actions%20loader%29"], "sourcesContent": ["export {getData as '40416fe263e60966356356bb294165af55bf27c98c'} from 'ACTIONS_MODULE0'\nexport {appendData as '60b2397e35e4d22eb574fd297096247fb0b426b814'} from 'ACTIONS_MODULE0'\nexport {updateData as '700091b6dbbe7aa4a45146966c103d91060aef6152'} from 'ACTIONS_MODULE0'\n"], "names": [], "mappings": ";AAAA", "debugId": null}}, {"offset": {"line": 299, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/AI_Projects/CRMcriadores/crm-interno/lib/utils.ts"], "sourcesContent": ["/**\n * Transforma dados de array de arrays (formato do Google Sheets) \n * em array de objetos JSON usando a primeira linha como cabeçalhos\n */\nexport function transformData(data: any[][]): Record<string, any>[] {\n  if (!data || data.length === 0) {\n    return [];\n  }\n\n  // A primeira linha contém os cabeçalhos\n  const headers = data[0];\n  \n  // As linhas restantes contêm os dados\n  const rows = data.slice(1);\n\n  return rows.map((row) => {\n    const obj: Record<string, any> = {};\n    \n    headers.forEach((header, index) => {\n      // Usa o cabeçalho como chave e o valor da linha correspondente\n      obj[header] = row[index] || '';\n    });\n\n    return obj;\n  });\n}\n\n/**\n * Converte um objeto em array de valores na ordem dos cabeçalhos fornecidos\n */\nexport function objectToRowData(obj: Record<string, any>, headers: string[]): any[] {\n  return headers.map(header => obj[header] || '');\n}\n\n/**\n * Valida se os dados têm a estrutura esperada\n */\nexport function validateSheetData(data: any[][]): boolean {\n  return Array.isArray(data) && data.length > 0 && Array.isArray(data[0]);\n}\n\n/**\n * Limpa e normaliza strings vindas do Google Sheets\n */\nexport function cleanSheetValue(value: any): string {\n  if (value === null || value === undefined) {\n    return '';\n  }\n  \n  return String(value).trim();\n}\n\n/**\n * Converte valores de string para tipos apropriados\n */\nexport function parseSheetValue(value: string, type: 'string' | 'number' | 'boolean' | 'date' = 'string'): any {\n  const cleanValue = cleanSheetValue(value);\n  \n  if (cleanValue === '') {\n    return type === 'number' ? 0 : type === 'boolean' ? false : '';\n  }\n\n  switch (type) {\n    case 'number':\n      const num = parseFloat(cleanValue);\n      return isNaN(num) ? 0 : num;\n    \n    case 'boolean':\n      return cleanValue.toLowerCase() === 'true' || cleanValue === '1';\n    \n    case 'date':\n      const date = new Date(cleanValue);\n      return isNaN(date.getTime()) ? null : date;\n    \n    default:\n      return cleanValue;\n  }\n}\n\n/**\n * Formata dados para exibição\n */\nexport function formatDisplayValue(value: any, type: 'currency' | 'percentage' | 'date' | 'number' | 'text' = 'text'): string {\n  if (value === null || value === undefined || value === '') {\n    return '-';\n  }\n\n  switch (type) {\n    case 'currency':\n      const numValue = typeof value === 'number' ? value : parseFloat(value);\n      return isNaN(numValue) ? '-' : new Intl.NumberFormat('pt-BR', {\n        style: 'currency',\n        currency: 'BRL'\n      }).format(numValue);\n    \n    case 'percentage':\n      const pctValue = typeof value === 'number' ? value : parseFloat(value);\n      return isNaN(pctValue) ? '-' : `${pctValue.toFixed(1)}%`;\n    \n    case 'date':\n      const date = value instanceof Date ? value : new Date(value);\n      return isNaN(date.getTime()) ? '-' : date.toLocaleDateString('pt-BR');\n    \n    case 'number':\n      const numberValue = typeof value === 'number' ? value : parseFloat(value);\n      return isNaN(numberValue) ? '-' : new Intl.NumberFormat('pt-BR').format(numberValue);\n    \n    default:\n      return String(value);\n  }\n}\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;;;;AACM,SAAS,cAAc,IAAa;IACzC,IAAI,CAAC,QAAQ,KAAK,MAAM,KAAK,GAAG;QAC9B,OAAO,EAAE;IACX;IAEA,wCAAwC;IACxC,MAAM,UAAU,IAAI,CAAC,EAAE;IAEvB,sCAAsC;IACtC,MAAM,OAAO,KAAK,KAAK,CAAC;IAExB,OAAO,KAAK,GAAG,CAAC,CAAC;QACf,MAAM,MAA2B,CAAC;QAElC,QAAQ,OAAO,CAAC,CAAC,QAAQ;YACvB,+DAA+D;YAC/D,GAAG,CAAC,OAAO,GAAG,GAAG,CAAC,MAAM,IAAI;QAC9B;QAEA,OAAO;IACT;AACF;AAKO,SAAS,gBAAgB,GAAwB,EAAE,OAAiB;IACzE,OAAO,QAAQ,GAAG,CAAC,CAAA,SAAU,GAAG,CAAC,OAAO,IAAI;AAC9C;AAKO,SAAS,kBAAkB,IAAa;IAC7C,OAAO,MAAM,OAAO,CAAC,SAAS,KAAK,MAAM,GAAG,KAAK,MAAM,OAAO,CAAC,IAAI,CAAC,EAAE;AACxE;AAKO,SAAS,gBAAgB,KAAU;IACxC,IAAI,UAAU,QAAQ,UAAU,WAAW;QACzC,OAAO;IACT;IAEA,OAAO,OAAO,OAAO,IAAI;AAC3B;AAKO,SAAS,gBAAgB,KAAa,EAAE,OAAiD,QAAQ;IACtG,MAAM,aAAa,gBAAgB;IAEnC,IAAI,eAAe,IAAI;QACrB,OAAO,SAAS,WAAW,IAAI,SAAS,YAAY,QAAQ;IAC9D;IAEA,OAAQ;QACN,KAAK;YACH,MAAM,MAAM,WAAW;YACvB,OAAO,MAAM,OAAO,IAAI;QAE1B,KAAK;YACH,OAAO,WAAW,WAAW,OAAO,UAAU,eAAe;QAE/D,KAAK;YACH,MAAM,OAAO,IAAI,KAAK;YACtB,OAAO,MAAM,KAAK,OAAO,MAAM,OAAO;QAExC;YACE,OAAO;IACX;AACF;AAKO,SAAS,mBAAmB,KAAU,EAAE,OAA+D,MAAM;IAClH,IAAI,UAAU,QAAQ,UAAU,aAAa,UAAU,IAAI;QACzD,OAAO;IACT;IAEA,OAAQ;QACN,KAAK;YACH,MAAM,WAAW,OAAO,UAAU,WAAW,QAAQ,WAAW;YAChE,OAAO,MAAM,YAAY,MAAM,IAAI,KAAK,YAAY,CAAC,SAAS;gBAC5D,OAAO;gBACP,UAAU;YACZ,GAAG,MAAM,CAAC;QAEZ,KAAK;YACH,MAAM,WAAW,OAAO,UAAU,WAAW,QAAQ,WAAW;YAChE,OAAO,MAAM,YAAY,MAAM,GAAG,SAAS,OAAO,CAAC,GAAG,CAAC,CAAC;QAE1D,KAAK;YACH,MAAM,OAAO,iBAAiB,OAAO,QAAQ,IAAI,KAAK;YACtD,OAAO,MAAM,KAAK,OAAO,MAAM,MAAM,KAAK,kBAAkB,CAAC;QAE/D,KAAK;YACH,MAAM,cAAc,OAAO,UAAU,WAAW,QAAQ,WAAW;YACnE,OAAO,MAAM,eAAe,MAAM,IAAI,KAAK,YAAY,CAAC,SAAS,MAAM,CAAC;QAE1E;YACE,OAAO,OAAO;IAClB;AACF", "debugId": null}}, {"offset": {"line": 387, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/AI_Projects/CRMcriadores/crm-interno/app/%28dashboard%29/jornada/page.tsx"], "sourcesContent": ["import React from 'react';\nimport { getData } from '@/app/actions/sheetsActions';\nimport { transformData } from '@/lib/utils';\n\n// Dados de exemplo para demonstração (serão substituídos pelos dados do Google Sheets)\nconst mockBusinesses = [\n  {\n    id: 1,\n    businessName: 'Loja de Roupas Fashion',\n    journeyStage: 'Agendamentos',\n    nextAction: 'Agendar sessões de fotos com influenciadores',\n    contactDate: '2024-01-15',\n    value: 15000,\n    description: 'Campanha de verão focada em roupas casuais para jovens de 18-30 anos',\n    influencers: [\n      { name: '<PERSON>', username: 'an<PERSON><PERSON><PERSON>', followers: 125000, engagementRate: 4.2 },\n      { name: '<PERSON>', username: 'carlossantos', followers: 89000, engagementRate: 6.8 }\n    ],\n    campaigns: [\n      { title: 'Campanha Verão 2024', status: 'Ativa', startDate: '2024-01-15', endDate: '2024-03-15' }\n    ]\n  },\n  {\n    id: 2,\n    businessName: 'Restaurante Gourmet',\n    journeyStage: 'Reunião Briefing',\n    nextAction: 'Definir estratégia de conteúdo gastronômico',\n    contactDate: '2024-01-10',\n    value: 8000,\n    description: 'Divulgação de pratos especiais e experiência gastronômica única',\n    influencers: [\n      { name: 'Maria Oliveira', username: 'mariaoliveira', followers: 234000, engagementRate: 3.1 }\n    ],\n    campaigns: []\n  },\n  {\n    id: 3,\n    businessName: 'Academia Fitness Plus',\n    journeyStage: 'Entrega Final',\n    nextAction: 'Finalizar edição dos vídeos de treino',\n    contactDate: '2024-01-20',\n    value: 25000,\n    description: 'Campanha de motivação fitness com foco em resultados reais',\n    influencers: [\n      { name: 'João Fitness', username: 'joaofitness', followers: 156000, engagementRate: 5.4 },\n      { name: 'Carla Strong', username: 'carlastrong', followers: 98000, engagementRate: 7.2 },\n      { name: 'Pedro Muscle', username: 'pedromuscle', followers: 67000, engagementRate: 4.8 }\n    ],\n    campaigns: [\n      { title: 'Transformação 90 Dias', status: 'Ativa', startDate: '2024-01-01', endDate: '2024-03-31' }\n    ]\n  },\n  {\n    id: 4,\n    businessName: 'Clínica de Estética',\n    journeyStage: 'Reunião Briefing',\n    nextAction: 'Alinhar diretrizes de comunicação sobre procedimentos',\n    contactDate: '2024-01-12',\n    value: 12000,\n    description: 'Divulgação de tratamentos estéticos com foco em naturalidade',\n    influencers: [\n      { name: 'Bella Beauty', username: 'bellabeauty', followers: 189000, engagementRate: 6.1 }\n    ],\n    campaigns: []\n  },\n  {\n    id: 5,\n    businessName: 'Loja de Eletrônicos',\n    journeyStage: 'Agendamentos',\n    nextAction: 'Coordenar reviews de produtos com tech influencers',\n    contactDate: '2024-01-08',\n    value: 18000,\n    description: 'Reviews autênticos de gadgets e eletrônicos inovadores',\n    influencers: [\n      { name: 'Tech Master', username: 'techmaster', followers: 145000, engagementRate: 5.9 },\n      { name: 'Gamer Pro', username: 'gamerpro', followers: 203000, engagementRate: 4.5 }\n    ],\n    campaigns: [\n      { title: 'Tech Reviews 2024', status: 'Planejamento', startDate: '2024-02-01', endDate: '2024-04-30' }\n    ]\n  }\n];\n\n// Definir as fases da jornada (3 fases principais)\nconst journeyStages = [\n  { id: 'Reunião Briefing', label: 'Reunião Briefing', color: 'bg-blue-100 text-blue-800', icon: '📋' },\n  { id: 'Agendamentos', label: 'Agendamentos', color: 'bg-yellow-100 text-yellow-800', icon: '📅' },\n  { id: 'Entrega Final', label: 'Entrega Final', color: 'bg-green-100 text-green-800', icon: '✅' }\n];\n\nexport default async function JornadaPage() {\n  let businesses = mockBusinesses;\n\n  // Tenta buscar dados do Google Sheets, mas usa dados mock se falhar\n  try {\n    const rawData = await getData('Businesses');\n    if (rawData && rawData.length > 0) {\n      const transformedData = transformData(rawData);\n      \n      // Mapeia os dados transformados para o formato esperado pelo componente\n      businesses = transformedData.map((item: any) => ({\n        businessName: item.businessName || item.Nome || item['Nome do Negócio'] || 'Negócio não informado',\n        journeyStage: item.journeyStage || item.Estágio || item['Estágio da Jornada'] || 'Agendamento',\n        nextAction: item.nextAction || item.Ação || item['Próxima Ação'] || 'Definir próxima ação',\n        contactDate: item.contactDate || item.Data || item['Data de Contato'] || new Date().toISOString().split('T')[0],\n        value: parseInt(item.value || item.Valor || item['Valor do Negócio'] || '0')\n      }));\n    }\n  } catch (error) {\n    console.log('Usando dados de exemplo - Google Sheets não configurado ainda');\n  }\n\n  // Agrupar negócios por fase da jornada\n  const businessesByStage = journeyStages.map(stage => ({\n    ...stage,\n    businesses: businesses.filter(business => business.journeyStage === stage.id),\n    totalValue: businesses\n      .filter(business => business.journeyStage === stage.id)\n      .reduce((sum, business) => sum + business.value, 0)\n  }));\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header com estatísticas gerais */}\n      <div className=\"grid grid-cols-1 md:grid-cols-4 gap-4\">\n        <div className=\"card-elevated p-4\">\n          <div className=\"flex items-center justify-between\">\n            <div>\n              <p className=\"text-sm text-on-surface-variant\">Total de Negócios</p>\n              <p className=\"text-2xl font-bold text-on-surface\">{businesses.length}</p>\n            </div>\n            <div className=\"text-2xl\">🏢</div>\n          </div>\n        </div>\n        \n        <div className=\"card-elevated p-4\">\n          <div className=\"flex items-center justify-between\">\n            <div>\n              <p className=\"text-sm text-on-surface-variant\">Valor Total</p>\n              <p className=\"text-2xl font-bold text-primary\">\n                R$ {(businesses.reduce((sum, b) => sum + b.value, 0) / 1000).toFixed(0)}K\n              </p>\n            </div>\n            <div className=\"text-2xl\">💰</div>\n          </div>\n        </div>\n        \n        <div className=\"card-elevated p-4\">\n          <div className=\"flex items-center justify-between\">\n            <div>\n              <p className=\"text-sm text-on-surface-variant\">Em Fechamento</p>\n              <p className=\"text-2xl font-bold text-green-600\">\n                {businesses.filter(b => b.journeyStage === 'Fechamento').length}\n              </p>\n            </div>\n            <div className=\"text-2xl\">🎯</div>\n          </div>\n        </div>\n        \n        <div className=\"card-elevated p-4\">\n          <div className=\"flex items-center justify-between\">\n            <div>\n              <p className=\"text-sm text-on-surface-variant\">Taxa Conversão</p>\n              <p className=\"text-2xl font-bold text-secondary\">\n                {businesses.length > 0 ? Math.round((businesses.filter(b => b.journeyStage === 'Pós-venda').length / businesses.length) * 100) : 0}%\n              </p>\n            </div>\n            <div className=\"text-2xl\">📈</div>\n          </div>\n        </div>\n      </div>\n\n      {/* Kanban da Jornada */}\n      <div className=\"grid grid-cols-1 lg:grid-cols-3 xl:grid-cols-6 gap-4\">\n        {businessesByStage.map((stage) => (\n          <div key={stage.id} className=\"card-elevated p-4\">\n            {/* Header da coluna */}\n            <div className=\"mb-4\">\n              <div className=\"flex items-center justify-between mb-2\">\n                <div className=\"flex items-center\">\n                  <span className=\"text-lg mr-2\">{stage.icon}</span>\n                  <h3 className=\"font-semibold text-on-surface\">{stage.label}</h3>\n                </div>\n                <span className=\"text-xs bg-surface-container px-2 py-1 rounded-full\">\n                  {stage.businesses.length}\n                </span>\n              </div>\n              \n              <div className=\"text-xs text-on-surface-variant\">\n                Total: R$ {(stage.totalValue / 1000).toFixed(0)}K\n              </div>\n            </div>\n\n            {/* Cards dos negócios */}\n            <div className=\"space-y-3\">\n              {stage.businesses.map((business, index) => (\n                <div key={index} className=\"bg-surface-container rounded-lg p-3 hover:shadow-sm transition-shadow\">\n                  <h4 className=\"font-medium text-sm text-on-surface mb-2\">\n                    {business.businessName}\n                  </h4>\n                  \n                  <p className=\"text-xs text-on-surface-variant mb-2 line-clamp-2\">\n                    {business.nextAction}\n                  </p>\n                  \n                  <div className=\"flex items-center justify-between text-xs\">\n                    <span className=\"text-on-surface-variant\">\n                      {new Date(business.contactDate).toLocaleDateString('pt-BR')}\n                    </span>\n                    <span className=\"font-medium text-primary\">\n                      R$ {(business.value / 1000).toFixed(0)}K\n                    </span>\n                  </div>\n                </div>\n              ))}\n              \n              {stage.businesses.length === 0 && (\n                <div className=\"text-center py-8 text-on-surface-variant\">\n                  <div className=\"text-2xl mb-2\">{stage.icon}</div>\n                  <p className=\"text-xs\">Nenhum negócio nesta fase</p>\n                </div>\n              )}\n            </div>\n          </div>\n        ))}\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AACA;AACA;;;;AAEA,uFAAuF;AACvF,MAAM,iBAAiB;IACrB;QACE,IAAI;QACJ,cAAc;QACd,cAAc;QACd,YAAY;QACZ,aAAa;QACb,OAAO;QACP,aAAa;QACb,aAAa;YACX;gBAAE,MAAM;gBAAa,UAAU;gBAAY,WAAW;gBAAQ,gBAAgB;YAAI;YAClF;gBAAE,MAAM;gBAAiB,UAAU;gBAAgB,WAAW;gBAAO,gBAAgB;YAAI;SAC1F;QACD,WAAW;YACT;gBAAE,OAAO;gBAAuB,QAAQ;gBAAS,WAAW;gBAAc,SAAS;YAAa;SACjG;IACH;IACA;QACE,IAAI;QACJ,cAAc;QACd,cAAc;QACd,YAAY;QACZ,aAAa;QACb,OAAO;QACP,aAAa;QACb,aAAa;YACX;gBAAE,MAAM;gBAAkB,UAAU;gBAAiB,WAAW;gBAAQ,gBAAgB;YAAI;SAC7F;QACD,WAAW,EAAE;IACf;IACA;QACE,IAAI;QACJ,cAAc;QACd,cAAc;QACd,YAAY;QACZ,aAAa;QACb,OAAO;QACP,aAAa;QACb,aAAa;YACX;gBAAE,MAAM;gBAAgB,UAAU;gBAAe,WAAW;gBAAQ,gBAAgB;YAAI;YACxF;gBAAE,MAAM;gBAAgB,UAAU;gBAAe,WAAW;gBAAO,gBAAgB;YAAI;YACvF;gBAAE,MAAM;gBAAgB,UAAU;gBAAe,WAAW;gBAAO,gBAAgB;YAAI;SACxF;QACD,WAAW;YACT;gBAAE,OAAO;gBAAyB,QAAQ;gBAAS,WAAW;gBAAc,SAAS;YAAa;SACnG;IACH;IACA;QACE,IAAI;QACJ,cAAc;QACd,cAAc;QACd,YAAY;QACZ,aAAa;QACb,OAAO;QACP,aAAa;QACb,aAAa;YACX;gBAAE,MAAM;gBAAgB,UAAU;gBAAe,WAAW;gBAAQ,gBAAgB;YAAI;SACzF;QACD,WAAW,EAAE;IACf;IACA;QACE,IAAI;QACJ,cAAc;QACd,cAAc;QACd,YAAY;QACZ,aAAa;QACb,OAAO;QACP,aAAa;QACb,aAAa;YACX;gBAAE,MAAM;gBAAe,UAAU;gBAAc,WAAW;gBAAQ,gBAAgB;YAAI;YACtF;gBAAE,MAAM;gBAAa,UAAU;gBAAY,WAAW;gBAAQ,gBAAgB;YAAI;SACnF;QACD,WAAW;YACT;gBAAE,OAAO;gBAAqB,QAAQ;gBAAgB,WAAW;gBAAc,SAAS;YAAa;SACtG;IACH;CACD;AAED,mDAAmD;AACnD,MAAM,gBAAgB;IACpB;QAAE,IAAI;QAAoB,OAAO;QAAoB,OAAO;QAA6B,MAAM;IAAK;IACpG;QAAE,IAAI;QAAgB,OAAO;QAAgB,OAAO;QAAiC,MAAM;IAAK;IAChG;QAAE,IAAI;QAAiB,OAAO;QAAiB,OAAO;QAA+B,MAAM;IAAI;CAChG;AAEc,eAAe;IAC5B,IAAI,aAAa;IAEjB,oEAAoE;IACpE,IAAI;QACF,MAAM,UAAU,MAAM,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD,EAAE;QAC9B,IAAI,WAAW,QAAQ,MAAM,GAAG,GAAG;YACjC,MAAM,kBAAkB,CAAA,GAAA,4GAAA,CAAA,gBAAa,AAAD,EAAE;YAEtC,wEAAwE;YACxE,aAAa,gBAAgB,GAAG,CAAC,CAAC,OAAc,CAAC;oBAC/C,cAAc,KAAK,YAAY,IAAI,KAAK,IAAI,IAAI,IAAI,CAAC,kBAAkB,IAAI;oBAC3E,cAAc,KAAK,YAAY,IAAI,KAAK,OAAO,IAAI,IAAI,CAAC,qBAAqB,IAAI;oBACjF,YAAY,KAAK,UAAU,IAAI,KAAK,IAAI,IAAI,IAAI,CAAC,eAAe,IAAI;oBACpE,aAAa,KAAK,WAAW,IAAI,KAAK,IAAI,IAAI,IAAI,CAAC,kBAAkB,IAAI,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;oBAC/G,OAAO,SAAS,KAAK,KAAK,IAAI,KAAK,KAAK,IAAI,IAAI,CAAC,mBAAmB,IAAI;gBAC1E,CAAC;QACH;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,GAAG,CAAC;IACd;IAEA,uCAAuC;IACvC,MAAM,oBAAoB,cAAc,GAAG,CAAC,CAAA,QAAS,CAAC;YACpD,GAAG,KAAK;YACR,YAAY,WAAW,MAAM,CAAC,CAAA,WAAY,SAAS,YAAY,KAAK,MAAM,EAAE;YAC5E,YAAY,WACT,MAAM,CAAC,CAAA,WAAY,SAAS,YAAY,KAAK,MAAM,EAAE,EACrD,MAAM,CAAC,CAAC,KAAK,WAAa,MAAM,SAAS,KAAK,EAAE;QACrD,CAAC;IAED,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;sDACC,8OAAC;4CAAE,WAAU;sDAAkC;;;;;;sDAC/C,8OAAC;4CAAE,WAAU;sDAAsC,WAAW,MAAM;;;;;;;;;;;;8CAEtE,8OAAC;oCAAI,WAAU;8CAAW;;;;;;;;;;;;;;;;;kCAI9B,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;sDACC,8OAAC;4CAAE,WAAU;sDAAkC;;;;;;sDAC/C,8OAAC;4CAAE,WAAU;;gDAAkC;gDACzC,CAAC,WAAW,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,EAAE,KAAK,EAAE,KAAK,IAAI,EAAE,OAAO,CAAC;gDAAG;;;;;;;;;;;;;8CAG5E,8OAAC;oCAAI,WAAU;8CAAW;;;;;;;;;;;;;;;;;kCAI9B,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;sDACC,8OAAC;4CAAE,WAAU;sDAAkC;;;;;;sDAC/C,8OAAC;4CAAE,WAAU;sDACV,WAAW,MAAM,CAAC,CAAA,IAAK,EAAE,YAAY,KAAK,cAAc,MAAM;;;;;;;;;;;;8CAGnE,8OAAC;oCAAI,WAAU;8CAAW;;;;;;;;;;;;;;;;;kCAI9B,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;sDACC,8OAAC;4CAAE,WAAU;sDAAkC;;;;;;sDAC/C,8OAAC;4CAAE,WAAU;;gDACV,WAAW,MAAM,GAAG,IAAI,KAAK,KAAK,CAAC,AAAC,WAAW,MAAM,CAAC,CAAA,IAAK,EAAE,YAAY,KAAK,aAAa,MAAM,GAAG,WAAW,MAAM,GAAI,OAAO;gDAAE;;;;;;;;;;;;;8CAGvI,8OAAC;oCAAI,WAAU;8CAAW;;;;;;;;;;;;;;;;;;;;;;;0BAMhC,8OAAC;gBAAI,WAAU;0BACZ,kBAAkB,GAAG,CAAC,CAAC,sBACtB,8OAAC;wBAAmB,WAAU;;0CAE5B,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAK,WAAU;kEAAgB,MAAM,IAAI;;;;;;kEAC1C,8OAAC;wDAAG,WAAU;kEAAiC,MAAM,KAAK;;;;;;;;;;;;0DAE5D,8OAAC;gDAAK,WAAU;0DACb,MAAM,UAAU,CAAC,MAAM;;;;;;;;;;;;kDAI5B,8OAAC;wCAAI,WAAU;;4CAAkC;4CACpC,CAAC,MAAM,UAAU,GAAG,IAAI,EAAE,OAAO,CAAC;4CAAG;;;;;;;;;;;;;0CAKpD,8OAAC;gCAAI,WAAU;;oCACZ,MAAM,UAAU,CAAC,GAAG,CAAC,CAAC,UAAU,sBAC/B,8OAAC;4CAAgB,WAAU;;8DACzB,8OAAC;oDAAG,WAAU;8DACX,SAAS,YAAY;;;;;;8DAGxB,8OAAC;oDAAE,WAAU;8DACV,SAAS,UAAU;;;;;;8DAGtB,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAK,WAAU;sEACb,IAAI,KAAK,SAAS,WAAW,EAAE,kBAAkB,CAAC;;;;;;sEAErD,8OAAC;4DAAK,WAAU;;gEAA2B;gEACrC,CAAC,SAAS,KAAK,GAAG,IAAI,EAAE,OAAO,CAAC;gEAAG;;;;;;;;;;;;;;2CAdnC;;;;;oCAoBX,MAAM,UAAU,CAAC,MAAM,KAAK,mBAC3B,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DAAiB,MAAM,IAAI;;;;;;0DAC1C,8OAAC;gDAAE,WAAU;0DAAU;;;;;;;;;;;;;;;;;;;uBA5CrB,MAAM,EAAE;;;;;;;;;;;;;;;;AAqD5B", "debugId": null}}]}
{"version": 3, "sources": [], "sections": [{"offset": {"line": 111, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/AI_Projects/CRMcriadores/crm-interno/app/actions/sheetsActions.ts"], "sourcesContent": ["'use server';\n\nimport { google } from 'googleapis';\n\n// Configuração da autenticação\nconst getGoogleSheetsAuth = () => {\n  const credentials = {\n    type: 'service_account',\n    project_id: process.env.GOOGLE_PROJECT_ID,\n    private_key_id: process.env.GOOGLE_PRIVATE_KEY_ID,\n    private_key: process.env.GOOGLE_PRIVATE_KEY?.replace(/\\\\n/g, '\\n'),\n    client_email: process.env.GOOGLE_CLIENT_EMAIL,\n    client_id: process.env.GOOGLE_CLIENT_ID,\n    auth_uri: 'https://accounts.google.com/o/oauth2/auth',\n    token_uri: 'https://oauth2.googleapis.com/token',\n    auth_provider_x509_cert_url: 'https://www.googleapis.com/oauth2/v1/certs',\n    client_x509_cert_url: `https://www.googleapis.com/robot/v1/metadata/x509/${process.env.GOOGLE_CLIENT_EMAIL}`,\n  };\n\n  const auth = new google.auth.GoogleAuth({\n    credentials,\n    scopes: ['https://www.googleapis.com/auth/spreadsheets'],\n  });\n\n  return auth;\n};\n\n// Função para ler dados da planilha\nexport async function getData(sheetName: string) {\n  try {\n    const auth = getGoogleSheetsAuth();\n    const sheets = google.sheets({ version: 'v4', auth });\n    \n    const spreadsheetId = process.env.GOOGLE_SPREADSHEET_ID;\n    \n    if (!spreadsheetId) {\n      throw new Error('GOOGLE_SPREADSHEET_ID não está configurado');\n    }\n\n    const response = await sheets.spreadsheets.values.get({\n      spreadsheetId,\n      range: `${sheetName}!A:Z`, // Lê todas as colunas de A até Z\n    });\n\n    return response.data.values || [];\n  } catch (error) {\n    console.error('Erro ao ler dados da planilha:', error);\n    throw new Error('Falha ao ler dados da planilha');\n  }\n}\n\n// Função para adicionar dados à planilha\nexport async function appendData(sheetName: string, rowData: any[]) {\n  try {\n    const auth = getGoogleSheetsAuth();\n    const sheets = google.sheets({ version: 'v4', auth });\n    \n    const spreadsheetId = process.env.GOOGLE_SPREADSHEET_ID;\n    \n    if (!spreadsheetId) {\n      throw new Error('GOOGLE_SPREADSHEET_ID não está configurado');\n    }\n\n    const response = await sheets.spreadsheets.values.append({\n      spreadsheetId,\n      range: `${sheetName}!A:Z`,\n      valueInputOption: 'USER_ENTERED',\n      requestBody: {\n        values: [rowData],\n      },\n    });\n\n    return response.data;\n  } catch (error) {\n    console.error('Erro ao adicionar dados à planilha:', error);\n    throw new Error('Falha ao adicionar dados à planilha');\n  }\n}\n\n// Função para atualizar dados na planilha\nexport async function updateData(sheetName: string, range: string, rowData: any[]) {\n  try {\n    const auth = getGoogleSheetsAuth();\n    const sheets = google.sheets({ version: 'v4', auth });\n    \n    const spreadsheetId = process.env.GOOGLE_SPREADSHEET_ID;\n    \n    if (!spreadsheetId) {\n      throw new Error('GOOGLE_SPREADSHEET_ID não está configurado');\n    }\n\n    const response = await sheets.spreadsheets.values.update({\n      spreadsheetId,\n      range: `${sheetName}!${range}`,\n      valueInputOption: 'USER_ENTERED',\n      requestBody: {\n        values: [rowData],\n      },\n    });\n\n    return response.data;\n  } catch (error) {\n    console.error('Erro ao atualizar dados da planilha:', error);\n    throw new Error('Falha ao atualizar dados da planilha');\n  }\n}\n"], "names": [], "mappings": ";;;;;;;AAEA;;;;;AAEA,+BAA+B;AAC/B,MAAM,sBAAsB;IAC1B,MAAM,cAAc;QAClB,MAAM;QACN,YAAY,QAAQ,GAAG,CAAC,iBAAiB;QACzC,gBAAgB,QAAQ,GAAG,CAAC,qBAAqB;QACjD,aAAa,QAAQ,GAAG,CAAC,kBAAkB,EAAE,QAAQ,QAAQ;QAC7D,cAAc,QAAQ,GAAG,CAAC,mBAAmB;QAC7C,WAAW,QAAQ,GAAG,CAAC,gBAAgB;QACvC,UAAU;QACV,WAAW;QACX,6BAA6B;QAC7B,sBAAsB,CAAC,kDAAkD,EAAE,QAAQ,GAAG,CAAC,mBAAmB,EAAE;IAC9G;IAEA,MAAM,OAAO,IAAI,mJAAA,CAAA,SAAM,CAAC,IAAI,CAAC,UAAU,CAAC;QACtC;QACA,QAAQ;YAAC;SAA+C;IAC1D;IAEA,OAAO;AACT;AAGO,eAAe,QAAQ,SAAiB;IAC7C,IAAI;QACF,MAAM,OAAO;QACb,MAAM,SAAS,mJAAA,CAAA,SAAM,CAAC,MAAM,CAAC;YAAE,SAAS;YAAM;QAAK;QAEnD,MAAM,gBAAgB,QAAQ,GAAG,CAAC,qBAAqB;QAEvD,IAAI,CAAC,eAAe;YAClB,MAAM,IAAI,MAAM;QAClB;QAEA,MAAM,WAAW,MAAM,OAAO,YAAY,CAAC,MAAM,CAAC,GAAG,CAAC;YACpD;YACA,OAAO,GAAG,UAAU,IAAI,CAAC;QAC3B;QAEA,OAAO,SAAS,IAAI,CAAC,MAAM,IAAI,EAAE;IACnC,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,kCAAkC;QAChD,MAAM,IAAI,MAAM;IAClB;AACF;AAGO,eAAe,WAAW,SAAiB,EAAE,OAAc;IAChE,IAAI;QACF,MAAM,OAAO;QACb,MAAM,SAAS,mJAAA,CAAA,SAAM,CAAC,MAAM,CAAC;YAAE,SAAS;YAAM;QAAK;QAEnD,MAAM,gBAAgB,QAAQ,GAAG,CAAC,qBAAqB;QAEvD,IAAI,CAAC,eAAe;YAClB,MAAM,IAAI,MAAM;QAClB;QAEA,MAAM,WAAW,MAAM,OAAO,YAAY,CAAC,MAAM,CAAC,MAAM,CAAC;YACvD;YACA,OAAO,GAAG,UAAU,IAAI,CAAC;YACzB,kBAAkB;YAClB,aAAa;gBACX,QAAQ;oBAAC;iBAAQ;YACnB;QACF;QAEA,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,uCAAuC;QACrD,MAAM,IAAI,MAAM;IAClB;AACF;AAGO,eAAe,WAAW,SAAiB,EAAE,KAAa,EAAE,OAAc;IAC/E,IAAI;QACF,MAAM,OAAO;QACb,MAAM,SAAS,mJAAA,CAAA,SAAM,CAAC,MAAM,CAAC;YAAE,SAAS;YAAM;QAAK;QAEnD,MAAM,gBAAgB,QAAQ,GAAG,CAAC,qBAAqB;QAEvD,IAAI,CAAC,eAAe;YAClB,MAAM,IAAI,MAAM;QAClB;QAEA,MAAM,WAAW,MAAM,OAAO,YAAY,CAAC,MAAM,CAAC,MAAM,CAAC;YACvD;YACA,OAAO,GAAG,UAAU,CAAC,EAAE,OAAO;YAC9B,kBAAkB;YAClB,aAAa;gBACX,QAAQ;oBAAC;iBAAQ;YACnB;QACF;QAEA,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,wCAAwC;QACtD,MAAM,IAAI,MAAM;IAClB;AACF;;;IA7EsB;IAwBA;IA4BA;;AApDA,+OAAA;AAwBA,+OAAA;AA4BA,+OAAA", "debugId": null}}, {"offset": {"line": 235, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/AI_Projects/CRMcriadores/crm-interno/.next-internal/server/app/%28dashboard%29/dashboard/businesses/page/actions.js%20%28server%20actions%20loader%29"], "sourcesContent": ["export {getData as '40416fe263e60966356356bb294165af55bf27c98c'} from 'ACTIONS_MODULE0'\nexport {appendData as '60b2397e35e4d22eb574fd297096247fb0b426b814'} from 'ACTIONS_MODULE0'\nexport {updateData as '700091b6dbbe7aa4a45146966c103d91060aef6152'} from 'ACTIONS_MODULE0'\n"], "names": [], "mappings": ";AAAA", "debugId": null}}, {"offset": {"line": 299, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/AI_Projects/CRMcriadores/crm-interno/lib/utils.ts"], "sourcesContent": ["/**\n * Transforma dados de array de arrays (formato do Google Sheets) \n * em array de objetos JSON usando a primeira linha como cabeçalhos\n */\nexport function transformData(data: any[][]): Record<string, any>[] {\n  if (!data || data.length === 0) {\n    return [];\n  }\n\n  // A primeira linha contém os cabeçalhos\n  const headers = data[0];\n  \n  // As linhas restantes contêm os dados\n  const rows = data.slice(1);\n\n  return rows.map((row) => {\n    const obj: Record<string, any> = {};\n    \n    headers.forEach((header, index) => {\n      // Usa o cabeçalho como chave e o valor da linha correspondente\n      obj[header] = row[index] || '';\n    });\n\n    return obj;\n  });\n}\n\n/**\n * Converte um objeto em array de valores na ordem dos cabeçalhos fornecidos\n */\nexport function objectToRowData(obj: Record<string, any>, headers: string[]): any[] {\n  return headers.map(header => obj[header] || '');\n}\n\n/**\n * Valida se os dados têm a estrutura esperada\n */\nexport function validateSheetData(data: any[][]): boolean {\n  return Array.isArray(data) && data.length > 0 && Array.isArray(data[0]);\n}\n\n/**\n * Limpa e normaliza strings vindas do Google Sheets\n */\nexport function cleanSheetValue(value: any): string {\n  if (value === null || value === undefined) {\n    return '';\n  }\n  \n  return String(value).trim();\n}\n\n/**\n * Converte valores de string para tipos apropriados\n */\nexport function parseSheetValue(value: string, type: 'string' | 'number' | 'boolean' | 'date' = 'string'): any {\n  const cleanValue = cleanSheetValue(value);\n  \n  if (cleanValue === '') {\n    return type === 'number' ? 0 : type === 'boolean' ? false : '';\n  }\n\n  switch (type) {\n    case 'number':\n      const num = parseFloat(cleanValue);\n      return isNaN(num) ? 0 : num;\n    \n    case 'boolean':\n      return cleanValue.toLowerCase() === 'true' || cleanValue === '1';\n    \n    case 'date':\n      const date = new Date(cleanValue);\n      return isNaN(date.getTime()) ? null : date;\n    \n    default:\n      return cleanValue;\n  }\n}\n\n/**\n * Formata dados para exibição\n */\nexport function formatDisplayValue(value: any, type: 'currency' | 'percentage' | 'date' | 'number' | 'text' = 'text'): string {\n  if (value === null || value === undefined || value === '') {\n    return '-';\n  }\n\n  switch (type) {\n    case 'currency':\n      const numValue = typeof value === 'number' ? value : parseFloat(value);\n      return isNaN(numValue) ? '-' : new Intl.NumberFormat('pt-BR', {\n        style: 'currency',\n        currency: 'BRL'\n      }).format(numValue);\n    \n    case 'percentage':\n      const pctValue = typeof value === 'number' ? value : parseFloat(value);\n      return isNaN(pctValue) ? '-' : `${pctValue.toFixed(1)}%`;\n    \n    case 'date':\n      const date = value instanceof Date ? value : new Date(value);\n      return isNaN(date.getTime()) ? '-' : date.toLocaleDateString('pt-BR');\n    \n    case 'number':\n      const numberValue = typeof value === 'number' ? value : parseFloat(value);\n      return isNaN(numberValue) ? '-' : new Intl.NumberFormat('pt-BR').format(numberValue);\n    \n    default:\n      return String(value);\n  }\n}\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;;;;AACM,SAAS,cAAc,IAAa;IACzC,IAAI,CAAC,QAAQ,KAAK,MAAM,KAAK,GAAG;QAC9B,OAAO,EAAE;IACX;IAEA,wCAAwC;IACxC,MAAM,UAAU,IAAI,CAAC,EAAE;IAEvB,sCAAsC;IACtC,MAAM,OAAO,KAAK,KAAK,CAAC;IAExB,OAAO,KAAK,GAAG,CAAC,CAAC;QACf,MAAM,MAA2B,CAAC;QAElC,QAAQ,OAAO,CAAC,CAAC,QAAQ;YACvB,+DAA+D;YAC/D,GAAG,CAAC,OAAO,GAAG,GAAG,CAAC,MAAM,IAAI;QAC9B;QAEA,OAAO;IACT;AACF;AAKO,SAAS,gBAAgB,GAAwB,EAAE,OAAiB;IACzE,OAAO,QAAQ,GAAG,CAAC,CAAA,SAAU,GAAG,CAAC,OAAO,IAAI;AAC9C;AAKO,SAAS,kBAAkB,IAAa;IAC7C,OAAO,MAAM,OAAO,CAAC,SAAS,KAAK,MAAM,GAAG,KAAK,MAAM,OAAO,CAAC,IAAI,CAAC,EAAE;AACxE;AAKO,SAAS,gBAAgB,KAAU;IACxC,IAAI,UAAU,QAAQ,UAAU,WAAW;QACzC,OAAO;IACT;IAEA,OAAO,OAAO,OAAO,IAAI;AAC3B;AAKO,SAAS,gBAAgB,KAAa,EAAE,OAAiD,QAAQ;IACtG,MAAM,aAAa,gBAAgB;IAEnC,IAAI,eAAe,IAAI;QACrB,OAAO,SAAS,WAAW,IAAI,SAAS,YAAY,QAAQ;IAC9D;IAEA,OAAQ;QACN,KAAK;YACH,MAAM,MAAM,WAAW;YACvB,OAAO,MAAM,OAAO,IAAI;QAE1B,KAAK;YACH,OAAO,WAAW,WAAW,OAAO,UAAU,eAAe;QAE/D,KAAK;YACH,MAAM,OAAO,IAAI,KAAK;YACtB,OAAO,MAAM,KAAK,OAAO,MAAM,OAAO;QAExC;YACE,OAAO;IACX;AACF;AAKO,SAAS,mBAAmB,KAAU,EAAE,OAA+D,MAAM;IAClH,IAAI,UAAU,QAAQ,UAAU,aAAa,UAAU,IAAI;QACzD,OAAO;IACT;IAEA,OAAQ;QACN,KAAK;YACH,MAAM,WAAW,OAAO,UAAU,WAAW,QAAQ,WAAW;YAChE,OAAO,MAAM,YAAY,MAAM,IAAI,KAAK,YAAY,CAAC,SAAS;gBAC5D,OAAO;gBACP,UAAU;YACZ,GAAG,MAAM,CAAC;QAEZ,KAAK;YACH,MAAM,WAAW,OAAO,UAAU,WAAW,QAAQ,WAAW;YAChE,OAAO,MAAM,YAAY,MAAM,GAAG,SAAS,OAAO,CAAC,GAAG,CAAC,CAAC;QAE1D,KAAK;YACH,MAAM,OAAO,iBAAiB,OAAO,QAAQ,IAAI,KAAK;YACtD,OAAO,MAAM,KAAK,OAAO,MAAM,MAAM,KAAK,kBAAkB,CAAC;QAE/D,KAAK;YACH,MAAM,cAAc,OAAO,UAAU,WAAW,QAAQ,WAAW;YACnE,OAAO,MAAM,eAAe,MAAM,IAAI,KAAK,YAAY,CAAC,SAAS,MAAM,CAAC;QAE1E;YACE,OAAO,OAAO;IAClB;AACF", "debugId": null}}, {"offset": {"line": 387, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/AI_Projects/CRMcriadores/crm-interno/components/BusinessCard.tsx"], "sourcesContent": ["import React from 'react';\n\ninterface BusinessCardProps {\n  businessName: string;\n  journeyStage: string;\n  nextAction: string;\n}\n\nexport default function BusinessCard({\n  businessName,\n  journeyStage,\n  nextAction\n}: BusinessCardProps) {\n  // Função para determinar a cor do badge baseado no estágio\n  const getBadgeColor = (stage: string): string => {\n    const stageColors: Record<string, string> = {\n      'Agendamento': 'bg-blue-100 text-blue-800 border-blue-200',\n      'Proposta': 'bg-yellow-100 text-yellow-800 border-yellow-200',\n      'Negociação': 'bg-orange-100 text-orange-800 border-orange-200',\n      'Fechamento': 'bg-green-100 text-green-800 border-green-200',\n      'Pós-venda': 'bg-purple-100 text-purple-800 border-purple-200',\n      'Perdido': 'bg-red-100 text-red-800 border-red-200',\n      'Pausado': 'bg-gray-100 text-gray-800 border-gray-200',\n    };\n    \n    return stageColors[stage] || 'bg-gray-100 text-gray-800 border-gray-200';\n  };\n\n  // Função para determinar o ícone baseado no estágio\n  const getStageIcon = (stage: string): string => {\n    const stageIcons: Record<string, string> = {\n      'Agendamento': '📅',\n      'Proposta': '📋',\n      'Negociação': '🤝',\n      'Fechamento': '✅',\n      'Pós-venda': '🎯',\n      'Perdido': '❌',\n      'Pausado': '⏸️',\n    };\n    \n    return stageIcons[stage] || '📊';\n  };\n\n  return (\n    <div className=\"bg-surface border border-outline-variant rounded-2xl p-6 shadow-sm hover:shadow-md transition-shadow\">\n      {/* Cabeçalho com nome do negócio */}\n      <div className=\"mb-4\">\n        <h3 className=\"text-xl font-semibold text-on-surface mb-2\">\n          {businessName}\n        </h3>\n        \n        {/* Badge do estágio */}\n        <div className=\"flex items-center mb-3\">\n          <span className=\"text-lg mr-2\">\n            {getStageIcon(journeyStage)}\n          </span>\n          <span \n            className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium border ${getBadgeColor(journeyStage)}`}\n          >\n            {journeyStage}\n          </span>\n        </div>\n      </div>\n\n      {/* Próxima ação */}\n      <div className=\"bg-surface-container rounded-xl p-4\">\n        <div className=\"flex items-start\">\n          <div className=\"flex-shrink-0 w-2 h-2 bg-primary rounded-full mt-2 mr-3\"></div>\n          <div className=\"flex-1\">\n            <h4 className=\"text-sm font-medium text-on-surface-variant mb-1\">\n              Próxima ação:\n            </h4>\n            <p className=\"text-sm text-on-surface\">\n              {nextAction}\n            </p>\n          </div>\n        </div>\n      </div>\n\n      {/* Footer com indicador de prioridade */}\n      <div className=\"mt-4 pt-4 border-t border-outline-variant\">\n        <div className=\"flex items-center justify-between\">\n          <div className=\"flex items-center text-xs text-on-surface-variant\">\n            <div className=\"w-1.5 h-1.5 bg-primary rounded-full mr-2\"></div>\n            <span>Em andamento</span>\n          </div>\n          \n          {/* Indicador de urgência baseado no estágio */}\n          <div className=\"flex items-center\">\n            {(journeyStage === 'Fechamento' || journeyStage === 'Negociação') && (\n              <span className=\"text-xs text-orange-600 font-medium\">\n                🔥 Urgente\n              </span>\n            )}\n            {journeyStage === 'Agendamento' && (\n              <span className=\"text-xs text-blue-600 font-medium\">\n                ⏰ Agendar\n              </span>\n            )}\n            {journeyStage === 'Proposta' && (\n              <span className=\"text-xs text-yellow-600 font-medium\">\n                📝 Aguardando\n              </span>\n            )}\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAQe,SAAS,aAAa,EACnC,YAAY,EACZ,YAAY,EACZ,UAAU,EACQ;IAClB,2DAA2D;IAC3D,MAAM,gBAAgB,CAAC;QACrB,MAAM,cAAsC;YAC1C,eAAe;YACf,YAAY;YACZ,cAAc;YACd,cAAc;YACd,aAAa;YACb,WAAW;YACX,WAAW;QACb;QAEA,OAAO,WAAW,CAAC,MAAM,IAAI;IAC/B;IAEA,oDAAoD;IACpD,MAAM,eAAe,CAAC;QACpB,MAAM,aAAqC;YACzC,eAAe;YACf,YAAY;YACZ,cAAc;YACd,cAAc;YACd,aAAa;YACb,WAAW;YACX,WAAW;QACb;QAEA,OAAO,UAAU,CAAC,MAAM,IAAI;IAC9B;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCACX;;;;;;kCAIH,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAK,WAAU;0CACb,aAAa;;;;;;0CAEhB,8OAAC;gCACC,WAAW,CAAC,2EAA2E,EAAE,cAAc,eAAe;0CAErH;;;;;;;;;;;;;;;;;;0BAMP,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;;;;;sCACf,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAmD;;;;;;8CAGjE,8OAAC;oCAAE,WAAU;8CACV;;;;;;;;;;;;;;;;;;;;;;;0BAOT,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;;;;;8CACf,8OAAC;8CAAK;;;;;;;;;;;;sCAIR,8OAAC;4BAAI,WAAU;;gCACZ,CAAC,iBAAiB,gBAAgB,iBAAiB,YAAY,mBAC9D,8OAAC;oCAAK,WAAU;8CAAsC;;;;;;gCAIvD,iBAAiB,+BAChB,8OAAC;oCAAK,WAAU;8CAAoC;;;;;;gCAIrD,iBAAiB,4BAChB,8OAAC;oCAAK,WAAU;8CAAsC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASpE", "debugId": null}}, {"offset": {"line": 597, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/AI_Projects/CRMcriadores/crm-interno/app/%28dashboard%29/dashboard/businesses/page.tsx"], "sourcesContent": ["import React from 'react';\nimport { getData } from '@/app/actions/sheetsActions';\nimport { transformData } from '@/lib/utils';\nimport BusinessCard from '@/components/BusinessCard';\n\n// Dados de exemplo para demonstração (serão substituídos pelos dados do Google Sheets)\nconst mockBusinesses = [\n  {\n    businessName: 'Loja de Roupas Fashion',\n    journeyStage: 'Negociação',\n    nextAction: 'Enviar proposta final com desconto de 15%'\n  },\n  {\n    businessName: 'Restaurante Gourmet',\n    journeyStage: 'Agendamento',\n    nextAction: 'Agendar reunião para apresentação do projeto'\n  },\n  {\n    businessName: 'Academia Fitness Plus',\n    journeyStage: 'Fechamento',\n    nextAction: 'Aguardar assinatura do contrato'\n  },\n  {\n    businessName: 'Clínica de Estética',\n    journeyStage: 'Proposta',\n    nextAction: 'Revisar briefing e ajustar orçamento'\n  }\n];\n\nexport default async function BusinessesPage() {\n  let businesses = mockBusinesses;\n\n  // Tenta buscar dados do Google Sheets, mas usa dados mock se falhar\n  try {\n    const rawData = await getData('Businesses');\n    if (rawData && rawData.length > 0) {\n      const transformedData = transformData(rawData);\n\n      // Mapeia os dados transformados para o formato esperado pelo componente\n      businesses = transformedData.map((item: any) => ({\n        businessName: item.businessName || item.Nome || item['Nome do Negócio'] || 'Negócio não informado',\n        journeyStage: item.journeyStage || item.Estágio || item['Estágio da Jornada'] || 'Agendamento',\n        nextAction: item.nextAction || item.Ação || item['Próxima Ação'] || 'Definir próxima ação'\n      }));\n    }\n  } catch (error) {\n    console.log('Usando dados de exemplo - Google Sheets não configurado ainda');\n  }\n\n  return (\n    <div>\n      <div className=\"mb-6\">\n        <h1 className=\"text-3xl font-bold text-on-surface mb-2\">Negócios</h1>\n        <p className=\"text-on-surface-variant\">\n          Acompanhe o pipeline de vendas e gerencie seus clientes\n        </p>\n      </div>\n\n      <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\n        {businesses.map((business, index) => (\n          <BusinessCard\n            key={index}\n            businessName={business.businessName}\n            journeyStage={business.journeyStage}\n            nextAction={business.nextAction}\n          />\n        ))}\n      </div>\n\n      {businesses.length === 0 && (\n        <div className=\"text-center py-12\">\n          <div className=\"text-4xl mb-4\">🏢</div>\n          <h3 className=\"text-lg font-medium text-on-surface mb-2\">\n            Nenhum negócio encontrado\n          </h3>\n          <p className=\"text-sm text-on-surface-variant\">\n            Configure o Google Sheets para ver os dados dos negócios.\n          </p>\n        </div>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;;;;;AAEA,uFAAuF;AACvF,MAAM,iBAAiB;IACrB;QACE,cAAc;QACd,cAAc;QACd,YAAY;IACd;IACA;QACE,cAAc;QACd,cAAc;QACd,YAAY;IACd;IACA;QACE,cAAc;QACd,cAAc;QACd,YAAY;IACd;IACA;QACE,cAAc;QACd,cAAc;QACd,YAAY;IACd;CACD;AAEc,eAAe;IAC5B,IAAI,aAAa;IAEjB,oEAAoE;IACpE,IAAI;QACF,MAAM,UAAU,MAAM,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD,EAAE;QAC9B,IAAI,WAAW,QAAQ,MAAM,GAAG,GAAG;YACjC,MAAM,kBAAkB,CAAA,GAAA,4GAAA,CAAA,gBAAa,AAAD,EAAE;YAEtC,wEAAwE;YACxE,aAAa,gBAAgB,GAAG,CAAC,CAAC,OAAc,CAAC;oBAC/C,cAAc,KAAK,YAAY,IAAI,KAAK,IAAI,IAAI,IAAI,CAAC,kBAAkB,IAAI;oBAC3E,cAAc,KAAK,YAAY,IAAI,KAAK,OAAO,IAAI,IAAI,CAAC,qBAAqB,IAAI;oBACjF,YAAY,KAAK,UAAU,IAAI,KAAK,IAAI,IAAI,IAAI,CAAC,eAAe,IAAI;gBACtE,CAAC;QACH;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,GAAG,CAAC;IACd;IAEA,qBACE,8OAAC;;0BACC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAA0C;;;;;;kCACxD,8OAAC;wBAAE,WAAU;kCAA0B;;;;;;;;;;;;0BAKzC,8OAAC;gBAAI,WAAU;0BACZ,WAAW,GAAG,CAAC,CAAC,UAAU,sBACzB,8OAAC,2HAAA,CAAA,UAAY;wBAEX,cAAc,SAAS,YAAY;wBACnC,cAAc,SAAS,YAAY;wBACnC,YAAY,SAAS,UAAU;uBAH1B;;;;;;;;;;YAQV,WAAW,MAAM,KAAK,mBACrB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCAAgB;;;;;;kCAC/B,8OAAC;wBAAG,WAAU;kCAA2C;;;;;;kCAGzD,8OAAC;wBAAE,WAAU;kCAAkC;;;;;;;;;;;;;;;;;;AAOzD", "debugId": null}}]}
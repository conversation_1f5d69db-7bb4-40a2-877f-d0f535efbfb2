{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/AI_Projects/CRMcriadores/crm-interno/node_modules/googleapis/build/src/apis/cloudidentity/v1.js"], "sourcesContent": ["\"use strict\";\n// Copyright 2020 Google LLC\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//    http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.cloudidentity_v1 = void 0;\n/* eslint-disable @typescript-eslint/no-explicit-any */\n/* eslint-disable @typescript-eslint/no-unused-vars */\n/* eslint-disable @typescript-eslint/no-empty-interface */\n/* eslint-disable @typescript-eslint/no-namespace */\n/* eslint-disable no-irregular-whitespace */\nconst googleapis_common_1 = require(\"googleapis-common\");\nvar cloudidentity_v1;\n(function (cloudidentity_v1) {\n    /**\n     * Cloud Identity API\n     *\n     * API for provisioning and managing identity resources.\n     *\n     * @example\n     * ```js\n     * const {google} = require('googleapis');\n     * const cloudidentity = google.cloudidentity('v1');\n     * ```\n     */\n    class Cloudidentity {\n        context;\n        customers;\n        devices;\n        groups;\n        inboundSamlSsoProfiles;\n        inboundSsoAssignments;\n        policies;\n        constructor(options, google) {\n            this.context = {\n                _options: options || {},\n                google,\n            };\n            this.customers = new Resource$Customers(this.context);\n            this.devices = new Resource$Devices(this.context);\n            this.groups = new Resource$Groups(this.context);\n            this.inboundSamlSsoProfiles = new Resource$Inboundsamlssoprofiles(this.context);\n            this.inboundSsoAssignments = new Resource$Inboundssoassignments(this.context);\n            this.policies = new Resource$Policies(this.context);\n        }\n    }\n    cloudidentity_v1.Cloudidentity = Cloudidentity;\n    class Resource$Customers {\n        context;\n        userinvitations;\n        constructor(context) {\n            this.context = context;\n            this.userinvitations = new Resource$Customers$Userinvitations(this.context);\n        }\n    }\n    cloudidentity_v1.Resource$Customers = Resource$Customers;\n    class Resource$Customers$Userinvitations {\n        context;\n        constructor(context) {\n            this.context = context;\n        }\n        cancel(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://cloudidentity.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1/{+name}:cancel').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'POST',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['name'],\n                pathParams: ['name'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        get(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://cloudidentity.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1/{+name}').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'GET',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['name'],\n                pathParams: ['name'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        isInvitableUser(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params =\n                    {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://cloudidentity.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1/{+name}:isInvitableUser').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'GET',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['name'],\n                pathParams: ['name'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        list(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://cloudidentity.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1/{+parent}/userinvitations').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'GET',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['parent'],\n                pathParams: ['parent'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        send(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://cloudidentity.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1/{+name}:send').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'POST',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['name'],\n                pathParams: ['name'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n    }\n    cloudidentity_v1.Resource$Customers$Userinvitations = Resource$Customers$Userinvitations;\n    class Resource$Devices {\n        context;\n        deviceUsers;\n        constructor(context) {\n            this.context = context;\n            this.deviceUsers = new Resource$Devices$Deviceusers(this.context);\n        }\n        cancelWipe(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://cloudidentity.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1/{+name}:cancelWipe').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'POST',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['name'],\n                pathParams: ['name'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        create(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback || {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://cloudidentity.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1/devices').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'POST',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: [],\n                pathParams: [],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        delete(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback || {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://cloudidentity.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1/{+name}').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'DELETE',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['name'],\n                pathParams: ['name'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        get(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback || {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://cloudidentity.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1/{+name}').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'GET',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['name'],\n                pathParams: ['name'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        list(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback || {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://cloudidentity.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1/devices').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'GET',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: [],\n                pathParams: [],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        wipe(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback || {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://cloudidentity.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1/{+name}:wipe').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'POST',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['name'],\n                pathParams: ['name'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n    }\n    cloudidentity_v1.Resource$Devices = Resource$Devices;\n    class Resource$Devices$Deviceusers {\n        context;\n        clientStates;\n        constructor(context) {\n            this.context = context;\n            this.clientStates = new Resource$Devices$Deviceusers$Clientstates(this.context);\n        }\n        approve(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://cloudidentity.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1/{+name}:approve').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'POST',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['name'],\n                pathParams: ['name'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        block(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://cloudidentity.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1/{+name}:block').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'POST',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['name'],\n                pathParams: ['name'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        cancelWipe(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://cloudidentity.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1/{+name}:cancelWipe').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'POST',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['name'],\n                pathParams: ['name'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        delete(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://cloudidentity.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1/{+name}').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'DELETE',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['name'],\n                pathParams: ['name'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        get(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://cloudidentity.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1/{+name}').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'GET',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['name'],\n                pathParams: ['name'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        list(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://cloudidentity.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1/{+parent}/deviceUsers').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'GET',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['parent'],\n                pathParams: ['parent'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        lookup(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://cloudidentity.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1/{+parent}:lookup').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'GET',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['parent'],\n                pathParams: ['parent'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        wipe(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://cloudidentity.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1/{+name}:wipe').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'POST',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['name'],\n                pathParams: ['name'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n    }\n    cloudidentity_v1.Resource$Devices$Deviceusers = Resource$Devices$Deviceusers;\n    class Resource$Devices$Deviceusers$Clientstates {\n        context;\n        constructor(context) {\n            this.context = context;\n        }\n        get(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://cloudidentity.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1/{+name}').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'GET',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['name'],\n                pathParams: ['name'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        list(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://cloudidentity.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1/{+parent}/clientStates').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'GET',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['parent'],\n                pathParams: ['parent'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        patch(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://cloudidentity.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1/{+name}').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'PATCH',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['name'],\n                pathParams: ['name'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n    }\n    cloudidentity_v1.Resource$Devices$Deviceusers$Clientstates = Resource$Devices$Deviceusers$Clientstates;\n    class Resource$Groups {\n        context;\n        memberships;\n        constructor(context) {\n            this.context = context;\n            this.memberships = new Resource$Groups$Memberships(this.context);\n        }\n        create(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback || {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://cloudidentity.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1/groups').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'POST',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: [],\n                pathParams: [],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        delete(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback || {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://cloudidentity.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1/{+name}').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'DELETE',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['name'],\n                pathParams: ['name'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        get(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback || {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://cloudidentity.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1/{+name}').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'GET',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['name'],\n                pathParams: ['name'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        getSecuritySettings(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://cloudidentity.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1/{+name}').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'GET',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['name'],\n                pathParams: ['name'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        list(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback || {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://cloudidentity.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1/groups').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'GET',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: [],\n                pathParams: [],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        lookup(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback || {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://cloudidentity.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1/groups:lookup').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'GET',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: [],\n                pathParams: [],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        patch(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback || {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://cloudidentity.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1/{+name}').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'PATCH',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['name'],\n                pathParams: ['name'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        search(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback || {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://cloudidentity.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1/groups:search').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'GET',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: [],\n                pathParams: [],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        updateSecuritySettings(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://cloudidentity.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1/{+name}').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'PATCH',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['name'],\n                pathParams: ['name'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n    }\n    cloudidentity_v1.Resource$Groups = Resource$Groups;\n    class Resource$Groups$Memberships {\n        context;\n        constructor(context) {\n            this.context = context;\n        }\n        checkTransitiveMembership(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params =\n                    {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://cloudidentity.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1/{+parent}/memberships:checkTransitiveMembership').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'GET',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['parent'],\n                pathParams: ['parent'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        create(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://cloudidentity.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1/{+parent}/memberships').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'POST',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['parent'],\n                pathParams: ['parent'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        delete(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://cloudidentity.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1/{+name}').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'DELETE',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['name'],\n                pathParams: ['name'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        get(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://cloudidentity.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1/{+name}').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'GET',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['name'],\n                pathParams: ['name'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        getMembershipGraph(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://cloudidentity.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1/{+parent}/memberships:getMembershipGraph').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'GET',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['parent'],\n                pathParams: ['parent'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        list(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://cloudidentity.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1/{+parent}/memberships').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'GET',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['parent'],\n                pathParams: ['parent'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        lookup(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://cloudidentity.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1/{+parent}/memberships:lookup').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'GET',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['parent'],\n                pathParams: ['parent'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        modifyMembershipRoles(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://cloudidentity.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1/{+name}:modifyMembershipRoles').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'POST',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['name'],\n                pathParams: ['name'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        searchDirectGroups(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://cloudidentity.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1/{+parent}/memberships:searchDirectGroups').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'GET',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['parent'],\n                pathParams: ['parent'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        searchTransitiveGroups(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params =\n                    {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://cloudidentity.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1/{+parent}/memberships:searchTransitiveGroups').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'GET',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['parent'],\n                pathParams: ['parent'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        searchTransitiveMemberships(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params =\n                    {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://cloudidentity.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1/{+parent}/memberships:searchTransitiveMemberships').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'GET',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['parent'],\n                pathParams: ['parent'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n    }\n    cloudidentity_v1.Resource$Groups$Memberships = Resource$Groups$Memberships;\n    class Resource$Inboundsamlssoprofiles {\n        context;\n        idpCredentials;\n        constructor(context) {\n            this.context = context;\n            this.idpCredentials = new Resource$Inboundsamlssoprofiles$Idpcredentials(this.context);\n        }\n        create(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://cloudidentity.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1/inboundSamlSsoProfiles').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'POST',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: [],\n                pathParams: [],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        delete(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://cloudidentity.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1/{+name}').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'DELETE',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['name'],\n                pathParams: ['name'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        get(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://cloudidentity.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1/{+name}').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'GET',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['name'],\n                pathParams: ['name'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        list(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://cloudidentity.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1/inboundSamlSsoProfiles').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'GET',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: [],\n                pathParams: [],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        patch(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://cloudidentity.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1/{+name}').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'PATCH',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['name'],\n                pathParams: ['name'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n    }\n    cloudidentity_v1.Resource$Inboundsamlssoprofiles = Resource$Inboundsamlssoprofiles;\n    class Resource$Inboundsamlssoprofiles$Idpcredentials {\n        context;\n        constructor(context) {\n            this.context = context;\n        }\n        add(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params =\n                    {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://cloudidentity.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1/{+parent}/idpCredentials:add').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'POST',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['parent'],\n                pathParams: ['parent'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        delete(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params =\n                    {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://cloudidentity.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1/{+name}').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'DELETE',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['name'],\n                pathParams: ['name'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        get(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params =\n                    {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://cloudidentity.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1/{+name}').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'GET',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['name'],\n                pathParams: ['name'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        list(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params =\n                    {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://cloudidentity.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1/{+parent}/idpCredentials').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'GET',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['parent'],\n                pathParams: ['parent'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n    }\n    cloudidentity_v1.Resource$Inboundsamlssoprofiles$Idpcredentials = Resource$Inboundsamlssoprofiles$Idpcredentials;\n    class Resource$Inboundssoassignments {\n        context;\n        constructor(context) {\n            this.context = context;\n        }\n        create(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://cloudidentity.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1/inboundSsoAssignments').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'POST',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: [],\n                pathParams: [],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        delete(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://cloudidentity.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1/{+name}').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'DELETE',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['name'],\n                pathParams: ['name'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        get(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://cloudidentity.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1/{+name}').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'GET',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['name'],\n                pathParams: ['name'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        list(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://cloudidentity.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1/inboundSsoAssignments').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'GET',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: [],\n                pathParams: [],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        patch(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://cloudidentity.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1/{+name}').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'PATCH',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['name'],\n                pathParams: ['name'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n    }\n    cloudidentity_v1.Resource$Inboundssoassignments = Resource$Inboundssoassignments;\n    class Resource$Policies {\n        context;\n        constructor(context) {\n            this.context = context;\n        }\n        get(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback || {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://cloudidentity.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1/{+name}').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'GET',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['name'],\n                pathParams: ['name'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        list(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback || {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://cloudidentity.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1/policies').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'GET',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: [],\n                pathParams: [],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n    }\n    cloudidentity_v1.Resource$Policies = Resource$Policies;\n})(cloudidentity_v1 || (exports.cloudidentity_v1 = cloudidentity_v1 = {}));\n"], "names": [], "mappings": "AAAA;AACA,4BAA4B;AAC5B,kEAAkE;AAClE,mEAAmE;AACnE,0CAA0C;AAC1C,EAAE;AACF,gDAAgD;AAChD,EAAE;AACF,sEAAsE;AACtE,oEAAoE;AACpE,2EAA2E;AAC3E,sEAAsE;AACtE,iCAAiC;AACjC,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,QAAQ,gBAAgB,GAAG,KAAK;AAChC,qDAAqD,GACrD,oDAAoD,GACpD,wDAAwD,GACxD,kDAAkD,GAClD,0CAA0C,GAC1C,MAAM;AACN,IAAI;AACJ,CAAC,SAAU,gBAAgB;IACvB;;;;;;;;;;KAUC,GACD,MAAM;QACF,QAAQ;QACR,UAAU;QACV,QAAQ;QACR,OAAO;QACP,uBAAuB;QACvB,sBAAsB;QACtB,SAAS;QACT,YAAY,OAAO,EAAE,MAAM,CAAE;YACzB,IAAI,CAAC,OAAO,GAAG;gBACX,UAAU,WAAW,CAAC;gBACtB;YACJ;YACA,IAAI,CAAC,SAAS,GAAG,IAAI,mBAAmB,IAAI,CAAC,OAAO;YACpD,IAAI,CAAC,OAAO,GAAG,IAAI,iBAAiB,IAAI,CAAC,OAAO;YAChD,IAAI,CAAC,MAAM,GAAG,IAAI,gBAAgB,IAAI,CAAC,OAAO;YAC9C,IAAI,CAAC,sBAAsB,GAAG,IAAI,gCAAgC,IAAI,CAAC,OAAO;YAC9E,IAAI,CAAC,qBAAqB,GAAG,IAAI,+BAA+B,IAAI,CAAC,OAAO;YAC5E,IAAI,CAAC,QAAQ,GAAG,IAAI,kBAAkB,IAAI,CAAC,OAAO;QACtD;IACJ;IACA,iBAAiB,aAAa,GAAG;IACjC,MAAM;QACF,QAAQ;QACR,gBAAgB;QAChB,YAAY,OAAO,CAAE;YACjB,IAAI,CAAC,OAAO,GAAG;YACf,IAAI,CAAC,eAAe,GAAG,IAAI,mCAAmC,IAAI,CAAC,OAAO;QAC9E;IACJ;IACA,iBAAiB,kBAAkB,GAAG;IACtC,MAAM;QACF,QAAQ;QACR,YAAY,OAAO,CAAE;YACjB,IAAI,CAAC,OAAO,GAAG;QACnB;QACA,OAAO,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAClD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,oBAAoB,EAAE,OAAO,CAAC,gBAAgB;oBAC9D,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAO;gBACxB,YAAY;oBAAC;iBAAO;gBACpB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,IAAI,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAC/C,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,aAAa,EAAE,OAAO,CAAC,gBAAgB;oBACvD,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAO;gBACxB,YAAY;oBAAC;iBAAO;gBACpB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,gBAAgB,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAC3D,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SACI,CAAC;gBACL,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,6BAA6B,EAAE,OAAO,CAAC,gBAAgB;oBACvE,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAO;gBACxB,YAAY;oBAAC;iBAAO;gBACpB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,KAAK,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAChD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,+BAA+B,EAAE,OAAO,CAAC,gBAAgB;oBACzE,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAS;gBAC1B,YAAY;oBAAC;iBAAS;gBACtB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,KAAK,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAChD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,kBAAkB,EAAE,OAAO,CAAC,gBAAgB;oBAC5D,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAO;gBACxB,YAAY;oBAAC;iBAAO;gBACpB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;IACJ;IACA,iBAAiB,kCAAkC,GAAG;IACtD,MAAM;QACF,QAAQ;QACR,YAAY;QACZ,YAAY,OAAO,CAAE;YACjB,IAAI,CAAC,OAAO,GAAG;YACf,IAAI,CAAC,WAAW,GAAG,IAAI,6BAA6B,IAAI,CAAC,OAAO;QACpE;QACA,WAAW,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YACtD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,wBAAwB,EAAE,OAAO,CAAC,gBAAgB;oBAClE,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAO;gBACxB,YAAY;oBAAC;iBAAO;gBACpB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,OAAO,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAClD,IAAI,SAAU,oBAAoB,CAAC;YACnC,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,aAAa,EAAE,OAAO,CAAC,gBAAgB;oBACvD,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB,EAAE;gBAClB,YAAY,EAAE;gBACd,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,OAAO,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAClD,IAAI,SAAU,oBAAoB,CAAC;YACnC,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,aAAa,EAAE,OAAO,CAAC,gBAAgB;oBACvD,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAO;gBACxB,YAAY;oBAAC;iBAAO;gBACpB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,IAAI,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAC/C,IAAI,SAAU,oBAAoB,CAAC;YACnC,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,aAAa,EAAE,OAAO,CAAC,gBAAgB;oBACvD,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAO;gBACxB,YAAY;oBAAC;iBAAO;gBACpB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,KAAK,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAChD,IAAI,SAAU,oBAAoB,CAAC;YACnC,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,aAAa,EAAE,OAAO,CAAC,gBAAgB;oBACvD,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB,EAAE;gBAClB,YAAY,EAAE;gBACd,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,KAAK,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAChD,IAAI,SAAU,oBAAoB,CAAC;YACnC,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,kBAAkB,EAAE,OAAO,CAAC,gBAAgB;oBAC5D,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAO;gBACxB,YAAY;oBAAC;iBAAO;gBACpB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;IACJ;IACA,iBAAiB,gBAAgB,GAAG;IACpC,MAAM;QACF,QAAQ;QACR,aAAa;QACb,YAAY,OAAO,CAAE;YACjB,IAAI,CAAC,OAAO,GAAG;YACf,IAAI,CAAC,YAAY,GAAG,IAAI,0CAA0C,IAAI,CAAC,OAAO;QAClF;QACA,QAAQ,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YACnD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,qBAAqB,EAAE,OAAO,CAAC,gBAAgB;oBAC/D,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAO;gBACxB,YAAY;oBAAC;iBAAO;gBACpB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,MAAM,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YACjD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,mBAAmB,EAAE,OAAO,CAAC,gBAAgB;oBAC7D,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAO;gBACxB,YAAY;oBAAC;iBAAO;gBACpB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,WAAW,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YACtD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,wBAAwB,EAAE,OAAO,CAAC,gBAAgB;oBAClE,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAO;gBACxB,YAAY;oBAAC;iBAAO;gBACpB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,OAAO,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAClD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,aAAa,EAAE,OAAO,CAAC,gBAAgB;oBACvD,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAO;gBACxB,YAAY;oBAAC;iBAAO;gBACpB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,IAAI,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAC/C,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,aAAa,EAAE,OAAO,CAAC,gBAAgB;oBACvD,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAO;gBACxB,YAAY;oBAAC;iBAAO;gBACpB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,KAAK,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAChD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,2BAA2B,EAAE,OAAO,CAAC,gBAAgB;oBACrE,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAS;gBAC1B,YAAY;oBAAC;iBAAS;gBACtB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,OAAO,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAClD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,sBAAsB,EAAE,OAAO,CAAC,gBAAgB;oBAChE,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAS;gBAC1B,YAAY;oBAAC;iBAAS;gBACtB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,KAAK,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAChD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,kBAAkB,EAAE,OAAO,CAAC,gBAAgB;oBAC5D,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAO;gBACxB,YAAY;oBAAC;iBAAO;gBACpB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;IACJ;IACA,iBAAiB,4BAA4B,GAAG;IAChD,MAAM;QACF,QAAQ;QACR,YAAY,OAAO,CAAE;YACjB,IAAI,CAAC,OAAO,GAAG;QACnB;QACA,IAAI,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAC/C,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,aAAa,EAAE,OAAO,CAAC,gBAAgB;oBACvD,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAO;gBACxB,YAAY;oBAAC;iBAAO;gBACpB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,KAAK,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAChD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,4BAA4B,EAAE,OAAO,CAAC,gBAAgB;oBACtE,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAS;gBAC1B,YAAY;oBAAC;iBAAS;gBACtB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,MAAM,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YACjD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,aAAa,EAAE,OAAO,CAAC,gBAAgB;oBACvD,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAO;gBACxB,YAAY;oBAAC;iBAAO;gBACpB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;IACJ;IACA,iBAAiB,yCAAyC,GAAG;IAC7D,MAAM;QACF,QAAQ;QACR,YAAY;QACZ,YAAY,OAAO,CAAE;YACjB,IAAI,CAAC,OAAO,GAAG;YACf,IAAI,CAAC,WAAW,GAAG,IAAI,4BAA4B,IAAI,CAAC,OAAO;QACnE;QACA,OAAO,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAClD,IAAI,SAAU,oBAAoB,CAAC;YACnC,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,YAAY,EAAE,OAAO,CAAC,gBAAgB;oBACtD,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB,EAAE;gBAClB,YAAY,EAAE;gBACd,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,OAAO,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAClD,IAAI,SAAU,oBAAoB,CAAC;YACnC,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,aAAa,EAAE,OAAO,CAAC,gBAAgB;oBACvD,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAO;gBACxB,YAAY;oBAAC;iBAAO;gBACpB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,IAAI,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAC/C,IAAI,SAAU,oBAAoB,CAAC;YACnC,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,aAAa,EAAE,OAAO,CAAC,gBAAgB;oBACvD,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAO;gBACxB,YAAY;oBAAC;iBAAO;gBACpB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,oBAAoB,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAC/D,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,aAAa,EAAE,OAAO,CAAC,gBAAgB;oBACvD,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAO;gBACxB,YAAY;oBAAC;iBAAO;gBACpB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,KAAK,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAChD,IAAI,SAAU,oBAAoB,CAAC;YACnC,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,YAAY,EAAE,OAAO,CAAC,gBAAgB;oBACtD,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB,EAAE;gBAClB,YAAY,EAAE;gBACd,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,OAAO,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAClD,IAAI,SAAU,oBAAoB,CAAC;YACnC,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,mBAAmB,EAAE,OAAO,CAAC,gBAAgB;oBAC7D,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB,EAAE;gBAClB,YAAY,EAAE;gBACd,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,MAAM,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YACjD,IAAI,SAAU,oBAAoB,CAAC;YACnC,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,aAAa,EAAE,OAAO,CAAC,gBAAgB;oBACvD,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAO;gBACxB,YAAY;oBAAC;iBAAO;gBACpB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,OAAO,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAClD,IAAI,SAAU,oBAAoB,CAAC;YACnC,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,mBAAmB,EAAE,OAAO,CAAC,gBAAgB;oBAC7D,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB,EAAE;gBAClB,YAAY,EAAE;gBACd,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,uBAAuB,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAClE,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,aAAa,EAAE,OAAO,CAAC,gBAAgB;oBACvD,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAO;gBACxB,YAAY;oBAAC;iBAAO;gBACpB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;IACJ;IACA,iBAAiB,eAAe,GAAG;IACnC,MAAM;QACF,QAAQ;QACR,YAAY,OAAO,CAAE;YACjB,IAAI,CAAC,OAAO,GAAG;QACnB;QACA,0BAA0B,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YACrE,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SACI,CAAC;gBACL,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,qDAAqD,EAAE,OAAO,CAAC,gBAAgB;oBAC/F,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAS;gBAC1B,YAAY;oBAAC;iBAAS;gBACtB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,OAAO,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAClD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,2BAA2B,EAAE,OAAO,CAAC,gBAAgB;oBACrE,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAS;gBAC1B,YAAY;oBAAC;iBAAS;gBACtB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,OAAO,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAClD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,aAAa,EAAE,OAAO,CAAC,gBAAgB;oBACvD,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAO;gBACxB,YAAY;oBAAC;iBAAO;gBACpB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,IAAI,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAC/C,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,aAAa,EAAE,OAAO,CAAC,gBAAgB;oBACvD,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAO;gBACxB,YAAY;oBAAC;iBAAO;gBACpB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,mBAAmB,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAC9D,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,8CAA8C,EAAE,OAAO,CAAC,gBAAgB;oBACxF,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAS;gBAC1B,YAAY;oBAAC;iBAAS;gBACtB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,KAAK,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAChD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,2BAA2B,EAAE,OAAO,CAAC,gBAAgB;oBACrE,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAS;gBAC1B,YAAY;oBAAC;iBAAS;gBACtB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,OAAO,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAClD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,kCAAkC,EAAE,OAAO,CAAC,gBAAgB;oBAC5E,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAS;gBAC1B,YAAY;oBAAC;iBAAS;gBACtB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,sBAAsB,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YACjE,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,mCAAmC,EAAE,OAAO,CAAC,gBAAgB;oBAC7E,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAO;gBACxB,YAAY;oBAAC;iBAAO;gBACpB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,mBAAmB,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAC9D,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,8CAA8C,EAAE,OAAO,CAAC,gBAAgB;oBACxF,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAS;gBAC1B,YAAY;oBAAC;iBAAS;gBACtB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,uBAAuB,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAClE,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SACI,CAAC;gBACL,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,kDAAkD,EAAE,OAAO,CAAC,gBAAgB;oBAC5F,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAS;gBAC1B,YAAY;oBAAC;iBAAS;gBACtB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,4BAA4B,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YACvE,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SACI,CAAC;gBACL,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,uDAAuD,EAAE,OAAO,CAAC,gBAAgB;oBACjG,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAS;gBAC1B,YAAY;oBAAC;iBAAS;gBACtB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;IACJ;IACA,iBAAiB,2BAA2B,GAAG;IAC/C,MAAM;QACF,QAAQ;QACR,eAAe;QACf,YAAY,OAAO,CAAE;YACjB,IAAI,CAAC,OAAO,GAAG;YACf,IAAI,CAAC,cAAc,GAAG,IAAI,+CAA+C,IAAI,CAAC,OAAO;QACzF;QACA,OAAO,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAClD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,4BAA4B,EAAE,OAAO,CAAC,gBAAgB;oBACtE,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB,EAAE;gBAClB,YAAY,EAAE;gBACd,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,OAAO,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAClD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,aAAa,EAAE,OAAO,CAAC,gBAAgB;oBACvD,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAO;gBACxB,YAAY;oBAAC;iBAAO;gBACpB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,IAAI,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAC/C,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,aAAa,EAAE,OAAO,CAAC,gBAAgB;oBACvD,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAO;gBACxB,YAAY;oBAAC;iBAAO;gBACpB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,KAAK,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAChD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,4BAA4B,EAAE,OAAO,CAAC,gBAAgB;oBACtE,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB,EAAE;gBAClB,YAAY,EAAE;gBACd,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,MAAM,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YACjD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,aAAa,EAAE,OAAO,CAAC,gBAAgB;oBACvD,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAO;gBACxB,YAAY;oBAAC;iBAAO;gBACpB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;IACJ;IACA,iBAAiB,+BAA+B,GAAG;IACnD,MAAM;QACF,QAAQ;QACR,YAAY,OAAO,CAAE;YACjB,IAAI,CAAC,OAAO,GAAG;QACnB;QACA,IAAI,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAC/C,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SACI,CAAC;gBACL,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,kCAAkC,EAAE,OAAO,CAAC,gBAAgB;oBAC5E,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAS;gBAC1B,YAAY;oBAAC;iBAAS;gBACtB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,OAAO,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAClD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SACI,CAAC;gBACL,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,aAAa,EAAE,OAAO,CAAC,gBAAgB;oBACvD,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAO;gBACxB,YAAY;oBAAC;iBAAO;gBACpB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,IAAI,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAC/C,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SACI,CAAC;gBACL,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,aAAa,EAAE,OAAO,CAAC,gBAAgB;oBACvD,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAO;gBACxB,YAAY;oBAAC;iBAAO;gBACpB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,KAAK,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAChD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SACI,CAAC;gBACL,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,8BAA8B,EAAE,OAAO,CAAC,gBAAgB;oBACxE,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAS;gBAC1B,YAAY;oBAAC;iBAAS;gBACtB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;IACJ;IACA,iBAAiB,8CAA8C,GAAG;IAClE,MAAM;QACF,QAAQ;QACR,YAAY,OAAO,CAAE;YACjB,IAAI,CAAC,OAAO,GAAG;QACnB;QACA,OAAO,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAClD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,2BAA2B,EAAE,OAAO,CAAC,gBAAgB;oBACrE,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB,EAAE;gBAClB,YAAY,EAAE;gBACd,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,OAAO,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAClD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,aAAa,EAAE,OAAO,CAAC,gBAAgB;oBACvD,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAO;gBACxB,YAAY;oBAAC;iBAAO;gBACpB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,IAAI,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAC/C,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,aAAa,EAAE,OAAO,CAAC,gBAAgB;oBACvD,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAO;gBACxB,YAAY;oBAAC;iBAAO;gBACpB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,KAAK,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAChD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,2BAA2B,EAAE,OAAO,CAAC,gBAAgB;oBACrE,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB,EAAE;gBAClB,YAAY,EAAE;gBACd,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,MAAM,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YACjD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,aAAa,EAAE,OAAO,CAAC,gBAAgB;oBACvD,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAO;gBACxB,YAAY;oBAAC;iBAAO;gBACpB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;IACJ;IACA,iBAAiB,8BAA8B,GAAG;IAClD,MAAM;QACF,QAAQ;QACR,YAAY,OAAO,CAAE;YACjB,IAAI,CAAC,OAAO,GAAG;QACnB;QACA,IAAI,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAC/C,IAAI,SAAU,oBAAoB,CAAC;YACnC,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,aAAa,EAAE,OAAO,CAAC,gBAAgB;oBACvD,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAO;gBACxB,YAAY;oBAAC;iBAAO;gBACpB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,KAAK,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAChD,IAAI,SAAU,oBAAoB,CAAC;YACnC,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,cAAc,EAAE,OAAO,CAAC,gBAAgB;oBACxD,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB,EAAE;gBAClB,YAAY,EAAE;gBACd,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;IACJ;IACA,iBAAiB,iBAAiB,GAAG;AACzC,CAAC,EAAE,oBAAoB,CAAC,QAAQ,gBAAgB,GAAG,mBAAmB,CAAC,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2079, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/AI_Projects/CRMcriadores/crm-interno/node_modules/googleapis/build/src/apis/cloudidentity/v1beta1.js"], "sourcesContent": ["\"use strict\";\n// Copyright 2020 Google LLC\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//    http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.cloudidentity_v1beta1 = void 0;\n/* eslint-disable @typescript-eslint/no-explicit-any */\n/* eslint-disable @typescript-eslint/no-unused-vars */\n/* eslint-disable @typescript-eslint/no-empty-interface */\n/* eslint-disable @typescript-eslint/no-namespace */\n/* eslint-disable no-irregular-whitespace */\nconst googleapis_common_1 = require(\"googleapis-common\");\nvar cloudidentity_v1beta1;\n(function (cloudidentity_v1beta1) {\n    /**\n     * Cloud Identity API\n     *\n     * API for provisioning and managing identity resources.\n     *\n     * @example\n     * ```js\n     * const {google} = require('googleapis');\n     * const cloudidentity = google.cloudidentity('v1beta1');\n     * ```\n     */\n    class Cloudidentity {\n        context;\n        customers;\n        devices;\n        groups;\n        inboundSamlSsoProfiles;\n        inboundSsoAssignments;\n        orgUnits;\n        policies;\n        constructor(options, google) {\n            this.context = {\n                _options: options || {},\n                google,\n            };\n            this.customers = new Resource$Customers(this.context);\n            this.devices = new Resource$Devices(this.context);\n            this.groups = new Resource$Groups(this.context);\n            this.inboundSamlSsoProfiles = new Resource$Inboundsamlssoprofiles(this.context);\n            this.inboundSsoAssignments = new Resource$Inboundssoassignments(this.context);\n            this.orgUnits = new Resource$Orgunits(this.context);\n            this.policies = new Resource$Policies(this.context);\n        }\n    }\n    cloudidentity_v1beta1.Cloudidentity = Cloudidentity;\n    class Resource$Customers {\n        context;\n        userinvitations;\n        constructor(context) {\n            this.context = context;\n            this.userinvitations = new Resource$Customers$Userinvitations(this.context);\n        }\n    }\n    cloudidentity_v1beta1.Resource$Customers = Resource$Customers;\n    class Resource$Customers$Userinvitations {\n        context;\n        constructor(context) {\n            this.context = context;\n        }\n        cancel(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://cloudidentity.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1beta1/{+name}:cancel').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'POST',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['name'],\n                pathParams: ['name'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        get(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://cloudidentity.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1beta1/{+name}').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'GET',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['name'],\n                pathParams: ['name'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        isInvitableUser(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params =\n                    {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://cloudidentity.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1beta1/{+name}:isInvitableUser').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'GET',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['name'],\n                pathParams: ['name'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        list(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://cloudidentity.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1beta1/{+parent}/userinvitations').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'GET',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['parent'],\n                pathParams: ['parent'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        send(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://cloudidentity.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1beta1/{+name}:send').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'POST',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['name'],\n                pathParams: ['name'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n    }\n    cloudidentity_v1beta1.Resource$Customers$Userinvitations = Resource$Customers$Userinvitations;\n    class Resource$Devices {\n        context;\n        deviceUsers;\n        constructor(context) {\n            this.context = context;\n            this.deviceUsers = new Resource$Devices$Deviceusers(this.context);\n        }\n        cancelWipe(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://cloudidentity.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1beta1/{+name}:cancelWipe').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'POST',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['name'],\n                pathParams: ['name'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        create(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback || {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://cloudidentity.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1beta1/devices').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'POST',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: [],\n                pathParams: [],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        delete(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback || {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://cloudidentity.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1beta1/{+name}').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'DELETE',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['name'],\n                pathParams: ['name'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        get(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback || {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://cloudidentity.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1beta1/{+name}').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'GET',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['name'],\n                pathParams: ['name'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        list(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback || {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://cloudidentity.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1beta1/devices').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'GET',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: [],\n                pathParams: [],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        wipe(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback || {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://cloudidentity.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1beta1/{+name}:wipe').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'POST',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['name'],\n                pathParams: ['name'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n    }\n    cloudidentity_v1beta1.Resource$Devices = Resource$Devices;\n    class Resource$Devices$Deviceusers {\n        context;\n        clientStates;\n        constructor(context) {\n            this.context = context;\n            this.clientStates = new Resource$Devices$Deviceusers$Clientstates(this.context);\n        }\n        approve(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://cloudidentity.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1beta1/{+name}:approve').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'POST',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['name'],\n                pathParams: ['name'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        block(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://cloudidentity.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1beta1/{+name}:block').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'POST',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['name'],\n                pathParams: ['name'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        cancelWipe(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://cloudidentity.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1beta1/{+name}:cancelWipe').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'POST',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['name'],\n                pathParams: ['name'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        delete(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://cloudidentity.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1beta1/{+name}').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'DELETE',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['name'],\n                pathParams: ['name'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        get(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://cloudidentity.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1beta1/{+name}').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'GET',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['name'],\n                pathParams: ['name'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        list(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://cloudidentity.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1beta1/{+parent}/deviceUsers').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'GET',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['parent'],\n                pathParams: ['parent'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        lookup(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://cloudidentity.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1beta1/{+parent}:lookup').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'GET',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['parent'],\n                pathParams: ['parent'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        wipe(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://cloudidentity.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1beta1/{+name}:wipe').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'POST',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['name'],\n                pathParams: ['name'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n    }\n    cloudidentity_v1beta1.Resource$Devices$Deviceusers = Resource$Devices$Deviceusers;\n    class Resource$Devices$Deviceusers$Clientstates {\n        context;\n        constructor(context) {\n            this.context = context;\n        }\n        get(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://cloudidentity.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1beta1/{+name}').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'GET',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['name'],\n                pathParams: ['name'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        patch(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://cloudidentity.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1beta1/{+name}').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'PATCH',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['name'],\n                pathParams: ['name'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n    }\n    cloudidentity_v1beta1.Resource$Devices$Deviceusers$Clientstates = Resource$Devices$Deviceusers$Clientstates;\n    class Resource$Groups {\n        context;\n        memberships;\n        constructor(context) {\n            this.context = context;\n            this.memberships = new Resource$Groups$Memberships(this.context);\n        }\n        create(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback || {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://cloudidentity.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1beta1/groups').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'POST',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: [],\n                pathParams: [],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        delete(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback || {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://cloudidentity.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1beta1/{+name}').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'DELETE',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['name'],\n                pathParams: ['name'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        get(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback || {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://cloudidentity.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1beta1/{+name}').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'GET',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['name'],\n                pathParams: ['name'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        getSecuritySettings(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://cloudidentity.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1beta1/{+name}').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'GET',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['name'],\n                pathParams: ['name'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        list(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback || {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://cloudidentity.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1beta1/groups').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'GET',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: [],\n                pathParams: [],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        lookup(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback || {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://cloudidentity.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1beta1/groups:lookup').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'GET',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: [],\n                pathParams: [],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        patch(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback || {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://cloudidentity.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1beta1/{+name}').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'PATCH',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['name'],\n                pathParams: ['name'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        search(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback || {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://cloudidentity.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1beta1/groups:search').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'GET',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: [],\n                pathParams: [],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        updateSecuritySettings(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://cloudidentity.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1beta1/{+name}').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'PATCH',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['name'],\n                pathParams: ['name'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n    }\n    cloudidentity_v1beta1.Resource$Groups = Resource$Groups;\n    class Resource$Groups$Memberships {\n        context;\n        constructor(context) {\n            this.context = context;\n        }\n        checkTransitiveMembership(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params =\n                    {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://cloudidentity.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl +\n                        '/v1beta1/{+parent}/memberships:checkTransitiveMembership').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'GET',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['parent'],\n                pathParams: ['parent'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        create(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://cloudidentity.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1beta1/{+parent}/memberships').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'POST',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['parent'],\n                pathParams: ['parent'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        delete(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://cloudidentity.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1beta1/{+name}').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'DELETE',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['name'],\n                pathParams: ['name'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        get(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://cloudidentity.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1beta1/{+name}').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'GET',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['name'],\n                pathParams: ['name'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        getMembershipGraph(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://cloudidentity.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1beta1/{+parent}/memberships:getMembershipGraph').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'GET',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['parent'],\n                pathParams: ['parent'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        list(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://cloudidentity.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1beta1/{+parent}/memberships').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'GET',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['parent'],\n                pathParams: ['parent'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        lookup(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://cloudidentity.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1beta1/{+parent}/memberships:lookup').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'GET',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['parent'],\n                pathParams: ['parent'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        modifyMembershipRoles(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://cloudidentity.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1beta1/{+name}:modifyMembershipRoles').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'POST',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['name'],\n                pathParams: ['name'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        searchDirectGroups(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://cloudidentity.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1beta1/{+parent}/memberships:searchDirectGroups').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'GET',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['parent'],\n                pathParams: ['parent'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        searchTransitiveGroups(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params =\n                    {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://cloudidentity.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1beta1/{+parent}/memberships:searchTransitiveGroups').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'GET',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['parent'],\n                pathParams: ['parent'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        searchTransitiveMemberships(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params =\n                    {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://cloudidentity.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl +\n                        '/v1beta1/{+parent}/memberships:searchTransitiveMemberships').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'GET',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['parent'],\n                pathParams: ['parent'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n    }\n    cloudidentity_v1beta1.Resource$Groups$Memberships = Resource$Groups$Memberships;\n    class Resource$Inboundsamlssoprofiles {\n        context;\n        idpCredentials;\n        constructor(context) {\n            this.context = context;\n            this.idpCredentials = new Resource$Inboundsamlssoprofiles$Idpcredentials(this.context);\n        }\n        create(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://cloudidentity.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1beta1/inboundSamlSsoProfiles').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'POST',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: [],\n                pathParams: [],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        delete(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://cloudidentity.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1beta1/{+name}').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'DELETE',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['name'],\n                pathParams: ['name'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        get(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://cloudidentity.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1beta1/{+name}').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'GET',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['name'],\n                pathParams: ['name'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        list(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://cloudidentity.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1beta1/inboundSamlSsoProfiles').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'GET',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: [],\n                pathParams: [],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        patch(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://cloudidentity.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1beta1/{+name}').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'PATCH',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['name'],\n                pathParams: ['name'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n    }\n    cloudidentity_v1beta1.Resource$Inboundsamlssoprofiles = Resource$Inboundsamlssoprofiles;\n    class Resource$Inboundsamlssoprofiles$Idpcredentials {\n        context;\n        constructor(context) {\n            this.context = context;\n        }\n        add(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params =\n                    {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://cloudidentity.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1beta1/{+parent}/idpCredentials:add').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'POST',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['parent'],\n                pathParams: ['parent'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        delete(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params =\n                    {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://cloudidentity.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1beta1/{+name}').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'DELETE',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['name'],\n                pathParams: ['name'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        get(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params =\n                    {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://cloudidentity.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1beta1/{+name}').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'GET',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['name'],\n                pathParams: ['name'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        list(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params =\n                    {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://cloudidentity.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1beta1/{+parent}/idpCredentials').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'GET',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['parent'],\n                pathParams: ['parent'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n    }\n    cloudidentity_v1beta1.Resource$Inboundsamlssoprofiles$Idpcredentials = Resource$Inboundsamlssoprofiles$Idpcredentials;\n    class Resource$Inboundssoassignments {\n        context;\n        constructor(context) {\n            this.context = context;\n        }\n        create(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://cloudidentity.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1beta1/inboundSsoAssignments').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'POST',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: [],\n                pathParams: [],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        delete(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://cloudidentity.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1beta1/{+name}').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'DELETE',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['name'],\n                pathParams: ['name'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        get(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://cloudidentity.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1beta1/{+name}').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'GET',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['name'],\n                pathParams: ['name'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        list(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://cloudidentity.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1beta1/inboundSsoAssignments').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'GET',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: [],\n                pathParams: [],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        patch(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://cloudidentity.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1beta1/{+name}').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'PATCH',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['name'],\n                pathParams: ['name'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n    }\n    cloudidentity_v1beta1.Resource$Inboundssoassignments = Resource$Inboundssoassignments;\n    class Resource$Orgunits {\n        context;\n        memberships;\n        constructor(context) {\n            this.context = context;\n            this.memberships = new Resource$Orgunits$Memberships(this.context);\n        }\n    }\n    cloudidentity_v1beta1.Resource$Orgunits = Resource$Orgunits;\n    class Resource$Orgunits$Memberships {\n        context;\n        constructor(context) {\n            this.context = context;\n        }\n        list(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://cloudidentity.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1beta1/{+parent}/memberships').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'GET',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['parent'],\n                pathParams: ['parent'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        move(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://cloudidentity.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1beta1/{+name}:move').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'POST',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['name'],\n                pathParams: ['name'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n    }\n    cloudidentity_v1beta1.Resource$Orgunits$Memberships = Resource$Orgunits$Memberships;\n    class Resource$Policies {\n        context;\n        constructor(context) {\n            this.context = context;\n        }\n        get(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback || {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://cloudidentity.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1beta1/{+name}').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'GET',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['name'],\n                pathParams: ['name'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        list(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback || {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://cloudidentity.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1beta1/policies').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'GET',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: [],\n                pathParams: [],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n    }\n    cloudidentity_v1beta1.Resource$Policies = Resource$Policies;\n})(cloudidentity_v1beta1 || (exports.cloudidentity_v1beta1 = cloudidentity_v1beta1 = {}));\n"], "names": [], "mappings": "AAAA;AACA,4BAA4B;AAC5B,kEAAkE;AAClE,mEAAmE;AACnE,0CAA0C;AAC1C,EAAE;AACF,gDAAgD;AAChD,EAAE;AACF,sEAAsE;AACtE,oEAAoE;AACpE,2EAA2E;AAC3E,sEAAsE;AACtE,iCAAiC;AACjC,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,QAAQ,qBAAqB,GAAG,KAAK;AACrC,qDAAqD,GACrD,oDAAoD,GACpD,wDAAwD,GACxD,kDAAkD,GAClD,0CAA0C,GAC1C,MAAM;AACN,IAAI;AACJ,CAAC,SAAU,qBAAqB;IAC5B;;;;;;;;;;KAUC,GACD,MAAM;QACF,QAAQ;QACR,UAAU;QACV,QAAQ;QACR,OAAO;QACP,uBAAuB;QACvB,sBAAsB;QACtB,SAAS;QACT,SAAS;QACT,YAAY,OAAO,EAAE,MAAM,CAAE;YACzB,IAAI,CAAC,OAAO,GAAG;gBACX,UAAU,WAAW,CAAC;gBACtB;YACJ;YACA,IAAI,CAAC,SAAS,GAAG,IAAI,mBAAmB,IAAI,CAAC,OAAO;YACpD,IAAI,CAAC,OAAO,GAAG,IAAI,iBAAiB,IAAI,CAAC,OAAO;YAChD,IAAI,CAAC,MAAM,GAAG,IAAI,gBAAgB,IAAI,CAAC,OAAO;YAC9C,IAAI,CAAC,sBAAsB,GAAG,IAAI,gCAAgC,IAAI,CAAC,OAAO;YAC9E,IAAI,CAAC,qBAAqB,GAAG,IAAI,+BAA+B,IAAI,CAAC,OAAO;YAC5E,IAAI,CAAC,QAAQ,GAAG,IAAI,kBAAkB,IAAI,CAAC,OAAO;YAClD,IAAI,CAAC,QAAQ,GAAG,IAAI,kBAAkB,IAAI,CAAC,OAAO;QACtD;IACJ;IACA,sBAAsB,aAAa,GAAG;IACtC,MAAM;QACF,QAAQ;QACR,gBAAgB;QAChB,YAAY,OAAO,CAAE;YACjB,IAAI,CAAC,OAAO,GAAG;YACf,IAAI,CAAC,eAAe,GAAG,IAAI,mCAAmC,IAAI,CAAC,OAAO;QAC9E;IACJ;IACA,sBAAsB,kBAAkB,GAAG;IAC3C,MAAM;QACF,QAAQ;QACR,YAAY,OAAO,CAAE;YACjB,IAAI,CAAC,OAAO,GAAG;QACnB;QACA,OAAO,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAClD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,yBAAyB,EAAE,OAAO,CAAC,gBAAgB;oBACnE,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAO;gBACxB,YAAY;oBAAC;iBAAO;gBACpB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,IAAI,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAC/C,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,kBAAkB,EAAE,OAAO,CAAC,gBAAgB;oBAC5D,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAO;gBACxB,YAAY;oBAAC;iBAAO;gBACpB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,gBAAgB,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAC3D,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SACI,CAAC;gBACL,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,kCAAkC,EAAE,OAAO,CAAC,gBAAgB;oBAC5E,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAO;gBACxB,YAAY;oBAAC;iBAAO;gBACpB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,KAAK,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAChD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,oCAAoC,EAAE,OAAO,CAAC,gBAAgB;oBAC9E,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAS;gBAC1B,YAAY;oBAAC;iBAAS;gBACtB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,KAAK,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAChD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,uBAAuB,EAAE,OAAO,CAAC,gBAAgB;oBACjE,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAO;gBACxB,YAAY;oBAAC;iBAAO;gBACpB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;IACJ;IACA,sBAAsB,kCAAkC,GAAG;IAC3D,MAAM;QACF,QAAQ;QACR,YAAY;QACZ,YAAY,OAAO,CAAE;YACjB,IAAI,CAAC,OAAO,GAAG;YACf,IAAI,CAAC,WAAW,GAAG,IAAI,6BAA6B,IAAI,CAAC,OAAO;QACpE;QACA,WAAW,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YACtD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,6BAA6B,EAAE,OAAO,CAAC,gBAAgB;oBACvE,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAO;gBACxB,YAAY;oBAAC;iBAAO;gBACpB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,OAAO,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAClD,IAAI,SAAU,oBAAoB,CAAC;YACnC,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,kBAAkB,EAAE,OAAO,CAAC,gBAAgB;oBAC5D,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB,EAAE;gBAClB,YAAY,EAAE;gBACd,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,OAAO,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAClD,IAAI,SAAU,oBAAoB,CAAC;YACnC,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,kBAAkB,EAAE,OAAO,CAAC,gBAAgB;oBAC5D,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAO;gBACxB,YAAY;oBAAC;iBAAO;gBACpB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,IAAI,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAC/C,IAAI,SAAU,oBAAoB,CAAC;YACnC,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,kBAAkB,EAAE,OAAO,CAAC,gBAAgB;oBAC5D,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAO;gBACxB,YAAY;oBAAC;iBAAO;gBACpB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,KAAK,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAChD,IAAI,SAAU,oBAAoB,CAAC;YACnC,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,kBAAkB,EAAE,OAAO,CAAC,gBAAgB;oBAC5D,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB,EAAE;gBAClB,YAAY,EAAE;gBACd,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,KAAK,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAChD,IAAI,SAAU,oBAAoB,CAAC;YACnC,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,uBAAuB,EAAE,OAAO,CAAC,gBAAgB;oBACjE,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAO;gBACxB,YAAY;oBAAC;iBAAO;gBACpB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;IACJ;IACA,sBAAsB,gBAAgB,GAAG;IACzC,MAAM;QACF,QAAQ;QACR,aAAa;QACb,YAAY,OAAO,CAAE;YACjB,IAAI,CAAC,OAAO,GAAG;YACf,IAAI,CAAC,YAAY,GAAG,IAAI,0CAA0C,IAAI,CAAC,OAAO;QAClF;QACA,QAAQ,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YACnD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,0BAA0B,EAAE,OAAO,CAAC,gBAAgB;oBACpE,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAO;gBACxB,YAAY;oBAAC;iBAAO;gBACpB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,MAAM,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YACjD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,wBAAwB,EAAE,OAAO,CAAC,gBAAgB;oBAClE,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAO;gBACxB,YAAY;oBAAC;iBAAO;gBACpB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,WAAW,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YACtD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,6BAA6B,EAAE,OAAO,CAAC,gBAAgB;oBACvE,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAO;gBACxB,YAAY;oBAAC;iBAAO;gBACpB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,OAAO,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAClD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,kBAAkB,EAAE,OAAO,CAAC,gBAAgB;oBAC5D,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAO;gBACxB,YAAY;oBAAC;iBAAO;gBACpB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,IAAI,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAC/C,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,kBAAkB,EAAE,OAAO,CAAC,gBAAgB;oBAC5D,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAO;gBACxB,YAAY;oBAAC;iBAAO;gBACpB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,KAAK,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAChD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,gCAAgC,EAAE,OAAO,CAAC,gBAAgB;oBAC1E,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAS;gBAC1B,YAAY;oBAAC;iBAAS;gBACtB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,OAAO,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAClD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,2BAA2B,EAAE,OAAO,CAAC,gBAAgB;oBACrE,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAS;gBAC1B,YAAY;oBAAC;iBAAS;gBACtB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,KAAK,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAChD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,uBAAuB,EAAE,OAAO,CAAC,gBAAgB;oBACjE,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAO;gBACxB,YAAY;oBAAC;iBAAO;gBACpB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;IACJ;IACA,sBAAsB,4BAA4B,GAAG;IACrD,MAAM;QACF,QAAQ;QACR,YAAY,OAAO,CAAE;YACjB,IAAI,CAAC,OAAO,GAAG;QACnB;QACA,IAAI,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAC/C,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,kBAAkB,EAAE,OAAO,CAAC,gBAAgB;oBAC5D,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAO;gBACxB,YAAY;oBAAC;iBAAO;gBACpB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,MAAM,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YACjD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,kBAAkB,EAAE,OAAO,CAAC,gBAAgB;oBAC5D,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAO;gBACxB,YAAY;oBAAC;iBAAO;gBACpB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;IACJ;IACA,sBAAsB,yCAAyC,GAAG;IAClE,MAAM;QACF,QAAQ;QACR,YAAY;QACZ,YAAY,OAAO,CAAE;YACjB,IAAI,CAAC,OAAO,GAAG;YACf,IAAI,CAAC,WAAW,GAAG,IAAI,4BAA4B,IAAI,CAAC,OAAO;QACnE;QACA,OAAO,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAClD,IAAI,SAAU,oBAAoB,CAAC;YACnC,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,iBAAiB,EAAE,OAAO,CAAC,gBAAgB;oBAC3D,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB,EAAE;gBAClB,YAAY,EAAE;gBACd,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,OAAO,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAClD,IAAI,SAAU,oBAAoB,CAAC;YACnC,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,kBAAkB,EAAE,OAAO,CAAC,gBAAgB;oBAC5D,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAO;gBACxB,YAAY;oBAAC;iBAAO;gBACpB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,IAAI,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAC/C,IAAI,SAAU,oBAAoB,CAAC;YACnC,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,kBAAkB,EAAE,OAAO,CAAC,gBAAgB;oBAC5D,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAO;gBACxB,YAAY;oBAAC;iBAAO;gBACpB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,oBAAoB,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAC/D,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,kBAAkB,EAAE,OAAO,CAAC,gBAAgB;oBAC5D,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAO;gBACxB,YAAY;oBAAC;iBAAO;gBACpB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,KAAK,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAChD,IAAI,SAAU,oBAAoB,CAAC;YACnC,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,iBAAiB,EAAE,OAAO,CAAC,gBAAgB;oBAC3D,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB,EAAE;gBAClB,YAAY,EAAE;gBACd,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,OAAO,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAClD,IAAI,SAAU,oBAAoB,CAAC;YACnC,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,wBAAwB,EAAE,OAAO,CAAC,gBAAgB;oBAClE,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB,EAAE;gBAClB,YAAY,EAAE;gBACd,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,MAAM,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YACjD,IAAI,SAAU,oBAAoB,CAAC;YACnC,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,kBAAkB,EAAE,OAAO,CAAC,gBAAgB;oBAC5D,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAO;gBACxB,YAAY;oBAAC;iBAAO;gBACpB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,OAAO,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAClD,IAAI,SAAU,oBAAoB,CAAC;YACnC,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,wBAAwB,EAAE,OAAO,CAAC,gBAAgB;oBAClE,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB,EAAE;gBAClB,YAAY,EAAE;gBACd,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,uBAAuB,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAClE,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,kBAAkB,EAAE,OAAO,CAAC,gBAAgB;oBAC5D,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAO;gBACxB,YAAY;oBAAC;iBAAO;gBACpB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;IACJ;IACA,sBAAsB,eAAe,GAAG;IACxC,MAAM;QACF,QAAQ;QACR,YAAY,OAAO,CAAE;YACjB,IAAI,CAAC,OAAO,GAAG;QACnB;QACA,0BAA0B,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YACrE,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SACI,CAAC;gBACL,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UACF,0DAA0D,EAAE,OAAO,CAAC,gBAAgB;oBACxF,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAS;gBAC1B,YAAY;oBAAC;iBAAS;gBACtB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,OAAO,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAClD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,gCAAgC,EAAE,OAAO,CAAC,gBAAgB;oBAC1E,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAS;gBAC1B,YAAY;oBAAC;iBAAS;gBACtB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,OAAO,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAClD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,kBAAkB,EAAE,OAAO,CAAC,gBAAgB;oBAC5D,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAO;gBACxB,YAAY;oBAAC;iBAAO;gBACpB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,IAAI,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAC/C,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,kBAAkB,EAAE,OAAO,CAAC,gBAAgB;oBAC5D,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAO;gBACxB,YAAY;oBAAC;iBAAO;gBACpB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,mBAAmB,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAC9D,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,mDAAmD,EAAE,OAAO,CAAC,gBAAgB;oBAC7F,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAS;gBAC1B,YAAY;oBAAC;iBAAS;gBACtB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,KAAK,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAChD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,gCAAgC,EAAE,OAAO,CAAC,gBAAgB;oBAC1E,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAS;gBAC1B,YAAY;oBAAC;iBAAS;gBACtB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,OAAO,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAClD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,uCAAuC,EAAE,OAAO,CAAC,gBAAgB;oBACjF,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAS;gBAC1B,YAAY;oBAAC;iBAAS;gBACtB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,sBAAsB,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YACjE,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,wCAAwC,EAAE,OAAO,CAAC,gBAAgB;oBAClF,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAO;gBACxB,YAAY;oBAAC;iBAAO;gBACpB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,mBAAmB,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAC9D,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,mDAAmD,EAAE,OAAO,CAAC,gBAAgB;oBAC7F,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAS;gBAC1B,YAAY;oBAAC;iBAAS;gBACtB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,uBAAuB,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAClE,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SACI,CAAC;gBACL,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,uDAAuD,EAAE,OAAO,CAAC,gBAAgB;oBACjG,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAS;gBAC1B,YAAY;oBAAC;iBAAS;gBACtB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,4BAA4B,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YACvE,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SACI,CAAC;gBACL,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UACF,4DAA4D,EAAE,OAAO,CAAC,gBAAgB;oBAC1F,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAS;gBAC1B,YAAY;oBAAC;iBAAS;gBACtB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;IACJ;IACA,sBAAsB,2BAA2B,GAAG;IACpD,MAAM;QACF,QAAQ;QACR,eAAe;QACf,YAAY,OAAO,CAAE;YACjB,IAAI,CAAC,OAAO,GAAG;YACf,IAAI,CAAC,cAAc,GAAG,IAAI,+CAA+C,IAAI,CAAC,OAAO;QACzF;QACA,OAAO,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAClD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,iCAAiC,EAAE,OAAO,CAAC,gBAAgB;oBAC3E,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB,EAAE;gBAClB,YAAY,EAAE;gBACd,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,OAAO,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAClD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,kBAAkB,EAAE,OAAO,CAAC,gBAAgB;oBAC5D,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAO;gBACxB,YAAY;oBAAC;iBAAO;gBACpB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,IAAI,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAC/C,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,kBAAkB,EAAE,OAAO,CAAC,gBAAgB;oBAC5D,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAO;gBACxB,YAAY;oBAAC;iBAAO;gBACpB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,KAAK,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAChD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,iCAAiC,EAAE,OAAO,CAAC,gBAAgB;oBAC3E,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB,EAAE;gBAClB,YAAY,EAAE;gBACd,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,MAAM,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YACjD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,kBAAkB,EAAE,OAAO,CAAC,gBAAgB;oBAC5D,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAO;gBACxB,YAAY;oBAAC;iBAAO;gBACpB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;IACJ;IACA,sBAAsB,+BAA+B,GAAG;IACxD,MAAM;QACF,QAAQ;QACR,YAAY,OAAO,CAAE;YACjB,IAAI,CAAC,OAAO,GAAG;QACnB;QACA,IAAI,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAC/C,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SACI,CAAC;gBACL,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,uCAAuC,EAAE,OAAO,CAAC,gBAAgB;oBACjF,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAS;gBAC1B,YAAY;oBAAC;iBAAS;gBACtB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,OAAO,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAClD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SACI,CAAC;gBACL,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,kBAAkB,EAAE,OAAO,CAAC,gBAAgB;oBAC5D,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAO;gBACxB,YAAY;oBAAC;iBAAO;gBACpB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,IAAI,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAC/C,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SACI,CAAC;gBACL,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,kBAAkB,EAAE,OAAO,CAAC,gBAAgB;oBAC5D,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAO;gBACxB,YAAY;oBAAC;iBAAO;gBACpB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,KAAK,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAChD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SACI,CAAC;gBACL,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,mCAAmC,EAAE,OAAO,CAAC,gBAAgB;oBAC7E,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAS;gBAC1B,YAAY;oBAAC;iBAAS;gBACtB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;IACJ;IACA,sBAAsB,8CAA8C,GAAG;IACvE,MAAM;QACF,QAAQ;QACR,YAAY,OAAO,CAAE;YACjB,IAAI,CAAC,OAAO,GAAG;QACnB;QACA,OAAO,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAClD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,gCAAgC,EAAE,OAAO,CAAC,gBAAgB;oBAC1E,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB,EAAE;gBAClB,YAAY,EAAE;gBACd,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,OAAO,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAClD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,kBAAkB,EAAE,OAAO,CAAC,gBAAgB;oBAC5D,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAO;gBACxB,YAAY;oBAAC;iBAAO;gBACpB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,IAAI,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAC/C,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,kBAAkB,EAAE,OAAO,CAAC,gBAAgB;oBAC5D,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAO;gBACxB,YAAY;oBAAC;iBAAO;gBACpB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,KAAK,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAChD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,gCAAgC,EAAE,OAAO,CAAC,gBAAgB;oBAC1E,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB,EAAE;gBAClB,YAAY,EAAE;gBACd,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,MAAM,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YACjD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,kBAAkB,EAAE,OAAO,CAAC,gBAAgB;oBAC5D,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAO;gBACxB,YAAY;oBAAC;iBAAO;gBACpB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;IACJ;IACA,sBAAsB,8BAA8B,GAAG;IACvD,MAAM;QACF,QAAQ;QACR,YAAY;QACZ,YAAY,OAAO,CAAE;YACjB,IAAI,CAAC,OAAO,GAAG;YACf,IAAI,CAAC,WAAW,GAAG,IAAI,8BAA8B,IAAI,CAAC,OAAO;QACrE;IACJ;IACA,sBAAsB,iBAAiB,GAAG;IAC1C,MAAM;QACF,QAAQ;QACR,YAAY,OAAO,CAAE;YACjB,IAAI,CAAC,OAAO,GAAG;QACnB;QACA,KAAK,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAChD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,gCAAgC,EAAE,OAAO,CAAC,gBAAgB;oBAC1E,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAS;gBAC1B,YAAY;oBAAC;iBAAS;gBACtB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,KAAK,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAChD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,uBAAuB,EAAE,OAAO,CAAC,gBAAgB;oBACjE,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAO;gBACxB,YAAY;oBAAC;iBAAO;gBACpB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;IACJ;IACA,sBAAsB,6BAA6B,GAAG;IACtD,MAAM;QACF,QAAQ;QACR,YAAY,OAAO,CAAE;YACjB,IAAI,CAAC,OAAO,GAAG;QACnB;QACA,IAAI,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAC/C,IAAI,SAAU,oBAAoB,CAAC;YACnC,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,kBAAkB,EAAE,OAAO,CAAC,gBAAgB;oBAC5D,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAO;gBACxB,YAAY;oBAAC;iBAAO;gBACpB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,KAAK,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAChD,IAAI,SAAU,oBAAoB,CAAC;YACnC,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,mBAAmB,EAAE,OAAO,CAAC,gBAAgB;oBAC7D,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB,EAAE;gBAClB,YAAY,EAAE;gBACd,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;IACJ;IACA,sBAAsB,iBAAiB,GAAG;AAC9C,CAAC,EAAE,yBAAyB,CAAC,QAAQ,qBAAqB,GAAG,wBAAwB,CAAC,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4204, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/AI_Projects/CRMcriadores/crm-interno/node_modules/googleapis/build/src/apis/cloudidentity/index.js"], "sourcesContent": ["\"use strict\";\n// Copyright 2020 Google LLC\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//    http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.AuthPlus = exports.cloudidentity_v1beta1 = exports.cloudidentity_v1 = exports.auth = exports.VERSIONS = void 0;\nexports.cloudidentity = cloudidentity;\n/*! THIS FILE IS AUTO-GENERATED */\nconst googleapis_common_1 = require(\"googleapis-common\");\nconst v1_1 = require(\"./v1\");\nObject.defineProperty(exports, \"cloudidentity_v1\", { enumerable: true, get: function () { return v1_1.cloudidentity_v1; } });\nconst v1beta1_1 = require(\"./v1beta1\");\nObject.defineProperty(exports, \"cloudidentity_v1beta1\", { enumerable: true, get: function () { return v1beta1_1.cloudidentity_v1beta1; } });\nexports.VERSIONS = {\n    v1: v1_1.cloudidentity_v1.Cloudidentity,\n    v1beta1: v1beta1_1.cloudidentity_v1beta1.Cloudidentity,\n};\nfunction cloudidentity(versionOrOptions) {\n    return (0, googleapis_common_1.getAPI)('cloudidentity', versionOrOptions, exports.VERSIONS, this);\n}\nconst auth = new googleapis_common_1.AuthPlus();\nexports.auth = auth;\nvar googleapis_common_2 = require(\"googleapis-common\");\nObject.defineProperty(exports, \"AuthPlus\", { enumerable: true, get: function () { return googleapis_common_2.AuthPlus; } });\n"], "names": [], "mappings": "AAAA;AACA,4BAA4B;AAC5B,kEAAkE;AAClE,mEAAmE;AACnE,0CAA0C;AAC1C,EAAE;AACF,gDAAgD;AAChD,EAAE;AACF,sEAAsE;AACtE,oEAAoE;AACpE,2EAA2E;AAC3E,sEAAsE;AACtE,iCAAiC;AACjC,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,QAAQ,QAAQ,GAAG,QAAQ,qBAAqB,GAAG,QAAQ,gBAAgB,GAAG,QAAQ,IAAI,GAAG,QAAQ,QAAQ,GAAG,KAAK;AACrH,QAAQ,aAAa,GAAG;AACxB,gCAAgC,GAChC,MAAM;AACN,MAAM;AACN,OAAO,cAAc,CAAC,SAAS,oBAAoB;IAAE,YAAY;IAAM,KAAK;QAAc,OAAO,KAAK,gBAAgB;IAAE;AAAE;AAC1H,MAAM;AACN,OAAO,cAAc,CAAC,SAAS,yBAAyB;IAAE,YAAY;IAAM,KAAK;QAAc,OAAO,UAAU,qBAAqB;IAAE;AAAE;AACzI,QAAQ,QAAQ,GAAG;IACf,IAAI,KAAK,gBAAgB,CAAC,aAAa;IACvC,SAAS,UAAU,qBAAqB,CAAC,aAAa;AAC1D;AACA,SAAS,cAAc,gBAAgB;IACnC,OAAO,CAAC,GAAG,oBAAoB,MAAM,EAAE,iBAAiB,kBAAkB,QAAQ,QAAQ,EAAE,IAAI;AACpG;AACA,MAAM,OAAO,IAAI,oBAAoB,QAAQ;AAC7C,QAAQ,IAAI,GAAG;AACf,IAAI;AACJ,OAAO,cAAc,CAAC,SAAS,YAAY;IAAE,YAAY;IAAM,KAAK;QAAc,OAAO,oBAAoB,QAAQ;IAAE;AAAE", "ignoreList": [0], "debugId": null}}]}
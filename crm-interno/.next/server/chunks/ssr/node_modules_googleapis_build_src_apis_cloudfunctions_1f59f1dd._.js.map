{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/AI_Projects/CRMcriadores/crm-interno/node_modules/googleapis/build/src/apis/cloudfunctions/v1.js"], "sourcesContent": ["\"use strict\";\n// Copyright 2020 Google LLC\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//    http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.cloudfunctions_v1 = void 0;\n/* eslint-disable @typescript-eslint/no-explicit-any */\n/* eslint-disable @typescript-eslint/no-unused-vars */\n/* eslint-disable @typescript-eslint/no-empty-interface */\n/* eslint-disable @typescript-eslint/no-namespace */\n/* eslint-disable no-irregular-whitespace */\nconst googleapis_common_1 = require(\"googleapis-common\");\nvar cloudfunctions_v1;\n(function (cloudfunctions_v1) {\n    /**\n     * Cloud Functions API\n     *\n     * Manages lightweight user-provided functions executed in response to events.\n     *\n     * @example\n     * ```js\n     * const {google} = require('googleapis');\n     * const cloudfunctions = google.cloudfunctions('v1');\n     * ```\n     */\n    class Cloudfunctions {\n        context;\n        operations;\n        projects;\n        constructor(options, google) {\n            this.context = {\n                _options: options || {},\n                google,\n            };\n            this.operations = new Resource$Operations(this.context);\n            this.projects = new Resource$Projects(this.context);\n        }\n    }\n    cloudfunctions_v1.Cloudfunctions = Cloudfunctions;\n    class Resource$Operations {\n        context;\n        constructor(context) {\n            this.context = context;\n        }\n        get(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback || {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://cloudfunctions.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1/{+name}').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'GET',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['name'],\n                pathParams: ['name'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        list(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback || {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://cloudfunctions.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1/operations').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'GET',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: [],\n                pathParams: [],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n    }\n    cloudfunctions_v1.Resource$Operations = Resource$Operations;\n    class Resource$Projects {\n        context;\n        locations;\n        constructor(context) {\n            this.context = context;\n            this.locations = new Resource$Projects$Locations(this.context);\n        }\n    }\n    cloudfunctions_v1.Resource$Projects = Resource$Projects;\n    class Resource$Projects$Locations {\n        context;\n        functions;\n        constructor(context) {\n            this.context = context;\n            this.functions = new Resource$Projects$Locations$Functions(this.context);\n        }\n        list(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://cloudfunctions.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1/{+name}/locations').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'GET',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['name'],\n                pathParams: ['name'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n    }\n    cloudfunctions_v1.Resource$Projects$Locations = Resource$Projects$Locations;\n    class Resource$Projects$Locations$Functions {\n        context;\n        constructor(context) {\n            this.context = context;\n        }\n        call(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://cloudfunctions.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1/{+name}:call').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'POST',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['name'],\n                pathParams: ['name'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        create(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://cloudfunctions.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1/{+location}/functions').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'POST',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['location'],\n                pathParams: ['location'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        delete(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://cloudfunctions.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1/{+name}').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'DELETE',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['name'],\n                pathParams: ['name'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        generateDownloadUrl(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params =\n                    {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://cloudfunctions.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1/{+name}:generateDownloadUrl').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'POST',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['name'],\n                pathParams: ['name'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        generateUploadUrl(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params =\n                    {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://cloudfunctions.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1/{+parent}/functions:generateUploadUrl').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'POST',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['parent'],\n                pathParams: ['parent'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        get(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://cloudfunctions.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1/{+name}').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'GET',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['name'],\n                pathParams: ['name'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        getIamPolicy(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params =\n                    {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://cloudfunctions.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1/{+resource}:getIamPolicy').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'GET',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['resource'],\n                pathParams: ['resource'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        list(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://cloudfunctions.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1/{+parent}/functions').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'GET',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['parent'],\n                pathParams: ['parent'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        patch(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://cloudfunctions.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1/{+name}').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'PATCH',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['name'],\n                pathParams: ['name'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        setIamPolicy(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params =\n                    {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://cloudfunctions.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1/{+resource}:setIamPolicy').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'POST',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['resource'],\n                pathParams: ['resource'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        testIamPermissions(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params =\n                    {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://cloudfunctions.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1/{+resource}:testIamPermissions').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'POST',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['resource'],\n                pathParams: ['resource'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n    }\n    cloudfunctions_v1.Resource$Projects$Locations$Functions = Resource$Projects$Locations$Functions;\n})(cloudfunctions_v1 || (exports.cloudfunctions_v1 = cloudfunctions_v1 = {}));\n"], "names": [], "mappings": "AAAA;AACA,4BAA4B;AAC5B,kEAAkE;AAClE,mEAAmE;AACnE,0CAA0C;AAC1C,EAAE;AACF,gDAAgD;AAChD,EAAE;AACF,sEAAsE;AACtE,oEAAoE;AACpE,2EAA2E;AAC3E,sEAAsE;AACtE,iCAAiC;AACjC,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,QAAQ,iBAAiB,GAAG,KAAK;AACjC,qDAAqD,GACrD,oDAAoD,GACpD,wDAAwD,GACxD,kDAAkD,GAClD,0CAA0C,GAC1C,MAAM;AACN,IAAI;AACJ,CAAC,SAAU,iBAAiB;IACxB;;;;;;;;;;KAUC,GACD,MAAM;QACF,QAAQ;QACR,WAAW;QACX,SAAS;QACT,YAAY,OAAO,EAAE,MAAM,CAAE;YACzB,IAAI,CAAC,OAAO,GAAG;gBACX,UAAU,WAAW,CAAC;gBACtB;YACJ;YACA,IAAI,CAAC,UAAU,GAAG,IAAI,oBAAoB,IAAI,CAAC,OAAO;YACtD,IAAI,CAAC,QAAQ,GAAG,IAAI,kBAAkB,IAAI,CAAC,OAAO;QACtD;IACJ;IACA,kBAAkB,cAAc,GAAG;IACnC,MAAM;QACF,QAAQ;QACR,YAAY,OAAO,CAAE;YACjB,IAAI,CAAC,OAAO,GAAG;QACnB;QACA,IAAI,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAC/C,IAAI,SAAU,oBAAoB,CAAC;YACnC,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,aAAa,EAAE,OAAO,CAAC,gBAAgB;oBACvD,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAO;gBACxB,YAAY;oBAAC;iBAAO;gBACpB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,KAAK,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAChD,IAAI,SAAU,oBAAoB,CAAC;YACnC,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,gBAAgB,EAAE,OAAO,CAAC,gBAAgB;oBAC1D,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB,EAAE;gBAClB,YAAY,EAAE;gBACd,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;IACJ;IACA,kBAAkB,mBAAmB,GAAG;IACxC,MAAM;QACF,QAAQ;QACR,UAAU;QACV,YAAY,OAAO,CAAE;YACjB,IAAI,CAAC,OAAO,GAAG;YACf,IAAI,CAAC,SAAS,GAAG,IAAI,4BAA4B,IAAI,CAAC,OAAO;QACjE;IACJ;IACA,kBAAkB,iBAAiB,GAAG;IACtC,MAAM;QACF,QAAQ;QACR,UAAU;QACV,YAAY,OAAO,CAAE;YACjB,IAAI,CAAC,OAAO,GAAG;YACf,IAAI,CAAC,SAAS,GAAG,IAAI,sCAAsC,IAAI,CAAC,OAAO;QAC3E;QACA,KAAK,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAChD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,uBAAuB,EAAE,OAAO,CAAC,gBAAgB;oBACjE,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAO;gBACxB,YAAY;oBAAC;iBAAO;gBACpB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;IACJ;IACA,kBAAkB,2BAA2B,GAAG;IAChD,MAAM;QACF,QAAQ;QACR,YAAY,OAAO,CAAE;YACjB,IAAI,CAAC,OAAO,GAAG;QACnB;QACA,KAAK,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAChD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,kBAAkB,EAAE,OAAO,CAAC,gBAAgB;oBAC5D,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAO;gBACxB,YAAY;oBAAC;iBAAO;gBACpB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,OAAO,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAClD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,2BAA2B,EAAE,OAAO,CAAC,gBAAgB;oBACrE,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAW;gBAC5B,YAAY;oBAAC;iBAAW;gBACxB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,OAAO,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAClD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,aAAa,EAAE,OAAO,CAAC,gBAAgB;oBACvD,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAO;gBACxB,YAAY;oBAAC;iBAAO;gBACpB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,oBAAoB,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAC/D,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SACI,CAAC;gBACL,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,iCAAiC,EAAE,OAAO,CAAC,gBAAgB;oBAC3E,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAO;gBACxB,YAAY;oBAAC;iBAAO;gBACpB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,kBAAkB,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAC7D,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SACI,CAAC;gBACL,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,2CAA2C,EAAE,OAAO,CAAC,gBAAgB;oBACrF,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAS;gBAC1B,YAAY;oBAAC;iBAAS;gBACtB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,IAAI,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAC/C,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,aAAa,EAAE,OAAO,CAAC,gBAAgB;oBACvD,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAO;gBACxB,YAAY;oBAAC;iBAAO;gBACpB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,aAAa,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YACxD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SACI,CAAC;gBACL,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,8BAA8B,EAAE,OAAO,CAAC,gBAAgB;oBACxE,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAW;gBAC5B,YAAY;oBAAC;iBAAW;gBACxB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,KAAK,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAChD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,yBAAyB,EAAE,OAAO,CAAC,gBAAgB;oBACnE,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAS;gBAC1B,YAAY;oBAAC;iBAAS;gBACtB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,MAAM,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YACjD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,aAAa,EAAE,OAAO,CAAC,gBAAgB;oBACvD,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAO;gBACxB,YAAY;oBAAC;iBAAO;gBACpB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,aAAa,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YACxD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SACI,CAAC;gBACL,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,8BAA8B,EAAE,OAAO,CAAC,gBAAgB;oBACxE,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAW;gBAC5B,YAAY;oBAAC;iBAAW;gBACxB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,mBAAmB,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAC9D,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SACI,CAAC;gBACL,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,oCAAoC,EAAE,OAAO,CAAC,gBAAgB;oBAC9E,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAW;gBAC5B,YAAY;oBAAC;iBAAW;gBACxB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;IACJ;IACA,kBAAkB,qCAAqC,GAAG;AAC9D,CAAC,EAAE,qBAAqB,CAAC,QAAQ,iBAAiB,GAAG,oBAAoB,CAAC,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 560, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/AI_Projects/CRMcriadores/crm-interno/node_modules/googleapis/build/src/apis/cloudfunctions/v1beta2.js"], "sourcesContent": ["\"use strict\";\n// Copyright 2020 Google LLC\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//    http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.cloudfunctions_v1beta2 = void 0;\n/* eslint-disable @typescript-eslint/no-explicit-any */\n/* eslint-disable @typescript-eslint/class-name-casing */\n/* eslint-disable @typescript-eslint/no-unused-vars */\n/* eslint-disable @typescript-eslint/no-empty-interface */\n/* eslint-disable @typescript-eslint/no-namespace */\n/* eslint-disable no-irregular-whitespace */\nconst googleapis_common_1 = require(\"googleapis-common\");\nvar cloudfunctions_v1beta2;\n(function (cloudfunctions_v1beta2) {\n    /**\n     * Cloud Functions API\n     *\n     * Manages lightweight user-provided functions executed in response to events.\n     *\n     * @example\n     * const {google} = require('googleapis');\n     * const cloudfunctions = google.cloudfunctions('v1beta2');\n     *\n     * @namespace cloudfunctions\n     * @type {Function}\n     * @version v1beta2\n     * @variation v1beta2\n     * @param {object=} options Options for Cloudfunctions\n     */\n    class Cloudfunctions {\n        context;\n        operations;\n        projects;\n        constructor(options, google) {\n            this.context = {\n                _options: options || {},\n                google,\n            };\n            this.operations = new Resource$Operations(this.context);\n            this.projects = new Resource$Projects(this.context);\n        }\n    }\n    cloudfunctions_v1beta2.Cloudfunctions = Cloudfunctions;\n    class Resource$Operations {\n        context;\n        constructor(context) {\n            this.context = context;\n        }\n        get(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback || {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://cloudfunctions.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1beta2/{+name}').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'GET',\n                }, options),\n                params,\n                requiredParams: ['name'],\n                pathParams: ['name'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        list(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback || {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://cloudfunctions.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1beta2/operations').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'GET',\n                }, options),\n                params,\n                requiredParams: [],\n                pathParams: [],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n    }\n    cloudfunctions_v1beta2.Resource$Operations = Resource$Operations;\n    class Resource$Projects {\n        context;\n        locations;\n        constructor(context) {\n            this.context = context;\n            this.locations = new Resource$Projects$Locations(this.context);\n        }\n    }\n    cloudfunctions_v1beta2.Resource$Projects = Resource$Projects;\n    class Resource$Projects$Locations {\n        context;\n        functions;\n        constructor(context) {\n            this.context = context;\n            this.functions = new Resource$Projects$Locations$Functions(this.context);\n        }\n        list(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://cloudfunctions.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1beta2/{+name}/locations').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'GET',\n                }, options),\n                params,\n                requiredParams: ['name'],\n                pathParams: ['name'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n    }\n    cloudfunctions_v1beta2.Resource$Projects$Locations = Resource$Projects$Locations;\n    class Resource$Projects$Locations$Functions {\n        context;\n        constructor(context) {\n            this.context = context;\n        }\n        call(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://cloudfunctions.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1beta2/{+name}:call').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'POST',\n                }, options),\n                params,\n                requiredParams: ['name'],\n                pathParams: ['name'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        create(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://cloudfunctions.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1beta2/{+location}/functions').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'POST',\n                }, options),\n                params,\n                requiredParams: ['location'],\n                pathParams: ['location'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        delete(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://cloudfunctions.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1beta2/{+name}').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'DELETE',\n                }, options),\n                params,\n                requiredParams: ['name'],\n                pathParams: ['name'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        generateDownloadUrl(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://cloudfunctions.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1beta2/{+name}:generateDownloadUrl').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'POST',\n                }, options),\n                params,\n                requiredParams: ['name'],\n                pathParams: ['name'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        generateUploadUrl(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://cloudfunctions.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1beta2/{+parent}/functions:generateUploadUrl').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'POST',\n                }, options),\n                params,\n                requiredParams: ['parent'],\n                pathParams: ['parent'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        get(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://cloudfunctions.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1beta2/{+name}').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'GET',\n                }, options),\n                params,\n                requiredParams: ['name'],\n                pathParams: ['name'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        list(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://cloudfunctions.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1beta2/{+location}/functions').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'GET',\n                }, options),\n                params,\n                requiredParams: ['location'],\n                pathParams: ['location'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        update(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://cloudfunctions.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1beta2/{+name}').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'PUT',\n                }, options),\n                params,\n                requiredParams: ['name'],\n                pathParams: ['name'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n    }\n    cloudfunctions_v1beta2.Resource$Projects$Locations$Functions = Resource$Projects$Locations$Functions;\n})(cloudfunctions_v1beta2 || (exports.cloudfunctions_v1beta2 = cloudfunctions_v1beta2 = {}));\n"], "names": [], "mappings": "AAAA;AACA,4BAA4B;AAC5B,kEAAkE;AAClE,mEAAmE;AACnE,0CAA0C;AAC1C,EAAE;AACF,gDAAgD;AAChD,EAAE;AACF,sEAAsE;AACtE,oEAAoE;AACpE,2EAA2E;AAC3E,sEAAsE;AACtE,iCAAiC;AACjC,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,QAAQ,sBAAsB,GAAG,KAAK;AACtC,qDAAqD,GACrD,uDAAuD,GACvD,oDAAoD,GACpD,wDAAwD,GACxD,kDAAkD,GAClD,0CAA0C,GAC1C,MAAM;AACN,IAAI;AACJ,CAAC,SAAU,sBAAsB;IAC7B;;;;;;;;;;;;;;KAcC,GACD,MAAM;QACF,QAAQ;QACR,WAAW;QACX,SAAS;QACT,YAAY,OAAO,EAAE,MAAM,CAAE;YACzB,IAAI,CAAC,OAAO,GAAG;gBACX,UAAU,WAAW,CAAC;gBACtB;YACJ;YACA,IAAI,CAAC,UAAU,GAAG,IAAI,oBAAoB,IAAI,CAAC,OAAO;YACtD,IAAI,CAAC,QAAQ,GAAG,IAAI,kBAAkB,IAAI,CAAC,OAAO;QACtD;IACJ;IACA,uBAAuB,cAAc,GAAG;IACxC,MAAM;QACF,QAAQ;QACR,YAAY,OAAO,CAAE;YACjB,IAAI,CAAC,OAAO,GAAG;QACnB;QACA,IAAI,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAC/C,IAAI,SAAU,oBAAoB,CAAC;YACnC,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,kBAAkB,EAAE,OAAO,CAAC,gBAAgB;oBAC5D,QAAQ;gBACZ,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAO;gBACxB,YAAY;oBAAC;iBAAO;gBACpB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,KAAK,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAChD,IAAI,SAAU,oBAAoB,CAAC;YACnC,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,qBAAqB,EAAE,OAAO,CAAC,gBAAgB;oBAC/D,QAAQ;gBACZ,GAAG;gBACH;gBACA,gBAAgB,EAAE;gBAClB,YAAY,EAAE;gBACd,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;IACJ;IACA,uBAAuB,mBAAmB,GAAG;IAC7C,MAAM;QACF,QAAQ;QACR,UAAU;QACV,YAAY,OAAO,CAAE;YACjB,IAAI,CAAC,OAAO,GAAG;YACf,IAAI,CAAC,SAAS,GAAG,IAAI,4BAA4B,IAAI,CAAC,OAAO;QACjE;IACJ;IACA,uBAAuB,iBAAiB,GAAG;IAC3C,MAAM;QACF,QAAQ;QACR,UAAU;QACV,YAAY,OAAO,CAAE;YACjB,IAAI,CAAC,OAAO,GAAG;YACf,IAAI,CAAC,SAAS,GAAG,IAAI,sCAAsC,IAAI,CAAC,OAAO;QAC3E;QACA,KAAK,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAChD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,4BAA4B,EAAE,OAAO,CAAC,gBAAgB;oBACtE,QAAQ;gBACZ,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAO;gBACxB,YAAY;oBAAC;iBAAO;gBACpB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;IACJ;IACA,uBAAuB,2BAA2B,GAAG;IACrD,MAAM;QACF,QAAQ;QACR,YAAY,OAAO,CAAE;YACjB,IAAI,CAAC,OAAO,GAAG;QACnB;QACA,KAAK,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAChD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,uBAAuB,EAAE,OAAO,CAAC,gBAAgB;oBACjE,QAAQ;gBACZ,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAO;gBACxB,YAAY;oBAAC;iBAAO;gBACpB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,OAAO,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAClD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,gCAAgC,EAAE,OAAO,CAAC,gBAAgB;oBAC1E,QAAQ;gBACZ,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAW;gBAC5B,YAAY;oBAAC;iBAAW;gBACxB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,OAAO,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAClD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,kBAAkB,EAAE,OAAO,CAAC,gBAAgB;oBAC5D,QAAQ;gBACZ,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAO;gBACxB,YAAY;oBAAC;iBAAO;gBACpB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,oBAAoB,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAC/D,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,sCAAsC,EAAE,OAAO,CAAC,gBAAgB;oBAChF,QAAQ;gBACZ,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAO;gBACxB,YAAY;oBAAC;iBAAO;gBACpB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,kBAAkB,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAC7D,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,gDAAgD,EAAE,OAAO,CAAC,gBAAgB;oBAC1F,QAAQ;gBACZ,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAS;gBAC1B,YAAY;oBAAC;iBAAS;gBACtB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,IAAI,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAC/C,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,kBAAkB,EAAE,OAAO,CAAC,gBAAgB;oBAC5D,QAAQ;gBACZ,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAO;gBACxB,YAAY;oBAAC;iBAAO;gBACpB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,KAAK,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAChD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,gCAAgC,EAAE,OAAO,CAAC,gBAAgB;oBAC1E,QAAQ;gBACZ,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAW;gBAC5B,YAAY;oBAAC;iBAAW;gBACxB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,OAAO,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAClD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,kBAAkB,EAAE,OAAO,CAAC,gBAAgB;oBAC5D,QAAQ;gBACZ,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAO;gBACxB,YAAY;oBAAC;iBAAO;gBACpB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;IACJ;IACA,uBAAuB,qCAAqC,GAAG;AACnE,CAAC,EAAE,0BAA0B,CAAC,QAAQ,sBAAsB,GAAG,yBAAyB,CAAC,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1005, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/AI_Projects/CRMcriadores/crm-interno/node_modules/googleapis/build/src/apis/cloudfunctions/v2.js"], "sourcesContent": ["\"use strict\";\n// Copyright 2020 Google LLC\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//    http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.cloudfunctions_v2 = void 0;\n/* eslint-disable @typescript-eslint/no-explicit-any */\n/* eslint-disable @typescript-eslint/no-unused-vars */\n/* eslint-disable @typescript-eslint/no-empty-interface */\n/* eslint-disable @typescript-eslint/no-namespace */\n/* eslint-disable no-irregular-whitespace */\nconst googleapis_common_1 = require(\"googleapis-common\");\nvar cloudfunctions_v2;\n(function (cloudfunctions_v2) {\n    /**\n     * Cloud Functions API\n     *\n     * Manages lightweight user-provided functions executed in response to events.\n     *\n     * @example\n     * ```js\n     * const {google} = require('googleapis');\n     * const cloudfunctions = google.cloudfunctions('v2');\n     * ```\n     */\n    class Cloudfunctions {\n        context;\n        projects;\n        constructor(options, google) {\n            this.context = {\n                _options: options || {},\n                google,\n            };\n            this.projects = new Resource$Projects(this.context);\n        }\n    }\n    cloudfunctions_v2.Cloudfunctions = Cloudfunctions;\n    class Resource$Projects {\n        context;\n        locations;\n        constructor(context) {\n            this.context = context;\n            this.locations = new Resource$Projects$Locations(this.context);\n        }\n    }\n    cloudfunctions_v2.Resource$Projects = Resource$Projects;\n    class Resource$Projects$Locations {\n        context;\n        functions;\n        operations;\n        runtimes;\n        constructor(context) {\n            this.context = context;\n            this.functions = new Resource$Projects$Locations$Functions(this.context);\n            this.operations = new Resource$Projects$Locations$Operations(this.context);\n            this.runtimes = new Resource$Projects$Locations$Runtimes(this.context);\n        }\n        list(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://cloudfunctions.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v2/{+name}/locations').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'GET',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['name'],\n                pathParams: ['name'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n    }\n    cloudfunctions_v2.Resource$Projects$Locations = Resource$Projects$Locations;\n    class Resource$Projects$Locations$Functions {\n        context;\n        constructor(context) {\n            this.context = context;\n        }\n        abortFunctionUpgrade(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params =\n                    {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://cloudfunctions.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v2/{+name}:abortFunctionUpgrade').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'POST',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['name'],\n                pathParams: ['name'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        commitFunctionUpgrade(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params =\n                    {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://cloudfunctions.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v2/{+name}:commitFunctionUpgrade').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'POST',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['name'],\n                pathParams: ['name'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        create(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://cloudfunctions.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v2/{+parent}/functions').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'POST',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['parent'],\n                pathParams: ['parent'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        delete(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://cloudfunctions.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v2/{+name}').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'DELETE',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['name'],\n                pathParams: ['name'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        detachFunction(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params =\n                    {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://cloudfunctions.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v2/{+name}:detachFunction').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'POST',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['name'],\n                pathParams: ['name'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        generateDownloadUrl(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params =\n                    {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://cloudfunctions.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v2/{+name}:generateDownloadUrl').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'POST',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['name'],\n                pathParams: ['name'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        generateUploadUrl(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params =\n                    {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://cloudfunctions.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v2/{+parent}/functions:generateUploadUrl').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'POST',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['parent'],\n                pathParams: ['parent'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        get(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://cloudfunctions.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v2/{+name}').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'GET',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['name'],\n                pathParams: ['name'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        getIamPolicy(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params =\n                    {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://cloudfunctions.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v2/{+resource}:getIamPolicy').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'GET',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['resource'],\n                pathParams: ['resource'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        list(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://cloudfunctions.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v2/{+parent}/functions').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'GET',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['parent'],\n                pathParams: ['parent'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        patch(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://cloudfunctions.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v2/{+name}').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'PATCH',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['name'],\n                pathParams: ['name'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        redirectFunctionUpgradeTraffic(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params =\n                    {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://cloudfunctions.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v2/{+name}:redirectFunctionUpgradeTraffic').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'POST',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['name'],\n                pathParams: ['name'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        rollbackFunctionUpgradeTraffic(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params =\n                    {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://cloudfunctions.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v2/{+name}:rollbackFunctionUpgradeTraffic').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'POST',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['name'],\n                pathParams: ['name'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        setIamPolicy(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params =\n                    {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://cloudfunctions.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v2/{+resource}:setIamPolicy').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'POST',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['resource'],\n                pathParams: ['resource'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        setupFunctionUpgradeConfig(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params =\n                    {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://cloudfunctions.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v2/{+name}:setupFunctionUpgradeConfig').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'POST',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['name'],\n                pathParams: ['name'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        testIamPermissions(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params =\n                    {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://cloudfunctions.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v2/{+resource}:testIamPermissions').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'POST',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['resource'],\n                pathParams: ['resource'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n    }\n    cloudfunctions_v2.Resource$Projects$Locations$Functions = Resource$Projects$Locations$Functions;\n    class Resource$Projects$Locations$Operations {\n        context;\n        constructor(context) {\n            this.context = context;\n        }\n        get(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://cloudfunctions.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v2/{+name}').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'GET',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['name'],\n                pathParams: ['name'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        list(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://cloudfunctions.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v2/{+name}/operations').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'GET',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['name'],\n                pathParams: ['name'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n    }\n    cloudfunctions_v2.Resource$Projects$Locations$Operations = Resource$Projects$Locations$Operations;\n    class Resource$Projects$Locations$Runtimes {\n        context;\n        constructor(context) {\n            this.context = context;\n        }\n        list(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://cloudfunctions.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v2/{+parent}/runtimes').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'GET',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['parent'],\n                pathParams: ['parent'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n    }\n    cloudfunctions_v2.Resource$Projects$Locations$Runtimes = Resource$Projects$Locations$Runtimes;\n})(cloudfunctions_v2 || (exports.cloudfunctions_v2 = cloudfunctions_v2 = {}));\n"], "names": [], "mappings": "AAAA;AACA,4BAA4B;AAC5B,kEAAkE;AAClE,mEAAmE;AACnE,0CAA0C;AAC1C,EAAE;AACF,gDAAgD;AAChD,EAAE;AACF,sEAAsE;AACtE,oEAAoE;AACpE,2EAA2E;AAC3E,sEAAsE;AACtE,iCAAiC;AACjC,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,QAAQ,iBAAiB,GAAG,KAAK;AACjC,qDAAqD,GACrD,oDAAoD,GACpD,wDAAwD,GACxD,kDAAkD,GAClD,0CAA0C,GAC1C,MAAM;AACN,IAAI;AACJ,CAAC,SAAU,iBAAiB;IACxB;;;;;;;;;;KAUC,GACD,MAAM;QACF,QAAQ;QACR,SAAS;QACT,YAAY,OAAO,EAAE,MAAM,CAAE;YACzB,IAAI,CAAC,OAAO,GAAG;gBACX,UAAU,WAAW,CAAC;gBACtB;YACJ;YACA,IAAI,CAAC,QAAQ,GAAG,IAAI,kBAAkB,IAAI,CAAC,OAAO;QACtD;IACJ;IACA,kBAAkB,cAAc,GAAG;IACnC,MAAM;QACF,QAAQ;QACR,UAAU;QACV,YAAY,OAAO,CAAE;YACjB,IAAI,CAAC,OAAO,GAAG;YACf,IAAI,CAAC,SAAS,GAAG,IAAI,4BAA4B,IAAI,CAAC,OAAO;QACjE;IACJ;IACA,kBAAkB,iBAAiB,GAAG;IACtC,MAAM;QACF,QAAQ;QACR,UAAU;QACV,WAAW;QACX,SAAS;QACT,YAAY,OAAO,CAAE;YACjB,IAAI,CAAC,OAAO,GAAG;YACf,IAAI,CAAC,SAAS,GAAG,IAAI,sCAAsC,IAAI,CAAC,OAAO;YACvE,IAAI,CAAC,UAAU,GAAG,IAAI,uCAAuC,IAAI,CAAC,OAAO;YACzE,IAAI,CAAC,QAAQ,GAAG,IAAI,qCAAqC,IAAI,CAAC,OAAO;QACzE;QACA,KAAK,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAChD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,uBAAuB,EAAE,OAAO,CAAC,gBAAgB;oBACjE,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAO;gBACxB,YAAY;oBAAC;iBAAO;gBACpB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;IACJ;IACA,kBAAkB,2BAA2B,GAAG;IAChD,MAAM;QACF,QAAQ;QACR,YAAY,OAAO,CAAE;YACjB,IAAI,CAAC,OAAO,GAAG;QACnB;QACA,qBAAqB,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAChE,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SACI,CAAC;gBACL,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,kCAAkC,EAAE,OAAO,CAAC,gBAAgB;oBAC5E,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAO;gBACxB,YAAY;oBAAC;iBAAO;gBACpB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,sBAAsB,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YACjE,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SACI,CAAC;gBACL,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,mCAAmC,EAAE,OAAO,CAAC,gBAAgB;oBAC7E,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAO;gBACxB,YAAY;oBAAC;iBAAO;gBACpB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,OAAO,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAClD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,yBAAyB,EAAE,OAAO,CAAC,gBAAgB;oBACnE,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAS;gBAC1B,YAAY;oBAAC;iBAAS;gBACtB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,OAAO,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAClD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,aAAa,EAAE,OAAO,CAAC,gBAAgB;oBACvD,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAO;gBACxB,YAAY;oBAAC;iBAAO;gBACpB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,eAAe,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAC1D,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SACI,CAAC;gBACL,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,4BAA4B,EAAE,OAAO,CAAC,gBAAgB;oBACtE,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAO;gBACxB,YAAY;oBAAC;iBAAO;gBACpB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,oBAAoB,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAC/D,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SACI,CAAC;gBACL,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,iCAAiC,EAAE,OAAO,CAAC,gBAAgB;oBAC3E,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAO;gBACxB,YAAY;oBAAC;iBAAO;gBACpB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,kBAAkB,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAC7D,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SACI,CAAC;gBACL,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,2CAA2C,EAAE,OAAO,CAAC,gBAAgB;oBACrF,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAS;gBAC1B,YAAY;oBAAC;iBAAS;gBACtB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,IAAI,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAC/C,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,aAAa,EAAE,OAAO,CAAC,gBAAgB;oBACvD,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAO;gBACxB,YAAY;oBAAC;iBAAO;gBACpB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,aAAa,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YACxD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SACI,CAAC;gBACL,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,8BAA8B,EAAE,OAAO,CAAC,gBAAgB;oBACxE,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAW;gBAC5B,YAAY;oBAAC;iBAAW;gBACxB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,KAAK,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAChD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,yBAAyB,EAAE,OAAO,CAAC,gBAAgB;oBACnE,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAS;gBAC1B,YAAY;oBAAC;iBAAS;gBACtB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,MAAM,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YACjD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,aAAa,EAAE,OAAO,CAAC,gBAAgB;oBACvD,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAO;gBACxB,YAAY;oBAAC;iBAAO;gBACpB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,+BAA+B,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAC1E,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SACI,CAAC;gBACL,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,4CAA4C,EAAE,OAAO,CAAC,gBAAgB;oBACtF,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAO;gBACxB,YAAY;oBAAC;iBAAO;gBACpB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,+BAA+B,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAC1E,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SACI,CAAC;gBACL,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,4CAA4C,EAAE,OAAO,CAAC,gBAAgB;oBACtF,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAO;gBACxB,YAAY;oBAAC;iBAAO;gBACpB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,aAAa,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YACxD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SACI,CAAC;gBACL,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,8BAA8B,EAAE,OAAO,CAAC,gBAAgB;oBACxE,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAW;gBAC5B,YAAY;oBAAC;iBAAW;gBACxB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,2BAA2B,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YACtE,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SACI,CAAC;gBACL,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,wCAAwC,EAAE,OAAO,CAAC,gBAAgB;oBAClF,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAO;gBACxB,YAAY;oBAAC;iBAAO;gBACpB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,mBAAmB,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAC9D,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SACI,CAAC;gBACL,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,oCAAoC,EAAE,OAAO,CAAC,gBAAgB;oBAC9E,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAW;gBAC5B,YAAY;oBAAC;iBAAW;gBACxB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;IACJ;IACA,kBAAkB,qCAAqC,GAAG;IAC1D,MAAM;QACF,QAAQ;QACR,YAAY,OAAO,CAAE;YACjB,IAAI,CAAC,OAAO,GAAG;QACnB;QACA,IAAI,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAC/C,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,aAAa,EAAE,OAAO,CAAC,gBAAgB;oBACvD,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAO;gBACxB,YAAY;oBAAC;iBAAO;gBACpB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,KAAK,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAChD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,wBAAwB,EAAE,OAAO,CAAC,gBAAgB;oBAClE,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAO;gBACxB,YAAY;oBAAC;iBAAO;gBACpB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;IACJ;IACA,kBAAkB,sCAAsC,GAAG;IAC3D,MAAM;QACF,QAAQ;QACR,YAAY,OAAO,CAAE;YACjB,IAAI,CAAC,OAAO,GAAG;QACnB;QACA,KAAK,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAChD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,wBAAwB,EAAE,OAAO,CAAC,gBAAgB;oBAClE,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAS;gBAC1B,YAAY;oBAAC;iBAAS;gBACtB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;IACJ;IACA,kBAAkB,oCAAoC,GAAG;AAC7D,CAAC,EAAE,qBAAqB,CAAC,QAAQ,iBAAiB,GAAG,oBAAoB,CAAC,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1776, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/AI_Projects/CRMcriadores/crm-interno/node_modules/googleapis/build/src/apis/cloudfunctions/v2alpha.js"], "sourcesContent": ["\"use strict\";\n// Copyright 2020 Google LLC\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//    http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.cloudfunctions_v2alpha = void 0;\n/* eslint-disable @typescript-eslint/no-explicit-any */\n/* eslint-disable @typescript-eslint/no-unused-vars */\n/* eslint-disable @typescript-eslint/no-empty-interface */\n/* eslint-disable @typescript-eslint/no-namespace */\n/* eslint-disable no-irregular-whitespace */\nconst googleapis_common_1 = require(\"googleapis-common\");\nvar cloudfunctions_v2alpha;\n(function (cloudfunctions_v2alpha) {\n    /**\n     * Cloud Functions API\n     *\n     * Manages lightweight user-provided functions executed in response to events.\n     *\n     * @example\n     * ```js\n     * const {google} = require('googleapis');\n     * const cloudfunctions = google.cloudfunctions('v2alpha');\n     * ```\n     */\n    class Cloudfunctions {\n        context;\n        projects;\n        constructor(options, google) {\n            this.context = {\n                _options: options || {},\n                google,\n            };\n            this.projects = new Resource$Projects(this.context);\n        }\n    }\n    cloudfunctions_v2alpha.Cloudfunctions = Cloudfunctions;\n    class Resource$Projects {\n        context;\n        locations;\n        constructor(context) {\n            this.context = context;\n            this.locations = new Resource$Projects$Locations(this.context);\n        }\n    }\n    cloudfunctions_v2alpha.Resource$Projects = Resource$Projects;\n    class Resource$Projects$Locations {\n        context;\n        functions;\n        operations;\n        runtimes;\n        constructor(context) {\n            this.context = context;\n            this.functions = new Resource$Projects$Locations$Functions(this.context);\n            this.operations = new Resource$Projects$Locations$Operations(this.context);\n            this.runtimes = new Resource$Projects$Locations$Runtimes(this.context);\n        }\n        list(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://cloudfunctions.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v2alpha/{+name}/locations').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'GET',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['name'],\n                pathParams: ['name'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n    }\n    cloudfunctions_v2alpha.Resource$Projects$Locations = Resource$Projects$Locations;\n    class Resource$Projects$Locations$Functions {\n        context;\n        constructor(context) {\n            this.context = context;\n        }\n        abortFunctionUpgrade(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params =\n                    {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://cloudfunctions.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v2alpha/{+name}:abortFunctionUpgrade').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'POST',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['name'],\n                pathParams: ['name'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        commitFunctionUpgrade(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params =\n                    {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://cloudfunctions.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v2alpha/{+name}:commitFunctionUpgrade').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'POST',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['name'],\n                pathParams: ['name'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        create(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://cloudfunctions.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v2alpha/{+parent}/functions').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'POST',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['parent'],\n                pathParams: ['parent'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        delete(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://cloudfunctions.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v2alpha/{+name}').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'DELETE',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['name'],\n                pathParams: ['name'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        detachFunction(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params =\n                    {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://cloudfunctions.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v2alpha/{+name}:detachFunction').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'POST',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['name'],\n                pathParams: ['name'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        generateDownloadUrl(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params =\n                    {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://cloudfunctions.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v2alpha/{+name}:generateDownloadUrl').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'POST',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['name'],\n                pathParams: ['name'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        generateUploadUrl(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params =\n                    {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://cloudfunctions.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v2alpha/{+parent}/functions:generateUploadUrl').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'POST',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['parent'],\n                pathParams: ['parent'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        get(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://cloudfunctions.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v2alpha/{+name}').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'GET',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['name'],\n                pathParams: ['name'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        getIamPolicy(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params =\n                    {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://cloudfunctions.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v2alpha/{+resource}:getIamPolicy').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'GET',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['resource'],\n                pathParams: ['resource'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        list(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://cloudfunctions.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v2alpha/{+parent}/functions').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'GET',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['parent'],\n                pathParams: ['parent'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        patch(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://cloudfunctions.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v2alpha/{+name}').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'PATCH',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['name'],\n                pathParams: ['name'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        redirectFunctionUpgradeTraffic(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params =\n                    {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://cloudfunctions.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v2alpha/{+name}:redirectFunctionUpgradeTraffic').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'POST',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['name'],\n                pathParams: ['name'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        rollbackFunctionUpgradeTraffic(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params =\n                    {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://cloudfunctions.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v2alpha/{+name}:rollbackFunctionUpgradeTraffic').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'POST',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['name'],\n                pathParams: ['name'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        setIamPolicy(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params =\n                    {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://cloudfunctions.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v2alpha/{+resource}:setIamPolicy').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'POST',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['resource'],\n                pathParams: ['resource'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        setupFunctionUpgradeConfig(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params =\n                    {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://cloudfunctions.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v2alpha/{+name}:setupFunctionUpgradeConfig').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'POST',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['name'],\n                pathParams: ['name'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        testIamPermissions(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params =\n                    {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://cloudfunctions.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v2alpha/{+resource}:testIamPermissions').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'POST',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['resource'],\n                pathParams: ['resource'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n    }\n    cloudfunctions_v2alpha.Resource$Projects$Locations$Functions = Resource$Projects$Locations$Functions;\n    class Resource$Projects$Locations$Operations {\n        context;\n        constructor(context) {\n            this.context = context;\n        }\n        get(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://cloudfunctions.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v2alpha/{+name}').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'GET',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['name'],\n                pathParams: ['name'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        list(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://cloudfunctions.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v2alpha/{+name}/operations').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'GET',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['name'],\n                pathParams: ['name'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n    }\n    cloudfunctions_v2alpha.Resource$Projects$Locations$Operations = Resource$Projects$Locations$Operations;\n    class Resource$Projects$Locations$Runtimes {\n        context;\n        constructor(context) {\n            this.context = context;\n        }\n        list(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://cloudfunctions.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v2alpha/{+parent}/runtimes').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'GET',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['parent'],\n                pathParams: ['parent'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n    }\n    cloudfunctions_v2alpha.Resource$Projects$Locations$Runtimes = Resource$Projects$Locations$Runtimes;\n})(cloudfunctions_v2alpha || (exports.cloudfunctions_v2alpha = cloudfunctions_v2alpha = {}));\n"], "names": [], "mappings": "AAAA;AACA,4BAA4B;AAC5B,kEAAkE;AAClE,mEAAmE;AACnE,0CAA0C;AAC1C,EAAE;AACF,gDAAgD;AAChD,EAAE;AACF,sEAAsE;AACtE,oEAAoE;AACpE,2EAA2E;AAC3E,sEAAsE;AACtE,iCAAiC;AACjC,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,QAAQ,sBAAsB,GAAG,KAAK;AACtC,qDAAqD,GACrD,oDAAoD,GACpD,wDAAwD,GACxD,kDAAkD,GAClD,0CAA0C,GAC1C,MAAM;AACN,IAAI;AACJ,CAAC,SAAU,sBAAsB;IAC7B;;;;;;;;;;KAUC,GACD,MAAM;QACF,QAAQ;QACR,SAAS;QACT,YAAY,OAAO,EAAE,MAAM,CAAE;YACzB,IAAI,CAAC,OAAO,GAAG;gBACX,UAAU,WAAW,CAAC;gBACtB;YACJ;YACA,IAAI,CAAC,QAAQ,GAAG,IAAI,kBAAkB,IAAI,CAAC,OAAO;QACtD;IACJ;IACA,uBAAuB,cAAc,GAAG;IACxC,MAAM;QACF,QAAQ;QACR,UAAU;QACV,YAAY,OAAO,CAAE;YACjB,IAAI,CAAC,OAAO,GAAG;YACf,IAAI,CAAC,SAAS,GAAG,IAAI,4BAA4B,IAAI,CAAC,OAAO;QACjE;IACJ;IACA,uBAAuB,iBAAiB,GAAG;IAC3C,MAAM;QACF,QAAQ;QACR,UAAU;QACV,WAAW;QACX,SAAS;QACT,YAAY,OAAO,CAAE;YACjB,IAAI,CAAC,OAAO,GAAG;YACf,IAAI,CAAC,SAAS,GAAG,IAAI,sCAAsC,IAAI,CAAC,OAAO;YACvE,IAAI,CAAC,UAAU,GAAG,IAAI,uCAAuC,IAAI,CAAC,OAAO;YACzE,IAAI,CAAC,QAAQ,GAAG,IAAI,qCAAqC,IAAI,CAAC,OAAO;QACzE;QACA,KAAK,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAChD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,4BAA4B,EAAE,OAAO,CAAC,gBAAgB;oBACtE,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAO;gBACxB,YAAY;oBAAC;iBAAO;gBACpB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;IACJ;IACA,uBAAuB,2BAA2B,GAAG;IACrD,MAAM;QACF,QAAQ;QACR,YAAY,OAAO,CAAE;YACjB,IAAI,CAAC,OAAO,GAAG;QACnB;QACA,qBAAqB,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAChE,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SACI,CAAC;gBACL,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,uCAAuC,EAAE,OAAO,CAAC,gBAAgB;oBACjF,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAO;gBACxB,YAAY;oBAAC;iBAAO;gBACpB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,sBAAsB,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YACjE,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SACI,CAAC;gBACL,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,wCAAwC,EAAE,OAAO,CAAC,gBAAgB;oBAClF,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAO;gBACxB,YAAY;oBAAC;iBAAO;gBACpB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,OAAO,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAClD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,8BAA8B,EAAE,OAAO,CAAC,gBAAgB;oBACxE,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAS;gBAC1B,YAAY;oBAAC;iBAAS;gBACtB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,OAAO,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAClD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,kBAAkB,EAAE,OAAO,CAAC,gBAAgB;oBAC5D,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAO;gBACxB,YAAY;oBAAC;iBAAO;gBACpB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,eAAe,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAC1D,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SACI,CAAC;gBACL,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,iCAAiC,EAAE,OAAO,CAAC,gBAAgB;oBAC3E,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAO;gBACxB,YAAY;oBAAC;iBAAO;gBACpB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,oBAAoB,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAC/D,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SACI,CAAC;gBACL,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,sCAAsC,EAAE,OAAO,CAAC,gBAAgB;oBAChF,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAO;gBACxB,YAAY;oBAAC;iBAAO;gBACpB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,kBAAkB,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAC7D,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SACI,CAAC;gBACL,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,gDAAgD,EAAE,OAAO,CAAC,gBAAgB;oBAC1F,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAS;gBAC1B,YAAY;oBAAC;iBAAS;gBACtB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,IAAI,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAC/C,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,kBAAkB,EAAE,OAAO,CAAC,gBAAgB;oBAC5D,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAO;gBACxB,YAAY;oBAAC;iBAAO;gBACpB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,aAAa,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YACxD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SACI,CAAC;gBACL,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,mCAAmC,EAAE,OAAO,CAAC,gBAAgB;oBAC7E,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAW;gBAC5B,YAAY;oBAAC;iBAAW;gBACxB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,KAAK,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAChD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,8BAA8B,EAAE,OAAO,CAAC,gBAAgB;oBACxE,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAS;gBAC1B,YAAY;oBAAC;iBAAS;gBACtB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,MAAM,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YACjD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,kBAAkB,EAAE,OAAO,CAAC,gBAAgB;oBAC5D,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAO;gBACxB,YAAY;oBAAC;iBAAO;gBACpB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,+BAA+B,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAC1E,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SACI,CAAC;gBACL,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,iDAAiD,EAAE,OAAO,CAAC,gBAAgB;oBAC3F,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAO;gBACxB,YAAY;oBAAC;iBAAO;gBACpB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,+BAA+B,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAC1E,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SACI,CAAC;gBACL,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,iDAAiD,EAAE,OAAO,CAAC,gBAAgB;oBAC3F,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAO;gBACxB,YAAY;oBAAC;iBAAO;gBACpB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,aAAa,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YACxD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SACI,CAAC;gBACL,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,mCAAmC,EAAE,OAAO,CAAC,gBAAgB;oBAC7E,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAW;gBAC5B,YAAY;oBAAC;iBAAW;gBACxB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,2BAA2B,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YACtE,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SACI,CAAC;gBACL,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,6CAA6C,EAAE,OAAO,CAAC,gBAAgB;oBACvF,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAO;gBACxB,YAAY;oBAAC;iBAAO;gBACpB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,mBAAmB,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAC9D,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SACI,CAAC;gBACL,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,yCAAyC,EAAE,OAAO,CAAC,gBAAgB;oBACnF,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAW;gBAC5B,YAAY;oBAAC;iBAAW;gBACxB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;IACJ;IACA,uBAAuB,qCAAqC,GAAG;IAC/D,MAAM;QACF,QAAQ;QACR,YAAY,OAAO,CAAE;YACjB,IAAI,CAAC,OAAO,GAAG;QACnB;QACA,IAAI,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAC/C,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,kBAAkB,EAAE,OAAO,CAAC,gBAAgB;oBAC5D,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAO;gBACxB,YAAY;oBAAC;iBAAO;gBACpB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,KAAK,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAChD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,6BAA6B,EAAE,OAAO,CAAC,gBAAgB;oBACvE,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAO;gBACxB,YAAY;oBAAC;iBAAO;gBACpB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;IACJ;IACA,uBAAuB,sCAAsC,GAAG;IAChE,MAAM;QACF,QAAQ;QACR,YAAY,OAAO,CAAE;YACjB,IAAI,CAAC,OAAO,GAAG;QACnB;QACA,KAAK,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAChD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,6BAA6B,EAAE,OAAO,CAAC,gBAAgB;oBACvE,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAS;gBAC1B,YAAY;oBAAC;iBAAS;gBACtB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;IACJ;IACA,uBAAuB,oCAAoC,GAAG;AAClE,CAAC,EAAE,0BAA0B,CAAC,QAAQ,sBAAsB,GAAG,yBAAyB,CAAC,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2547, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/AI_Projects/CRMcriadores/crm-interno/node_modules/googleapis/build/src/apis/cloudfunctions/v2beta.js"], "sourcesContent": ["\"use strict\";\n// Copyright 2020 Google LLC\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//    http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.cloudfunctions_v2beta = void 0;\n/* eslint-disable @typescript-eslint/no-explicit-any */\n/* eslint-disable @typescript-eslint/no-unused-vars */\n/* eslint-disable @typescript-eslint/no-empty-interface */\n/* eslint-disable @typescript-eslint/no-namespace */\n/* eslint-disable no-irregular-whitespace */\nconst googleapis_common_1 = require(\"googleapis-common\");\nvar cloudfunctions_v2beta;\n(function (cloudfunctions_v2beta) {\n    /**\n     * Cloud Functions API\n     *\n     * Manages lightweight user-provided functions executed in response to events.\n     *\n     * @example\n     * ```js\n     * const {google} = require('googleapis');\n     * const cloudfunctions = google.cloudfunctions('v2beta');\n     * ```\n     */\n    class Cloudfunctions {\n        context;\n        projects;\n        constructor(options, google) {\n            this.context = {\n                _options: options || {},\n                google,\n            };\n            this.projects = new Resource$Projects(this.context);\n        }\n    }\n    cloudfunctions_v2beta.Cloudfunctions = Cloudfunctions;\n    class Resource$Projects {\n        context;\n        locations;\n        constructor(context) {\n            this.context = context;\n            this.locations = new Resource$Projects$Locations(this.context);\n        }\n    }\n    cloudfunctions_v2beta.Resource$Projects = Resource$Projects;\n    class Resource$Projects$Locations {\n        context;\n        functions;\n        operations;\n        runtimes;\n        constructor(context) {\n            this.context = context;\n            this.functions = new Resource$Projects$Locations$Functions(this.context);\n            this.operations = new Resource$Projects$Locations$Operations(this.context);\n            this.runtimes = new Resource$Projects$Locations$Runtimes(this.context);\n        }\n        list(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://cloudfunctions.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v2beta/{+name}/locations').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'GET',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['name'],\n                pathParams: ['name'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n    }\n    cloudfunctions_v2beta.Resource$Projects$Locations = Resource$Projects$Locations;\n    class Resource$Projects$Locations$Functions {\n        context;\n        constructor(context) {\n            this.context = context;\n        }\n        abortFunctionUpgrade(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params =\n                    {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://cloudfunctions.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v2beta/{+name}:abortFunctionUpgrade').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'POST',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['name'],\n                pathParams: ['name'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        commitFunctionUpgrade(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params =\n                    {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://cloudfunctions.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v2beta/{+name}:commitFunctionUpgrade').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'POST',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['name'],\n                pathParams: ['name'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        create(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://cloudfunctions.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v2beta/{+parent}/functions').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'POST',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['parent'],\n                pathParams: ['parent'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        delete(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://cloudfunctions.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v2beta/{+name}').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'DELETE',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['name'],\n                pathParams: ['name'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        detachFunction(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params =\n                    {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://cloudfunctions.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v2beta/{+name}:detachFunction').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'POST',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['name'],\n                pathParams: ['name'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        generateDownloadUrl(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params =\n                    {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://cloudfunctions.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v2beta/{+name}:generateDownloadUrl').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'POST',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['name'],\n                pathParams: ['name'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        generateUploadUrl(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params =\n                    {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://cloudfunctions.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v2beta/{+parent}/functions:generateUploadUrl').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'POST',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['parent'],\n                pathParams: ['parent'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        get(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://cloudfunctions.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v2beta/{+name}').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'GET',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['name'],\n                pathParams: ['name'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        getIamPolicy(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params =\n                    {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://cloudfunctions.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v2beta/{+resource}:getIamPolicy').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'GET',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['resource'],\n                pathParams: ['resource'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        list(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://cloudfunctions.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v2beta/{+parent}/functions').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'GET',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['parent'],\n                pathParams: ['parent'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        patch(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://cloudfunctions.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v2beta/{+name}').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'PATCH',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['name'],\n                pathParams: ['name'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        redirectFunctionUpgradeTraffic(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params =\n                    {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://cloudfunctions.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v2beta/{+name}:redirectFunctionUpgradeTraffic').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'POST',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['name'],\n                pathParams: ['name'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        rollbackFunctionUpgradeTraffic(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params =\n                    {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://cloudfunctions.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v2beta/{+name}:rollbackFunctionUpgradeTraffic').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'POST',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['name'],\n                pathParams: ['name'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        setIamPolicy(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params =\n                    {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://cloudfunctions.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v2beta/{+resource}:setIamPolicy').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'POST',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['resource'],\n                pathParams: ['resource'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        setupFunctionUpgradeConfig(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params =\n                    {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://cloudfunctions.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v2beta/{+name}:setupFunctionUpgradeConfig').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'POST',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['name'],\n                pathParams: ['name'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        testIamPermissions(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params =\n                    {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://cloudfunctions.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v2beta/{+resource}:testIamPermissions').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'POST',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['resource'],\n                pathParams: ['resource'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n    }\n    cloudfunctions_v2beta.Resource$Projects$Locations$Functions = Resource$Projects$Locations$Functions;\n    class Resource$Projects$Locations$Operations {\n        context;\n        constructor(context) {\n            this.context = context;\n        }\n        get(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://cloudfunctions.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v2beta/{+name}').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'GET',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['name'],\n                pathParams: ['name'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        list(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://cloudfunctions.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v2beta/{+name}/operations').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'GET',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['name'],\n                pathParams: ['name'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n    }\n    cloudfunctions_v2beta.Resource$Projects$Locations$Operations = Resource$Projects$Locations$Operations;\n    class Resource$Projects$Locations$Runtimes {\n        context;\n        constructor(context) {\n            this.context = context;\n        }\n        list(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://cloudfunctions.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v2beta/{+parent}/runtimes').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'GET',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['parent'],\n                pathParams: ['parent'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n    }\n    cloudfunctions_v2beta.Resource$Projects$Locations$Runtimes = Resource$Projects$Locations$Runtimes;\n})(cloudfunctions_v2beta || (exports.cloudfunctions_v2beta = cloudfunctions_v2beta = {}));\n"], "names": [], "mappings": "AAAA;AACA,4BAA4B;AAC5B,kEAAkE;AAClE,mEAAmE;AACnE,0CAA0C;AAC1C,EAAE;AACF,gDAAgD;AAChD,EAAE;AACF,sEAAsE;AACtE,oEAAoE;AACpE,2EAA2E;AAC3E,sEAAsE;AACtE,iCAAiC;AACjC,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,QAAQ,qBAAqB,GAAG,KAAK;AACrC,qDAAqD,GACrD,oDAAoD,GACpD,wDAAwD,GACxD,kDAAkD,GAClD,0CAA0C,GAC1C,MAAM;AACN,IAAI;AACJ,CAAC,SAAU,qBAAqB;IAC5B;;;;;;;;;;KAUC,GACD,MAAM;QACF,QAAQ;QACR,SAAS;QACT,YAAY,OAAO,EAAE,MAAM,CAAE;YACzB,IAAI,CAAC,OAAO,GAAG;gBACX,UAAU,WAAW,CAAC;gBACtB;YACJ;YACA,IAAI,CAAC,QAAQ,GAAG,IAAI,kBAAkB,IAAI,CAAC,OAAO;QACtD;IACJ;IACA,sBAAsB,cAAc,GAAG;IACvC,MAAM;QACF,QAAQ;QACR,UAAU;QACV,YAAY,OAAO,CAAE;YACjB,IAAI,CAAC,OAAO,GAAG;YACf,IAAI,CAAC,SAAS,GAAG,IAAI,4BAA4B,IAAI,CAAC,OAAO;QACjE;IACJ;IACA,sBAAsB,iBAAiB,GAAG;IAC1C,MAAM;QACF,QAAQ;QACR,UAAU;QACV,WAAW;QACX,SAAS;QACT,YAAY,OAAO,CAAE;YACjB,IAAI,CAAC,OAAO,GAAG;YACf,IAAI,CAAC,SAAS,GAAG,IAAI,sCAAsC,IAAI,CAAC,OAAO;YACvE,IAAI,CAAC,UAAU,GAAG,IAAI,uCAAuC,IAAI,CAAC,OAAO;YACzE,IAAI,CAAC,QAAQ,GAAG,IAAI,qCAAqC,IAAI,CAAC,OAAO;QACzE;QACA,KAAK,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAChD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,2BAA2B,EAAE,OAAO,CAAC,gBAAgB;oBACrE,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAO;gBACxB,YAAY;oBAAC;iBAAO;gBACpB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;IACJ;IACA,sBAAsB,2BAA2B,GAAG;IACpD,MAAM;QACF,QAAQ;QACR,YAAY,OAAO,CAAE;YACjB,IAAI,CAAC,OAAO,GAAG;QACnB;QACA,qBAAqB,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAChE,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SACI,CAAC;gBACL,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,sCAAsC,EAAE,OAAO,CAAC,gBAAgB;oBAChF,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAO;gBACxB,YAAY;oBAAC;iBAAO;gBACpB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,sBAAsB,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YACjE,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SACI,CAAC;gBACL,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,uCAAuC,EAAE,OAAO,CAAC,gBAAgB;oBACjF,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAO;gBACxB,YAAY;oBAAC;iBAAO;gBACpB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,OAAO,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAClD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,6BAA6B,EAAE,OAAO,CAAC,gBAAgB;oBACvE,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAS;gBAC1B,YAAY;oBAAC;iBAAS;gBACtB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,OAAO,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAClD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,iBAAiB,EAAE,OAAO,CAAC,gBAAgB;oBAC3D,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAO;gBACxB,YAAY;oBAAC;iBAAO;gBACpB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,eAAe,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAC1D,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SACI,CAAC;gBACL,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,gCAAgC,EAAE,OAAO,CAAC,gBAAgB;oBAC1E,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAO;gBACxB,YAAY;oBAAC;iBAAO;gBACpB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,oBAAoB,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAC/D,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SACI,CAAC;gBACL,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,qCAAqC,EAAE,OAAO,CAAC,gBAAgB;oBAC/E,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAO;gBACxB,YAAY;oBAAC;iBAAO;gBACpB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,kBAAkB,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAC7D,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SACI,CAAC;gBACL,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,+CAA+C,EAAE,OAAO,CAAC,gBAAgB;oBACzF,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAS;gBAC1B,YAAY;oBAAC;iBAAS;gBACtB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,IAAI,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAC/C,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,iBAAiB,EAAE,OAAO,CAAC,gBAAgB;oBAC3D,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAO;gBACxB,YAAY;oBAAC;iBAAO;gBACpB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,aAAa,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YACxD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SACI,CAAC;gBACL,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,kCAAkC,EAAE,OAAO,CAAC,gBAAgB;oBAC5E,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAW;gBAC5B,YAAY;oBAAC;iBAAW;gBACxB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,KAAK,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAChD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,6BAA6B,EAAE,OAAO,CAAC,gBAAgB;oBACvE,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAS;gBAC1B,YAAY;oBAAC;iBAAS;gBACtB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,MAAM,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YACjD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,iBAAiB,EAAE,OAAO,CAAC,gBAAgB;oBAC3D,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAO;gBACxB,YAAY;oBAAC;iBAAO;gBACpB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,+BAA+B,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAC1E,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SACI,CAAC;gBACL,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,gDAAgD,EAAE,OAAO,CAAC,gBAAgB;oBAC1F,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAO;gBACxB,YAAY;oBAAC;iBAAO;gBACpB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,+BAA+B,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAC1E,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SACI,CAAC;gBACL,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,gDAAgD,EAAE,OAAO,CAAC,gBAAgB;oBAC1F,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAO;gBACxB,YAAY;oBAAC;iBAAO;gBACpB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,aAAa,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YACxD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SACI,CAAC;gBACL,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,kCAAkC,EAAE,OAAO,CAAC,gBAAgB;oBAC5E,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAW;gBAC5B,YAAY;oBAAC;iBAAW;gBACxB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,2BAA2B,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YACtE,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SACI,CAAC;gBACL,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,4CAA4C,EAAE,OAAO,CAAC,gBAAgB;oBACtF,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAO;gBACxB,YAAY;oBAAC;iBAAO;gBACpB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,mBAAmB,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAC9D,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SACI,CAAC;gBACL,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,wCAAwC,EAAE,OAAO,CAAC,gBAAgB;oBAClF,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAW;gBAC5B,YAAY;oBAAC;iBAAW;gBACxB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;IACJ;IACA,sBAAsB,qCAAqC,GAAG;IAC9D,MAAM;QACF,QAAQ;QACR,YAAY,OAAO,CAAE;YACjB,IAAI,CAAC,OAAO,GAAG;QACnB;QACA,IAAI,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAC/C,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,iBAAiB,EAAE,OAAO,CAAC,gBAAgB;oBAC3D,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAO;gBACxB,YAAY;oBAAC;iBAAO;gBACpB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,KAAK,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAChD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,4BAA4B,EAAE,OAAO,CAAC,gBAAgB;oBACtE,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAO;gBACxB,YAAY;oBAAC;iBAAO;gBACpB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;IACJ;IACA,sBAAsB,sCAAsC,GAAG;IAC/D,MAAM;QACF,QAAQ;QACR,YAAY,OAAO,CAAE;YACjB,IAAI,CAAC,OAAO,GAAG;QACnB;QACA,KAAK,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAChD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,4BAA4B,EAAE,OAAO,CAAC,gBAAgB;oBACtE,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAS;gBAC1B,YAAY;oBAAC;iBAAS;gBACtB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;IACJ;IACA,sBAAsB,oCAAoC,GAAG;AACjE,CAAC,EAAE,yBAAyB,CAAC,QAAQ,qBAAqB,GAAG,wBAAwB,CAAC,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3318, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/AI_Projects/CRMcriadores/crm-interno/node_modules/googleapis/build/src/apis/cloudfunctions/index.js"], "sourcesContent": ["\"use strict\";\n// Copyright 2020 Google LLC\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//    http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.AuthPlus = exports.cloudfunctions_v2beta = exports.cloudfunctions_v2alpha = exports.cloudfunctions_v2 = exports.cloudfunctions_v1beta2 = exports.cloudfunctions_v1 = exports.auth = exports.VERSIONS = void 0;\nexports.cloudfunctions = cloudfunctions;\n/*! THIS FILE IS AUTO-GENERATED */\nconst googleapis_common_1 = require(\"googleapis-common\");\nconst v1_1 = require(\"./v1\");\nObject.defineProperty(exports, \"cloudfunctions_v1\", { enumerable: true, get: function () { return v1_1.cloudfunctions_v1; } });\nconst v1beta2_1 = require(\"./v1beta2\");\nObject.defineProperty(exports, \"cloudfunctions_v1beta2\", { enumerable: true, get: function () { return v1beta2_1.cloudfunctions_v1beta2; } });\nconst v2_1 = require(\"./v2\");\nObject.defineProperty(exports, \"cloudfunctions_v2\", { enumerable: true, get: function () { return v2_1.cloudfunctions_v2; } });\nconst v2alpha_1 = require(\"./v2alpha\");\nObject.defineProperty(exports, \"cloudfunctions_v2alpha\", { enumerable: true, get: function () { return v2alpha_1.cloudfunctions_v2alpha; } });\nconst v2beta_1 = require(\"./v2beta\");\nObject.defineProperty(exports, \"cloudfunctions_v2beta\", { enumerable: true, get: function () { return v2beta_1.cloudfunctions_v2beta; } });\nexports.VERSIONS = {\n    v1: v1_1.cloudfunctions_v1.Cloudfunctions,\n    v1beta2: v1beta2_1.cloudfunctions_v1beta2.Cloudfunctions,\n    v2: v2_1.cloudfunctions_v2.Cloudfunctions,\n    v2alpha: v2alpha_1.cloudfunctions_v2alpha.Cloudfunctions,\n    v2beta: v2beta_1.cloudfunctions_v2beta.Cloudfunctions,\n};\nfunction cloudfunctions(versionOrOptions) {\n    return (0, googleapis_common_1.getAPI)('cloudfunctions', versionOrOptions, exports.VERSIONS, this);\n}\nconst auth = new googleapis_common_1.AuthPlus();\nexports.auth = auth;\nvar googleapis_common_2 = require(\"googleapis-common\");\nObject.defineProperty(exports, \"AuthPlus\", { enumerable: true, get: function () { return googleapis_common_2.AuthPlus; } });\n"], "names": [], "mappings": "AAAA;AACA,4BAA4B;AAC5B,kEAAkE;AAClE,mEAAmE;AACnE,0CAA0C;AAC1C,EAAE;AACF,gDAAgD;AAChD,EAAE;AACF,sEAAsE;AACtE,oEAAoE;AACpE,2EAA2E;AAC3E,sEAAsE;AACtE,iCAAiC;AACjC,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,QAAQ,QAAQ,GAAG,QAAQ,qBAAqB,GAAG,QAAQ,sBAAsB,GAAG,QAAQ,iBAAiB,GAAG,QAAQ,sBAAsB,GAAG,QAAQ,iBAAiB,GAAG,QAAQ,IAAI,GAAG,QAAQ,QAAQ,GAAG,KAAK;AACpN,QAAQ,cAAc,GAAG;AACzB,gCAAgC,GAChC,MAAM;AACN,MAAM;AACN,OAAO,cAAc,CAAC,SAAS,qBAAqB;IAAE,YAAY;IAAM,KAAK;QAAc,OAAO,KAAK,iBAAiB;IAAE;AAAE;AAC5H,MAAM;AACN,OAAO,cAAc,CAAC,SAAS,0BAA0B;IAAE,YAAY;IAAM,KAAK;QAAc,OAAO,UAAU,sBAAsB;IAAE;AAAE;AAC3I,MAAM;AACN,OAAO,cAAc,CAAC,SAAS,qBAAqB;IAAE,YAAY;IAAM,KAAK;QAAc,OAAO,KAAK,iBAAiB;IAAE;AAAE;AAC5H,MAAM;AACN,OAAO,cAAc,CAAC,SAAS,0BAA0B;IAAE,YAAY;IAAM,KAAK;QAAc,OAAO,UAAU,sBAAsB;IAAE;AAAE;AAC3I,MAAM;AACN,OAAO,cAAc,CAAC,SAAS,yBAAyB;IAAE,YAAY;IAAM,KAAK;QAAc,OAAO,SAAS,qBAAqB;IAAE;AAAE;AACxI,QAAQ,QAAQ,GAAG;IACf,IAAI,KAAK,iBAAiB,CAAC,cAAc;IACzC,SAAS,UAAU,sBAAsB,CAAC,cAAc;IACxD,IAAI,KAAK,iBAAiB,CAAC,cAAc;IACzC,SAAS,UAAU,sBAAsB,CAAC,cAAc;IACxD,QAAQ,SAAS,qBAAqB,CAAC,cAAc;AACzD;AACA,SAAS,eAAe,gBAAgB;IACpC,OAAO,CAAC,GAAG,oBAAoB,MAAM,EAAE,kBAAkB,kBAAkB,QAAQ,QAAQ,EAAE,IAAI;AACrG;AACA,MAAM,OAAO,IAAI,oBAAoB,QAAQ;AAC7C,QAAQ,IAAI,GAAG;AACf,IAAI;AACJ,OAAO,cAAc,CAAC,SAAS,YAAY;IAAE,YAAY;IAAM,KAAK;QAAc,OAAO,oBAAoB,QAAQ;IAAE;AAAE", "ignoreList": [0], "debugId": null}}]}
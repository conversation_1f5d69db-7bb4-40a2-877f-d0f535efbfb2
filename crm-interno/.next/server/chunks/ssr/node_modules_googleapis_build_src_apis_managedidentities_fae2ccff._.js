module.exports = {

"[project]/node_modules/googleapis/build/src/apis/managedidentities/v1.js [app-rsc] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
// Copyright 2020 Google LLC
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//    http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.managedidentities_v1 = void 0;
/* eslint-disable @typescript-eslint/no-explicit-any */ /* eslint-disable @typescript-eslint/no-unused-vars */ /* eslint-disable @typescript-eslint/no-empty-interface */ /* eslint-disable @typescript-eslint/no-namespace */ /* eslint-disable no-irregular-whitespace */ const googleapis_common_1 = __turbopack_context__.r("[project]/node_modules/googleapis-common/build/src/index.js [app-rsc] (ecmascript)");
var managedidentities_v1;
(function(managedidentities_v1) {
    /**
     * Managed Service for Microsoft Active Directory API
     *
     * The Managed Service for Microsoft Active Directory API is used for managing a highly available, hardened service running Microsoft Active Directory (AD).
     *
     * @example
     * ```js
     * const {google} = require('googleapis');
     * const managedidentities = google.managedidentities('v1');
     * ```
     */ class Managedidentities {
        context;
        projects;
        constructor(options, google){
            this.context = {
                _options: options || {},
                google
            };
            this.projects = new Resource$Projects(this.context);
        }
    }
    managedidentities_v1.Managedidentities = Managedidentities;
    class Resource$Projects {
        context;
        locations;
        constructor(context){
            this.context = context;
            this.locations = new Resource$Projects$Locations(this.context);
        }
    }
    managedidentities_v1.Resource$Projects = Resource$Projects;
    class Resource$Projects$Locations {
        context;
        global;
        constructor(context){
            this.context = context;
            this.global = new Resource$Projects$Locations$Global(this.context);
        }
        get(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://managedidentities.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v1/{+name}').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'GET',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'name'
                ],
                pathParams: [
                    'name'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        list(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://managedidentities.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v1/{+name}/locations').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'GET',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'name'
                ],
                pathParams: [
                    'name'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
    }
    managedidentities_v1.Resource$Projects$Locations = Resource$Projects$Locations;
    class Resource$Projects$Locations$Global {
        context;
        domains;
        operations;
        peerings;
        constructor(context){
            this.context = context;
            this.domains = new Resource$Projects$Locations$Global$Domains(this.context);
            this.operations = new Resource$Projects$Locations$Global$Operations(this.context);
            this.peerings = new Resource$Projects$Locations$Global$Peerings(this.context);
        }
    }
    managedidentities_v1.Resource$Projects$Locations$Global = Resource$Projects$Locations$Global;
    class Resource$Projects$Locations$Global$Domains {
        context;
        backups;
        sqlIntegrations;
        constructor(context){
            this.context = context;
            this.backups = new Resource$Projects$Locations$Global$Domains$Backups(this.context);
            this.sqlIntegrations = new Resource$Projects$Locations$Global$Domains$Sqlintegrations(this.context);
        }
        attachTrust(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://managedidentities.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v1/{+name}:attachTrust').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'POST',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'name'
                ],
                pathParams: [
                    'name'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        checkMigrationPermission(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://managedidentities.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v1/{+domain}:checkMigrationPermission').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'POST',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'domain'
                ],
                pathParams: [
                    'domain'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        create(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://managedidentities.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v1/{+parent}/domains').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'POST',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'parent'
                ],
                pathParams: [
                    'parent'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        delete(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://managedidentities.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v1/{+name}').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'DELETE',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'name'
                ],
                pathParams: [
                    'name'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        detachTrust(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://managedidentities.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v1/{+name}:detachTrust').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'POST',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'name'
                ],
                pathParams: [
                    'name'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        disableMigration(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://managedidentities.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v1/{+domain}:disableMigration').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'POST',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'domain'
                ],
                pathParams: [
                    'domain'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        domainJoinMachine(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://managedidentities.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v1/{+domain}:domainJoinMachine').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'POST',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'domain'
                ],
                pathParams: [
                    'domain'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        enableMigration(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://managedidentities.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v1/{+domain}:enableMigration').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'POST',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'domain'
                ],
                pathParams: [
                    'domain'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        extendSchema(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://managedidentities.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v1/{+domain}:extendSchema').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'POST',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'domain'
                ],
                pathParams: [
                    'domain'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        get(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://managedidentities.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v1/{+name}').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'GET',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'name'
                ],
                pathParams: [
                    'name'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        getIamPolicy(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://managedidentities.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v1/{+resource}:getIamPolicy').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'GET',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'resource'
                ],
                pathParams: [
                    'resource'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        getLdapssettings(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://managedidentities.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v1/{+name}/ldapssettings').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'GET',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'name'
                ],
                pathParams: [
                    'name'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        list(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://managedidentities.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v1/{+parent}/domains').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'GET',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'parent'
                ],
                pathParams: [
                    'parent'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        patch(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://managedidentities.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v1/{+name}').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'PATCH',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'name'
                ],
                pathParams: [
                    'name'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        reconfigureTrust(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://managedidentities.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v1/{+name}:reconfigureTrust').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'POST',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'name'
                ],
                pathParams: [
                    'name'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        resetAdminPassword(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://managedidentities.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v1/{+name}:resetAdminPassword').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'POST',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'name'
                ],
                pathParams: [
                    'name'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        restore(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://managedidentities.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v1/{+name}:restore').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'POST',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'name'
                ],
                pathParams: [
                    'name'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        setIamPolicy(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://managedidentities.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v1/{+resource}:setIamPolicy').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'POST',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'resource'
                ],
                pathParams: [
                    'resource'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        testIamPermissions(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://managedidentities.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v1/{+resource}:testIamPermissions').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'POST',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'resource'
                ],
                pathParams: [
                    'resource'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        updateLdapssettings(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://managedidentities.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v1/{+name}/ldapssettings').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'PATCH',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'name'
                ],
                pathParams: [
                    'name'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        validateTrust(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://managedidentities.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v1/{+name}:validateTrust').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'POST',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'name'
                ],
                pathParams: [
                    'name'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
    }
    managedidentities_v1.Resource$Projects$Locations$Global$Domains = Resource$Projects$Locations$Global$Domains;
    class Resource$Projects$Locations$Global$Domains$Backups {
        context;
        constructor(context){
            this.context = context;
        }
        create(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://managedidentities.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v1/{+parent}/backups').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'POST',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'parent'
                ],
                pathParams: [
                    'parent'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        delete(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://managedidentities.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v1/{+name}').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'DELETE',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'name'
                ],
                pathParams: [
                    'name'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        get(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://managedidentities.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v1/{+name}').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'GET',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'name'
                ],
                pathParams: [
                    'name'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        getIamPolicy(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://managedidentities.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v1/{+resource}:getIamPolicy').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'GET',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'resource'
                ],
                pathParams: [
                    'resource'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        list(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://managedidentities.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v1/{+parent}/backups').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'GET',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'parent'
                ],
                pathParams: [
                    'parent'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        patch(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://managedidentities.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v1/{+name}').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'PATCH',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'name'
                ],
                pathParams: [
                    'name'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        setIamPolicy(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://managedidentities.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v1/{+resource}:setIamPolicy').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'POST',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'resource'
                ],
                pathParams: [
                    'resource'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        testIamPermissions(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://managedidentities.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v1/{+resource}:testIamPermissions').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'POST',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'resource'
                ],
                pathParams: [
                    'resource'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
    }
    managedidentities_v1.Resource$Projects$Locations$Global$Domains$Backups = Resource$Projects$Locations$Global$Domains$Backups;
    class Resource$Projects$Locations$Global$Domains$Sqlintegrations {
        context;
        constructor(context){
            this.context = context;
        }
        get(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://managedidentities.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v1/{+name}').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'GET',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'name'
                ],
                pathParams: [
                    'name'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        list(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://managedidentities.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v1/{+parent}/sqlIntegrations').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'GET',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'parent'
                ],
                pathParams: [
                    'parent'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
    }
    managedidentities_v1.Resource$Projects$Locations$Global$Domains$Sqlintegrations = Resource$Projects$Locations$Global$Domains$Sqlintegrations;
    class Resource$Projects$Locations$Global$Operations {
        context;
        constructor(context){
            this.context = context;
        }
        cancel(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://managedidentities.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v1/{+name}:cancel').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'POST',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'name'
                ],
                pathParams: [
                    'name'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        delete(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://managedidentities.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v1/{+name}').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'DELETE',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'name'
                ],
                pathParams: [
                    'name'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        get(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://managedidentities.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v1/{+name}').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'GET',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'name'
                ],
                pathParams: [
                    'name'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        list(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://managedidentities.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v1/{+name}').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'GET',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'name'
                ],
                pathParams: [
                    'name'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
    }
    managedidentities_v1.Resource$Projects$Locations$Global$Operations = Resource$Projects$Locations$Global$Operations;
    class Resource$Projects$Locations$Global$Peerings {
        context;
        constructor(context){
            this.context = context;
        }
        create(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://managedidentities.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v1/{+parent}/peerings').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'POST',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'parent'
                ],
                pathParams: [
                    'parent'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        delete(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://managedidentities.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v1/{+name}').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'DELETE',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'name'
                ],
                pathParams: [
                    'name'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        get(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://managedidentities.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v1/{+name}').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'GET',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'name'
                ],
                pathParams: [
                    'name'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        getIamPolicy(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://managedidentities.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v1/{+resource}:getIamPolicy').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'GET',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'resource'
                ],
                pathParams: [
                    'resource'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        list(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://managedidentities.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v1/{+parent}/peerings').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'GET',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'parent'
                ],
                pathParams: [
                    'parent'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        patch(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://managedidentities.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v1/{+name}').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'PATCH',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'name'
                ],
                pathParams: [
                    'name'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        setIamPolicy(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://managedidentities.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v1/{+resource}:setIamPolicy').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'POST',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'resource'
                ],
                pathParams: [
                    'resource'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        testIamPermissions(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://managedidentities.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v1/{+resource}:testIamPermissions').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'POST',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'resource'
                ],
                pathParams: [
                    'resource'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
    }
    managedidentities_v1.Resource$Projects$Locations$Global$Peerings = Resource$Projects$Locations$Global$Peerings;
})(managedidentities_v1 || (exports.managedidentities_v1 = managedidentities_v1 = {}));
}}),
"[project]/node_modules/googleapis/build/src/apis/managedidentities/v1alpha1.js [app-rsc] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
// Copyright 2020 Google LLC
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//    http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.managedidentities_v1alpha1 = void 0;
/* eslint-disable @typescript-eslint/no-explicit-any */ /* eslint-disable @typescript-eslint/no-unused-vars */ /* eslint-disable @typescript-eslint/no-empty-interface */ /* eslint-disable @typescript-eslint/no-namespace */ /* eslint-disable no-irregular-whitespace */ const googleapis_common_1 = __turbopack_context__.r("[project]/node_modules/googleapis-common/build/src/index.js [app-rsc] (ecmascript)");
var managedidentities_v1alpha1;
(function(managedidentities_v1alpha1) {
    /**
     * Managed Service for Microsoft Active Directory API
     *
     * The Managed Service for Microsoft Active Directory API is used for managing a highly available, hardened service running Microsoft Active Directory (AD).
     *
     * @example
     * ```js
     * const {google} = require('googleapis');
     * const managedidentities = google.managedidentities('v1alpha1');
     * ```
     */ class Managedidentities {
        context;
        projects;
        constructor(options, google){
            this.context = {
                _options: options || {},
                google
            };
            this.projects = new Resource$Projects(this.context);
        }
    }
    managedidentities_v1alpha1.Managedidentities = Managedidentities;
    class Resource$Projects {
        context;
        locations;
        constructor(context){
            this.context = context;
            this.locations = new Resource$Projects$Locations(this.context);
        }
    }
    managedidentities_v1alpha1.Resource$Projects = Resource$Projects;
    class Resource$Projects$Locations {
        context;
        global;
        constructor(context){
            this.context = context;
            this.global = new Resource$Projects$Locations$Global(this.context);
        }
        get(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://managedidentities.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v1alpha1/{+name}').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'GET',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'name'
                ],
                pathParams: [
                    'name'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        list(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://managedidentities.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v1alpha1/{+name}/locations').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'GET',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'name'
                ],
                pathParams: [
                    'name'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
    }
    managedidentities_v1alpha1.Resource$Projects$Locations = Resource$Projects$Locations;
    class Resource$Projects$Locations$Global {
        context;
        domains;
        operations;
        peerings;
        constructor(context){
            this.context = context;
            this.domains = new Resource$Projects$Locations$Global$Domains(this.context);
            this.operations = new Resource$Projects$Locations$Global$Operations(this.context);
            this.peerings = new Resource$Projects$Locations$Global$Peerings(this.context);
        }
    }
    managedidentities_v1alpha1.Resource$Projects$Locations$Global = Resource$Projects$Locations$Global;
    class Resource$Projects$Locations$Global$Domains {
        context;
        backups;
        sqlIntegrations;
        constructor(context){
            this.context = context;
            this.backups = new Resource$Projects$Locations$Global$Domains$Backups(this.context);
            this.sqlIntegrations = new Resource$Projects$Locations$Global$Domains$Sqlintegrations(this.context);
        }
        attachTrust(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://managedidentities.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v1alpha1/{+name}:attachTrust').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'POST',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'name'
                ],
                pathParams: [
                    'name'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        checkMigrationPermission(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://managedidentities.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v1alpha1/{+domain}:checkMigrationPermission').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'POST',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'domain'
                ],
                pathParams: [
                    'domain'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        create(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://managedidentities.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v1alpha1/{+parent}/domains').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'POST',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'parent'
                ],
                pathParams: [
                    'parent'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        delete(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://managedidentities.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v1alpha1/{+name}').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'DELETE',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'name'
                ],
                pathParams: [
                    'name'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        detachTrust(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://managedidentities.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v1alpha1/{+name}:detachTrust').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'POST',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'name'
                ],
                pathParams: [
                    'name'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        disableMigration(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://managedidentities.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v1alpha1/{+domain}:disableMigration').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'POST',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'domain'
                ],
                pathParams: [
                    'domain'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        domainJoinMachine(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://managedidentities.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v1alpha1/{+domain}:domainJoinMachine').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'POST',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'domain'
                ],
                pathParams: [
                    'domain'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        enableMigration(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://managedidentities.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v1alpha1/{+domain}:enableMigration').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'POST',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'domain'
                ],
                pathParams: [
                    'domain'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        extendSchema(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://managedidentities.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v1alpha1/{+domain}:extendSchema').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'POST',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'domain'
                ],
                pathParams: [
                    'domain'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        get(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://managedidentities.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v1alpha1/{+name}').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'GET',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'name'
                ],
                pathParams: [
                    'name'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        getIamPolicy(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://managedidentities.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v1alpha1/{+resource}:getIamPolicy').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'GET',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'resource'
                ],
                pathParams: [
                    'resource'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        getLdapssettings(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://managedidentities.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v1alpha1/{+name}/ldapssettings').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'GET',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'name'
                ],
                pathParams: [
                    'name'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        list(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://managedidentities.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v1alpha1/{+parent}/domains').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'GET',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'parent'
                ],
                pathParams: [
                    'parent'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        patch(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://managedidentities.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v1alpha1/{+name}').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'PATCH',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'name'
                ],
                pathParams: [
                    'name'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        reconfigureTrust(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://managedidentities.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v1alpha1/{+name}:reconfigureTrust').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'POST',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'name'
                ],
                pathParams: [
                    'name'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        resetAdminPassword(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://managedidentities.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v1alpha1/{+name}:resetAdminPassword').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'POST',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'name'
                ],
                pathParams: [
                    'name'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        restore(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://managedidentities.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v1alpha1/{+name}:restore').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'POST',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'name'
                ],
                pathParams: [
                    'name'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        setIamPolicy(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://managedidentities.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v1alpha1/{+resource}:setIamPolicy').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'POST',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'resource'
                ],
                pathParams: [
                    'resource'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        testIamPermissions(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://managedidentities.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v1alpha1/{+resource}:testIamPermissions').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'POST',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'resource'
                ],
                pathParams: [
                    'resource'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        updateLdapssettings(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://managedidentities.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v1alpha1/{+name}/ldapssettings').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'PATCH',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'name'
                ],
                pathParams: [
                    'name'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        validateTrust(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://managedidentities.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v1alpha1/{+name}:validateTrust').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'POST',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'name'
                ],
                pathParams: [
                    'name'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
    }
    managedidentities_v1alpha1.Resource$Projects$Locations$Global$Domains = Resource$Projects$Locations$Global$Domains;
    class Resource$Projects$Locations$Global$Domains$Backups {
        context;
        constructor(context){
            this.context = context;
        }
        create(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://managedidentities.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v1alpha1/{+parent}/backups').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'POST',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'parent'
                ],
                pathParams: [
                    'parent'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        delete(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://managedidentities.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v1alpha1/{+name}').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'DELETE',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'name'
                ],
                pathParams: [
                    'name'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        get(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://managedidentities.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v1alpha1/{+name}').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'GET',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'name'
                ],
                pathParams: [
                    'name'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        getIamPolicy(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://managedidentities.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v1alpha1/{+resource}:getIamPolicy').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'GET',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'resource'
                ],
                pathParams: [
                    'resource'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        list(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://managedidentities.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v1alpha1/{+parent}/backups').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'GET',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'parent'
                ],
                pathParams: [
                    'parent'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        patch(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://managedidentities.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v1alpha1/{+name}').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'PATCH',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'name'
                ],
                pathParams: [
                    'name'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        setIamPolicy(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://managedidentities.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v1alpha1/{+resource}:setIamPolicy').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'POST',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'resource'
                ],
                pathParams: [
                    'resource'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        testIamPermissions(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://managedidentities.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v1alpha1/{+resource}:testIamPermissions').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'POST',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'resource'
                ],
                pathParams: [
                    'resource'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
    }
    managedidentities_v1alpha1.Resource$Projects$Locations$Global$Domains$Backups = Resource$Projects$Locations$Global$Domains$Backups;
    class Resource$Projects$Locations$Global$Domains$Sqlintegrations {
        context;
        constructor(context){
            this.context = context;
        }
        get(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://managedidentities.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v1alpha1/{+name}').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'GET',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'name'
                ],
                pathParams: [
                    'name'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        list(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://managedidentities.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v1alpha1/{+parent}/sqlIntegrations').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'GET',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'parent'
                ],
                pathParams: [
                    'parent'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
    }
    managedidentities_v1alpha1.Resource$Projects$Locations$Global$Domains$Sqlintegrations = Resource$Projects$Locations$Global$Domains$Sqlintegrations;
    class Resource$Projects$Locations$Global$Operations {
        context;
        constructor(context){
            this.context = context;
        }
        cancel(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://managedidentities.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v1alpha1/{+name}:cancel').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'POST',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'name'
                ],
                pathParams: [
                    'name'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        delete(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://managedidentities.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v1alpha1/{+name}').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'DELETE',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'name'
                ],
                pathParams: [
                    'name'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        get(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://managedidentities.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v1alpha1/{+name}').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'GET',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'name'
                ],
                pathParams: [
                    'name'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        list(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://managedidentities.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v1alpha1/{+name}/operations').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'GET',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'name'
                ],
                pathParams: [
                    'name'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
    }
    managedidentities_v1alpha1.Resource$Projects$Locations$Global$Operations = Resource$Projects$Locations$Global$Operations;
    class Resource$Projects$Locations$Global$Peerings {
        context;
        constructor(context){
            this.context = context;
        }
        create(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://managedidentities.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v1alpha1/{+parent}/peerings').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'POST',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'parent'
                ],
                pathParams: [
                    'parent'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        delete(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://managedidentities.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v1alpha1/{+name}').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'DELETE',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'name'
                ],
                pathParams: [
                    'name'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        get(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://managedidentities.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v1alpha1/{+name}').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'GET',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'name'
                ],
                pathParams: [
                    'name'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        getIamPolicy(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://managedidentities.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v1alpha1/{+resource}:getIamPolicy').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'GET',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'resource'
                ],
                pathParams: [
                    'resource'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        list(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://managedidentities.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v1alpha1/{+parent}/peerings').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'GET',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'parent'
                ],
                pathParams: [
                    'parent'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        patch(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://managedidentities.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v1alpha1/{+name}').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'PATCH',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'name'
                ],
                pathParams: [
                    'name'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        setIamPolicy(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://managedidentities.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v1alpha1/{+resource}:setIamPolicy').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'POST',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'resource'
                ],
                pathParams: [
                    'resource'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        testIamPermissions(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://managedidentities.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v1alpha1/{+resource}:testIamPermissions').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'POST',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'resource'
                ],
                pathParams: [
                    'resource'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
    }
    managedidentities_v1alpha1.Resource$Projects$Locations$Global$Peerings = Resource$Projects$Locations$Global$Peerings;
})(managedidentities_v1alpha1 || (exports.managedidentities_v1alpha1 = managedidentities_v1alpha1 = {}));
}}),
"[project]/node_modules/googleapis/build/src/apis/managedidentities/v1beta1.js [app-rsc] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
// Copyright 2020 Google LLC
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//    http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.managedidentities_v1beta1 = void 0;
/* eslint-disable @typescript-eslint/no-explicit-any */ /* eslint-disable @typescript-eslint/no-unused-vars */ /* eslint-disable @typescript-eslint/no-empty-interface */ /* eslint-disable @typescript-eslint/no-namespace */ /* eslint-disable no-irregular-whitespace */ const googleapis_common_1 = __turbopack_context__.r("[project]/node_modules/googleapis-common/build/src/index.js [app-rsc] (ecmascript)");
var managedidentities_v1beta1;
(function(managedidentities_v1beta1) {
    /**
     * Managed Service for Microsoft Active Directory API
     *
     * The Managed Service for Microsoft Active Directory API is used for managing a highly available, hardened service running Microsoft Active Directory (AD).
     *
     * @example
     * ```js
     * const {google} = require('googleapis');
     * const managedidentities = google.managedidentities('v1beta1');
     * ```
     */ class Managedidentities {
        context;
        projects;
        constructor(options, google){
            this.context = {
                _options: options || {},
                google
            };
            this.projects = new Resource$Projects(this.context);
        }
    }
    managedidentities_v1beta1.Managedidentities = Managedidentities;
    class Resource$Projects {
        context;
        locations;
        constructor(context){
            this.context = context;
            this.locations = new Resource$Projects$Locations(this.context);
        }
    }
    managedidentities_v1beta1.Resource$Projects = Resource$Projects;
    class Resource$Projects$Locations {
        context;
        global;
        constructor(context){
            this.context = context;
            this.global = new Resource$Projects$Locations$Global(this.context);
        }
        get(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://managedidentities.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v1beta1/{+name}').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'GET',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'name'
                ],
                pathParams: [
                    'name'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        list(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://managedidentities.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v1beta1/{+name}/locations').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'GET',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'name'
                ],
                pathParams: [
                    'name'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
    }
    managedidentities_v1beta1.Resource$Projects$Locations = Resource$Projects$Locations;
    class Resource$Projects$Locations$Global {
        context;
        domains;
        operations;
        peerings;
        constructor(context){
            this.context = context;
            this.domains = new Resource$Projects$Locations$Global$Domains(this.context);
            this.operations = new Resource$Projects$Locations$Global$Operations(this.context);
            this.peerings = new Resource$Projects$Locations$Global$Peerings(this.context);
        }
    }
    managedidentities_v1beta1.Resource$Projects$Locations$Global = Resource$Projects$Locations$Global;
    class Resource$Projects$Locations$Global$Domains {
        context;
        backups;
        sqlIntegrations;
        constructor(context){
            this.context = context;
            this.backups = new Resource$Projects$Locations$Global$Domains$Backups(this.context);
            this.sqlIntegrations = new Resource$Projects$Locations$Global$Domains$Sqlintegrations(this.context);
        }
        attachTrust(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://managedidentities.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v1beta1/{+name}:attachTrust').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'POST',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'name'
                ],
                pathParams: [
                    'name'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        checkMigrationPermission(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://managedidentities.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v1beta1/{+domain}:checkMigrationPermission').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'POST',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'domain'
                ],
                pathParams: [
                    'domain'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        create(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://managedidentities.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v1beta1/{+parent}/domains').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'POST',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'parent'
                ],
                pathParams: [
                    'parent'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        delete(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://managedidentities.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v1beta1/{+name}').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'DELETE',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'name'
                ],
                pathParams: [
                    'name'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        detachTrust(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://managedidentities.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v1beta1/{+name}:detachTrust').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'POST',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'name'
                ],
                pathParams: [
                    'name'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        disableMigration(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://managedidentities.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v1beta1/{+domain}:disableMigration').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'POST',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'domain'
                ],
                pathParams: [
                    'domain'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        domainJoinMachine(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://managedidentities.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v1beta1/{+domain}:domainJoinMachine').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'POST',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'domain'
                ],
                pathParams: [
                    'domain'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        enableMigration(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://managedidentities.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v1beta1/{+domain}:enableMigration').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'POST',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'domain'
                ],
                pathParams: [
                    'domain'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        extendSchema(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://managedidentities.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v1beta1/{+domain}:extendSchema').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'POST',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'domain'
                ],
                pathParams: [
                    'domain'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        get(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://managedidentities.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v1beta1/{+name}').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'GET',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'name'
                ],
                pathParams: [
                    'name'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        getIamPolicy(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://managedidentities.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v1beta1/{+resource}:getIamPolicy').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'GET',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'resource'
                ],
                pathParams: [
                    'resource'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        getLdapssettings(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://managedidentities.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v1beta1/{+name}/ldapssettings').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'GET',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'name'
                ],
                pathParams: [
                    'name'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        list(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://managedidentities.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v1beta1/{+parent}/domains').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'GET',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'parent'
                ],
                pathParams: [
                    'parent'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        patch(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://managedidentities.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v1beta1/{+name}').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'PATCH',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'name'
                ],
                pathParams: [
                    'name'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        reconfigureTrust(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://managedidentities.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v1beta1/{+name}:reconfigureTrust').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'POST',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'name'
                ],
                pathParams: [
                    'name'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        resetAdminPassword(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://managedidentities.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v1beta1/{+name}:resetAdminPassword').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'POST',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'name'
                ],
                pathParams: [
                    'name'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        restore(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://managedidentities.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v1beta1/{+name}:restore').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'POST',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'name'
                ],
                pathParams: [
                    'name'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        setIamPolicy(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://managedidentities.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v1beta1/{+resource}:setIamPolicy').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'POST',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'resource'
                ],
                pathParams: [
                    'resource'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        testIamPermissions(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://managedidentities.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v1beta1/{+resource}:testIamPermissions').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'POST',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'resource'
                ],
                pathParams: [
                    'resource'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        updateLdapssettings(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://managedidentities.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v1beta1/{+name}/ldapssettings').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'PATCH',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'name'
                ],
                pathParams: [
                    'name'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        validateTrust(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://managedidentities.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v1beta1/{+name}:validateTrust').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'POST',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'name'
                ],
                pathParams: [
                    'name'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
    }
    managedidentities_v1beta1.Resource$Projects$Locations$Global$Domains = Resource$Projects$Locations$Global$Domains;
    class Resource$Projects$Locations$Global$Domains$Backups {
        context;
        constructor(context){
            this.context = context;
        }
        create(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://managedidentities.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v1beta1/{+parent}/backups').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'POST',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'parent'
                ],
                pathParams: [
                    'parent'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        delete(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://managedidentities.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v1beta1/{+name}').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'DELETE',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'name'
                ],
                pathParams: [
                    'name'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        get(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://managedidentities.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v1beta1/{+name}').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'GET',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'name'
                ],
                pathParams: [
                    'name'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        getIamPolicy(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://managedidentities.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v1beta1/{+resource}:getIamPolicy').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'GET',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'resource'
                ],
                pathParams: [
                    'resource'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        list(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://managedidentities.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v1beta1/{+parent}/backups').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'GET',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'parent'
                ],
                pathParams: [
                    'parent'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        patch(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://managedidentities.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v1beta1/{+name}').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'PATCH',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'name'
                ],
                pathParams: [
                    'name'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        setIamPolicy(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://managedidentities.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v1beta1/{+resource}:setIamPolicy').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'POST',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'resource'
                ],
                pathParams: [
                    'resource'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        testIamPermissions(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://managedidentities.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v1beta1/{+resource}:testIamPermissions').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'POST',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'resource'
                ],
                pathParams: [
                    'resource'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
    }
    managedidentities_v1beta1.Resource$Projects$Locations$Global$Domains$Backups = Resource$Projects$Locations$Global$Domains$Backups;
    class Resource$Projects$Locations$Global$Domains$Sqlintegrations {
        context;
        constructor(context){
            this.context = context;
        }
        get(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://managedidentities.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v1beta1/{+name}').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'GET',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'name'
                ],
                pathParams: [
                    'name'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        list(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://managedidentities.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v1beta1/{+parent}/sqlIntegrations').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'GET',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'parent'
                ],
                pathParams: [
                    'parent'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
    }
    managedidentities_v1beta1.Resource$Projects$Locations$Global$Domains$Sqlintegrations = Resource$Projects$Locations$Global$Domains$Sqlintegrations;
    class Resource$Projects$Locations$Global$Operations {
        context;
        constructor(context){
            this.context = context;
        }
        cancel(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://managedidentities.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v1beta1/{+name}:cancel').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'POST',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'name'
                ],
                pathParams: [
                    'name'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        delete(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://managedidentities.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v1beta1/{+name}').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'DELETE',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'name'
                ],
                pathParams: [
                    'name'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        get(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://managedidentities.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v1beta1/{+name}').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'GET',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'name'
                ],
                pathParams: [
                    'name'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        list(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://managedidentities.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v1beta1/{+name}').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'GET',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'name'
                ],
                pathParams: [
                    'name'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
    }
    managedidentities_v1beta1.Resource$Projects$Locations$Global$Operations = Resource$Projects$Locations$Global$Operations;
    class Resource$Projects$Locations$Global$Peerings {
        context;
        constructor(context){
            this.context = context;
        }
        create(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://managedidentities.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v1beta1/{+parent}/peerings').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'POST',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'parent'
                ],
                pathParams: [
                    'parent'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        delete(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://managedidentities.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v1beta1/{+name}').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'DELETE',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'name'
                ],
                pathParams: [
                    'name'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        get(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://managedidentities.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v1beta1/{+name}').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'GET',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'name'
                ],
                pathParams: [
                    'name'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        getIamPolicy(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://managedidentities.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v1beta1/{+resource}:getIamPolicy').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'GET',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'resource'
                ],
                pathParams: [
                    'resource'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        list(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://managedidentities.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v1beta1/{+parent}/peerings').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'GET',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'parent'
                ],
                pathParams: [
                    'parent'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        patch(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://managedidentities.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v1beta1/{+name}').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'PATCH',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'name'
                ],
                pathParams: [
                    'name'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        setIamPolicy(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://managedidentities.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v1beta1/{+resource}:setIamPolicy').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'POST',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'resource'
                ],
                pathParams: [
                    'resource'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        testIamPermissions(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://managedidentities.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v1beta1/{+resource}:testIamPermissions').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'POST',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'resource'
                ],
                pathParams: [
                    'resource'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
    }
    managedidentities_v1beta1.Resource$Projects$Locations$Global$Peerings = Resource$Projects$Locations$Global$Peerings;
})(managedidentities_v1beta1 || (exports.managedidentities_v1beta1 = managedidentities_v1beta1 = {}));
}}),
"[project]/node_modules/googleapis/build/src/apis/managedidentities/index.js [app-rsc] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
// Copyright 2020 Google LLC
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//    http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.AuthPlus = exports.managedidentities_v1beta1 = exports.managedidentities_v1alpha1 = exports.managedidentities_v1 = exports.auth = exports.VERSIONS = void 0;
exports.managedidentities = managedidentities;
/*! THIS FILE IS AUTO-GENERATED */ const googleapis_common_1 = __turbopack_context__.r("[project]/node_modules/googleapis-common/build/src/index.js [app-rsc] (ecmascript)");
const v1_1 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/managedidentities/v1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "managedidentities_v1", {
    enumerable: true,
    get: function() {
        return v1_1.managedidentities_v1;
    }
});
const v1alpha1_1 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/managedidentities/v1alpha1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "managedidentities_v1alpha1", {
    enumerable: true,
    get: function() {
        return v1alpha1_1.managedidentities_v1alpha1;
    }
});
const v1beta1_1 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/managedidentities/v1beta1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "managedidentities_v1beta1", {
    enumerable: true,
    get: function() {
        return v1beta1_1.managedidentities_v1beta1;
    }
});
exports.VERSIONS = {
    v1: v1_1.managedidentities_v1.Managedidentities,
    v1alpha1: v1alpha1_1.managedidentities_v1alpha1.Managedidentities,
    v1beta1: v1beta1_1.managedidentities_v1beta1.Managedidentities
};
function managedidentities(versionOrOptions) {
    return (0, googleapis_common_1.getAPI)('managedidentities', versionOrOptions, exports.VERSIONS, this);
}
const auth = new googleapis_common_1.AuthPlus();
exports.auth = auth;
var googleapis_common_2 = __turbopack_context__.r("[project]/node_modules/googleapis-common/build/src/index.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "AuthPlus", {
    enumerable: true,
    get: function() {
        return googleapis_common_2.AuthPlus;
    }
});
}}),

};

//# sourceMappingURL=node_modules_googleapis_build_src_apis_managedidentities_fae2ccff._.js.map
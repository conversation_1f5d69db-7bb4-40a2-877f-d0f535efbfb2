module.exports = {

"[project]/node_modules/googleapis/build/src/apis/domains/v1.js [app-rsc] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
// Copyright 2020 Google LLC
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//    http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.domains_v1 = void 0;
/* eslint-disable @typescript-eslint/no-explicit-any */ /* eslint-disable @typescript-eslint/no-unused-vars */ /* eslint-disable @typescript-eslint/no-empty-interface */ /* eslint-disable @typescript-eslint/no-namespace */ /* eslint-disable no-irregular-whitespace */ const googleapis_common_1 = __turbopack_context__.r("[project]/node_modules/googleapis-common/build/src/index.js [app-rsc] (ecmascript)");
var domains_v1;
(function(domains_v1) {
    /**
     * Cloud Domains API
     *
     * Enables management and configuration of domain names.
     *
     * @example
     * ```js
     * const {google} = require('googleapis');
     * const domains = google.domains('v1');
     * ```
     */ class Domains {
        context;
        projects;
        constructor(options, google){
            this.context = {
                _options: options || {},
                google
            };
            this.projects = new Resource$Projects(this.context);
        }
    }
    domains_v1.Domains = Domains;
    class Resource$Projects {
        context;
        locations;
        constructor(context){
            this.context = context;
            this.locations = new Resource$Projects$Locations(this.context);
        }
    }
    domains_v1.Resource$Projects = Resource$Projects;
    class Resource$Projects$Locations {
        context;
        operations;
        registrations;
        constructor(context){
            this.context = context;
            this.operations = new Resource$Projects$Locations$Operations(this.context);
            this.registrations = new Resource$Projects$Locations$Registrations(this.context);
        }
        get(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://domains.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v1/{+name}').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'GET',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'name'
                ],
                pathParams: [
                    'name'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        list(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://domains.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v1/{+name}/locations').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'GET',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'name'
                ],
                pathParams: [
                    'name'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
    }
    domains_v1.Resource$Projects$Locations = Resource$Projects$Locations;
    class Resource$Projects$Locations$Operations {
        context;
        constructor(context){
            this.context = context;
        }
        get(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://domains.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v1/{+name}').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'GET',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'name'
                ],
                pathParams: [
                    'name'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        list(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://domains.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v1/{+name}/operations').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'GET',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'name'
                ],
                pathParams: [
                    'name'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
    }
    domains_v1.Resource$Projects$Locations$Operations = Resource$Projects$Locations$Operations;
    class Resource$Projects$Locations$Registrations {
        context;
        constructor(context){
            this.context = context;
        }
        configureContactSettings(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://domains.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v1/{+registration}:configureContactSettings').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'POST',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'registration'
                ],
                pathParams: [
                    'registration'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        configureDnsSettings(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://domains.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v1/{+registration}:configureDnsSettings').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'POST',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'registration'
                ],
                pathParams: [
                    'registration'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        configureManagementSettings(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://domains.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v1/{+registration}:configureManagementSettings').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'POST',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'registration'
                ],
                pathParams: [
                    'registration'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        delete(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://domains.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v1/{+name}').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'DELETE',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'name'
                ],
                pathParams: [
                    'name'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        export(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://domains.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v1/{+name}:export').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'POST',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'name'
                ],
                pathParams: [
                    'name'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        get(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://domains.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v1/{+name}').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'GET',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'name'
                ],
                pathParams: [
                    'name'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        getIamPolicy(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://domains.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v1/{+resource}:getIamPolicy').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'GET',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'resource'
                ],
                pathParams: [
                    'resource'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        import(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://domains.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v1/{+parent}/registrations:import').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'POST',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'parent'
                ],
                pathParams: [
                    'parent'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        initiatePushTransfer(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://domains.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v1/{+registration}:initiatePushTransfer').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'POST',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'registration'
                ],
                pathParams: [
                    'registration'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        list(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://domains.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v1/{+parent}/registrations').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'GET',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'parent'
                ],
                pathParams: [
                    'parent'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        patch(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://domains.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v1/{+name}').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'PATCH',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'name'
                ],
                pathParams: [
                    'name'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        register(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://domains.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v1/{+parent}/registrations:register').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'POST',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'parent'
                ],
                pathParams: [
                    'parent'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        renewDomain(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://domains.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v1/{+registration}:renewDomain').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'POST',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'registration'
                ],
                pathParams: [
                    'registration'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        resetAuthorizationCode(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://domains.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v1/{+registration}:resetAuthorizationCode').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'POST',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'registration'
                ],
                pathParams: [
                    'registration'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        retrieveAuthorizationCode(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://domains.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v1/{+registration}:retrieveAuthorizationCode').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'GET',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'registration'
                ],
                pathParams: [
                    'registration'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        retrieveGoogleDomainsDnsRecords(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://domains.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v1/{+registration}:retrieveGoogleDomainsDnsRecords').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'GET',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'registration'
                ],
                pathParams: [
                    'registration'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        retrieveGoogleDomainsForwardingConfig(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://domains.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v1/{+registration}:retrieveGoogleDomainsForwardingConfig').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'GET',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'registration'
                ],
                pathParams: [
                    'registration'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        retrieveImportableDomains(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://domains.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v1/{+location}/registrations:retrieveImportableDomains').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'GET',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'location'
                ],
                pathParams: [
                    'location'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        retrieveRegisterParameters(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://domains.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v1/{+location}/registrations:retrieveRegisterParameters').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'GET',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'location'
                ],
                pathParams: [
                    'location'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        retrieveTransferParameters(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://domains.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v1/{+location}/registrations:retrieveTransferParameters').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'GET',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'location'
                ],
                pathParams: [
                    'location'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        searchDomains(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://domains.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v1/{+location}/registrations:searchDomains').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'GET',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'location'
                ],
                pathParams: [
                    'location'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        setIamPolicy(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://domains.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v1/{+resource}:setIamPolicy').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'POST',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'resource'
                ],
                pathParams: [
                    'resource'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        testIamPermissions(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://domains.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v1/{+resource}:testIamPermissions').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'POST',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'resource'
                ],
                pathParams: [
                    'resource'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        transfer(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://domains.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v1/{+parent}/registrations:transfer').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'POST',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'parent'
                ],
                pathParams: [
                    'parent'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
    }
    domains_v1.Resource$Projects$Locations$Registrations = Resource$Projects$Locations$Registrations;
})(domains_v1 || (exports.domains_v1 = domains_v1 = {}));
}}),
"[project]/node_modules/googleapis/build/src/apis/domains/v1alpha2.js [app-rsc] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
// Copyright 2020 Google LLC
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//    http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.domains_v1alpha2 = void 0;
/* eslint-disable @typescript-eslint/no-explicit-any */ /* eslint-disable @typescript-eslint/no-unused-vars */ /* eslint-disable @typescript-eslint/no-empty-interface */ /* eslint-disable @typescript-eslint/no-namespace */ /* eslint-disable no-irregular-whitespace */ const googleapis_common_1 = __turbopack_context__.r("[project]/node_modules/googleapis-common/build/src/index.js [app-rsc] (ecmascript)");
var domains_v1alpha2;
(function(domains_v1alpha2) {
    /**
     * Cloud Domains API
     *
     * Enables management and configuration of domain names.
     *
     * @example
     * ```js
     * const {google} = require('googleapis');
     * const domains = google.domains('v1alpha2');
     * ```
     */ class Domains {
        context;
        projects;
        constructor(options, google){
            this.context = {
                _options: options || {},
                google
            };
            this.projects = new Resource$Projects(this.context);
        }
    }
    domains_v1alpha2.Domains = Domains;
    class Resource$Projects {
        context;
        locations;
        constructor(context){
            this.context = context;
            this.locations = new Resource$Projects$Locations(this.context);
        }
    }
    domains_v1alpha2.Resource$Projects = Resource$Projects;
    class Resource$Projects$Locations {
        context;
        operations;
        registrations;
        constructor(context){
            this.context = context;
            this.operations = new Resource$Projects$Locations$Operations(this.context);
            this.registrations = new Resource$Projects$Locations$Registrations(this.context);
        }
        get(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://domains.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v1alpha2/{+name}').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'GET',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'name'
                ],
                pathParams: [
                    'name'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        list(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://domains.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v1alpha2/{+name}/locations').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'GET',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'name'
                ],
                pathParams: [
                    'name'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
    }
    domains_v1alpha2.Resource$Projects$Locations = Resource$Projects$Locations;
    class Resource$Projects$Locations$Operations {
        context;
        constructor(context){
            this.context = context;
        }
        get(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://domains.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v1alpha2/{+name}').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'GET',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'name'
                ],
                pathParams: [
                    'name'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        list(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://domains.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v1alpha2/{+name}/operations').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'GET',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'name'
                ],
                pathParams: [
                    'name'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
    }
    domains_v1alpha2.Resource$Projects$Locations$Operations = Resource$Projects$Locations$Operations;
    class Resource$Projects$Locations$Registrations {
        context;
        constructor(context){
            this.context = context;
        }
        configureContactSettings(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://domains.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v1alpha2/{+registration}:configureContactSettings').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'POST',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'registration'
                ],
                pathParams: [
                    'registration'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        configureDnsSettings(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://domains.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v1alpha2/{+registration}:configureDnsSettings').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'POST',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'registration'
                ],
                pathParams: [
                    'registration'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        configureManagementSettings(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://domains.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v1alpha2/{+registration}:configureManagementSettings').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'POST',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'registration'
                ],
                pathParams: [
                    'registration'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        delete(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://domains.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v1alpha2/{+name}').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'DELETE',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'name'
                ],
                pathParams: [
                    'name'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        export(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://domains.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v1alpha2/{+name}:export').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'POST',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'name'
                ],
                pathParams: [
                    'name'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        get(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://domains.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v1alpha2/{+name}').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'GET',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'name'
                ],
                pathParams: [
                    'name'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        getIamPolicy(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://domains.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v1alpha2/{+resource}:getIamPolicy').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'GET',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'resource'
                ],
                pathParams: [
                    'resource'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        import(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://domains.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v1alpha2/{+parent}/registrations:import').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'POST',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'parent'
                ],
                pathParams: [
                    'parent'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        initiatePushTransfer(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://domains.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v1alpha2/{+registration}:initiatePushTransfer').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'POST',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'registration'
                ],
                pathParams: [
                    'registration'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        list(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://domains.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v1alpha2/{+parent}/registrations').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'GET',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'parent'
                ],
                pathParams: [
                    'parent'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        patch(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://domains.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v1alpha2/{+name}').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'PATCH',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'name'
                ],
                pathParams: [
                    'name'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        register(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://domains.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v1alpha2/{+parent}/registrations:register').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'POST',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'parent'
                ],
                pathParams: [
                    'parent'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        renewDomain(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://domains.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v1alpha2/{+registration}:renewDomain').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'POST',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'registration'
                ],
                pathParams: [
                    'registration'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        resetAuthorizationCode(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://domains.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v1alpha2/{+registration}:resetAuthorizationCode').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'POST',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'registration'
                ],
                pathParams: [
                    'registration'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        retrieveAuthorizationCode(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://domains.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v1alpha2/{+registration}:retrieveAuthorizationCode').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'GET',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'registration'
                ],
                pathParams: [
                    'registration'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        retrieveGoogleDomainsDnsRecords(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://domains.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v1alpha2/{+registration}:retrieveGoogleDomainsDnsRecords').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'GET',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'registration'
                ],
                pathParams: [
                    'registration'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        retrieveGoogleDomainsForwardingConfig(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://domains.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v1alpha2/{+registration}:retrieveGoogleDomainsForwardingConfig').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'GET',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'registration'
                ],
                pathParams: [
                    'registration'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        retrieveImportableDomains(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://domains.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v1alpha2/{+location}/registrations:retrieveImportableDomains').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'GET',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'location'
                ],
                pathParams: [
                    'location'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        retrieveRegisterParameters(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://domains.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v1alpha2/{+location}/registrations:retrieveRegisterParameters').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'GET',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'location'
                ],
                pathParams: [
                    'location'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        retrieveTransferParameters(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://domains.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v1alpha2/{+location}/registrations:retrieveTransferParameters').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'GET',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'location'
                ],
                pathParams: [
                    'location'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        searchDomains(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://domains.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v1alpha2/{+location}/registrations:searchDomains').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'GET',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'location'
                ],
                pathParams: [
                    'location'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        setIamPolicy(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://domains.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v1alpha2/{+resource}:setIamPolicy').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'POST',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'resource'
                ],
                pathParams: [
                    'resource'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        testIamPermissions(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://domains.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v1alpha2/{+resource}:testIamPermissions').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'POST',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'resource'
                ],
                pathParams: [
                    'resource'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        transfer(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://domains.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v1alpha2/{+parent}/registrations:transfer').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'POST',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'parent'
                ],
                pathParams: [
                    'parent'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
    }
    domains_v1alpha2.Resource$Projects$Locations$Registrations = Resource$Projects$Locations$Registrations;
})(domains_v1alpha2 || (exports.domains_v1alpha2 = domains_v1alpha2 = {}));
}}),
"[project]/node_modules/googleapis/build/src/apis/domains/v1beta1.js [app-rsc] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
// Copyright 2020 Google LLC
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//    http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.domains_v1beta1 = void 0;
/* eslint-disable @typescript-eslint/no-explicit-any */ /* eslint-disable @typescript-eslint/no-unused-vars */ /* eslint-disable @typescript-eslint/no-empty-interface */ /* eslint-disable @typescript-eslint/no-namespace */ /* eslint-disable no-irregular-whitespace */ const googleapis_common_1 = __turbopack_context__.r("[project]/node_modules/googleapis-common/build/src/index.js [app-rsc] (ecmascript)");
var domains_v1beta1;
(function(domains_v1beta1) {
    /**
     * Cloud Domains API
     *
     * Enables management and configuration of domain names.
     *
     * @example
     * ```js
     * const {google} = require('googleapis');
     * const domains = google.domains('v1beta1');
     * ```
     */ class Domains {
        context;
        projects;
        constructor(options, google){
            this.context = {
                _options: options || {},
                google
            };
            this.projects = new Resource$Projects(this.context);
        }
    }
    domains_v1beta1.Domains = Domains;
    class Resource$Projects {
        context;
        locations;
        constructor(context){
            this.context = context;
            this.locations = new Resource$Projects$Locations(this.context);
        }
    }
    domains_v1beta1.Resource$Projects = Resource$Projects;
    class Resource$Projects$Locations {
        context;
        operations;
        registrations;
        constructor(context){
            this.context = context;
            this.operations = new Resource$Projects$Locations$Operations(this.context);
            this.registrations = new Resource$Projects$Locations$Registrations(this.context);
        }
        get(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://domains.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v1beta1/{+name}').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'GET',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'name'
                ],
                pathParams: [
                    'name'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        list(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://domains.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v1beta1/{+name}/locations').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'GET',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'name'
                ],
                pathParams: [
                    'name'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
    }
    domains_v1beta1.Resource$Projects$Locations = Resource$Projects$Locations;
    class Resource$Projects$Locations$Operations {
        context;
        constructor(context){
            this.context = context;
        }
        get(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://domains.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v1beta1/{+name}').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'GET',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'name'
                ],
                pathParams: [
                    'name'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        list(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://domains.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v1beta1/{+name}/operations').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'GET',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'name'
                ],
                pathParams: [
                    'name'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
    }
    domains_v1beta1.Resource$Projects$Locations$Operations = Resource$Projects$Locations$Operations;
    class Resource$Projects$Locations$Registrations {
        context;
        constructor(context){
            this.context = context;
        }
        configureContactSettings(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://domains.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v1beta1/{+registration}:configureContactSettings').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'POST',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'registration'
                ],
                pathParams: [
                    'registration'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        configureDnsSettings(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://domains.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v1beta1/{+registration}:configureDnsSettings').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'POST',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'registration'
                ],
                pathParams: [
                    'registration'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        configureManagementSettings(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://domains.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v1beta1/{+registration}:configureManagementSettings').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'POST',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'registration'
                ],
                pathParams: [
                    'registration'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        delete(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://domains.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v1beta1/{+name}').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'DELETE',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'name'
                ],
                pathParams: [
                    'name'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        export(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://domains.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v1beta1/{+name}:export').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'POST',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'name'
                ],
                pathParams: [
                    'name'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        get(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://domains.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v1beta1/{+name}').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'GET',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'name'
                ],
                pathParams: [
                    'name'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        getIamPolicy(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://domains.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v1beta1/{+resource}:getIamPolicy').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'GET',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'resource'
                ],
                pathParams: [
                    'resource'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        import(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://domains.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v1beta1/{+parent}/registrations:import').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'POST',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'parent'
                ],
                pathParams: [
                    'parent'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        initiatePushTransfer(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://domains.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v1beta1/{+registration}:initiatePushTransfer').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'POST',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'registration'
                ],
                pathParams: [
                    'registration'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        list(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://domains.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v1beta1/{+parent}/registrations').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'GET',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'parent'
                ],
                pathParams: [
                    'parent'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        patch(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://domains.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v1beta1/{+name}').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'PATCH',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'name'
                ],
                pathParams: [
                    'name'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        register(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://domains.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v1beta1/{+parent}/registrations:register').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'POST',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'parent'
                ],
                pathParams: [
                    'parent'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        renewDomain(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://domains.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v1beta1/{+registration}:renewDomain').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'POST',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'registration'
                ],
                pathParams: [
                    'registration'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        resetAuthorizationCode(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://domains.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v1beta1/{+registration}:resetAuthorizationCode').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'POST',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'registration'
                ],
                pathParams: [
                    'registration'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        retrieveAuthorizationCode(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://domains.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v1beta1/{+registration}:retrieveAuthorizationCode').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'GET',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'registration'
                ],
                pathParams: [
                    'registration'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        retrieveGoogleDomainsDnsRecords(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://domains.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v1beta1/{+registration}:retrieveGoogleDomainsDnsRecords').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'GET',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'registration'
                ],
                pathParams: [
                    'registration'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        retrieveGoogleDomainsForwardingConfig(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://domains.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v1beta1/{+registration}:retrieveGoogleDomainsForwardingConfig').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'GET',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'registration'
                ],
                pathParams: [
                    'registration'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        retrieveImportableDomains(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://domains.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v1beta1/{+location}/registrations:retrieveImportableDomains').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'GET',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'location'
                ],
                pathParams: [
                    'location'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        retrieveRegisterParameters(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://domains.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v1beta1/{+location}/registrations:retrieveRegisterParameters').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'GET',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'location'
                ],
                pathParams: [
                    'location'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        retrieveTransferParameters(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://domains.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v1beta1/{+location}/registrations:retrieveTransferParameters').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'GET',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'location'
                ],
                pathParams: [
                    'location'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        searchDomains(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://domains.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v1beta1/{+location}/registrations:searchDomains').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'GET',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'location'
                ],
                pathParams: [
                    'location'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        setIamPolicy(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://domains.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v1beta1/{+resource}:setIamPolicy').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'POST',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'resource'
                ],
                pathParams: [
                    'resource'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        testIamPermissions(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://domains.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v1beta1/{+resource}:testIamPermissions').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'POST',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'resource'
                ],
                pathParams: [
                    'resource'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        transfer(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://domains.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v1beta1/{+parent}/registrations:transfer').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'POST',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'parent'
                ],
                pathParams: [
                    'parent'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
    }
    domains_v1beta1.Resource$Projects$Locations$Registrations = Resource$Projects$Locations$Registrations;
})(domains_v1beta1 || (exports.domains_v1beta1 = domains_v1beta1 = {}));
}}),
"[project]/node_modules/googleapis/build/src/apis/domains/index.js [app-rsc] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
// Copyright 2020 Google LLC
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//    http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.AuthPlus = exports.domains_v1beta1 = exports.domains_v1alpha2 = exports.domains_v1 = exports.auth = exports.VERSIONS = void 0;
exports.domains = domains;
/*! THIS FILE IS AUTO-GENERATED */ const googleapis_common_1 = __turbopack_context__.r("[project]/node_modules/googleapis-common/build/src/index.js [app-rsc] (ecmascript)");
const v1_1 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/domains/v1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "domains_v1", {
    enumerable: true,
    get: function() {
        return v1_1.domains_v1;
    }
});
const v1alpha2_1 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/domains/v1alpha2.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "domains_v1alpha2", {
    enumerable: true,
    get: function() {
        return v1alpha2_1.domains_v1alpha2;
    }
});
const v1beta1_1 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/domains/v1beta1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "domains_v1beta1", {
    enumerable: true,
    get: function() {
        return v1beta1_1.domains_v1beta1;
    }
});
exports.VERSIONS = {
    v1: v1_1.domains_v1.Domains,
    v1alpha2: v1alpha2_1.domains_v1alpha2.Domains,
    v1beta1: v1beta1_1.domains_v1beta1.Domains
};
function domains(versionOrOptions) {
    return (0, googleapis_common_1.getAPI)('domains', versionOrOptions, exports.VERSIONS, this);
}
const auth = new googleapis_common_1.AuthPlus();
exports.auth = auth;
var googleapis_common_2 = __turbopack_context__.r("[project]/node_modules/googleapis-common/build/src/index.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "AuthPlus", {
    enumerable: true,
    get: function() {
        return googleapis_common_2.AuthPlus;
    }
});
}}),

};

//# sourceMappingURL=node_modules_googleapis_build_src_apis_domains_331eee6e._.js.map
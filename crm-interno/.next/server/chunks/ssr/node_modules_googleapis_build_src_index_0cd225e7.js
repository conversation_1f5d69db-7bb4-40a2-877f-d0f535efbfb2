module.exports = {

"[project]/node_modules/googleapis/build/src/index.js [app-rsc] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
// Copyright 2020 Google LLC
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//    http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
/*! THIS FILE IS AUTO-GENERATED */ Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.androidpublisher_v3 = exports.androidpublisher_v2 = exports.androidpublisher_v1 = exports.androidpublisher_v1_1 = exports.androidmanagement_v1 = exports.androidenterprise_v1 = exports.androiddeviceprovisioning_v1 = exports.analyticsreporting_v4 = exports.analyticshub_v1beta1 = exports.analyticshub_v1 = exports.analyticsdata_v1beta = exports.analyticsdata_v1alpha = exports.analyticsadmin_v1beta = exports.analyticsadmin_v1alpha = exports.analytics_v3 = exports.alloydb_v1beta = exports.alloydb_v1alpha = exports.alloydb_v1 = exports.alertcenter_v1beta1 = exports.airquality_v1 = exports.aiplatform_v1beta1 = exports.aiplatform_v1 = exports.advisorynotifications_v1 = exports.adsenseplatform_v1alpha = exports.adsenseplatform_v1 = exports.adsensehost_v4_1 = exports.adsense_v2 = exports.adsense_v1_4 = exports.admob_v1beta = exports.admob_v1 = exports.admin_reports_v1 = exports.admin_directory_v1 = exports.admin_datatransfer_v1 = exports.adexperiencereport_v1 = exports.adexchangebuyer2_v2beta1 = exports.adexchangebuyer_v1_4 = exports.adexchangebuyer_v1_3 = exports.adexchangebuyer_v1_2 = exports.addressvalidation_v1 = exports.acmedns_v1 = exports.accesscontextmanager_v1beta = exports.accesscontextmanager_v1 = exports.accessapproval_v1beta1 = exports.accessapproval_v1 = exports.acceleratedmobilepageurl_v1 = exports.abusiveexperiencereport_v1 = exports.Auth = exports.Common = exports.GoogleApis = exports.google = void 0;
exports.calendar_v3 = exports.businessprofileperformance_v1 = exports.books_v1 = exports.blogger_v3 = exports.blogger_v2 = exports.blockchainnodeengine_v1 = exports.binaryauthorization_v1beta1 = exports.binaryauthorization_v1 = exports.billingbudgets_v1beta1 = exports.billingbudgets_v1 = exports.bigtableadmin_v2 = exports.bigtableadmin_v1 = exports.bigqueryreservation_v1beta1 = exports.bigqueryreservation_v1alpha2 = exports.bigqueryreservation_v1 = exports.bigquerydatatransfer_v1 = exports.bigquerydatapolicy_v1 = exports.bigqueryconnection_v1beta1 = exports.bigqueryconnection_v1 = exports.bigquery_v2 = exports.biglake_v1 = exports.beyondcorp_v1alpha = exports.beyondcorp_v1 = exports.batch_v1 = exports.baremetalsolution_v2 = exports.baremetalsolution_v1alpha1 = exports.baremetalsolution_v1 = exports.backupdr_v1 = exports.authorizedbuyersmarketplace_v1beta = exports.authorizedbuyersmarketplace_v1alpha = exports.authorizedbuyersmarketplace_v1 = exports.assuredworkloads_v1beta1 = exports.assuredworkloads_v1 = exports.artifactregistry_v1beta2 = exports.artifactregistry_v1beta1 = exports.artifactregistry_v1 = exports.areainsights_v1 = exports.area120tables_v1alpha1 = exports.appsactivity_v1 = exports.apphub_v1alpha = exports.apphub_v1 = exports.appengine_v1beta = exports.appengine_v1alpha = exports.appengine_v1 = exports.apim_v1alpha = exports.apikeys_v2 = exports.apihub_v1 = exports.apigeeregistry_v1 = exports.apigateway_v1beta = exports.apigateway_v1 = void 0;
exports.cloudsupport_v2beta = exports.cloudsupport_v2 = exports.cloudshell_v1alpha1 = exports.cloudshell_v1 = exports.cloudsearch_v1 = exports.cloudscheduler_v1beta1 = exports.cloudscheduler_v1 = exports.cloudresourcemanager_v3 = exports.cloudresourcemanager_v2beta1 = exports.cloudresourcemanager_v2 = exports.cloudresourcemanager_v1beta1 = exports.cloudresourcemanager_v1 = exports.cloudprofiler_v2 = exports.cloudlocationfinder_v1alpha = exports.cloudkms_v1 = exports.cloudiot_v1 = exports.cloudidentity_v1beta1 = exports.cloudidentity_v1 = exports.cloudfunctions_v2beta = exports.cloudfunctions_v2alpha = exports.cloudfunctions_v2 = exports.cloudfunctions_v1beta2 = exports.cloudfunctions_v1 = exports.clouderrorreporting_v1beta1 = exports.clouddeploy_v1 = exports.clouddebugger_v2 = exports.cloudcontrolspartner_v1beta = exports.cloudcontrolspartner_v1 = exports.cloudchannel_v1 = exports.cloudbuild_v2 = exports.cloudbuild_v1beta1 = exports.cloudbuild_v1alpha2 = exports.cloudbuild_v1alpha1 = exports.cloudbuild_v1 = exports.cloudbilling_v1beta = exports.cloudbilling_v1 = exports.cloudasset_v1p7beta1 = exports.cloudasset_v1p5beta1 = exports.cloudasset_v1p4beta1 = exports.cloudasset_v1p1beta1 = exports.cloudasset_v1beta1 = exports.cloudasset_v1 = exports.classroom_v1 = exports.civicinfo_v2 = exports.chromeuxreport_v1 = exports.chromepolicy_v1 = exports.chromemanagement_v1 = exports.checks_v1alpha = exports.chat_v1 = exports.certificatemanager_v1 = void 0;
exports.deploymentmanager_v2beta = exports.deploymentmanager_v2 = exports.deploymentmanager_alpha = exports.datastream_v1alpha1 = exports.datastream_v1 = exports.datastore_v1beta3 = exports.datastore_v1beta1 = exports.datastore_v1 = exports.dataproc_v1beta2 = exports.dataproc_v1 = exports.dataportability_v1beta = exports.dataportability_v1 = exports.dataplex_v1 = exports.datapipelines_v1 = exports.datamigration_v1beta1 = exports.datamigration_v1 = exports.datalineage_v1 = exports.datalabeling_v1beta1 = exports.datafusion_v1beta1 = exports.datafusion_v1 = exports.dataform_v1beta1 = exports.dataflow_v1b3 = exports.datacatalog_v1beta1 = exports.datacatalog_v1 = exports.customsearch_v1 = exports.css_v1 = exports.contentwarehouse_v1 = exports.content_v2 = exports.content_v2_1 = exports.containeranalysis_v1beta1 = exports.containeranalysis_v1alpha1 = exports.containeranalysis_v1 = exports.container_v1beta1 = exports.container_v1 = exports.contactcenterinsights_v1 = exports.contactcenteraiplatform_v1alpha1 = exports.connectors_v2 = exports.connectors_v1 = exports.config_v1 = exports.compute_v1 = exports.compute_beta = exports.compute_alpha = exports.composer_v1beta1 = exports.composer_v1 = exports.cloudtrace_v2beta1 = exports.cloudtrace_v2 = exports.cloudtrace_v1 = exports.cloudtasks_v2beta3 = exports.cloudtasks_v2beta2 = exports.cloudtasks_v2 = void 0;
exports.file_v1 = exports.fcmdata_v1beta1 = exports.fcm_v1 = exports.factchecktools_v1alpha1 = exports.eventarc_v1beta1 = exports.eventarc_v1 = exports.essentialcontacts_v1 = exports.drivelabels_v2beta = exports.drivelabels_v2 = exports.driveactivity_v2 = exports.drive_v3 = exports.drive_v2 = exports.doubleclicksearch_v2 = exports.doubleclickbidmanager_v2 = exports.doubleclickbidmanager_v1 = exports.doubleclickbidmanager_v1_1 = exports.domainsrdap_v1 = exports.domains_v1beta1 = exports.domains_v1alpha2 = exports.domains_v1 = exports.documentai_v1beta3 = exports.documentai_v1beta2 = exports.documentai_v1 = exports.docs_v1 = exports.dns_v2beta1 = exports.dns_v2 = exports.dns_v1beta2 = exports.dns_v1 = exports.dlp_v2 = exports.displayvideo_v4 = exports.displayvideo_v3 = exports.displayvideo_v2 = exports.displayvideo_v1dev = exports.displayvideo_v1beta2 = exports.displayvideo_v1beta = exports.displayvideo_v1 = exports.discoveryengine_v1beta = exports.discoveryengine_v1alpha = exports.discoveryengine_v1 = exports.discovery_v1 = exports.digitalassetlinks_v1 = exports.dialogflow_v3beta1 = exports.dialogflow_v3 = exports.dialogflow_v2beta1 = exports.dialogflow_v2 = exports.dfareporting_v4 = exports.dfareporting_v3_5 = exports.dfareporting_v3_4 = exports.dfareporting_v3_3 = exports.developerconnect_v1 = void 0;
exports.homegraph_v1 = exports.healthcare_v1beta1 = exports.healthcare_v1 = exports.groupssettings_v1 = exports.groupsmigration_v1 = exports.gmailpostmastertools_v1beta1 = exports.gmailpostmastertools_v1 = exports.gmail_v1 = exports.gkeonprem_v1 = exports.gkehub_v2beta = exports.gkehub_v2alpha = exports.gkehub_v2 = exports.gkehub_v1beta1 = exports.gkehub_v1beta = exports.gkehub_v1alpha2 = exports.gkehub_v1alpha = exports.gkehub_v1 = exports.gkebackup_v1 = exports.genomics_v2alpha1 = exports.genomics_v1alpha2 = exports.genomics_v1 = exports.gameservices_v1beta = exports.gameservices_v1 = exports.gamesManagement_v1management = exports.gamesConfiguration_v1configuration = exports.games_v1 = exports.forms_v1 = exports.fitness_v1 = exports.firestore_v1beta2 = exports.firestore_v1beta1 = exports.firestore_v1 = exports.firebasestorage_v1beta = exports.firebaserules_v1 = exports.firebaseml_v2beta = exports.firebaseml_v1beta2 = exports.firebaseml_v1 = exports.firebasehosting_v1beta1 = exports.firebasehosting_v1 = exports.firebasedynamiclinks_v1 = exports.firebasedataconnect_v1beta = exports.firebasedataconnect_v1 = exports.firebasedatabase_v1beta = exports.firebaseapphosting_v1beta = exports.firebaseapphosting_v1 = exports.firebaseappdistribution_v1alpha = exports.firebaseappdistribution_v1 = exports.firebaseappcheck_v1beta = exports.firebaseappcheck_v1 = exports.firebase_v1beta1 = exports.file_v1beta1 = void 0;
exports.merchantapi_quota_v1beta = exports.merchantapi_promotions_v1beta = exports.merchantapi_products_v1beta = exports.merchantapi_ordertracking_v1beta = exports.merchantapi_notifications_v1beta = exports.merchantapi_lfp_v1beta = exports.merchantapi_issueresolution_v1beta = exports.merchantapi_inventories_v1beta = exports.merchantapi_datasources_v1beta = exports.merchantapi_conversions_v1beta = exports.merchantapi_accounts_v1beta = exports.memcache_v1beta2 = exports.memcache_v1 = exports.meet_v2 = exports.marketingplatformadmin_v1alpha = exports.manufacturers_v1 = exports.managedkafka_v1 = exports.managedidentities_v1beta1 = exports.managedidentities_v1alpha1 = exports.managedidentities_v1 = exports.looker_v1 = exports.logging_v2 = exports.localservices_v1 = exports.lifesciences_v2beta = exports.licensing_v1 = exports.libraryagent_v1 = exports.language_v2 = exports.language_v1beta2 = exports.language_v1beta1 = exports.language_v1 = exports.kmsinventory_v1 = exports.kgsearch_v1 = exports.keep_v1 = exports.jobs_v4 = exports.jobs_v3p1beta1 = exports.jobs_v3 = exports.jobs_v2 = exports.integrations_v1alpha = exports.indexing_v3 = exports.ids_v1 = exports.identitytoolkit_v3 = exports.identitytoolkit_v2 = exports.ideahub_v1beta = exports.ideahub_v1alpha = exports.iap_v1beta1 = exports.iap_v1 = exports.iamcredentials_v1 = exports.iam_v2beta = exports.iam_v2 = exports.iam_v1 = void 0;
exports.parallelstore_v1beta = exports.parallelstore_v1 = exports.pagespeedonline_v5 = exports.oslogin_v1beta = exports.oslogin_v1alpha = exports.oslogin_v1 = exports.osconfig_v2beta = exports.osconfig_v2 = exports.osconfig_v1beta = exports.osconfig_v1alpha = exports.osconfig_v1 = exports.orgpolicy_v2 = exports.oracledatabase_v1 = exports.ondemandscanning_v1beta1 = exports.ondemandscanning_v1 = exports.observability_v1 = exports.oauth2_v2 = exports.notebooks_v2 = exports.notebooks_v1 = exports.networkservices_v1beta1 = exports.networkservices_v1 = exports.networksecurity_v1beta1 = exports.networksecurity_v1 = exports.networkmanagement_v1beta1 = exports.networkmanagement_v1 = exports.networkconnectivity_v1alpha1 = exports.networkconnectivity_v1 = exports.netapp_v1beta1 = exports.netapp_v1 = exports.mybusinessverifications_v1 = exports.mybusinessqanda_v1 = exports.mybusinessplaceactions_v1 = exports.mybusinessnotifications_v1 = exports.mybusinesslodging_v1 = exports.mybusinessbusinessinformation_v1 = exports.mybusinessbusinesscalls_v1 = exports.mybusinessaccountmanagement_v1 = exports.monitoring_v3 = exports.monitoring_v1 = exports.ml_v1 = exports.migrationcenter_v1alpha1 = exports.migrationcenter_v1 = exports.metastore_v2beta = exports.metastore_v2alpha = exports.metastore_v2 = exports.metastore_v1beta = exports.metastore_v1alpha = exports.metastore_v1 = exports.merchantapi_reviews_v1beta = exports.merchantapi_reports_v1beta = void 0;
exports.run_v1alpha1 = exports.run_v1 = exports.retail_v2beta = exports.retail_v2alpha = exports.retail_v2 = exports.resourcesettings_v1 = exports.reseller_v1 = exports.remotebuildexecution_v2 = exports.remotebuildexecution_v1alpha = exports.remotebuildexecution_v1 = exports.redis_v1beta1 = exports.redis_v1 = exports.recommender_v1beta1 = exports.recommender_v1 = exports.recommendationengine_v1beta1 = exports.recaptchaenterprise_v1 = exports.realtimebidding_v1alpha = exports.realtimebidding_v1 = exports.readerrevenuesubscriptionlinking_v1 = exports.rapidmigrationassessment_v1 = exports.pubsublite_v1 = exports.pubsub_v1beta2 = exports.pubsub_v1beta1a = exports.pubsub_v1 = exports.publicca_v1beta1 = exports.publicca_v1alpha1 = exports.publicca_v1 = exports.prod_tt_sasportal_v1alpha1 = exports.privateca_v1beta1 = exports.privateca_v1 = exports.poly_v1 = exports.pollen_v1 = exports.policytroubleshooter_v1beta = exports.policytroubleshooter_v1 = exports.policysimulator_v1beta1 = exports.policysimulator_v1beta = exports.policysimulator_v1alpha = exports.policysimulator_v1 = exports.policyanalyzer_v1beta1 = exports.policyanalyzer_v1 = exports.plus_v1 = exports.playintegrity_v1 = exports.playgrouping_v1alpha1 = exports.playdeveloperreporting_v1beta1 = exports.playdeveloperreporting_v1alpha1 = exports.playcustomapp_v1 = exports.playablelocations_v3 = exports.places_v1 = exports.people_v1 = exports.paymentsresellersubscription_v1 = void 0;
exports.sts_v1 = exports.streetviewpublish_v1 = exports.storagetransfer_v1 = exports.storagebatchoperations_v1 = exports.storage_v1beta2 = exports.storage_v1 = exports.sqladmin_v1beta4 = exports.sqladmin_v1 = exports.sql_v1beta4 = exports.speech_v2beta1 = exports.speech_v1p1beta1 = exports.speech_v1 = exports.spanner_v1 = exports.sourcerepo_v1 = exports.solar_v1 = exports.smartdevicemanagement_v1 = exports.slides_v1 = exports.siteVerification_v1 = exports.sheets_v4 = exports.serviceusage_v1beta1 = exports.serviceusage_v1 = exports.servicenetworking_v1beta = exports.servicenetworking_v1 = exports.servicemanagement_v1 = exports.servicedirectory_v1beta1 = exports.servicedirectory_v1 = exports.servicecontrol_v2 = exports.servicecontrol_v1 = exports.serviceconsumermanagement_v1beta1 = exports.serviceconsumermanagement_v1 = exports.securityposture_v1 = exports.securitycenter_v1p1beta1 = exports.securitycenter_v1p1alpha1 = exports.securitycenter_v1beta2 = exports.securitycenter_v1beta1 = exports.securitycenter_v1 = exports.secretmanager_v1beta2 = exports.secretmanager_v1beta1 = exports.secretmanager_v1 = exports.searchconsole_v1 = exports.searchads360_v0 = exports.script_v1 = exports.sasportal_v1alpha1 = exports.safebrowsing_v5 = exports.safebrowsing_v4 = exports.saasservicemgmt_v1beta1 = exports.runtimeconfig_v1beta1 = exports.runtimeconfig_v1 = exports.run_v2 = exports.run_v1beta1 = void 0;
exports.workloadmanager_v1 = exports.workflows_v1beta = exports.workflows_v1 = exports.workflowexecutions_v1beta = exports.workflowexecutions_v1 = exports.websecurityscanner_v1beta = exports.websecurityscanner_v1alpha = exports.websecurityscanner_v1 = exports.webrisk_v1 = exports.webmasters_v3 = exports.webfonts_v1 = exports.walletobjects_v1 = exports.vpcaccess_v1beta1 = exports.vpcaccess_v1 = exports.vmwareengine_v1 = exports.vmmigration_v1alpha1 = exports.vmmigration_v1 = exports.vision_v1p2beta1 = exports.vision_v1p1beta1 = exports.vision_v1 = exports.videointelligence_v1p3beta1 = exports.videointelligence_v1p2beta1 = exports.videointelligence_v1p1beta1 = exports.videointelligence_v1beta2 = exports.videointelligence_v1 = exports.versionhistory_v1 = exports.verifiedaccess_v2 = exports.verifiedaccess_v1 = exports.vectortile_v1 = exports.vault_v1 = exports.travelimpactmodel_v1 = exports.translate_v3beta1 = exports.translate_v3 = exports.translate_v2 = exports.transcoder_v1beta1 = exports.transcoder_v1 = exports.trafficdirector_v3 = exports.trafficdirector_v2 = exports.tpu_v2alpha1 = exports.tpu_v2 = exports.tpu_v1alpha1 = exports.tpu_v1 = exports.toolresults_v1beta3 = exports.texttospeech_v1beta1 = exports.texttospeech_v1 = exports.testing_v1 = exports.tasks_v1 = exports.tagmanager_v2 = exports.tagmanager_v1 = exports.sts_v1beta = void 0;
exports.youtubereporting_v1 = exports.youtubeAnalytics_v2 = exports.youtubeAnalytics_v1 = exports.youtube_v3 = exports.workstations_v1beta = exports.workstations_v1 = exports.workspaceevents_v1 = void 0;
const googleapis_1 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/googleapis.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "GoogleApis", {
    enumerable: true,
    get: function() {
        return googleapis_1.GoogleApis;
    }
});
const google = new googleapis_1.GoogleApis();
exports.google = google;
exports.Common = __turbopack_context__.r("[project]/node_modules/googleapis-common/build/src/index.js [app-rsc] (ecmascript)");
exports.Auth = __turbopack_context__.r("[project]/node_modules/google-auth-library/build/src/index.js [app-rsc] (ecmascript)");
var v1_1 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/abusiveexperiencereport/v1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "abusiveexperiencereport_v1", {
    enumerable: true,
    get: function() {
        return v1_1.abusiveexperiencereport_v1;
    }
});
var v1_2 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/acceleratedmobilepageurl/v1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "acceleratedmobilepageurl_v1", {
    enumerable: true,
    get: function() {
        return v1_2.acceleratedmobilepageurl_v1;
    }
});
var v1_3 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/accessapproval/v1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "accessapproval_v1", {
    enumerable: true,
    get: function() {
        return v1_3.accessapproval_v1;
    }
});
var v1beta1_1 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/accessapproval/v1beta1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "accessapproval_v1beta1", {
    enumerable: true,
    get: function() {
        return v1beta1_1.accessapproval_v1beta1;
    }
});
var v1_4 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/accesscontextmanager/v1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "accesscontextmanager_v1", {
    enumerable: true,
    get: function() {
        return v1_4.accesscontextmanager_v1;
    }
});
var v1beta_1 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/accesscontextmanager/v1beta.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "accesscontextmanager_v1beta", {
    enumerable: true,
    get: function() {
        return v1beta_1.accesscontextmanager_v1beta;
    }
});
var v1_5 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/acmedns/v1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "acmedns_v1", {
    enumerable: true,
    get: function() {
        return v1_5.acmedns_v1;
    }
});
var v1_6 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/addressvalidation/v1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "addressvalidation_v1", {
    enumerable: true,
    get: function() {
        return v1_6.addressvalidation_v1;
    }
});
var v1_2_1 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/adexchangebuyer/v1.2.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "adexchangebuyer_v1_2", {
    enumerable: true,
    get: function() {
        return v1_2_1.adexchangebuyer_v1_2;
    }
});
var v1_3_1 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/adexchangebuyer/v1.3.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "adexchangebuyer_v1_3", {
    enumerable: true,
    get: function() {
        return v1_3_1.adexchangebuyer_v1_3;
    }
});
var v1_4_1 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/adexchangebuyer/v1.4.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "adexchangebuyer_v1_4", {
    enumerable: true,
    get: function() {
        return v1_4_1.adexchangebuyer_v1_4;
    }
});
var v2beta1_1 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/adexchangebuyer2/v2beta1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "adexchangebuyer2_v2beta1", {
    enumerable: true,
    get: function() {
        return v2beta1_1.adexchangebuyer2_v2beta1;
    }
});
var v1_7 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/adexperiencereport/v1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "adexperiencereport_v1", {
    enumerable: true,
    get: function() {
        return v1_7.adexperiencereport_v1;
    }
});
var datatransfer_v1_1 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/admin/datatransfer_v1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "admin_datatransfer_v1", {
    enumerable: true,
    get: function() {
        return datatransfer_v1_1.admin_datatransfer_v1;
    }
});
var directory_v1_1 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/admin/directory_v1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "admin_directory_v1", {
    enumerable: true,
    get: function() {
        return directory_v1_1.admin_directory_v1;
    }
});
var reports_v1_1 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/admin/reports_v1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "admin_reports_v1", {
    enumerable: true,
    get: function() {
        return reports_v1_1.admin_reports_v1;
    }
});
var v1_8 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/admob/v1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "admob_v1", {
    enumerable: true,
    get: function() {
        return v1_8.admob_v1;
    }
});
var v1beta_2 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/admob/v1beta.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "admob_v1beta", {
    enumerable: true,
    get: function() {
        return v1beta_2.admob_v1beta;
    }
});
var v1_4_2 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/adsense/v1.4.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "adsense_v1_4", {
    enumerable: true,
    get: function() {
        return v1_4_2.adsense_v1_4;
    }
});
var v2_1 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/adsense/v2.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "adsense_v2", {
    enumerable: true,
    get: function() {
        return v2_1.adsense_v2;
    }
});
var v4_1_1 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/adsensehost/v4.1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "adsensehost_v4_1", {
    enumerable: true,
    get: function() {
        return v4_1_1.adsensehost_v4_1;
    }
});
var v1_9 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/adsenseplatform/v1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "adsenseplatform_v1", {
    enumerable: true,
    get: function() {
        return v1_9.adsenseplatform_v1;
    }
});
var v1alpha_1 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/adsenseplatform/v1alpha.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "adsenseplatform_v1alpha", {
    enumerable: true,
    get: function() {
        return v1alpha_1.adsenseplatform_v1alpha;
    }
});
var v1_10 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/advisorynotifications/v1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "advisorynotifications_v1", {
    enumerable: true,
    get: function() {
        return v1_10.advisorynotifications_v1;
    }
});
var v1_11 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/aiplatform/v1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "aiplatform_v1", {
    enumerable: true,
    get: function() {
        return v1_11.aiplatform_v1;
    }
});
var v1beta1_2 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/aiplatform/v1beta1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "aiplatform_v1beta1", {
    enumerable: true,
    get: function() {
        return v1beta1_2.aiplatform_v1beta1;
    }
});
var v1_12 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/airquality/v1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "airquality_v1", {
    enumerable: true,
    get: function() {
        return v1_12.airquality_v1;
    }
});
var v1beta1_3 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/alertcenter/v1beta1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "alertcenter_v1beta1", {
    enumerable: true,
    get: function() {
        return v1beta1_3.alertcenter_v1beta1;
    }
});
var v1_13 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/alloydb/v1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "alloydb_v1", {
    enumerable: true,
    get: function() {
        return v1_13.alloydb_v1;
    }
});
var v1alpha_2 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/alloydb/v1alpha.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "alloydb_v1alpha", {
    enumerable: true,
    get: function() {
        return v1alpha_2.alloydb_v1alpha;
    }
});
var v1beta_3 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/alloydb/v1beta.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "alloydb_v1beta", {
    enumerable: true,
    get: function() {
        return v1beta_3.alloydb_v1beta;
    }
});
var v3_1 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/analytics/v3.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "analytics_v3", {
    enumerable: true,
    get: function() {
        return v3_1.analytics_v3;
    }
});
var v1alpha_3 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/analyticsadmin/v1alpha.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "analyticsadmin_v1alpha", {
    enumerable: true,
    get: function() {
        return v1alpha_3.analyticsadmin_v1alpha;
    }
});
var v1beta_4 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/analyticsadmin/v1beta.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "analyticsadmin_v1beta", {
    enumerable: true,
    get: function() {
        return v1beta_4.analyticsadmin_v1beta;
    }
});
var v1alpha_4 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/analyticsdata/v1alpha.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "analyticsdata_v1alpha", {
    enumerable: true,
    get: function() {
        return v1alpha_4.analyticsdata_v1alpha;
    }
});
var v1beta_5 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/analyticsdata/v1beta.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "analyticsdata_v1beta", {
    enumerable: true,
    get: function() {
        return v1beta_5.analyticsdata_v1beta;
    }
});
var v1_14 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/analyticshub/v1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "analyticshub_v1", {
    enumerable: true,
    get: function() {
        return v1_14.analyticshub_v1;
    }
});
var v1beta1_4 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/analyticshub/v1beta1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "analyticshub_v1beta1", {
    enumerable: true,
    get: function() {
        return v1beta1_4.analyticshub_v1beta1;
    }
});
var v4_1 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/analyticsreporting/v4.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "analyticsreporting_v4", {
    enumerable: true,
    get: function() {
        return v4_1.analyticsreporting_v4;
    }
});
var v1_15 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/androiddeviceprovisioning/v1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "androiddeviceprovisioning_v1", {
    enumerable: true,
    get: function() {
        return v1_15.androiddeviceprovisioning_v1;
    }
});
var v1_16 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/androidenterprise/v1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "androidenterprise_v1", {
    enumerable: true,
    get: function() {
        return v1_16.androidenterprise_v1;
    }
});
var v1_17 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/androidmanagement/v1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "androidmanagement_v1", {
    enumerable: true,
    get: function() {
        return v1_17.androidmanagement_v1;
    }
});
var v1_1_1 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/androidpublisher/v1.1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "androidpublisher_v1_1", {
    enumerable: true,
    get: function() {
        return v1_1_1.androidpublisher_v1_1;
    }
});
var v1_18 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/androidpublisher/v1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "androidpublisher_v1", {
    enumerable: true,
    get: function() {
        return v1_18.androidpublisher_v1;
    }
});
var v2_2 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/androidpublisher/v2.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "androidpublisher_v2", {
    enumerable: true,
    get: function() {
        return v2_2.androidpublisher_v2;
    }
});
var v3_2 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/androidpublisher/v3.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "androidpublisher_v3", {
    enumerable: true,
    get: function() {
        return v3_2.androidpublisher_v3;
    }
});
var v1_19 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/apigateway/v1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "apigateway_v1", {
    enumerable: true,
    get: function() {
        return v1_19.apigateway_v1;
    }
});
var v1beta_6 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/apigateway/v1beta.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "apigateway_v1beta", {
    enumerable: true,
    get: function() {
        return v1beta_6.apigateway_v1beta;
    }
});
var v1_20 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/apigeeregistry/v1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "apigeeregistry_v1", {
    enumerable: true,
    get: function() {
        return v1_20.apigeeregistry_v1;
    }
});
var v1_21 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/apihub/v1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "apihub_v1", {
    enumerable: true,
    get: function() {
        return v1_21.apihub_v1;
    }
});
var v2_3 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/apikeys/v2.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "apikeys_v2", {
    enumerable: true,
    get: function() {
        return v2_3.apikeys_v2;
    }
});
var v1alpha_5 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/apim/v1alpha.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "apim_v1alpha", {
    enumerable: true,
    get: function() {
        return v1alpha_5.apim_v1alpha;
    }
});
var v1_22 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/appengine/v1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "appengine_v1", {
    enumerable: true,
    get: function() {
        return v1_22.appengine_v1;
    }
});
var v1alpha_6 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/appengine/v1alpha.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "appengine_v1alpha", {
    enumerable: true,
    get: function() {
        return v1alpha_6.appengine_v1alpha;
    }
});
var v1beta_7 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/appengine/v1beta.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "appengine_v1beta", {
    enumerable: true,
    get: function() {
        return v1beta_7.appengine_v1beta;
    }
});
var v1_23 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/apphub/v1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "apphub_v1", {
    enumerable: true,
    get: function() {
        return v1_23.apphub_v1;
    }
});
var v1alpha_7 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/apphub/v1alpha.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "apphub_v1alpha", {
    enumerable: true,
    get: function() {
        return v1alpha_7.apphub_v1alpha;
    }
});
var v1_24 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/appsactivity/v1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "appsactivity_v1", {
    enumerable: true,
    get: function() {
        return v1_24.appsactivity_v1;
    }
});
var v1alpha1_1 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/area120tables/v1alpha1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "area120tables_v1alpha1", {
    enumerable: true,
    get: function() {
        return v1alpha1_1.area120tables_v1alpha1;
    }
});
var v1_25 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/areainsights/v1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "areainsights_v1", {
    enumerable: true,
    get: function() {
        return v1_25.areainsights_v1;
    }
});
var v1_26 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/artifactregistry/v1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "artifactregistry_v1", {
    enumerable: true,
    get: function() {
        return v1_26.artifactregistry_v1;
    }
});
var v1beta1_5 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/artifactregistry/v1beta1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "artifactregistry_v1beta1", {
    enumerable: true,
    get: function() {
        return v1beta1_5.artifactregistry_v1beta1;
    }
});
var v1beta2_1 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/artifactregistry/v1beta2.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "artifactregistry_v1beta2", {
    enumerable: true,
    get: function() {
        return v1beta2_1.artifactregistry_v1beta2;
    }
});
var v1_27 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/assuredworkloads/v1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "assuredworkloads_v1", {
    enumerable: true,
    get: function() {
        return v1_27.assuredworkloads_v1;
    }
});
var v1beta1_6 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/assuredworkloads/v1beta1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "assuredworkloads_v1beta1", {
    enumerable: true,
    get: function() {
        return v1beta1_6.assuredworkloads_v1beta1;
    }
});
var v1_28 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/authorizedbuyersmarketplace/v1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "authorizedbuyersmarketplace_v1", {
    enumerable: true,
    get: function() {
        return v1_28.authorizedbuyersmarketplace_v1;
    }
});
var v1alpha_8 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/authorizedbuyersmarketplace/v1alpha.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "authorizedbuyersmarketplace_v1alpha", {
    enumerable: true,
    get: function() {
        return v1alpha_8.authorizedbuyersmarketplace_v1alpha;
    }
});
var v1beta_8 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/authorizedbuyersmarketplace/v1beta.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "authorizedbuyersmarketplace_v1beta", {
    enumerable: true,
    get: function() {
        return v1beta_8.authorizedbuyersmarketplace_v1beta;
    }
});
var v1_29 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/backupdr/v1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "backupdr_v1", {
    enumerable: true,
    get: function() {
        return v1_29.backupdr_v1;
    }
});
var v1_30 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/baremetalsolution/v1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "baremetalsolution_v1", {
    enumerable: true,
    get: function() {
        return v1_30.baremetalsolution_v1;
    }
});
var v1alpha1_2 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/baremetalsolution/v1alpha1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "baremetalsolution_v1alpha1", {
    enumerable: true,
    get: function() {
        return v1alpha1_2.baremetalsolution_v1alpha1;
    }
});
var v2_4 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/baremetalsolution/v2.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "baremetalsolution_v2", {
    enumerable: true,
    get: function() {
        return v2_4.baremetalsolution_v2;
    }
});
var v1_31 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/batch/v1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "batch_v1", {
    enumerable: true,
    get: function() {
        return v1_31.batch_v1;
    }
});
var v1_32 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/beyondcorp/v1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "beyondcorp_v1", {
    enumerable: true,
    get: function() {
        return v1_32.beyondcorp_v1;
    }
});
var v1alpha_9 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/beyondcorp/v1alpha.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "beyondcorp_v1alpha", {
    enumerable: true,
    get: function() {
        return v1alpha_9.beyondcorp_v1alpha;
    }
});
var v1_33 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/biglake/v1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "biglake_v1", {
    enumerable: true,
    get: function() {
        return v1_33.biglake_v1;
    }
});
var v2_5 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/bigquery/v2.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "bigquery_v2", {
    enumerable: true,
    get: function() {
        return v2_5.bigquery_v2;
    }
});
var v1_34 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/bigqueryconnection/v1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "bigqueryconnection_v1", {
    enumerable: true,
    get: function() {
        return v1_34.bigqueryconnection_v1;
    }
});
var v1beta1_7 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/bigqueryconnection/v1beta1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "bigqueryconnection_v1beta1", {
    enumerable: true,
    get: function() {
        return v1beta1_7.bigqueryconnection_v1beta1;
    }
});
var v1_35 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/bigquerydatapolicy/v1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "bigquerydatapolicy_v1", {
    enumerable: true,
    get: function() {
        return v1_35.bigquerydatapolicy_v1;
    }
});
var v1_36 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/bigquerydatatransfer/v1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "bigquerydatatransfer_v1", {
    enumerable: true,
    get: function() {
        return v1_36.bigquerydatatransfer_v1;
    }
});
var v1_37 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/bigqueryreservation/v1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "bigqueryreservation_v1", {
    enumerable: true,
    get: function() {
        return v1_37.bigqueryreservation_v1;
    }
});
var v1alpha2_1 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/bigqueryreservation/v1alpha2.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "bigqueryreservation_v1alpha2", {
    enumerable: true,
    get: function() {
        return v1alpha2_1.bigqueryreservation_v1alpha2;
    }
});
var v1beta1_8 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/bigqueryreservation/v1beta1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "bigqueryreservation_v1beta1", {
    enumerable: true,
    get: function() {
        return v1beta1_8.bigqueryreservation_v1beta1;
    }
});
var v1_38 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/bigtableadmin/v1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "bigtableadmin_v1", {
    enumerable: true,
    get: function() {
        return v1_38.bigtableadmin_v1;
    }
});
var v2_6 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/bigtableadmin/v2.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "bigtableadmin_v2", {
    enumerable: true,
    get: function() {
        return v2_6.bigtableadmin_v2;
    }
});
var v1_39 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/billingbudgets/v1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "billingbudgets_v1", {
    enumerable: true,
    get: function() {
        return v1_39.billingbudgets_v1;
    }
});
var v1beta1_9 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/billingbudgets/v1beta1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "billingbudgets_v1beta1", {
    enumerable: true,
    get: function() {
        return v1beta1_9.billingbudgets_v1beta1;
    }
});
var v1_40 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/binaryauthorization/v1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "binaryauthorization_v1", {
    enumerable: true,
    get: function() {
        return v1_40.binaryauthorization_v1;
    }
});
var v1beta1_10 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/binaryauthorization/v1beta1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "binaryauthorization_v1beta1", {
    enumerable: true,
    get: function() {
        return v1beta1_10.binaryauthorization_v1beta1;
    }
});
var v1_41 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/blockchainnodeengine/v1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "blockchainnodeengine_v1", {
    enumerable: true,
    get: function() {
        return v1_41.blockchainnodeengine_v1;
    }
});
var v2_7 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/blogger/v2.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "blogger_v2", {
    enumerable: true,
    get: function() {
        return v2_7.blogger_v2;
    }
});
var v3_3 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/blogger/v3.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "blogger_v3", {
    enumerable: true,
    get: function() {
        return v3_3.blogger_v3;
    }
});
var v1_42 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/books/v1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "books_v1", {
    enumerable: true,
    get: function() {
        return v1_42.books_v1;
    }
});
var v1_43 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/businessprofileperformance/v1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "businessprofileperformance_v1", {
    enumerable: true,
    get: function() {
        return v1_43.businessprofileperformance_v1;
    }
});
var v3_4 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/calendar/v3.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "calendar_v3", {
    enumerable: true,
    get: function() {
        return v3_4.calendar_v3;
    }
});
var v1_44 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/certificatemanager/v1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "certificatemanager_v1", {
    enumerable: true,
    get: function() {
        return v1_44.certificatemanager_v1;
    }
});
var v1_45 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/chat/v1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "chat_v1", {
    enumerable: true,
    get: function() {
        return v1_45.chat_v1;
    }
});
var v1alpha_10 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/checks/v1alpha.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "checks_v1alpha", {
    enumerable: true,
    get: function() {
        return v1alpha_10.checks_v1alpha;
    }
});
var v1_46 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/chromemanagement/v1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "chromemanagement_v1", {
    enumerable: true,
    get: function() {
        return v1_46.chromemanagement_v1;
    }
});
var v1_47 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/chromepolicy/v1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "chromepolicy_v1", {
    enumerable: true,
    get: function() {
        return v1_47.chromepolicy_v1;
    }
});
var v1_48 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/chromeuxreport/v1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "chromeuxreport_v1", {
    enumerable: true,
    get: function() {
        return v1_48.chromeuxreport_v1;
    }
});
var v2_8 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/civicinfo/v2.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "civicinfo_v2", {
    enumerable: true,
    get: function() {
        return v2_8.civicinfo_v2;
    }
});
var v1_49 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/classroom/v1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "classroom_v1", {
    enumerable: true,
    get: function() {
        return v1_49.classroom_v1;
    }
});
var v1_50 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/cloudasset/v1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "cloudasset_v1", {
    enumerable: true,
    get: function() {
        return v1_50.cloudasset_v1;
    }
});
var v1beta1_11 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/cloudasset/v1beta1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "cloudasset_v1beta1", {
    enumerable: true,
    get: function() {
        return v1beta1_11.cloudasset_v1beta1;
    }
});
var v1p1beta1_1 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/cloudasset/v1p1beta1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "cloudasset_v1p1beta1", {
    enumerable: true,
    get: function() {
        return v1p1beta1_1.cloudasset_v1p1beta1;
    }
});
var v1p4beta1_1 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/cloudasset/v1p4beta1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "cloudasset_v1p4beta1", {
    enumerable: true,
    get: function() {
        return v1p4beta1_1.cloudasset_v1p4beta1;
    }
});
var v1p5beta1_1 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/cloudasset/v1p5beta1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "cloudasset_v1p5beta1", {
    enumerable: true,
    get: function() {
        return v1p5beta1_1.cloudasset_v1p5beta1;
    }
});
var v1p7beta1_1 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/cloudasset/v1p7beta1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "cloudasset_v1p7beta1", {
    enumerable: true,
    get: function() {
        return v1p7beta1_1.cloudasset_v1p7beta1;
    }
});
var v1_51 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/cloudbilling/v1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "cloudbilling_v1", {
    enumerable: true,
    get: function() {
        return v1_51.cloudbilling_v1;
    }
});
var v1beta_9 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/cloudbilling/v1beta.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "cloudbilling_v1beta", {
    enumerable: true,
    get: function() {
        return v1beta_9.cloudbilling_v1beta;
    }
});
var v1_52 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/cloudbuild/v1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "cloudbuild_v1", {
    enumerable: true,
    get: function() {
        return v1_52.cloudbuild_v1;
    }
});
var v1alpha1_3 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/cloudbuild/v1alpha1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "cloudbuild_v1alpha1", {
    enumerable: true,
    get: function() {
        return v1alpha1_3.cloudbuild_v1alpha1;
    }
});
var v1alpha2_2 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/cloudbuild/v1alpha2.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "cloudbuild_v1alpha2", {
    enumerable: true,
    get: function() {
        return v1alpha2_2.cloudbuild_v1alpha2;
    }
});
var v1beta1_12 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/cloudbuild/v1beta1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "cloudbuild_v1beta1", {
    enumerable: true,
    get: function() {
        return v1beta1_12.cloudbuild_v1beta1;
    }
});
var v2_9 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/cloudbuild/v2.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "cloudbuild_v2", {
    enumerable: true,
    get: function() {
        return v2_9.cloudbuild_v2;
    }
});
var v1_53 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/cloudchannel/v1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "cloudchannel_v1", {
    enumerable: true,
    get: function() {
        return v1_53.cloudchannel_v1;
    }
});
var v1_54 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/cloudcontrolspartner/v1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "cloudcontrolspartner_v1", {
    enumerable: true,
    get: function() {
        return v1_54.cloudcontrolspartner_v1;
    }
});
var v1beta_10 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/cloudcontrolspartner/v1beta.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "cloudcontrolspartner_v1beta", {
    enumerable: true,
    get: function() {
        return v1beta_10.cloudcontrolspartner_v1beta;
    }
});
var v2_10 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/clouddebugger/v2.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "clouddebugger_v2", {
    enumerable: true,
    get: function() {
        return v2_10.clouddebugger_v2;
    }
});
var v1_55 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/clouddeploy/v1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "clouddeploy_v1", {
    enumerable: true,
    get: function() {
        return v1_55.clouddeploy_v1;
    }
});
var v1beta1_13 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/clouderrorreporting/v1beta1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "clouderrorreporting_v1beta1", {
    enumerable: true,
    get: function() {
        return v1beta1_13.clouderrorreporting_v1beta1;
    }
});
var v1_56 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/cloudfunctions/v1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "cloudfunctions_v1", {
    enumerable: true,
    get: function() {
        return v1_56.cloudfunctions_v1;
    }
});
var v1beta2_2 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/cloudfunctions/v1beta2.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "cloudfunctions_v1beta2", {
    enumerable: true,
    get: function() {
        return v1beta2_2.cloudfunctions_v1beta2;
    }
});
var v2_11 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/cloudfunctions/v2.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "cloudfunctions_v2", {
    enumerable: true,
    get: function() {
        return v2_11.cloudfunctions_v2;
    }
});
var v2alpha_1 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/cloudfunctions/v2alpha.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "cloudfunctions_v2alpha", {
    enumerable: true,
    get: function() {
        return v2alpha_1.cloudfunctions_v2alpha;
    }
});
var v2beta_1 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/cloudfunctions/v2beta.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "cloudfunctions_v2beta", {
    enumerable: true,
    get: function() {
        return v2beta_1.cloudfunctions_v2beta;
    }
});
var v1_57 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/cloudidentity/v1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "cloudidentity_v1", {
    enumerable: true,
    get: function() {
        return v1_57.cloudidentity_v1;
    }
});
var v1beta1_14 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/cloudidentity/v1beta1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "cloudidentity_v1beta1", {
    enumerable: true,
    get: function() {
        return v1beta1_14.cloudidentity_v1beta1;
    }
});
var v1_58 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/cloudiot/v1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "cloudiot_v1", {
    enumerable: true,
    get: function() {
        return v1_58.cloudiot_v1;
    }
});
var v1_59 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/cloudkms/v1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "cloudkms_v1", {
    enumerable: true,
    get: function() {
        return v1_59.cloudkms_v1;
    }
});
var v1alpha_11 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/cloudlocationfinder/v1alpha.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "cloudlocationfinder_v1alpha", {
    enumerable: true,
    get: function() {
        return v1alpha_11.cloudlocationfinder_v1alpha;
    }
});
var v2_12 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/cloudprofiler/v2.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "cloudprofiler_v2", {
    enumerable: true,
    get: function() {
        return v2_12.cloudprofiler_v2;
    }
});
var v1_60 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/cloudresourcemanager/v1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "cloudresourcemanager_v1", {
    enumerable: true,
    get: function() {
        return v1_60.cloudresourcemanager_v1;
    }
});
var v1beta1_15 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/cloudresourcemanager/v1beta1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "cloudresourcemanager_v1beta1", {
    enumerable: true,
    get: function() {
        return v1beta1_15.cloudresourcemanager_v1beta1;
    }
});
var v2_13 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/cloudresourcemanager/v2.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "cloudresourcemanager_v2", {
    enumerable: true,
    get: function() {
        return v2_13.cloudresourcemanager_v2;
    }
});
var v2beta1_2 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/cloudresourcemanager/v2beta1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "cloudresourcemanager_v2beta1", {
    enumerable: true,
    get: function() {
        return v2beta1_2.cloudresourcemanager_v2beta1;
    }
});
var v3_5 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/cloudresourcemanager/v3.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "cloudresourcemanager_v3", {
    enumerable: true,
    get: function() {
        return v3_5.cloudresourcemanager_v3;
    }
});
var v1_61 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/cloudscheduler/v1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "cloudscheduler_v1", {
    enumerable: true,
    get: function() {
        return v1_61.cloudscheduler_v1;
    }
});
var v1beta1_16 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/cloudscheduler/v1beta1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "cloudscheduler_v1beta1", {
    enumerable: true,
    get: function() {
        return v1beta1_16.cloudscheduler_v1beta1;
    }
});
var v1_62 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/cloudsearch/v1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "cloudsearch_v1", {
    enumerable: true,
    get: function() {
        return v1_62.cloudsearch_v1;
    }
});
var v1_63 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/cloudshell/v1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "cloudshell_v1", {
    enumerable: true,
    get: function() {
        return v1_63.cloudshell_v1;
    }
});
var v1alpha1_4 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/cloudshell/v1alpha1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "cloudshell_v1alpha1", {
    enumerable: true,
    get: function() {
        return v1alpha1_4.cloudshell_v1alpha1;
    }
});
var v2_14 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/cloudsupport/v2.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "cloudsupport_v2", {
    enumerable: true,
    get: function() {
        return v2_14.cloudsupport_v2;
    }
});
var v2beta_2 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/cloudsupport/v2beta.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "cloudsupport_v2beta", {
    enumerable: true,
    get: function() {
        return v2beta_2.cloudsupport_v2beta;
    }
});
var v2_15 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/cloudtasks/v2.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "cloudtasks_v2", {
    enumerable: true,
    get: function() {
        return v2_15.cloudtasks_v2;
    }
});
var v2beta2_1 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/cloudtasks/v2beta2.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "cloudtasks_v2beta2", {
    enumerable: true,
    get: function() {
        return v2beta2_1.cloudtasks_v2beta2;
    }
});
var v2beta3_1 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/cloudtasks/v2beta3.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "cloudtasks_v2beta3", {
    enumerable: true,
    get: function() {
        return v2beta3_1.cloudtasks_v2beta3;
    }
});
var v1_64 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/cloudtrace/v1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "cloudtrace_v1", {
    enumerable: true,
    get: function() {
        return v1_64.cloudtrace_v1;
    }
});
var v2_16 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/cloudtrace/v2.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "cloudtrace_v2", {
    enumerable: true,
    get: function() {
        return v2_16.cloudtrace_v2;
    }
});
var v2beta1_3 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/cloudtrace/v2beta1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "cloudtrace_v2beta1", {
    enumerable: true,
    get: function() {
        return v2beta1_3.cloudtrace_v2beta1;
    }
});
var v1_65 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/composer/v1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "composer_v1", {
    enumerable: true,
    get: function() {
        return v1_65.composer_v1;
    }
});
var v1beta1_17 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/composer/v1beta1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "composer_v1beta1", {
    enumerable: true,
    get: function() {
        return v1beta1_17.composer_v1beta1;
    }
});
var alpha_1 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/compute/alpha.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "compute_alpha", {
    enumerable: true,
    get: function() {
        return alpha_1.compute_alpha;
    }
});
var beta_1 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/compute/beta.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "compute_beta", {
    enumerable: true,
    get: function() {
        return beta_1.compute_beta;
    }
});
var v1_66 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/compute/v1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "compute_v1", {
    enumerable: true,
    get: function() {
        return v1_66.compute_v1;
    }
});
var v1_67 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/config/v1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "config_v1", {
    enumerable: true,
    get: function() {
        return v1_67.config_v1;
    }
});
var v1_68 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/connectors/v1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "connectors_v1", {
    enumerable: true,
    get: function() {
        return v1_68.connectors_v1;
    }
});
var v2_17 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/connectors/v2.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "connectors_v2", {
    enumerable: true,
    get: function() {
        return v2_17.connectors_v2;
    }
});
var v1alpha1_5 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/contactcenteraiplatform/v1alpha1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "contactcenteraiplatform_v1alpha1", {
    enumerable: true,
    get: function() {
        return v1alpha1_5.contactcenteraiplatform_v1alpha1;
    }
});
var v1_69 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/contactcenterinsights/v1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "contactcenterinsights_v1", {
    enumerable: true,
    get: function() {
        return v1_69.contactcenterinsights_v1;
    }
});
var v1_70 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/container/v1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "container_v1", {
    enumerable: true,
    get: function() {
        return v1_70.container_v1;
    }
});
var v1beta1_18 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/container/v1beta1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "container_v1beta1", {
    enumerable: true,
    get: function() {
        return v1beta1_18.container_v1beta1;
    }
});
var v1_71 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/containeranalysis/v1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "containeranalysis_v1", {
    enumerable: true,
    get: function() {
        return v1_71.containeranalysis_v1;
    }
});
var v1alpha1_6 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/containeranalysis/v1alpha1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "containeranalysis_v1alpha1", {
    enumerable: true,
    get: function() {
        return v1alpha1_6.containeranalysis_v1alpha1;
    }
});
var v1beta1_19 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/containeranalysis/v1beta1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "containeranalysis_v1beta1", {
    enumerable: true,
    get: function() {
        return v1beta1_19.containeranalysis_v1beta1;
    }
});
var v2_1_1 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/content/v2.1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "content_v2_1", {
    enumerable: true,
    get: function() {
        return v2_1_1.content_v2_1;
    }
});
var v2_18 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/content/v2.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "content_v2", {
    enumerable: true,
    get: function() {
        return v2_18.content_v2;
    }
});
var v1_72 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/contentwarehouse/v1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "contentwarehouse_v1", {
    enumerable: true,
    get: function() {
        return v1_72.contentwarehouse_v1;
    }
});
var v1_73 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/css/v1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "css_v1", {
    enumerable: true,
    get: function() {
        return v1_73.css_v1;
    }
});
var v1_74 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/customsearch/v1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "customsearch_v1", {
    enumerable: true,
    get: function() {
        return v1_74.customsearch_v1;
    }
});
var v1_75 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/datacatalog/v1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "datacatalog_v1", {
    enumerable: true,
    get: function() {
        return v1_75.datacatalog_v1;
    }
});
var v1beta1_20 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/datacatalog/v1beta1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "datacatalog_v1beta1", {
    enumerable: true,
    get: function() {
        return v1beta1_20.datacatalog_v1beta1;
    }
});
var v1b3_1 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/dataflow/v1b3.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "dataflow_v1b3", {
    enumerable: true,
    get: function() {
        return v1b3_1.dataflow_v1b3;
    }
});
var v1beta1_21 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/dataform/v1beta1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "dataform_v1beta1", {
    enumerable: true,
    get: function() {
        return v1beta1_21.dataform_v1beta1;
    }
});
var v1_76 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/datafusion/v1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "datafusion_v1", {
    enumerable: true,
    get: function() {
        return v1_76.datafusion_v1;
    }
});
var v1beta1_22 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/datafusion/v1beta1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "datafusion_v1beta1", {
    enumerable: true,
    get: function() {
        return v1beta1_22.datafusion_v1beta1;
    }
});
var v1beta1_23 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/datalabeling/v1beta1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "datalabeling_v1beta1", {
    enumerable: true,
    get: function() {
        return v1beta1_23.datalabeling_v1beta1;
    }
});
var v1_77 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/datalineage/v1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "datalineage_v1", {
    enumerable: true,
    get: function() {
        return v1_77.datalineage_v1;
    }
});
var v1_78 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/datamigration/v1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "datamigration_v1", {
    enumerable: true,
    get: function() {
        return v1_78.datamigration_v1;
    }
});
var v1beta1_24 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/datamigration/v1beta1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "datamigration_v1beta1", {
    enumerable: true,
    get: function() {
        return v1beta1_24.datamigration_v1beta1;
    }
});
var v1_79 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/datapipelines/v1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "datapipelines_v1", {
    enumerable: true,
    get: function() {
        return v1_79.datapipelines_v1;
    }
});
var v1_80 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/dataplex/v1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "dataplex_v1", {
    enumerable: true,
    get: function() {
        return v1_80.dataplex_v1;
    }
});
var v1_81 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/dataportability/v1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "dataportability_v1", {
    enumerable: true,
    get: function() {
        return v1_81.dataportability_v1;
    }
});
var v1beta_11 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/dataportability/v1beta.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "dataportability_v1beta", {
    enumerable: true,
    get: function() {
        return v1beta_11.dataportability_v1beta;
    }
});
var v1_82 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/dataproc/v1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "dataproc_v1", {
    enumerable: true,
    get: function() {
        return v1_82.dataproc_v1;
    }
});
var v1beta2_3 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/dataproc/v1beta2.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "dataproc_v1beta2", {
    enumerable: true,
    get: function() {
        return v1beta2_3.dataproc_v1beta2;
    }
});
var v1_83 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/datastore/v1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "datastore_v1", {
    enumerable: true,
    get: function() {
        return v1_83.datastore_v1;
    }
});
var v1beta1_25 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/datastore/v1beta1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "datastore_v1beta1", {
    enumerable: true,
    get: function() {
        return v1beta1_25.datastore_v1beta1;
    }
});
var v1beta3_1 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/datastore/v1beta3.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "datastore_v1beta3", {
    enumerable: true,
    get: function() {
        return v1beta3_1.datastore_v1beta3;
    }
});
var v1_84 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/datastream/v1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "datastream_v1", {
    enumerable: true,
    get: function() {
        return v1_84.datastream_v1;
    }
});
var v1alpha1_7 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/datastream/v1alpha1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "datastream_v1alpha1", {
    enumerable: true,
    get: function() {
        return v1alpha1_7.datastream_v1alpha1;
    }
});
var alpha_2 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/deploymentmanager/alpha.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "deploymentmanager_alpha", {
    enumerable: true,
    get: function() {
        return alpha_2.deploymentmanager_alpha;
    }
});
var v2_19 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/deploymentmanager/v2.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "deploymentmanager_v2", {
    enumerable: true,
    get: function() {
        return v2_19.deploymentmanager_v2;
    }
});
var v2beta_3 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/deploymentmanager/v2beta.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "deploymentmanager_v2beta", {
    enumerable: true,
    get: function() {
        return v2beta_3.deploymentmanager_v2beta;
    }
});
var v1_85 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/developerconnect/v1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "developerconnect_v1", {
    enumerable: true,
    get: function() {
        return v1_85.developerconnect_v1;
    }
});
var v3_3_1 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/dfareporting/v3.3.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "dfareporting_v3_3", {
    enumerable: true,
    get: function() {
        return v3_3_1.dfareporting_v3_3;
    }
});
var v3_4_1 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/dfareporting/v3.4.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "dfareporting_v3_4", {
    enumerable: true,
    get: function() {
        return v3_4_1.dfareporting_v3_4;
    }
});
var v3_5_1 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/dfareporting/v3.5.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "dfareporting_v3_5", {
    enumerable: true,
    get: function() {
        return v3_5_1.dfareporting_v3_5;
    }
});
var v4_2 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/dfareporting/v4.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "dfareporting_v4", {
    enumerable: true,
    get: function() {
        return v4_2.dfareporting_v4;
    }
});
var v2_20 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/dialogflow/v2.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "dialogflow_v2", {
    enumerable: true,
    get: function() {
        return v2_20.dialogflow_v2;
    }
});
var v2beta1_4 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/dialogflow/v2beta1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "dialogflow_v2beta1", {
    enumerable: true,
    get: function() {
        return v2beta1_4.dialogflow_v2beta1;
    }
});
var v3_6 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/dialogflow/v3.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "dialogflow_v3", {
    enumerable: true,
    get: function() {
        return v3_6.dialogflow_v3;
    }
});
var v3beta1_1 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/dialogflow/v3beta1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "dialogflow_v3beta1", {
    enumerable: true,
    get: function() {
        return v3beta1_1.dialogflow_v3beta1;
    }
});
var v1_86 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/digitalassetlinks/v1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "digitalassetlinks_v1", {
    enumerable: true,
    get: function() {
        return v1_86.digitalassetlinks_v1;
    }
});
var v1_87 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/discovery/v1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "discovery_v1", {
    enumerable: true,
    get: function() {
        return v1_87.discovery_v1;
    }
});
var v1_88 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/discoveryengine/v1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "discoveryengine_v1", {
    enumerable: true,
    get: function() {
        return v1_88.discoveryengine_v1;
    }
});
var v1alpha_12 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/discoveryengine/v1alpha.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "discoveryengine_v1alpha", {
    enumerable: true,
    get: function() {
        return v1alpha_12.discoveryengine_v1alpha;
    }
});
var v1beta_12 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/discoveryengine/v1beta.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "discoveryengine_v1beta", {
    enumerable: true,
    get: function() {
        return v1beta_12.discoveryengine_v1beta;
    }
});
var v1_89 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/displayvideo/v1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "displayvideo_v1", {
    enumerable: true,
    get: function() {
        return v1_89.displayvideo_v1;
    }
});
var v1beta_13 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/displayvideo/v1beta.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "displayvideo_v1beta", {
    enumerable: true,
    get: function() {
        return v1beta_13.displayvideo_v1beta;
    }
});
var v1beta2_4 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/displayvideo/v1beta2.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "displayvideo_v1beta2", {
    enumerable: true,
    get: function() {
        return v1beta2_4.displayvideo_v1beta2;
    }
});
var v1dev_1 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/displayvideo/v1dev.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "displayvideo_v1dev", {
    enumerable: true,
    get: function() {
        return v1dev_1.displayvideo_v1dev;
    }
});
var v2_21 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/displayvideo/v2.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "displayvideo_v2", {
    enumerable: true,
    get: function() {
        return v2_21.displayvideo_v2;
    }
});
var v3_7 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/displayvideo/v3.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "displayvideo_v3", {
    enumerable: true,
    get: function() {
        return v3_7.displayvideo_v3;
    }
});
var v4_3 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/displayvideo/v4.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "displayvideo_v4", {
    enumerable: true,
    get: function() {
        return v4_3.displayvideo_v4;
    }
});
var v2_22 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/dlp/v2.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "dlp_v2", {
    enumerable: true,
    get: function() {
        return v2_22.dlp_v2;
    }
});
var v1_90 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/dns/v1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "dns_v1", {
    enumerable: true,
    get: function() {
        return v1_90.dns_v1;
    }
});
var v1beta2_5 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/dns/v1beta2.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "dns_v1beta2", {
    enumerable: true,
    get: function() {
        return v1beta2_5.dns_v1beta2;
    }
});
var v2_23 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/dns/v2.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "dns_v2", {
    enumerable: true,
    get: function() {
        return v2_23.dns_v2;
    }
});
var v2beta1_5 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/dns/v2beta1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "dns_v2beta1", {
    enumerable: true,
    get: function() {
        return v2beta1_5.dns_v2beta1;
    }
});
var v1_91 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/docs/v1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "docs_v1", {
    enumerable: true,
    get: function() {
        return v1_91.docs_v1;
    }
});
var v1_92 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/documentai/v1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "documentai_v1", {
    enumerable: true,
    get: function() {
        return v1_92.documentai_v1;
    }
});
var v1beta2_6 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/documentai/v1beta2.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "documentai_v1beta2", {
    enumerable: true,
    get: function() {
        return v1beta2_6.documentai_v1beta2;
    }
});
var v1beta3_2 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/documentai/v1beta3.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "documentai_v1beta3", {
    enumerable: true,
    get: function() {
        return v1beta3_2.documentai_v1beta3;
    }
});
var v1_93 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/domains/v1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "domains_v1", {
    enumerable: true,
    get: function() {
        return v1_93.domains_v1;
    }
});
var v1alpha2_3 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/domains/v1alpha2.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "domains_v1alpha2", {
    enumerable: true,
    get: function() {
        return v1alpha2_3.domains_v1alpha2;
    }
});
var v1beta1_26 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/domains/v1beta1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "domains_v1beta1", {
    enumerable: true,
    get: function() {
        return v1beta1_26.domains_v1beta1;
    }
});
var v1_94 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/domainsrdap/v1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "domainsrdap_v1", {
    enumerable: true,
    get: function() {
        return v1_94.domainsrdap_v1;
    }
});
var v1_1_2 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/doubleclickbidmanager/v1.1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "doubleclickbidmanager_v1_1", {
    enumerable: true,
    get: function() {
        return v1_1_2.doubleclickbidmanager_v1_1;
    }
});
var v1_95 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/doubleclickbidmanager/v1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "doubleclickbidmanager_v1", {
    enumerable: true,
    get: function() {
        return v1_95.doubleclickbidmanager_v1;
    }
});
var v2_24 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/doubleclickbidmanager/v2.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "doubleclickbidmanager_v2", {
    enumerable: true,
    get: function() {
        return v2_24.doubleclickbidmanager_v2;
    }
});
var v2_25 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/doubleclicksearch/v2.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "doubleclicksearch_v2", {
    enumerable: true,
    get: function() {
        return v2_25.doubleclicksearch_v2;
    }
});
var v2_26 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/drive/v2.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "drive_v2", {
    enumerable: true,
    get: function() {
        return v2_26.drive_v2;
    }
});
var v3_8 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/drive/v3.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "drive_v3", {
    enumerable: true,
    get: function() {
        return v3_8.drive_v3;
    }
});
var v2_27 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/driveactivity/v2.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "driveactivity_v2", {
    enumerable: true,
    get: function() {
        return v2_27.driveactivity_v2;
    }
});
var v2_28 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/drivelabels/v2.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "drivelabels_v2", {
    enumerable: true,
    get: function() {
        return v2_28.drivelabels_v2;
    }
});
var v2beta_4 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/drivelabels/v2beta.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "drivelabels_v2beta", {
    enumerable: true,
    get: function() {
        return v2beta_4.drivelabels_v2beta;
    }
});
var v1_96 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/essentialcontacts/v1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "essentialcontacts_v1", {
    enumerable: true,
    get: function() {
        return v1_96.essentialcontacts_v1;
    }
});
var v1_97 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/eventarc/v1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "eventarc_v1", {
    enumerable: true,
    get: function() {
        return v1_97.eventarc_v1;
    }
});
var v1beta1_27 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/eventarc/v1beta1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "eventarc_v1beta1", {
    enumerable: true,
    get: function() {
        return v1beta1_27.eventarc_v1beta1;
    }
});
var v1alpha1_8 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/factchecktools/v1alpha1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "factchecktools_v1alpha1", {
    enumerable: true,
    get: function() {
        return v1alpha1_8.factchecktools_v1alpha1;
    }
});
var v1_98 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/fcm/v1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "fcm_v1", {
    enumerable: true,
    get: function() {
        return v1_98.fcm_v1;
    }
});
var v1beta1_28 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/fcmdata/v1beta1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "fcmdata_v1beta1", {
    enumerable: true,
    get: function() {
        return v1beta1_28.fcmdata_v1beta1;
    }
});
var v1_99 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/file/v1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "file_v1", {
    enumerable: true,
    get: function() {
        return v1_99.file_v1;
    }
});
var v1beta1_29 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/file/v1beta1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "file_v1beta1", {
    enumerable: true,
    get: function() {
        return v1beta1_29.file_v1beta1;
    }
});
var v1beta1_30 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/firebase/v1beta1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "firebase_v1beta1", {
    enumerable: true,
    get: function() {
        return v1beta1_30.firebase_v1beta1;
    }
});
var v1_100 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/firebaseappcheck/v1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "firebaseappcheck_v1", {
    enumerable: true,
    get: function() {
        return v1_100.firebaseappcheck_v1;
    }
});
var v1beta_14 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/firebaseappcheck/v1beta.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "firebaseappcheck_v1beta", {
    enumerable: true,
    get: function() {
        return v1beta_14.firebaseappcheck_v1beta;
    }
});
var v1_101 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/firebaseappdistribution/v1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "firebaseappdistribution_v1", {
    enumerable: true,
    get: function() {
        return v1_101.firebaseappdistribution_v1;
    }
});
var v1alpha_13 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/firebaseappdistribution/v1alpha.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "firebaseappdistribution_v1alpha", {
    enumerable: true,
    get: function() {
        return v1alpha_13.firebaseappdistribution_v1alpha;
    }
});
var v1_102 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/firebaseapphosting/v1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "firebaseapphosting_v1", {
    enumerable: true,
    get: function() {
        return v1_102.firebaseapphosting_v1;
    }
});
var v1beta_15 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/firebaseapphosting/v1beta.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "firebaseapphosting_v1beta", {
    enumerable: true,
    get: function() {
        return v1beta_15.firebaseapphosting_v1beta;
    }
});
var v1beta_16 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/firebasedatabase/v1beta.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "firebasedatabase_v1beta", {
    enumerable: true,
    get: function() {
        return v1beta_16.firebasedatabase_v1beta;
    }
});
var v1_103 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/firebasedataconnect/v1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "firebasedataconnect_v1", {
    enumerable: true,
    get: function() {
        return v1_103.firebasedataconnect_v1;
    }
});
var v1beta_17 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/firebasedataconnect/v1beta.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "firebasedataconnect_v1beta", {
    enumerable: true,
    get: function() {
        return v1beta_17.firebasedataconnect_v1beta;
    }
});
var v1_104 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/firebasedynamiclinks/v1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "firebasedynamiclinks_v1", {
    enumerable: true,
    get: function() {
        return v1_104.firebasedynamiclinks_v1;
    }
});
var v1_105 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/firebasehosting/v1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "firebasehosting_v1", {
    enumerable: true,
    get: function() {
        return v1_105.firebasehosting_v1;
    }
});
var v1beta1_31 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/firebasehosting/v1beta1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "firebasehosting_v1beta1", {
    enumerable: true,
    get: function() {
        return v1beta1_31.firebasehosting_v1beta1;
    }
});
var v1_106 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/firebaseml/v1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "firebaseml_v1", {
    enumerable: true,
    get: function() {
        return v1_106.firebaseml_v1;
    }
});
var v1beta2_7 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/firebaseml/v1beta2.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "firebaseml_v1beta2", {
    enumerable: true,
    get: function() {
        return v1beta2_7.firebaseml_v1beta2;
    }
});
var v2beta_5 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/firebaseml/v2beta.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "firebaseml_v2beta", {
    enumerable: true,
    get: function() {
        return v2beta_5.firebaseml_v2beta;
    }
});
var v1_107 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/firebaserules/v1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "firebaserules_v1", {
    enumerable: true,
    get: function() {
        return v1_107.firebaserules_v1;
    }
});
var v1beta_18 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/firebasestorage/v1beta.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "firebasestorage_v1beta", {
    enumerable: true,
    get: function() {
        return v1beta_18.firebasestorage_v1beta;
    }
});
var v1_108 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/firestore/v1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "firestore_v1", {
    enumerable: true,
    get: function() {
        return v1_108.firestore_v1;
    }
});
var v1beta1_32 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/firestore/v1beta1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "firestore_v1beta1", {
    enumerable: true,
    get: function() {
        return v1beta1_32.firestore_v1beta1;
    }
});
var v1beta2_8 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/firestore/v1beta2.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "firestore_v1beta2", {
    enumerable: true,
    get: function() {
        return v1beta2_8.firestore_v1beta2;
    }
});
var v1_109 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/fitness/v1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "fitness_v1", {
    enumerable: true,
    get: function() {
        return v1_109.fitness_v1;
    }
});
var v1_110 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/forms/v1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "forms_v1", {
    enumerable: true,
    get: function() {
        return v1_110.forms_v1;
    }
});
var v1_111 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/games/v1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "games_v1", {
    enumerable: true,
    get: function() {
        return v1_111.games_v1;
    }
});
var v1configuration_1 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/gamesConfiguration/v1configuration.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "gamesConfiguration_v1configuration", {
    enumerable: true,
    get: function() {
        return v1configuration_1.gamesConfiguration_v1configuration;
    }
});
var v1management_1 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/gamesManagement/v1management.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "gamesManagement_v1management", {
    enumerable: true,
    get: function() {
        return v1management_1.gamesManagement_v1management;
    }
});
var v1_112 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/gameservices/v1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "gameservices_v1", {
    enumerable: true,
    get: function() {
        return v1_112.gameservices_v1;
    }
});
var v1beta_19 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/gameservices/v1beta.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "gameservices_v1beta", {
    enumerable: true,
    get: function() {
        return v1beta_19.gameservices_v1beta;
    }
});
var v1_113 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/genomics/v1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "genomics_v1", {
    enumerable: true,
    get: function() {
        return v1_113.genomics_v1;
    }
});
var v1alpha2_4 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/genomics/v1alpha2.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "genomics_v1alpha2", {
    enumerable: true,
    get: function() {
        return v1alpha2_4.genomics_v1alpha2;
    }
});
var v2alpha1_1 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/genomics/v2alpha1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "genomics_v2alpha1", {
    enumerable: true,
    get: function() {
        return v2alpha1_1.genomics_v2alpha1;
    }
});
var v1_114 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/gkebackup/v1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "gkebackup_v1", {
    enumerable: true,
    get: function() {
        return v1_114.gkebackup_v1;
    }
});
var v1_115 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/gkehub/v1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "gkehub_v1", {
    enumerable: true,
    get: function() {
        return v1_115.gkehub_v1;
    }
});
var v1alpha_14 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/gkehub/v1alpha.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "gkehub_v1alpha", {
    enumerable: true,
    get: function() {
        return v1alpha_14.gkehub_v1alpha;
    }
});
var v1alpha2_5 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/gkehub/v1alpha2.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "gkehub_v1alpha2", {
    enumerable: true,
    get: function() {
        return v1alpha2_5.gkehub_v1alpha2;
    }
});
var v1beta_20 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/gkehub/v1beta.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "gkehub_v1beta", {
    enumerable: true,
    get: function() {
        return v1beta_20.gkehub_v1beta;
    }
});
var v1beta1_33 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/gkehub/v1beta1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "gkehub_v1beta1", {
    enumerable: true,
    get: function() {
        return v1beta1_33.gkehub_v1beta1;
    }
});
var v2_29 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/gkehub/v2.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "gkehub_v2", {
    enumerable: true,
    get: function() {
        return v2_29.gkehub_v2;
    }
});
var v2alpha_2 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/gkehub/v2alpha.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "gkehub_v2alpha", {
    enumerable: true,
    get: function() {
        return v2alpha_2.gkehub_v2alpha;
    }
});
var v2beta_6 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/gkehub/v2beta.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "gkehub_v2beta", {
    enumerable: true,
    get: function() {
        return v2beta_6.gkehub_v2beta;
    }
});
var v1_116 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/gkeonprem/v1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "gkeonprem_v1", {
    enumerable: true,
    get: function() {
        return v1_116.gkeonprem_v1;
    }
});
var v1_117 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/gmail/v1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "gmail_v1", {
    enumerable: true,
    get: function() {
        return v1_117.gmail_v1;
    }
});
var v1_118 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/gmailpostmastertools/v1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "gmailpostmastertools_v1", {
    enumerable: true,
    get: function() {
        return v1_118.gmailpostmastertools_v1;
    }
});
var v1beta1_34 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/gmailpostmastertools/v1beta1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "gmailpostmastertools_v1beta1", {
    enumerable: true,
    get: function() {
        return v1beta1_34.gmailpostmastertools_v1beta1;
    }
});
var v1_119 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/groupsmigration/v1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "groupsmigration_v1", {
    enumerable: true,
    get: function() {
        return v1_119.groupsmigration_v1;
    }
});
var v1_120 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/groupssettings/v1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "groupssettings_v1", {
    enumerable: true,
    get: function() {
        return v1_120.groupssettings_v1;
    }
});
var v1_121 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/healthcare/v1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "healthcare_v1", {
    enumerable: true,
    get: function() {
        return v1_121.healthcare_v1;
    }
});
var v1beta1_35 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/healthcare/v1beta1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "healthcare_v1beta1", {
    enumerable: true,
    get: function() {
        return v1beta1_35.healthcare_v1beta1;
    }
});
var v1_122 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/homegraph/v1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "homegraph_v1", {
    enumerable: true,
    get: function() {
        return v1_122.homegraph_v1;
    }
});
var v1_123 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/iam/v1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "iam_v1", {
    enumerable: true,
    get: function() {
        return v1_123.iam_v1;
    }
});
var v2_30 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/iam/v2.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "iam_v2", {
    enumerable: true,
    get: function() {
        return v2_30.iam_v2;
    }
});
var v2beta_7 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/iam/v2beta.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "iam_v2beta", {
    enumerable: true,
    get: function() {
        return v2beta_7.iam_v2beta;
    }
});
var v1_124 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/iamcredentials/v1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "iamcredentials_v1", {
    enumerable: true,
    get: function() {
        return v1_124.iamcredentials_v1;
    }
});
var v1_125 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/iap/v1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "iap_v1", {
    enumerable: true,
    get: function() {
        return v1_125.iap_v1;
    }
});
var v1beta1_36 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/iap/v1beta1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "iap_v1beta1", {
    enumerable: true,
    get: function() {
        return v1beta1_36.iap_v1beta1;
    }
});
var v1alpha_15 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/ideahub/v1alpha.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "ideahub_v1alpha", {
    enumerable: true,
    get: function() {
        return v1alpha_15.ideahub_v1alpha;
    }
});
var v1beta_21 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/ideahub/v1beta.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "ideahub_v1beta", {
    enumerable: true,
    get: function() {
        return v1beta_21.ideahub_v1beta;
    }
});
var v2_31 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/identitytoolkit/v2.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "identitytoolkit_v2", {
    enumerable: true,
    get: function() {
        return v2_31.identitytoolkit_v2;
    }
});
var v3_9 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/identitytoolkit/v3.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "identitytoolkit_v3", {
    enumerable: true,
    get: function() {
        return v3_9.identitytoolkit_v3;
    }
});
var v1_126 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/ids/v1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "ids_v1", {
    enumerable: true,
    get: function() {
        return v1_126.ids_v1;
    }
});
var v3_10 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/indexing/v3.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "indexing_v3", {
    enumerable: true,
    get: function() {
        return v3_10.indexing_v3;
    }
});
var v1alpha_16 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/integrations/v1alpha.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "integrations_v1alpha", {
    enumerable: true,
    get: function() {
        return v1alpha_16.integrations_v1alpha;
    }
});
var v2_32 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/jobs/v2.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "jobs_v2", {
    enumerable: true,
    get: function() {
        return v2_32.jobs_v2;
    }
});
var v3_11 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/jobs/v3.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "jobs_v3", {
    enumerable: true,
    get: function() {
        return v3_11.jobs_v3;
    }
});
var v3p1beta1_1 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/jobs/v3p1beta1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "jobs_v3p1beta1", {
    enumerable: true,
    get: function() {
        return v3p1beta1_1.jobs_v3p1beta1;
    }
});
var v4_4 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/jobs/v4.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "jobs_v4", {
    enumerable: true,
    get: function() {
        return v4_4.jobs_v4;
    }
});
var v1_127 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/keep/v1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "keep_v1", {
    enumerable: true,
    get: function() {
        return v1_127.keep_v1;
    }
});
var v1_128 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/kgsearch/v1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "kgsearch_v1", {
    enumerable: true,
    get: function() {
        return v1_128.kgsearch_v1;
    }
});
var v1_129 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/kmsinventory/v1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "kmsinventory_v1", {
    enumerable: true,
    get: function() {
        return v1_129.kmsinventory_v1;
    }
});
var v1_130 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/language/v1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "language_v1", {
    enumerable: true,
    get: function() {
        return v1_130.language_v1;
    }
});
var v1beta1_37 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/language/v1beta1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "language_v1beta1", {
    enumerable: true,
    get: function() {
        return v1beta1_37.language_v1beta1;
    }
});
var v1beta2_9 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/language/v1beta2.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "language_v1beta2", {
    enumerable: true,
    get: function() {
        return v1beta2_9.language_v1beta2;
    }
});
var v2_33 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/language/v2.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "language_v2", {
    enumerable: true,
    get: function() {
        return v2_33.language_v2;
    }
});
var v1_131 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/libraryagent/v1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "libraryagent_v1", {
    enumerable: true,
    get: function() {
        return v1_131.libraryagent_v1;
    }
});
var v1_132 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/licensing/v1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "licensing_v1", {
    enumerable: true,
    get: function() {
        return v1_132.licensing_v1;
    }
});
var v2beta_8 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/lifesciences/v2beta.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "lifesciences_v2beta", {
    enumerable: true,
    get: function() {
        return v2beta_8.lifesciences_v2beta;
    }
});
var v1_133 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/localservices/v1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "localservices_v1", {
    enumerable: true,
    get: function() {
        return v1_133.localservices_v1;
    }
});
var v2_34 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/logging/v2.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "logging_v2", {
    enumerable: true,
    get: function() {
        return v2_34.logging_v2;
    }
});
var v1_134 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/looker/v1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "looker_v1", {
    enumerable: true,
    get: function() {
        return v1_134.looker_v1;
    }
});
var v1_135 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/managedidentities/v1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "managedidentities_v1", {
    enumerable: true,
    get: function() {
        return v1_135.managedidentities_v1;
    }
});
var v1alpha1_9 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/managedidentities/v1alpha1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "managedidentities_v1alpha1", {
    enumerable: true,
    get: function() {
        return v1alpha1_9.managedidentities_v1alpha1;
    }
});
var v1beta1_38 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/managedidentities/v1beta1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "managedidentities_v1beta1", {
    enumerable: true,
    get: function() {
        return v1beta1_38.managedidentities_v1beta1;
    }
});
var v1_136 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/managedkafka/v1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "managedkafka_v1", {
    enumerable: true,
    get: function() {
        return v1_136.managedkafka_v1;
    }
});
var v1_137 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/manufacturers/v1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "manufacturers_v1", {
    enumerable: true,
    get: function() {
        return v1_137.manufacturers_v1;
    }
});
var v1alpha_17 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/marketingplatformadmin/v1alpha.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "marketingplatformadmin_v1alpha", {
    enumerable: true,
    get: function() {
        return v1alpha_17.marketingplatformadmin_v1alpha;
    }
});
var v2_35 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/meet/v2.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "meet_v2", {
    enumerable: true,
    get: function() {
        return v2_35.meet_v2;
    }
});
var v1_138 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/memcache/v1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "memcache_v1", {
    enumerable: true,
    get: function() {
        return v1_138.memcache_v1;
    }
});
var v1beta2_10 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/memcache/v1beta2.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "memcache_v1beta2", {
    enumerable: true,
    get: function() {
        return v1beta2_10.memcache_v1beta2;
    }
});
var accounts_v1beta_1 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/merchantapi/accounts_v1beta.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "merchantapi_accounts_v1beta", {
    enumerable: true,
    get: function() {
        return accounts_v1beta_1.merchantapi_accounts_v1beta;
    }
});
var conversions_v1beta_1 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/merchantapi/conversions_v1beta.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "merchantapi_conversions_v1beta", {
    enumerable: true,
    get: function() {
        return conversions_v1beta_1.merchantapi_conversions_v1beta;
    }
});
var datasources_v1beta_1 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/merchantapi/datasources_v1beta.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "merchantapi_datasources_v1beta", {
    enumerable: true,
    get: function() {
        return datasources_v1beta_1.merchantapi_datasources_v1beta;
    }
});
var inventories_v1beta_1 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/merchantapi/inventories_v1beta.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "merchantapi_inventories_v1beta", {
    enumerable: true,
    get: function() {
        return inventories_v1beta_1.merchantapi_inventories_v1beta;
    }
});
var issueresolution_v1beta_1 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/merchantapi/issueresolution_v1beta.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "merchantapi_issueresolution_v1beta", {
    enumerable: true,
    get: function() {
        return issueresolution_v1beta_1.merchantapi_issueresolution_v1beta;
    }
});
var lfp_v1beta_1 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/merchantapi/lfp_v1beta.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "merchantapi_lfp_v1beta", {
    enumerable: true,
    get: function() {
        return lfp_v1beta_1.merchantapi_lfp_v1beta;
    }
});
var notifications_v1beta_1 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/merchantapi/notifications_v1beta.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "merchantapi_notifications_v1beta", {
    enumerable: true,
    get: function() {
        return notifications_v1beta_1.merchantapi_notifications_v1beta;
    }
});
var ordertracking_v1beta_1 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/merchantapi/ordertracking_v1beta.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "merchantapi_ordertracking_v1beta", {
    enumerable: true,
    get: function() {
        return ordertracking_v1beta_1.merchantapi_ordertracking_v1beta;
    }
});
var products_v1beta_1 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/merchantapi/products_v1beta.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "merchantapi_products_v1beta", {
    enumerable: true,
    get: function() {
        return products_v1beta_1.merchantapi_products_v1beta;
    }
});
var promotions_v1beta_1 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/merchantapi/promotions_v1beta.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "merchantapi_promotions_v1beta", {
    enumerable: true,
    get: function() {
        return promotions_v1beta_1.merchantapi_promotions_v1beta;
    }
});
var quota_v1beta_1 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/merchantapi/quota_v1beta.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "merchantapi_quota_v1beta", {
    enumerable: true,
    get: function() {
        return quota_v1beta_1.merchantapi_quota_v1beta;
    }
});
var reports_v1beta_1 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/merchantapi/reports_v1beta.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "merchantapi_reports_v1beta", {
    enumerable: true,
    get: function() {
        return reports_v1beta_1.merchantapi_reports_v1beta;
    }
});
var reviews_v1beta_1 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/merchantapi/reviews_v1beta.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "merchantapi_reviews_v1beta", {
    enumerable: true,
    get: function() {
        return reviews_v1beta_1.merchantapi_reviews_v1beta;
    }
});
var v1_139 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/metastore/v1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "metastore_v1", {
    enumerable: true,
    get: function() {
        return v1_139.metastore_v1;
    }
});
var v1alpha_18 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/metastore/v1alpha.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "metastore_v1alpha", {
    enumerable: true,
    get: function() {
        return v1alpha_18.metastore_v1alpha;
    }
});
var v1beta_22 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/metastore/v1beta.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "metastore_v1beta", {
    enumerable: true,
    get: function() {
        return v1beta_22.metastore_v1beta;
    }
});
var v2_36 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/metastore/v2.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "metastore_v2", {
    enumerable: true,
    get: function() {
        return v2_36.metastore_v2;
    }
});
var v2alpha_3 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/metastore/v2alpha.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "metastore_v2alpha", {
    enumerable: true,
    get: function() {
        return v2alpha_3.metastore_v2alpha;
    }
});
var v2beta_9 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/metastore/v2beta.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "metastore_v2beta", {
    enumerable: true,
    get: function() {
        return v2beta_9.metastore_v2beta;
    }
});
var v1_140 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/migrationcenter/v1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "migrationcenter_v1", {
    enumerable: true,
    get: function() {
        return v1_140.migrationcenter_v1;
    }
});
var v1alpha1_10 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/migrationcenter/v1alpha1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "migrationcenter_v1alpha1", {
    enumerable: true,
    get: function() {
        return v1alpha1_10.migrationcenter_v1alpha1;
    }
});
var v1_141 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/ml/v1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "ml_v1", {
    enumerable: true,
    get: function() {
        return v1_141.ml_v1;
    }
});
var v1_142 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/monitoring/v1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "monitoring_v1", {
    enumerable: true,
    get: function() {
        return v1_142.monitoring_v1;
    }
});
var v3_12 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/monitoring/v3.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "monitoring_v3", {
    enumerable: true,
    get: function() {
        return v3_12.monitoring_v3;
    }
});
var v1_143 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/mybusinessaccountmanagement/v1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "mybusinessaccountmanagement_v1", {
    enumerable: true,
    get: function() {
        return v1_143.mybusinessaccountmanagement_v1;
    }
});
var v1_144 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/mybusinessbusinesscalls/v1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "mybusinessbusinesscalls_v1", {
    enumerable: true,
    get: function() {
        return v1_144.mybusinessbusinesscalls_v1;
    }
});
var v1_145 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/mybusinessbusinessinformation/v1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "mybusinessbusinessinformation_v1", {
    enumerable: true,
    get: function() {
        return v1_145.mybusinessbusinessinformation_v1;
    }
});
var v1_146 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/mybusinesslodging/v1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "mybusinesslodging_v1", {
    enumerable: true,
    get: function() {
        return v1_146.mybusinesslodging_v1;
    }
});
var v1_147 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/mybusinessnotifications/v1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "mybusinessnotifications_v1", {
    enumerable: true,
    get: function() {
        return v1_147.mybusinessnotifications_v1;
    }
});
var v1_148 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/mybusinessplaceactions/v1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "mybusinessplaceactions_v1", {
    enumerable: true,
    get: function() {
        return v1_148.mybusinessplaceactions_v1;
    }
});
var v1_149 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/mybusinessqanda/v1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "mybusinessqanda_v1", {
    enumerable: true,
    get: function() {
        return v1_149.mybusinessqanda_v1;
    }
});
var v1_150 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/mybusinessverifications/v1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "mybusinessverifications_v1", {
    enumerable: true,
    get: function() {
        return v1_150.mybusinessverifications_v1;
    }
});
var v1_151 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/netapp/v1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "netapp_v1", {
    enumerable: true,
    get: function() {
        return v1_151.netapp_v1;
    }
});
var v1beta1_39 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/netapp/v1beta1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "netapp_v1beta1", {
    enumerable: true,
    get: function() {
        return v1beta1_39.netapp_v1beta1;
    }
});
var v1_152 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/networkconnectivity/v1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "networkconnectivity_v1", {
    enumerable: true,
    get: function() {
        return v1_152.networkconnectivity_v1;
    }
});
var v1alpha1_11 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/networkconnectivity/v1alpha1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "networkconnectivity_v1alpha1", {
    enumerable: true,
    get: function() {
        return v1alpha1_11.networkconnectivity_v1alpha1;
    }
});
var v1_153 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/networkmanagement/v1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "networkmanagement_v1", {
    enumerable: true,
    get: function() {
        return v1_153.networkmanagement_v1;
    }
});
var v1beta1_40 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/networkmanagement/v1beta1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "networkmanagement_v1beta1", {
    enumerable: true,
    get: function() {
        return v1beta1_40.networkmanagement_v1beta1;
    }
});
var v1_154 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/networksecurity/v1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "networksecurity_v1", {
    enumerable: true,
    get: function() {
        return v1_154.networksecurity_v1;
    }
});
var v1beta1_41 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/networksecurity/v1beta1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "networksecurity_v1beta1", {
    enumerable: true,
    get: function() {
        return v1beta1_41.networksecurity_v1beta1;
    }
});
var v1_155 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/networkservices/v1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "networkservices_v1", {
    enumerable: true,
    get: function() {
        return v1_155.networkservices_v1;
    }
});
var v1beta1_42 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/networkservices/v1beta1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "networkservices_v1beta1", {
    enumerable: true,
    get: function() {
        return v1beta1_42.networkservices_v1beta1;
    }
});
var v1_156 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/notebooks/v1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "notebooks_v1", {
    enumerable: true,
    get: function() {
        return v1_156.notebooks_v1;
    }
});
var v2_37 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/notebooks/v2.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "notebooks_v2", {
    enumerable: true,
    get: function() {
        return v2_37.notebooks_v2;
    }
});
var v2_38 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/oauth2/v2.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "oauth2_v2", {
    enumerable: true,
    get: function() {
        return v2_38.oauth2_v2;
    }
});
var v1_157 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/observability/v1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "observability_v1", {
    enumerable: true,
    get: function() {
        return v1_157.observability_v1;
    }
});
var v1_158 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/ondemandscanning/v1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "ondemandscanning_v1", {
    enumerable: true,
    get: function() {
        return v1_158.ondemandscanning_v1;
    }
});
var v1beta1_43 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/ondemandscanning/v1beta1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "ondemandscanning_v1beta1", {
    enumerable: true,
    get: function() {
        return v1beta1_43.ondemandscanning_v1beta1;
    }
});
var v1_159 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/oracledatabase/v1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "oracledatabase_v1", {
    enumerable: true,
    get: function() {
        return v1_159.oracledatabase_v1;
    }
});
var v2_39 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/orgpolicy/v2.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "orgpolicy_v2", {
    enumerable: true,
    get: function() {
        return v2_39.orgpolicy_v2;
    }
});
var v1_160 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/osconfig/v1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "osconfig_v1", {
    enumerable: true,
    get: function() {
        return v1_160.osconfig_v1;
    }
});
var v1alpha_19 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/osconfig/v1alpha.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "osconfig_v1alpha", {
    enumerable: true,
    get: function() {
        return v1alpha_19.osconfig_v1alpha;
    }
});
var v1beta_23 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/osconfig/v1beta.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "osconfig_v1beta", {
    enumerable: true,
    get: function() {
        return v1beta_23.osconfig_v1beta;
    }
});
var v2_40 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/osconfig/v2.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "osconfig_v2", {
    enumerable: true,
    get: function() {
        return v2_40.osconfig_v2;
    }
});
var v2beta_10 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/osconfig/v2beta.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "osconfig_v2beta", {
    enumerable: true,
    get: function() {
        return v2beta_10.osconfig_v2beta;
    }
});
var v1_161 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/oslogin/v1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "oslogin_v1", {
    enumerable: true,
    get: function() {
        return v1_161.oslogin_v1;
    }
});
var v1alpha_20 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/oslogin/v1alpha.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "oslogin_v1alpha", {
    enumerable: true,
    get: function() {
        return v1alpha_20.oslogin_v1alpha;
    }
});
var v1beta_24 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/oslogin/v1beta.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "oslogin_v1beta", {
    enumerable: true,
    get: function() {
        return v1beta_24.oslogin_v1beta;
    }
});
var v5_1 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/pagespeedonline/v5.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "pagespeedonline_v5", {
    enumerable: true,
    get: function() {
        return v5_1.pagespeedonline_v5;
    }
});
var v1_162 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/parallelstore/v1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "parallelstore_v1", {
    enumerable: true,
    get: function() {
        return v1_162.parallelstore_v1;
    }
});
var v1beta_25 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/parallelstore/v1beta.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "parallelstore_v1beta", {
    enumerable: true,
    get: function() {
        return v1beta_25.parallelstore_v1beta;
    }
});
var v1_163 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/paymentsresellersubscription/v1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "paymentsresellersubscription_v1", {
    enumerable: true,
    get: function() {
        return v1_163.paymentsresellersubscription_v1;
    }
});
var v1_164 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/people/v1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "people_v1", {
    enumerable: true,
    get: function() {
        return v1_164.people_v1;
    }
});
var v1_165 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/places/v1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "places_v1", {
    enumerable: true,
    get: function() {
        return v1_165.places_v1;
    }
});
var v3_13 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/playablelocations/v3.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "playablelocations_v3", {
    enumerable: true,
    get: function() {
        return v3_13.playablelocations_v3;
    }
});
var v1_166 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/playcustomapp/v1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "playcustomapp_v1", {
    enumerable: true,
    get: function() {
        return v1_166.playcustomapp_v1;
    }
});
var v1alpha1_12 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/playdeveloperreporting/v1alpha1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "playdeveloperreporting_v1alpha1", {
    enumerable: true,
    get: function() {
        return v1alpha1_12.playdeveloperreporting_v1alpha1;
    }
});
var v1beta1_44 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/playdeveloperreporting/v1beta1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "playdeveloperreporting_v1beta1", {
    enumerable: true,
    get: function() {
        return v1beta1_44.playdeveloperreporting_v1beta1;
    }
});
var v1alpha1_13 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/playgrouping/v1alpha1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "playgrouping_v1alpha1", {
    enumerable: true,
    get: function() {
        return v1alpha1_13.playgrouping_v1alpha1;
    }
});
var v1_167 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/playintegrity/v1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "playintegrity_v1", {
    enumerable: true,
    get: function() {
        return v1_167.playintegrity_v1;
    }
});
var v1_168 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/plus/v1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "plus_v1", {
    enumerable: true,
    get: function() {
        return v1_168.plus_v1;
    }
});
var v1_169 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/policyanalyzer/v1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "policyanalyzer_v1", {
    enumerable: true,
    get: function() {
        return v1_169.policyanalyzer_v1;
    }
});
var v1beta1_45 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/policyanalyzer/v1beta1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "policyanalyzer_v1beta1", {
    enumerable: true,
    get: function() {
        return v1beta1_45.policyanalyzer_v1beta1;
    }
});
var v1_170 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/policysimulator/v1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "policysimulator_v1", {
    enumerable: true,
    get: function() {
        return v1_170.policysimulator_v1;
    }
});
var v1alpha_21 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/policysimulator/v1alpha.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "policysimulator_v1alpha", {
    enumerable: true,
    get: function() {
        return v1alpha_21.policysimulator_v1alpha;
    }
});
var v1beta_26 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/policysimulator/v1beta.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "policysimulator_v1beta", {
    enumerable: true,
    get: function() {
        return v1beta_26.policysimulator_v1beta;
    }
});
var v1beta1_46 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/policysimulator/v1beta1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "policysimulator_v1beta1", {
    enumerable: true,
    get: function() {
        return v1beta1_46.policysimulator_v1beta1;
    }
});
var v1_171 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/policytroubleshooter/v1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "policytroubleshooter_v1", {
    enumerable: true,
    get: function() {
        return v1_171.policytroubleshooter_v1;
    }
});
var v1beta_27 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/policytroubleshooter/v1beta.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "policytroubleshooter_v1beta", {
    enumerable: true,
    get: function() {
        return v1beta_27.policytroubleshooter_v1beta;
    }
});
var v1_172 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/pollen/v1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "pollen_v1", {
    enumerable: true,
    get: function() {
        return v1_172.pollen_v1;
    }
});
var v1_173 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/poly/v1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "poly_v1", {
    enumerable: true,
    get: function() {
        return v1_173.poly_v1;
    }
});
var v1_174 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/privateca/v1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "privateca_v1", {
    enumerable: true,
    get: function() {
        return v1_174.privateca_v1;
    }
});
var v1beta1_47 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/privateca/v1beta1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "privateca_v1beta1", {
    enumerable: true,
    get: function() {
        return v1beta1_47.privateca_v1beta1;
    }
});
var v1alpha1_14 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/prod_tt_sasportal/v1alpha1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "prod_tt_sasportal_v1alpha1", {
    enumerable: true,
    get: function() {
        return v1alpha1_14.prod_tt_sasportal_v1alpha1;
    }
});
var v1_175 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/publicca/v1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "publicca_v1", {
    enumerable: true,
    get: function() {
        return v1_175.publicca_v1;
    }
});
var v1alpha1_15 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/publicca/v1alpha1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "publicca_v1alpha1", {
    enumerable: true,
    get: function() {
        return v1alpha1_15.publicca_v1alpha1;
    }
});
var v1beta1_48 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/publicca/v1beta1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "publicca_v1beta1", {
    enumerable: true,
    get: function() {
        return v1beta1_48.publicca_v1beta1;
    }
});
var v1_176 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/pubsub/v1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "pubsub_v1", {
    enumerable: true,
    get: function() {
        return v1_176.pubsub_v1;
    }
});
var v1beta1a_1 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/pubsub/v1beta1a.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "pubsub_v1beta1a", {
    enumerable: true,
    get: function() {
        return v1beta1a_1.pubsub_v1beta1a;
    }
});
var v1beta2_11 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/pubsub/v1beta2.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "pubsub_v1beta2", {
    enumerable: true,
    get: function() {
        return v1beta2_11.pubsub_v1beta2;
    }
});
var v1_177 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/pubsublite/v1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "pubsublite_v1", {
    enumerable: true,
    get: function() {
        return v1_177.pubsublite_v1;
    }
});
var v1_178 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/rapidmigrationassessment/v1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "rapidmigrationassessment_v1", {
    enumerable: true,
    get: function() {
        return v1_178.rapidmigrationassessment_v1;
    }
});
var v1_179 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/readerrevenuesubscriptionlinking/v1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "readerrevenuesubscriptionlinking_v1", {
    enumerable: true,
    get: function() {
        return v1_179.readerrevenuesubscriptionlinking_v1;
    }
});
var v1_180 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/realtimebidding/v1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "realtimebidding_v1", {
    enumerable: true,
    get: function() {
        return v1_180.realtimebidding_v1;
    }
});
var v1alpha_22 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/realtimebidding/v1alpha.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "realtimebidding_v1alpha", {
    enumerable: true,
    get: function() {
        return v1alpha_22.realtimebidding_v1alpha;
    }
});
var v1_181 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/recaptchaenterprise/v1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "recaptchaenterprise_v1", {
    enumerable: true,
    get: function() {
        return v1_181.recaptchaenterprise_v1;
    }
});
var v1beta1_49 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/recommendationengine/v1beta1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "recommendationengine_v1beta1", {
    enumerable: true,
    get: function() {
        return v1beta1_49.recommendationengine_v1beta1;
    }
});
var v1_182 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/recommender/v1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "recommender_v1", {
    enumerable: true,
    get: function() {
        return v1_182.recommender_v1;
    }
});
var v1beta1_50 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/recommender/v1beta1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "recommender_v1beta1", {
    enumerable: true,
    get: function() {
        return v1beta1_50.recommender_v1beta1;
    }
});
var v1_183 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/redis/v1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "redis_v1", {
    enumerable: true,
    get: function() {
        return v1_183.redis_v1;
    }
});
var v1beta1_51 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/redis/v1beta1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "redis_v1beta1", {
    enumerable: true,
    get: function() {
        return v1beta1_51.redis_v1beta1;
    }
});
var v1_184 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/remotebuildexecution/v1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "remotebuildexecution_v1", {
    enumerable: true,
    get: function() {
        return v1_184.remotebuildexecution_v1;
    }
});
var v1alpha_23 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/remotebuildexecution/v1alpha.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "remotebuildexecution_v1alpha", {
    enumerable: true,
    get: function() {
        return v1alpha_23.remotebuildexecution_v1alpha;
    }
});
var v2_41 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/remotebuildexecution/v2.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "remotebuildexecution_v2", {
    enumerable: true,
    get: function() {
        return v2_41.remotebuildexecution_v2;
    }
});
var v1_185 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/reseller/v1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "reseller_v1", {
    enumerable: true,
    get: function() {
        return v1_185.reseller_v1;
    }
});
var v1_186 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/resourcesettings/v1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "resourcesettings_v1", {
    enumerable: true,
    get: function() {
        return v1_186.resourcesettings_v1;
    }
});
var v2_42 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/retail/v2.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "retail_v2", {
    enumerable: true,
    get: function() {
        return v2_42.retail_v2;
    }
});
var v2alpha_4 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/retail/v2alpha.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "retail_v2alpha", {
    enumerable: true,
    get: function() {
        return v2alpha_4.retail_v2alpha;
    }
});
var v2beta_11 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/retail/v2beta.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "retail_v2beta", {
    enumerable: true,
    get: function() {
        return v2beta_11.retail_v2beta;
    }
});
var v1_187 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/run/v1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "run_v1", {
    enumerable: true,
    get: function() {
        return v1_187.run_v1;
    }
});
var v1alpha1_16 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/run/v1alpha1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "run_v1alpha1", {
    enumerable: true,
    get: function() {
        return v1alpha1_16.run_v1alpha1;
    }
});
var v1beta1_52 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/run/v1beta1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "run_v1beta1", {
    enumerable: true,
    get: function() {
        return v1beta1_52.run_v1beta1;
    }
});
var v2_43 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/run/v2.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "run_v2", {
    enumerable: true,
    get: function() {
        return v2_43.run_v2;
    }
});
var v1_188 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/runtimeconfig/v1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "runtimeconfig_v1", {
    enumerable: true,
    get: function() {
        return v1_188.runtimeconfig_v1;
    }
});
var v1beta1_53 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/runtimeconfig/v1beta1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "runtimeconfig_v1beta1", {
    enumerable: true,
    get: function() {
        return v1beta1_53.runtimeconfig_v1beta1;
    }
});
var v1beta1_54 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/saasservicemgmt/v1beta1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "saasservicemgmt_v1beta1", {
    enumerable: true,
    get: function() {
        return v1beta1_54.saasservicemgmt_v1beta1;
    }
});
var v4_5 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/safebrowsing/v4.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "safebrowsing_v4", {
    enumerable: true,
    get: function() {
        return v4_5.safebrowsing_v4;
    }
});
var v5_2 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/safebrowsing/v5.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "safebrowsing_v5", {
    enumerable: true,
    get: function() {
        return v5_2.safebrowsing_v5;
    }
});
var v1alpha1_17 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/sasportal/v1alpha1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "sasportal_v1alpha1", {
    enumerable: true,
    get: function() {
        return v1alpha1_17.sasportal_v1alpha1;
    }
});
var v1_189 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/script/v1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "script_v1", {
    enumerable: true,
    get: function() {
        return v1_189.script_v1;
    }
});
var v0_1 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/searchads360/v0.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "searchads360_v0", {
    enumerable: true,
    get: function() {
        return v0_1.searchads360_v0;
    }
});
var v1_190 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/searchconsole/v1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "searchconsole_v1", {
    enumerable: true,
    get: function() {
        return v1_190.searchconsole_v1;
    }
});
var v1_191 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/secretmanager/v1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "secretmanager_v1", {
    enumerable: true,
    get: function() {
        return v1_191.secretmanager_v1;
    }
});
var v1beta1_55 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/secretmanager/v1beta1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "secretmanager_v1beta1", {
    enumerable: true,
    get: function() {
        return v1beta1_55.secretmanager_v1beta1;
    }
});
var v1beta2_12 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/secretmanager/v1beta2.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "secretmanager_v1beta2", {
    enumerable: true,
    get: function() {
        return v1beta2_12.secretmanager_v1beta2;
    }
});
var v1_192 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/securitycenter/v1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "securitycenter_v1", {
    enumerable: true,
    get: function() {
        return v1_192.securitycenter_v1;
    }
});
var v1beta1_56 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/securitycenter/v1beta1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "securitycenter_v1beta1", {
    enumerable: true,
    get: function() {
        return v1beta1_56.securitycenter_v1beta1;
    }
});
var v1beta2_13 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/securitycenter/v1beta2.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "securitycenter_v1beta2", {
    enumerable: true,
    get: function() {
        return v1beta2_13.securitycenter_v1beta2;
    }
});
var v1p1alpha1_1 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/securitycenter/v1p1alpha1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "securitycenter_v1p1alpha1", {
    enumerable: true,
    get: function() {
        return v1p1alpha1_1.securitycenter_v1p1alpha1;
    }
});
var v1p1beta1_2 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/securitycenter/v1p1beta1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "securitycenter_v1p1beta1", {
    enumerable: true,
    get: function() {
        return v1p1beta1_2.securitycenter_v1p1beta1;
    }
});
var v1_193 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/securityposture/v1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "securityposture_v1", {
    enumerable: true,
    get: function() {
        return v1_193.securityposture_v1;
    }
});
var v1_194 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/serviceconsumermanagement/v1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "serviceconsumermanagement_v1", {
    enumerable: true,
    get: function() {
        return v1_194.serviceconsumermanagement_v1;
    }
});
var v1beta1_57 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/serviceconsumermanagement/v1beta1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "serviceconsumermanagement_v1beta1", {
    enumerable: true,
    get: function() {
        return v1beta1_57.serviceconsumermanagement_v1beta1;
    }
});
var v1_195 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/servicecontrol/v1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "servicecontrol_v1", {
    enumerable: true,
    get: function() {
        return v1_195.servicecontrol_v1;
    }
});
var v2_44 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/servicecontrol/v2.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "servicecontrol_v2", {
    enumerable: true,
    get: function() {
        return v2_44.servicecontrol_v2;
    }
});
var v1_196 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/servicedirectory/v1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "servicedirectory_v1", {
    enumerable: true,
    get: function() {
        return v1_196.servicedirectory_v1;
    }
});
var v1beta1_58 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/servicedirectory/v1beta1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "servicedirectory_v1beta1", {
    enumerable: true,
    get: function() {
        return v1beta1_58.servicedirectory_v1beta1;
    }
});
var v1_197 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/servicemanagement/v1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "servicemanagement_v1", {
    enumerable: true,
    get: function() {
        return v1_197.servicemanagement_v1;
    }
});
var v1_198 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/servicenetworking/v1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "servicenetworking_v1", {
    enumerable: true,
    get: function() {
        return v1_198.servicenetworking_v1;
    }
});
var v1beta_28 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/servicenetworking/v1beta.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "servicenetworking_v1beta", {
    enumerable: true,
    get: function() {
        return v1beta_28.servicenetworking_v1beta;
    }
});
var v1_199 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/serviceusage/v1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "serviceusage_v1", {
    enumerable: true,
    get: function() {
        return v1_199.serviceusage_v1;
    }
});
var v1beta1_59 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/serviceusage/v1beta1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "serviceusage_v1beta1", {
    enumerable: true,
    get: function() {
        return v1beta1_59.serviceusage_v1beta1;
    }
});
var v4_6 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/sheets/v4.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "sheets_v4", {
    enumerable: true,
    get: function() {
        return v4_6.sheets_v4;
    }
});
var v1_200 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/siteVerification/v1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "siteVerification_v1", {
    enumerable: true,
    get: function() {
        return v1_200.siteVerification_v1;
    }
});
var v1_201 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/slides/v1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "slides_v1", {
    enumerable: true,
    get: function() {
        return v1_201.slides_v1;
    }
});
var v1_202 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/smartdevicemanagement/v1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "smartdevicemanagement_v1", {
    enumerable: true,
    get: function() {
        return v1_202.smartdevicemanagement_v1;
    }
});
var v1_203 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/solar/v1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "solar_v1", {
    enumerable: true,
    get: function() {
        return v1_203.solar_v1;
    }
});
var v1_204 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/sourcerepo/v1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "sourcerepo_v1", {
    enumerable: true,
    get: function() {
        return v1_204.sourcerepo_v1;
    }
});
var v1_205 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/spanner/v1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "spanner_v1", {
    enumerable: true,
    get: function() {
        return v1_205.spanner_v1;
    }
});
var v1_206 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/speech/v1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "speech_v1", {
    enumerable: true,
    get: function() {
        return v1_206.speech_v1;
    }
});
var v1p1beta1_3 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/speech/v1p1beta1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "speech_v1p1beta1", {
    enumerable: true,
    get: function() {
        return v1p1beta1_3.speech_v1p1beta1;
    }
});
var v2beta1_6 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/speech/v2beta1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "speech_v2beta1", {
    enumerable: true,
    get: function() {
        return v2beta1_6.speech_v2beta1;
    }
});
var v1beta4_1 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/sql/v1beta4.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "sql_v1beta4", {
    enumerable: true,
    get: function() {
        return v1beta4_1.sql_v1beta4;
    }
});
var v1_207 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/sqladmin/v1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "sqladmin_v1", {
    enumerable: true,
    get: function() {
        return v1_207.sqladmin_v1;
    }
});
var v1beta4_2 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/sqladmin/v1beta4.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "sqladmin_v1beta4", {
    enumerable: true,
    get: function() {
        return v1beta4_2.sqladmin_v1beta4;
    }
});
var v1_208 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/storage/v1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "storage_v1", {
    enumerable: true,
    get: function() {
        return v1_208.storage_v1;
    }
});
var v1beta2_14 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/storage/v1beta2.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "storage_v1beta2", {
    enumerable: true,
    get: function() {
        return v1beta2_14.storage_v1beta2;
    }
});
var v1_209 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/storagebatchoperations/v1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "storagebatchoperations_v1", {
    enumerable: true,
    get: function() {
        return v1_209.storagebatchoperations_v1;
    }
});
var v1_210 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/storagetransfer/v1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "storagetransfer_v1", {
    enumerable: true,
    get: function() {
        return v1_210.storagetransfer_v1;
    }
});
var v1_211 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/streetviewpublish/v1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "streetviewpublish_v1", {
    enumerable: true,
    get: function() {
        return v1_211.streetviewpublish_v1;
    }
});
var v1_212 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/sts/v1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "sts_v1", {
    enumerable: true,
    get: function() {
        return v1_212.sts_v1;
    }
});
var v1beta_29 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/sts/v1beta.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "sts_v1beta", {
    enumerable: true,
    get: function() {
        return v1beta_29.sts_v1beta;
    }
});
var v1_213 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/tagmanager/v1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "tagmanager_v1", {
    enumerable: true,
    get: function() {
        return v1_213.tagmanager_v1;
    }
});
var v2_45 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/tagmanager/v2.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "tagmanager_v2", {
    enumerable: true,
    get: function() {
        return v2_45.tagmanager_v2;
    }
});
var v1_214 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/tasks/v1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "tasks_v1", {
    enumerable: true,
    get: function() {
        return v1_214.tasks_v1;
    }
});
var v1_215 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/testing/v1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "testing_v1", {
    enumerable: true,
    get: function() {
        return v1_215.testing_v1;
    }
});
var v1_216 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/texttospeech/v1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "texttospeech_v1", {
    enumerable: true,
    get: function() {
        return v1_216.texttospeech_v1;
    }
});
var v1beta1_60 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/texttospeech/v1beta1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "texttospeech_v1beta1", {
    enumerable: true,
    get: function() {
        return v1beta1_60.texttospeech_v1beta1;
    }
});
var v1beta3_3 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/toolresults/v1beta3.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "toolresults_v1beta3", {
    enumerable: true,
    get: function() {
        return v1beta3_3.toolresults_v1beta3;
    }
});
var v1_217 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/tpu/v1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "tpu_v1", {
    enumerable: true,
    get: function() {
        return v1_217.tpu_v1;
    }
});
var v1alpha1_18 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/tpu/v1alpha1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "tpu_v1alpha1", {
    enumerable: true,
    get: function() {
        return v1alpha1_18.tpu_v1alpha1;
    }
});
var v2_46 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/tpu/v2.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "tpu_v2", {
    enumerable: true,
    get: function() {
        return v2_46.tpu_v2;
    }
});
var v2alpha1_2 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/tpu/v2alpha1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "tpu_v2alpha1", {
    enumerable: true,
    get: function() {
        return v2alpha1_2.tpu_v2alpha1;
    }
});
var v2_47 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/trafficdirector/v2.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "trafficdirector_v2", {
    enumerable: true,
    get: function() {
        return v2_47.trafficdirector_v2;
    }
});
var v3_14 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/trafficdirector/v3.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "trafficdirector_v3", {
    enumerable: true,
    get: function() {
        return v3_14.trafficdirector_v3;
    }
});
var v1_218 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/transcoder/v1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "transcoder_v1", {
    enumerable: true,
    get: function() {
        return v1_218.transcoder_v1;
    }
});
var v1beta1_61 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/transcoder/v1beta1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "transcoder_v1beta1", {
    enumerable: true,
    get: function() {
        return v1beta1_61.transcoder_v1beta1;
    }
});
var v2_48 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/translate/v2.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "translate_v2", {
    enumerable: true,
    get: function() {
        return v2_48.translate_v2;
    }
});
var v3_15 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/translate/v3.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "translate_v3", {
    enumerable: true,
    get: function() {
        return v3_15.translate_v3;
    }
});
var v3beta1_2 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/translate/v3beta1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "translate_v3beta1", {
    enumerable: true,
    get: function() {
        return v3beta1_2.translate_v3beta1;
    }
});
var v1_219 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/travelimpactmodel/v1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "travelimpactmodel_v1", {
    enumerable: true,
    get: function() {
        return v1_219.travelimpactmodel_v1;
    }
});
var v1_220 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/vault/v1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "vault_v1", {
    enumerable: true,
    get: function() {
        return v1_220.vault_v1;
    }
});
var v1_221 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/vectortile/v1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "vectortile_v1", {
    enumerable: true,
    get: function() {
        return v1_221.vectortile_v1;
    }
});
var v1_222 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/verifiedaccess/v1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "verifiedaccess_v1", {
    enumerable: true,
    get: function() {
        return v1_222.verifiedaccess_v1;
    }
});
var v2_49 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/verifiedaccess/v2.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "verifiedaccess_v2", {
    enumerable: true,
    get: function() {
        return v2_49.verifiedaccess_v2;
    }
});
var v1_223 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/versionhistory/v1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "versionhistory_v1", {
    enumerable: true,
    get: function() {
        return v1_223.versionhistory_v1;
    }
});
var v1_224 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/videointelligence/v1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "videointelligence_v1", {
    enumerable: true,
    get: function() {
        return v1_224.videointelligence_v1;
    }
});
var v1beta2_15 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/videointelligence/v1beta2.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "videointelligence_v1beta2", {
    enumerable: true,
    get: function() {
        return v1beta2_15.videointelligence_v1beta2;
    }
});
var v1p1beta1_4 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/videointelligence/v1p1beta1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "videointelligence_v1p1beta1", {
    enumerable: true,
    get: function() {
        return v1p1beta1_4.videointelligence_v1p1beta1;
    }
});
var v1p2beta1_1 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/videointelligence/v1p2beta1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "videointelligence_v1p2beta1", {
    enumerable: true,
    get: function() {
        return v1p2beta1_1.videointelligence_v1p2beta1;
    }
});
var v1p3beta1_1 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/videointelligence/v1p3beta1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "videointelligence_v1p3beta1", {
    enumerable: true,
    get: function() {
        return v1p3beta1_1.videointelligence_v1p3beta1;
    }
});
var v1_225 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/vision/v1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "vision_v1", {
    enumerable: true,
    get: function() {
        return v1_225.vision_v1;
    }
});
var v1p1beta1_5 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/vision/v1p1beta1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "vision_v1p1beta1", {
    enumerable: true,
    get: function() {
        return v1p1beta1_5.vision_v1p1beta1;
    }
});
var v1p2beta1_2 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/vision/v1p2beta1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "vision_v1p2beta1", {
    enumerable: true,
    get: function() {
        return v1p2beta1_2.vision_v1p2beta1;
    }
});
var v1_226 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/vmmigration/v1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "vmmigration_v1", {
    enumerable: true,
    get: function() {
        return v1_226.vmmigration_v1;
    }
});
var v1alpha1_19 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/vmmigration/v1alpha1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "vmmigration_v1alpha1", {
    enumerable: true,
    get: function() {
        return v1alpha1_19.vmmigration_v1alpha1;
    }
});
var v1_227 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/vmwareengine/v1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "vmwareengine_v1", {
    enumerable: true,
    get: function() {
        return v1_227.vmwareengine_v1;
    }
});
var v1_228 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/vpcaccess/v1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "vpcaccess_v1", {
    enumerable: true,
    get: function() {
        return v1_228.vpcaccess_v1;
    }
});
var v1beta1_62 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/vpcaccess/v1beta1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "vpcaccess_v1beta1", {
    enumerable: true,
    get: function() {
        return v1beta1_62.vpcaccess_v1beta1;
    }
});
var v1_229 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/walletobjects/v1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "walletobjects_v1", {
    enumerable: true,
    get: function() {
        return v1_229.walletobjects_v1;
    }
});
var v1_230 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/webfonts/v1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "webfonts_v1", {
    enumerable: true,
    get: function() {
        return v1_230.webfonts_v1;
    }
});
var v3_16 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/webmasters/v3.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "webmasters_v3", {
    enumerable: true,
    get: function() {
        return v3_16.webmasters_v3;
    }
});
var v1_231 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/webrisk/v1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "webrisk_v1", {
    enumerable: true,
    get: function() {
        return v1_231.webrisk_v1;
    }
});
var v1_232 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/websecurityscanner/v1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "websecurityscanner_v1", {
    enumerable: true,
    get: function() {
        return v1_232.websecurityscanner_v1;
    }
});
var v1alpha_24 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/websecurityscanner/v1alpha.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "websecurityscanner_v1alpha", {
    enumerable: true,
    get: function() {
        return v1alpha_24.websecurityscanner_v1alpha;
    }
});
var v1beta_30 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/websecurityscanner/v1beta.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "websecurityscanner_v1beta", {
    enumerable: true,
    get: function() {
        return v1beta_30.websecurityscanner_v1beta;
    }
});
var v1_233 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/workflowexecutions/v1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "workflowexecutions_v1", {
    enumerable: true,
    get: function() {
        return v1_233.workflowexecutions_v1;
    }
});
var v1beta_31 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/workflowexecutions/v1beta.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "workflowexecutions_v1beta", {
    enumerable: true,
    get: function() {
        return v1beta_31.workflowexecutions_v1beta;
    }
});
var v1_234 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/workflows/v1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "workflows_v1", {
    enumerable: true,
    get: function() {
        return v1_234.workflows_v1;
    }
});
var v1beta_32 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/workflows/v1beta.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "workflows_v1beta", {
    enumerable: true,
    get: function() {
        return v1beta_32.workflows_v1beta;
    }
});
var v1_235 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/workloadmanager/v1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "workloadmanager_v1", {
    enumerable: true,
    get: function() {
        return v1_235.workloadmanager_v1;
    }
});
var v1_236 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/workspaceevents/v1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "workspaceevents_v1", {
    enumerable: true,
    get: function() {
        return v1_236.workspaceevents_v1;
    }
});
var v1_237 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/workstations/v1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "workstations_v1", {
    enumerable: true,
    get: function() {
        return v1_237.workstations_v1;
    }
});
var v1beta_33 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/workstations/v1beta.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "workstations_v1beta", {
    enumerable: true,
    get: function() {
        return v1beta_33.workstations_v1beta;
    }
});
var v3_17 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/youtube/v3.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "youtube_v3", {
    enumerable: true,
    get: function() {
        return v3_17.youtube_v3;
    }
});
var v1_238 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/youtubeAnalytics/v1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "youtubeAnalytics_v1", {
    enumerable: true,
    get: function() {
        return v1_238.youtubeAnalytics_v1;
    }
});
var v2_50 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/youtubeAnalytics/v2.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "youtubeAnalytics_v2", {
    enumerable: true,
    get: function() {
        return v2_50.youtubeAnalytics_v2;
    }
});
var v1_239 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/youtubereporting/v1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "youtubereporting_v1", {
    enumerable: true,
    get: function() {
        return v1_239.youtubereporting_v1;
    }
});
}}),

};

//# sourceMappingURL=node_modules_googleapis_build_src_index_0cd225e7.js.map
{"version": 3, "sources": [], "sections": [{"offset": {"line": 111, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/AI_Projects/CRMcriadores/crm-interno/app/actions/sheetsActions.ts"], "sourcesContent": ["'use server';\n\nimport { google } from 'googleapis';\n\n// Configuração da autenticação\nconst getGoogleSheetsAuth = () => {\n  const credentials = {\n    type: 'service_account',\n    project_id: process.env.GOOGLE_PROJECT_ID,\n    private_key_id: process.env.GOOGLE_PRIVATE_KEY_ID,\n    private_key: process.env.GOOGLE_PRIVATE_KEY?.replace(/\\\\n/g, '\\n'),\n    client_email: process.env.GOOGLE_CLIENT_EMAIL,\n    client_id: process.env.GOOGLE_CLIENT_ID,\n    auth_uri: 'https://accounts.google.com/o/oauth2/auth',\n    token_uri: 'https://oauth2.googleapis.com/token',\n    auth_provider_x509_cert_url: 'https://www.googleapis.com/oauth2/v1/certs',\n    client_x509_cert_url: `https://www.googleapis.com/robot/v1/metadata/x509/${process.env.GOOGLE_CLIENT_EMAIL}`,\n  };\n\n  const auth = new google.auth.GoogleAuth({\n    credentials,\n    scopes: ['https://www.googleapis.com/auth/spreadsheets'],\n  });\n\n  return auth;\n};\n\n// Função para ler dados da planilha\nexport async function getData(sheetName: string) {\n  try {\n    const auth = getGoogleSheetsAuth();\n    const sheets = google.sheets({ version: 'v4', auth });\n    \n    const spreadsheetId = process.env.GOOGLE_SPREADSHEET_ID;\n    \n    if (!spreadsheetId) {\n      throw new Error('GOOGLE_SPREADSHEET_ID não está configurado');\n    }\n\n    const response = await sheets.spreadsheets.values.get({\n      spreadsheetId,\n      range: `${sheetName}!A:Z`, // Lê todas as colunas de A até Z\n    });\n\n    return response.data.values || [];\n  } catch (error) {\n    console.error('Erro ao ler dados da planilha:', error);\n    throw new Error('Falha ao ler dados da planilha');\n  }\n}\n\n// Função para adicionar dados à planilha\nexport async function appendData(sheetName: string, rowData: any[]) {\n  try {\n    const auth = getGoogleSheetsAuth();\n    const sheets = google.sheets({ version: 'v4', auth });\n    \n    const spreadsheetId = process.env.GOOGLE_SPREADSHEET_ID;\n    \n    if (!spreadsheetId) {\n      throw new Error('GOOGLE_SPREADSHEET_ID não está configurado');\n    }\n\n    const response = await sheets.spreadsheets.values.append({\n      spreadsheetId,\n      range: `${sheetName}!A:Z`,\n      valueInputOption: 'USER_ENTERED',\n      requestBody: {\n        values: [rowData],\n      },\n    });\n\n    return response.data;\n  } catch (error) {\n    console.error('Erro ao adicionar dados à planilha:', error);\n    throw new Error('Falha ao adicionar dados à planilha');\n  }\n}\n\n// Função para atualizar dados na planilha\nexport async function updateData(sheetName: string, range: string, rowData: any[]) {\n  try {\n    const auth = getGoogleSheetsAuth();\n    const sheets = google.sheets({ version: 'v4', auth });\n    \n    const spreadsheetId = process.env.GOOGLE_SPREADSHEET_ID;\n    \n    if (!spreadsheetId) {\n      throw new Error('GOOGLE_SPREADSHEET_ID não está configurado');\n    }\n\n    const response = await sheets.spreadsheets.values.update({\n      spreadsheetId,\n      range: `${sheetName}!${range}`,\n      valueInputOption: 'USER_ENTERED',\n      requestBody: {\n        values: [rowData],\n      },\n    });\n\n    return response.data;\n  } catch (error) {\n    console.error('Erro ao atualizar dados da planilha:', error);\n    throw new Error('Falha ao atualizar dados da planilha');\n  }\n}\n"], "names": [], "mappings": ";;;;;;;AAEA;;;;;AAEA,+BAA+B;AAC/B,MAAM,sBAAsB;IAC1B,MAAM,cAAc;QAClB,MAAM;QACN,YAAY,QAAQ,GAAG,CAAC,iBAAiB;QACzC,gBAAgB,QAAQ,GAAG,CAAC,qBAAqB;QACjD,aAAa,QAAQ,GAAG,CAAC,kBAAkB,EAAE,QAAQ,QAAQ;QAC7D,cAAc,QAAQ,GAAG,CAAC,mBAAmB;QAC7C,WAAW,QAAQ,GAAG,CAAC,gBAAgB;QACvC,UAAU;QACV,WAAW;QACX,6BAA6B;QAC7B,sBAAsB,CAAC,kDAAkD,EAAE,QAAQ,GAAG,CAAC,mBAAmB,EAAE;IAC9G;IAEA,MAAM,OAAO,IAAI,mJAAA,CAAA,SAAM,CAAC,IAAI,CAAC,UAAU,CAAC;QACtC;QACA,QAAQ;YAAC;SAA+C;IAC1D;IAEA,OAAO;AACT;AAGO,eAAe,QAAQ,SAAiB;IAC7C,IAAI;QACF,MAAM,OAAO;QACb,MAAM,SAAS,mJAAA,CAAA,SAAM,CAAC,MAAM,CAAC;YAAE,SAAS;YAAM;QAAK;QAEnD,MAAM,gBAAgB,QAAQ,GAAG,CAAC,qBAAqB;QAEvD,IAAI,CAAC,eAAe;YAClB,MAAM,IAAI,MAAM;QAClB;QAEA,MAAM,WAAW,MAAM,OAAO,YAAY,CAAC,MAAM,CAAC,GAAG,CAAC;YACpD;YACA,OAAO,GAAG,UAAU,IAAI,CAAC;QAC3B;QAEA,OAAO,SAAS,IAAI,CAAC,MAAM,IAAI,EAAE;IACnC,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,kCAAkC;QAChD,MAAM,IAAI,MAAM;IAClB;AACF;AAGO,eAAe,WAAW,SAAiB,EAAE,OAAc;IAChE,IAAI;QACF,MAAM,OAAO;QACb,MAAM,SAAS,mJAAA,CAAA,SAAM,CAAC,MAAM,CAAC;YAAE,SAAS;YAAM;QAAK;QAEnD,MAAM,gBAAgB,QAAQ,GAAG,CAAC,qBAAqB;QAEvD,IAAI,CAAC,eAAe;YAClB,MAAM,IAAI,MAAM;QAClB;QAEA,MAAM,WAAW,MAAM,OAAO,YAAY,CAAC,MAAM,CAAC,MAAM,CAAC;YACvD;YACA,OAAO,GAAG,UAAU,IAAI,CAAC;YACzB,kBAAkB;YAClB,aAAa;gBACX,QAAQ;oBAAC;iBAAQ;YACnB;QACF;QAEA,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,uCAAuC;QACrD,MAAM,IAAI,MAAM;IAClB;AACF;AAGO,eAAe,WAAW,SAAiB,EAAE,KAAa,EAAE,OAAc;IAC/E,IAAI;QACF,MAAM,OAAO;QACb,MAAM,SAAS,mJAAA,CAAA,SAAM,CAAC,MAAM,CAAC;YAAE,SAAS;YAAM;QAAK;QAEnD,MAAM,gBAAgB,QAAQ,GAAG,CAAC,qBAAqB;QAEvD,IAAI,CAAC,eAAe;YAClB,MAAM,IAAI,MAAM;QAClB;QAEA,MAAM,WAAW,MAAM,OAAO,YAAY,CAAC,MAAM,CAAC,MAAM,CAAC;YACvD;YACA,OAAO,GAAG,UAAU,CAAC,EAAE,OAAO;YAC9B,kBAAkB;YAClB,aAAa;gBACX,QAAQ;oBAAC;iBAAQ;YACnB;QACF;QAEA,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,wCAAwC;QACtD,MAAM,IAAI,MAAM;IAClB;AACF;;;IA7EsB;IAwBA;IA4BA;;AApDA,+OAAA;AAwBA,+OAAA;AA4BA,+OAAA", "debugId": null}}, {"offset": {"line": 235, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/AI_Projects/CRMcriadores/crm-interno/.next-internal/server/app/%28dashboard%29/influencers/page/actions.js%20%28server%20actions%20loader%29"], "sourcesContent": ["export {getData as '40416fe263e60966356356bb294165af55bf27c98c'} from 'ACTIONS_MODULE0'\nexport {appendData as '60b2397e35e4d22eb574fd297096247fb0b426b814'} from 'ACTIONS_MODULE0'\nexport {updateData as '700091b6dbbe7aa4a45146966c103d91060aef6152'} from 'ACTIONS_MODULE0'\n"], "names": [], "mappings": ";AAAA", "debugId": null}}, {"offset": {"line": 299, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/AI_Projects/CRMcriadores/crm-interno/lib/utils.ts"], "sourcesContent": ["/**\n * Transforma dados de array de arrays (formato do Google Sheets) \n * em array de objetos JSON usando a primeira linha como cabeçalhos\n */\nexport function transformData(data: any[][]): Record<string, any>[] {\n  if (!data || data.length === 0) {\n    return [];\n  }\n\n  // A primeira linha contém os cabeçalhos\n  const headers = data[0];\n  \n  // As linhas restantes contêm os dados\n  const rows = data.slice(1);\n\n  return rows.map((row) => {\n    const obj: Record<string, any> = {};\n    \n    headers.forEach((header, index) => {\n      // Usa o cabeçalho como chave e o valor da linha correspondente\n      obj[header] = row[index] || '';\n    });\n\n    return obj;\n  });\n}\n\n/**\n * Converte um objeto em array de valores na ordem dos cabeçalhos fornecidos\n */\nexport function objectToRowData(obj: Record<string, any>, headers: string[]): any[] {\n  return headers.map(header => obj[header] || '');\n}\n\n/**\n * Valida se os dados têm a estrutura esperada\n */\nexport function validateSheetData(data: any[][]): boolean {\n  return Array.isArray(data) && data.length > 0 && Array.isArray(data[0]);\n}\n\n/**\n * Limpa e normaliza strings vindas do Google Sheets\n */\nexport function cleanSheetValue(value: any): string {\n  if (value === null || value === undefined) {\n    return '';\n  }\n  \n  return String(value).trim();\n}\n\n/**\n * Converte valores de string para tipos apropriados\n */\nexport function parseSheetValue(value: string, type: 'string' | 'number' | 'boolean' | 'date' = 'string'): any {\n  const cleanValue = cleanSheetValue(value);\n  \n  if (cleanValue === '') {\n    return type === 'number' ? 0 : type === 'boolean' ? false : '';\n  }\n\n  switch (type) {\n    case 'number':\n      const num = parseFloat(cleanValue);\n      return isNaN(num) ? 0 : num;\n    \n    case 'boolean':\n      return cleanValue.toLowerCase() === 'true' || cleanValue === '1';\n    \n    case 'date':\n      const date = new Date(cleanValue);\n      return isNaN(date.getTime()) ? null : date;\n    \n    default:\n      return cleanValue;\n  }\n}\n\n/**\n * Formata dados para exibição\n */\nexport function formatDisplayValue(value: any, type: 'currency' | 'percentage' | 'date' | 'number' | 'text' = 'text'): string {\n  if (value === null || value === undefined || value === '') {\n    return '-';\n  }\n\n  switch (type) {\n    case 'currency':\n      const numValue = typeof value === 'number' ? value : parseFloat(value);\n      return isNaN(numValue) ? '-' : new Intl.NumberFormat('pt-BR', {\n        style: 'currency',\n        currency: 'BRL'\n      }).format(numValue);\n    \n    case 'percentage':\n      const pctValue = typeof value === 'number' ? value : parseFloat(value);\n      return isNaN(pctValue) ? '-' : `${pctValue.toFixed(1)}%`;\n    \n    case 'date':\n      const date = value instanceof Date ? value : new Date(value);\n      return isNaN(date.getTime()) ? '-' : date.toLocaleDateString('pt-BR');\n    \n    case 'number':\n      const numberValue = typeof value === 'number' ? value : parseFloat(value);\n      return isNaN(numberValue) ? '-' : new Intl.NumberFormat('pt-BR').format(numberValue);\n    \n    default:\n      return String(value);\n  }\n}\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;;;;AACM,SAAS,cAAc,IAAa;IACzC,IAAI,CAAC,QAAQ,KAAK,MAAM,KAAK,GAAG;QAC9B,OAAO,EAAE;IACX;IAEA,wCAAwC;IACxC,MAAM,UAAU,IAAI,CAAC,EAAE;IAEvB,sCAAsC;IACtC,MAAM,OAAO,KAAK,KAAK,CAAC;IAExB,OAAO,KAAK,GAAG,CAAC,CAAC;QACf,MAAM,MAA2B,CAAC;QAElC,QAAQ,OAAO,CAAC,CAAC,QAAQ;YACvB,+DAA+D;YAC/D,GAAG,CAAC,OAAO,GAAG,GAAG,CAAC,MAAM,IAAI;QAC9B;QAEA,OAAO;IACT;AACF;AAKO,SAAS,gBAAgB,GAAwB,EAAE,OAAiB;IACzE,OAAO,QAAQ,GAAG,CAAC,CAAA,SAAU,GAAG,CAAC,OAAO,IAAI;AAC9C;AAKO,SAAS,kBAAkB,IAAa;IAC7C,OAAO,MAAM,OAAO,CAAC,SAAS,KAAK,MAAM,GAAG,KAAK,MAAM,OAAO,CAAC,IAAI,CAAC,EAAE;AACxE;AAKO,SAAS,gBAAgB,KAAU;IACxC,IAAI,UAAU,QAAQ,UAAU,WAAW;QACzC,OAAO;IACT;IAEA,OAAO,OAAO,OAAO,IAAI;AAC3B;AAKO,SAAS,gBAAgB,KAAa,EAAE,OAAiD,QAAQ;IACtG,MAAM,aAAa,gBAAgB;IAEnC,IAAI,eAAe,IAAI;QACrB,OAAO,SAAS,WAAW,IAAI,SAAS,YAAY,QAAQ;IAC9D;IAEA,OAAQ;QACN,KAAK;YACH,MAAM,MAAM,WAAW;YACvB,OAAO,MAAM,OAAO,IAAI;QAE1B,KAAK;YACH,OAAO,WAAW,WAAW,OAAO,UAAU,eAAe;QAE/D,KAAK;YACH,MAAM,OAAO,IAAI,KAAK;YACtB,OAAO,MAAM,KAAK,OAAO,MAAM,OAAO;QAExC;YACE,OAAO;IACX;AACF;AAKO,SAAS,mBAAmB,KAAU,EAAE,OAA+D,MAAM;IAClH,IAAI,UAAU,QAAQ,UAAU,aAAa,UAAU,IAAI;QACzD,OAAO;IACT;IAEA,OAAQ;QACN,KAAK;YACH,MAAM,WAAW,OAAO,UAAU,WAAW,QAAQ,WAAW;YAChE,OAAO,MAAM,YAAY,MAAM,IAAI,KAAK,YAAY,CAAC,SAAS;gBAC5D,OAAO;gBACP,UAAU;YACZ,GAAG,MAAM,CAAC;QAEZ,KAAK;YACH,MAAM,WAAW,OAAO,UAAU,WAAW,QAAQ,WAAW;YAChE,OAAO,MAAM,YAAY,MAAM,GAAG,SAAS,OAAO,CAAC,GAAG,CAAC,CAAC;QAE1D,KAAK;YACH,MAAM,OAAO,iBAAiB,OAAO,QAAQ,IAAI,KAAK;YACtD,OAAO,MAAM,KAAK,OAAO,MAAM,MAAM,KAAK,kBAAkB,CAAC;QAE/D,KAAK;YACH,MAAM,cAAc,OAAO,UAAU,WAAW,QAAQ,WAAW;YACnE,OAAO,MAAM,eAAe,MAAM,IAAI,KAAK,YAAY,CAAC,SAAS,MAAM,CAAC;QAE1E;YACE,OAAO,OAAO;IAClB;AACF", "debugId": null}}, {"offset": {"line": 387, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/AI_Projects/CRMcriadores/crm-interno/components/InfluencerCard.tsx"], "sourcesContent": ["import React from 'react';\nimport Image from 'next/image';\n\ninterface InfluencerCardProps {\n  avatarUrl: string;\n  name: string;\n  username: string;\n  followers: number;\n  engagementRate: number;\n}\n\nexport default function InfluencerCard({\n  avatarUrl,\n  name,\n  username,\n  followers,\n  engagementRate\n}: InfluencerCardProps) {\n  // Função para formatar número de seguidores\n  const formatFollowers = (count: number): string => {\n    if (count >= 1000000) {\n      return `${(count / 1000000).toFixed(1)}M`;\n    } else if (count >= 1000) {\n      return `${(count / 1000).toFixed(1)}K`;\n    }\n    return count.toString();\n  };\n\n  return (\n    <div className=\"card-elevated p-6 hover:shadow-lg transition-all duration-200\">\n      {/* Avatar e informações principais */}\n      <div className=\"flex flex-col items-center text-center mb-4\">\n        <div className=\"relative w-16 h-16 mb-3\">\n          <Image\n            src={avatarUrl || '/placeholder-avatar.svg'}\n            alt={`Avatar de ${name}`}\n            fill\n            className=\"rounded-full object-cover\"\n            sizes=\"64px\"\n          />\n        </div>\n\n        <h3 className=\"text-lg font-semibold text-on-surface mb-1\">\n          {name}\n        </h3>\n\n        <p className=\"text-sm text-on-surface-variant mb-3\">\n          @{username}\n        </p>\n      </div>\n\n      {/* Métricas */}\n      <div className=\"grid grid-cols-2 gap-4 mb-4\">\n        <div className=\"text-center bg-surface-container rounded-lg p-3\">\n          <div className=\"text-xl font-bold text-primary mb-1\">\n            {formatFollowers(followers)}\n          </div>\n          <div className=\"text-xs text-on-surface-variant uppercase tracking-wide\">\n            Seguidores\n          </div>\n        </div>\n\n        <div className=\"text-center bg-surface-container rounded-lg p-3\">\n          <div className=\"text-xl font-bold text-secondary mb-1\">\n            {engagementRate.toFixed(1)}%\n          </div>\n          <div className=\"text-xs text-on-surface-variant uppercase tracking-wide\">\n            Engajamento\n          </div>\n        </div>\n      </div>\n\n      {/* Indicador de status de engajamento */}\n      <div className=\"flex items-center justify-center\">\n        <div\n          className={`w-2 h-2 rounded-full mr-2 ${\n            engagementRate >= 5\n              ? 'bg-green-500'\n              : engagementRate >= 2\n              ? 'bg-yellow-500'\n              : 'bg-red-500'\n          }`}\n        />\n        <span className=\"text-xs text-on-surface-variant\">\n          {engagementRate >= 5\n            ? 'Alto engajamento'\n            : engagementRate >= 2\n            ? 'Engajamento médio'\n            : 'Baixo engajamento'\n          }\n        </span>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AACA;;;AAUe,SAAS,eAAe,EACrC,SAAS,EACT,IAAI,EACJ,QAAQ,EACR,SAAS,EACT,cAAc,EACM;IACpB,4CAA4C;IAC5C,MAAM,kBAAkB,CAAC;QACvB,IAAI,SAAS,SAAS;YACpB,OAAO,GAAG,CAAC,QAAQ,OAAO,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC;QAC3C,OAAO,IAAI,SAAS,MAAM;YACxB,OAAO,GAAG,CAAC,QAAQ,IAAI,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC;QACxC;QACA,OAAO,MAAM,QAAQ;IACvB;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,6HAAA,CAAA,UAAK;4BACJ,KAAK,aAAa;4BAClB,KAAK,CAAC,UAAU,EAAE,MAAM;4BACxB,IAAI;4BACJ,WAAU;4BACV,OAAM;;;;;;;;;;;kCAIV,8OAAC;wBAAG,WAAU;kCACX;;;;;;kCAGH,8OAAC;wBAAE,WAAU;;4BAAuC;4BAChD;;;;;;;;;;;;;0BAKN,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACZ,gBAAgB;;;;;;0CAEnB,8OAAC;gCAAI,WAAU;0CAA0D;;;;;;;;;;;;kCAK3E,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;oCACZ,eAAe,OAAO,CAAC;oCAAG;;;;;;;0CAE7B,8OAAC;gCAAI,WAAU;0CAA0D;;;;;;;;;;;;;;;;;;0BAO7E,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBACC,WAAW,CAAC,0BAA0B,EACpC,kBAAkB,IACd,iBACA,kBAAkB,IAClB,kBACA,cACJ;;;;;;kCAEJ,8OAAC;wBAAK,WAAU;kCACb,kBAAkB,IACf,qBACA,kBAAkB,IAClB,sBACA;;;;;;;;;;;;;;;;;;AAMd", "debugId": null}}, {"offset": {"line": 552, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/AI_Projects/CRMcriadores/crm-interno/app/%28dashboard%29/influencers/page.tsx"], "sourcesContent": ["import React from 'react';\nimport { getData } from '@/app/actions/sheetsActions';\nimport { transformData } from '@/lib/utils';\nimport InfluencerCard from '@/components/InfluencerCard';\n\n// Dados de exemplo para demonstração (serão substituídos pelos dados do Google Sheets)\nconst mockInfluencers = [\n  {\n    avatarUrl: '/placeholder-avatar.svg',\n    name: '<PERSON>',\n    username: 'an<PERSON><PERSON><PERSON>',\n    followers: 125000,\n    engagementRate: 4.2\n  },\n  {\n    avatarUrl: '/placeholder-avatar.svg',\n    name: '<PERSON>',\n    username: 'carlossant<PERSON>',\n    followers: 89000,\n    engagementRate: 6.8\n  },\n  {\n    avatarUrl: '/placeholder-avatar.svg',\n    name: '<PERSON>',\n    username: 'ma<PERSON><PERSON><PERSON><PERSON>',\n    followers: 234000,\n    engagementRate: 3.1\n  }\n];\n\nexport default async function InfluencersPage() {\n  let influencers = mockInfluencers;\n\n  // Tenta buscar dados do Google Sheets, mas usa dados mock se falhar\n  try {\n    const rawData = await getData('Influencers');\n    if (rawData && rawData.length > 0) {\n      const transformedData = transformData(rawData);\n\n      // Mapeia os dados transformados para o formato esperado pelo componente\n      influencers = transformedData.map((item: any) => ({\n        avatarUrl: item.avatarUrl || '/placeholder-avatar.svg',\n        name: item.name || item.Nome || 'Nome não informado',\n        username: item.username || item.Username || 'username',\n        followers: parseInt(item.followers || item.Seguidores || '0'),\n        engagementRate: parseFloat(item.engagementRate || item.Engajamento || '0')\n      }));\n    }\n  } catch (error) {\n    console.log('Usando dados de exemplo - Google Sheets não configurado ainda');\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Stats Cards */}\n      <div className=\"grid grid-cols-1 md:grid-cols-4 gap-4\">\n        <div className=\"card-elevated p-4\">\n          <div className=\"flex items-center justify-between\">\n            <div>\n              <p className=\"text-sm text-on-surface-variant\">Total</p>\n              <p className=\"text-2xl font-bold text-on-surface\">{influencers.length}</p>\n            </div>\n            <div className=\"text-2xl\">👥</div>\n          </div>\n        </div>\n\n        <div className=\"card-elevated p-4\">\n          <div className=\"flex items-center justify-between\">\n            <div>\n              <p className=\"text-sm text-on-surface-variant\">Alto Engajamento</p>\n              <p className=\"text-2xl font-bold text-green-600\">\n                {influencers.filter(i => i.engagementRate >= 5).length}\n              </p>\n            </div>\n            <div className=\"text-2xl\">🔥</div>\n          </div>\n        </div>\n\n        <div className=\"card-elevated p-4\">\n          <div className=\"flex items-center justify-between\">\n            <div>\n              <p className=\"text-sm text-on-surface-variant\">Seguidores Totais</p>\n              <p className=\"text-2xl font-bold text-primary\">\n                {(influencers.reduce((acc, i) => acc + i.followers, 0) / 1000000).toFixed(1)}M\n              </p>\n            </div>\n            <div className=\"text-2xl\">📊</div>\n          </div>\n        </div>\n\n        <div className=\"card-elevated p-4\">\n          <div className=\"flex items-center justify-between\">\n            <div>\n              <p className=\"text-sm text-on-surface-variant\">Engajamento Médio</p>\n              <p className=\"text-2xl font-bold text-secondary\">\n                {(influencers.reduce((acc, i) => acc + i.engagementRate, 0) / influencers.length || 0).toFixed(1)}%\n              </p>\n            </div>\n            <div className=\"text-2xl\">⚡</div>\n          </div>\n        </div>\n      </div>\n\n      {/* Influencers Grid */}\n      <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\">\n        {influencers.map((influencer, index) => (\n          <InfluencerCard\n            key={index}\n            avatarUrl={influencer.avatarUrl}\n            name={influencer.name}\n            username={influencer.username}\n            followers={influencer.followers}\n            engagementRate={influencer.engagementRate}\n          />\n        ))}\n      </div>\n\n      {influencers.length === 0 && (\n        <div className=\"text-center py-12\">\n          <div className=\"text-6xl mb-4\">👥</div>\n          <h3 className=\"text-xl font-medium text-on-surface mb-2\">\n            Nenhum influenciador encontrado\n          </h3>\n          <p className=\"text-on-surface-variant mb-6\">\n            Configure o Google Sheets para ver os dados dos influenciadores.\n          </p>\n          <button className=\"btn-primary\">\n            <span className=\"mr-2\">➕</span>\n            Adicionar Primeiro Influenciador\n          </button>\n        </div>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;;;;;AAEA,uFAAuF;AACvF,MAAM,kBAAkB;IACtB;QACE,WAAW;QACX,MAAM;QACN,UAAU;QACV,WAAW;QACX,gBAAgB;IAClB;IACA;QACE,WAAW;QACX,MAAM;QACN,UAAU;QACV,WAAW;QACX,gBAAgB;IAClB;IACA;QACE,WAAW;QACX,MAAM;QACN,UAAU;QACV,WAAW;QACX,gBAAgB;IAClB;CACD;AAEc,eAAe;IAC5B,IAAI,cAAc;IAElB,oEAAoE;IACpE,IAAI;QACF,MAAM,UAAU,MAAM,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD,EAAE;QAC9B,IAAI,WAAW,QAAQ,MAAM,GAAG,GAAG;YACjC,MAAM,kBAAkB,CAAA,GAAA,4GAAA,CAAA,gBAAa,AAAD,EAAE;YAEtC,wEAAwE;YACxE,cAAc,gBAAgB,GAAG,CAAC,CAAC,OAAc,CAAC;oBAChD,WAAW,KAAK,SAAS,IAAI;oBAC7B,MAAM,KAAK,IAAI,IAAI,KAAK,IAAI,IAAI;oBAChC,UAAU,KAAK,QAAQ,IAAI,KAAK,QAAQ,IAAI;oBAC5C,WAAW,SAAS,KAAK,SAAS,IAAI,KAAK,UAAU,IAAI;oBACzD,gBAAgB,WAAW,KAAK,cAAc,IAAI,KAAK,WAAW,IAAI;gBACxE,CAAC;QACH;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,GAAG,CAAC;IACd;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;sDACC,8OAAC;4CAAE,WAAU;sDAAkC;;;;;;sDAC/C,8OAAC;4CAAE,WAAU;sDAAsC,YAAY,MAAM;;;;;;;;;;;;8CAEvE,8OAAC;oCAAI,WAAU;8CAAW;;;;;;;;;;;;;;;;;kCAI9B,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;sDACC,8OAAC;4CAAE,WAAU;sDAAkC;;;;;;sDAC/C,8OAAC;4CAAE,WAAU;sDACV,YAAY,MAAM,CAAC,CAAA,IAAK,EAAE,cAAc,IAAI,GAAG,MAAM;;;;;;;;;;;;8CAG1D,8OAAC;oCAAI,WAAU;8CAAW;;;;;;;;;;;;;;;;;kCAI9B,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;sDACC,8OAAC;4CAAE,WAAU;sDAAkC;;;;;;sDAC/C,8OAAC;4CAAE,WAAU;;gDACV,CAAC,YAAY,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,EAAE,SAAS,EAAE,KAAK,OAAO,EAAE,OAAO,CAAC;gDAAG;;;;;;;;;;;;;8CAGjF,8OAAC;oCAAI,WAAU;8CAAW;;;;;;;;;;;;;;;;;kCAI9B,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;sDACC,8OAAC;4CAAE,WAAU;sDAAkC;;;;;;sDAC/C,8OAAC;4CAAE,WAAU;;gDACV,CAAC,YAAY,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,EAAE,cAAc,EAAE,KAAK,YAAY,MAAM,IAAI,CAAC,EAAE,OAAO,CAAC;gDAAG;;;;;;;;;;;;;8CAGtG,8OAAC;oCAAI,WAAU;8CAAW;;;;;;;;;;;;;;;;;;;;;;;0BAMhC,8OAAC;gBAAI,WAAU;0BACZ,YAAY,GAAG,CAAC,CAAC,YAAY,sBAC5B,8OAAC,6HAAA,CAAA,UAAc;wBAEb,WAAW,WAAW,SAAS;wBAC/B,MAAM,WAAW,IAAI;wBACrB,UAAU,WAAW,QAAQ;wBAC7B,WAAW,WAAW,SAAS;wBAC/B,gBAAgB,WAAW,cAAc;uBALpC;;;;;;;;;;YAUV,YAAY,MAAM,KAAK,mBACtB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCAAgB;;;;;;kCAC/B,8OAAC;wBAAG,WAAU;kCAA2C;;;;;;kCAGzD,8OAAC;wBAAE,WAAU;kCAA+B;;;;;;kCAG5C,8OAAC;wBAAO,WAAU;;0CAChB,8OAAC;gCAAK,WAAU;0CAAO;;;;;;4BAAQ;;;;;;;;;;;;;;;;;;;AAO3C", "debugId": null}}]}
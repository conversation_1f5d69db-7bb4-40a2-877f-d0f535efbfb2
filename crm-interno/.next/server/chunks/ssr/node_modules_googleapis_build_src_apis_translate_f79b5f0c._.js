module.exports = {

"[project]/node_modules/googleapis/build/src/apis/translate/v2.js [app-rsc] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
// Copyright 2020 Google LLC
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//    http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.translate_v2 = void 0;
/* eslint-disable @typescript-eslint/no-explicit-any */ /* eslint-disable @typescript-eslint/no-unused-vars */ /* eslint-disable @typescript-eslint/no-empty-interface */ /* eslint-disable @typescript-eslint/no-namespace */ /* eslint-disable no-irregular-whitespace */ const googleapis_common_1 = __turbopack_context__.r("[project]/node_modules/googleapis-common/build/src/index.js [app-rsc] (ecmascript)");
var translate_v2;
(function(translate_v2) {
    /**
     * Google Cloud Translation API
     *
     * The Google Cloud Translation API lets websites and programs integrate with
     *     Google Translate programmatically.
     *
     * @example
     * ```js
     * const {google} = require('googleapis');
     * const translate = google.translate('v2');
     * ```
     */ class Translate {
        context;
        detections;
        languages;
        translations;
        constructor(options, google){
            this.context = {
                _options: options || {},
                google
            };
            this.detections = new Resource$Detections(this.context);
            this.languages = new Resource$Languages(this.context);
            this.translations = new Resource$Translations(this.context);
        }
    }
    translate_v2.Translate = Translate;
    class Resource$Detections {
        context;
        constructor(context){
            this.context = context;
        }
        detect(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://translation.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/language/translate/v2/detect').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'POST',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [],
                pathParams: [],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        list(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://translation.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/language/translate/v2/detect').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'GET',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'q'
                ],
                pathParams: [],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
    }
    translate_v2.Resource$Detections = Resource$Detections;
    class Resource$Languages {
        context;
        constructor(context){
            this.context = context;
        }
        list(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://translation.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/language/translate/v2/languages').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'GET',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [],
                pathParams: [],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
    }
    translate_v2.Resource$Languages = Resource$Languages;
    class Resource$Translations {
        context;
        constructor(context){
            this.context = context;
        }
        list(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://translation.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/language/translate/v2').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'GET',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'q',
                    'target'
                ],
                pathParams: [],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        translate(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://translation.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/language/translate/v2').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'POST',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [],
                pathParams: [],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
    }
    translate_v2.Resource$Translations = Resource$Translations;
})(translate_v2 || (exports.translate_v2 = translate_v2 = {}));
}}),
"[project]/node_modules/googleapis/build/src/apis/translate/v3.js [app-rsc] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
// Copyright 2020 Google LLC
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//    http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.translate_v3 = void 0;
/* eslint-disable @typescript-eslint/no-explicit-any */ /* eslint-disable @typescript-eslint/no-unused-vars */ /* eslint-disable @typescript-eslint/no-empty-interface */ /* eslint-disable @typescript-eslint/no-namespace */ /* eslint-disable no-irregular-whitespace */ const googleapis_common_1 = __turbopack_context__.r("[project]/node_modules/googleapis-common/build/src/index.js [app-rsc] (ecmascript)");
var translate_v3;
(function(translate_v3) {
    /**
     * Cloud Translation API
     *
     * Integrates text translation into your website or application.
     *
     * @example
     * ```js
     * const {google} = require('googleapis');
     * const translate = google.translate('v3');
     * ```
     */ class Translate {
        context;
        projects;
        constructor(options, google){
            this.context = {
                _options: options || {},
                google
            };
            this.projects = new Resource$Projects(this.context);
        }
    }
    translate_v3.Translate = Translate;
    class Resource$Projects {
        context;
        locations;
        constructor(context){
            this.context = context;
            this.locations = new Resource$Projects$Locations(this.context);
        }
        detectLanguage(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://translation.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v3/{+parent}:detectLanguage').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'POST',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'parent'
                ],
                pathParams: [
                    'parent'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        getSupportedLanguages(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://translation.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v3/{+parent}/supportedLanguages').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'GET',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'parent'
                ],
                pathParams: [
                    'parent'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        romanizeText(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://translation.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v3/{+parent}:romanizeText').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'POST',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'parent'
                ],
                pathParams: [
                    'parent'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        translateText(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://translation.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v3/{+parent}:translateText').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'POST',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'parent'
                ],
                pathParams: [
                    'parent'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
    }
    translate_v3.Resource$Projects = Resource$Projects;
    class Resource$Projects$Locations {
        context;
        adaptiveMtDatasets;
        datasets;
        glossaries;
        models;
        operations;
        constructor(context){
            this.context = context;
            this.adaptiveMtDatasets = new Resource$Projects$Locations$Adaptivemtdatasets(this.context);
            this.datasets = new Resource$Projects$Locations$Datasets(this.context);
            this.glossaries = new Resource$Projects$Locations$Glossaries(this.context);
            this.models = new Resource$Projects$Locations$Models(this.context);
            this.operations = new Resource$Projects$Locations$Operations(this.context);
        }
        adaptiveMtTranslate(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://translation.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v3/{+parent}:adaptiveMtTranslate').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'POST',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'parent'
                ],
                pathParams: [
                    'parent'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        batchTranslateDocument(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://translation.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v3/{+parent}:batchTranslateDocument').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'POST',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'parent'
                ],
                pathParams: [
                    'parent'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        batchTranslateText(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://translation.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v3/{+parent}:batchTranslateText').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'POST',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'parent'
                ],
                pathParams: [
                    'parent'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        detectLanguage(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://translation.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v3/{+parent}:detectLanguage').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'POST',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'parent'
                ],
                pathParams: [
                    'parent'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        get(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://translation.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v3/{+name}').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'GET',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'name'
                ],
                pathParams: [
                    'name'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        getSupportedLanguages(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://translation.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v3/{+parent}/supportedLanguages').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'GET',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'parent'
                ],
                pathParams: [
                    'parent'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        list(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://translation.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v3/{+name}/locations').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'GET',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'name'
                ],
                pathParams: [
                    'name'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        romanizeText(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://translation.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v3/{+parent}:romanizeText').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'POST',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'parent'
                ],
                pathParams: [
                    'parent'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        translateDocument(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://translation.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v3/{+parent}:translateDocument').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'POST',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'parent'
                ],
                pathParams: [
                    'parent'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        translateText(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://translation.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v3/{+parent}:translateText').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'POST',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'parent'
                ],
                pathParams: [
                    'parent'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
    }
    translate_v3.Resource$Projects$Locations = Resource$Projects$Locations;
    class Resource$Projects$Locations$Adaptivemtdatasets {
        context;
        adaptiveMtFiles;
        adaptiveMtSentences;
        constructor(context){
            this.context = context;
            this.adaptiveMtFiles = new Resource$Projects$Locations$Adaptivemtdatasets$Adaptivemtfiles(this.context);
            this.adaptiveMtSentences = new Resource$Projects$Locations$Adaptivemtdatasets$Adaptivemtsentences(this.context);
        }
        create(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://translation.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v3/{+parent}/adaptiveMtDatasets').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'POST',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'parent'
                ],
                pathParams: [
                    'parent'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        delete(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://translation.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v3/{+name}').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'DELETE',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'name'
                ],
                pathParams: [
                    'name'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        get(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://translation.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v3/{+name}').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'GET',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'name'
                ],
                pathParams: [
                    'name'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        importAdaptiveMtFile(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://translation.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v3/{+parent}:importAdaptiveMtFile').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'POST',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'parent'
                ],
                pathParams: [
                    'parent'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        list(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://translation.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v3/{+parent}/adaptiveMtDatasets').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'GET',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'parent'
                ],
                pathParams: [
                    'parent'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
    }
    translate_v3.Resource$Projects$Locations$Adaptivemtdatasets = Resource$Projects$Locations$Adaptivemtdatasets;
    class Resource$Projects$Locations$Adaptivemtdatasets$Adaptivemtfiles {
        context;
        adaptiveMtSentences;
        constructor(context){
            this.context = context;
            this.adaptiveMtSentences = new Resource$Projects$Locations$Adaptivemtdatasets$Adaptivemtfiles$Adaptivemtsentences(this.context);
        }
        delete(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://translation.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v3/{+name}').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'DELETE',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'name'
                ],
                pathParams: [
                    'name'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        get(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://translation.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v3/{+name}').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'GET',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'name'
                ],
                pathParams: [
                    'name'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        list(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://translation.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v3/{+parent}/adaptiveMtFiles').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'GET',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'parent'
                ],
                pathParams: [
                    'parent'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
    }
    translate_v3.Resource$Projects$Locations$Adaptivemtdatasets$Adaptivemtfiles = Resource$Projects$Locations$Adaptivemtdatasets$Adaptivemtfiles;
    class Resource$Projects$Locations$Adaptivemtdatasets$Adaptivemtfiles$Adaptivemtsentences {
        context;
        constructor(context){
            this.context = context;
        }
        list(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://translation.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v3/{+parent}/adaptiveMtSentences').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'GET',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'parent'
                ],
                pathParams: [
                    'parent'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
    }
    translate_v3.Resource$Projects$Locations$Adaptivemtdatasets$Adaptivemtfiles$Adaptivemtsentences = Resource$Projects$Locations$Adaptivemtdatasets$Adaptivemtfiles$Adaptivemtsentences;
    class Resource$Projects$Locations$Adaptivemtdatasets$Adaptivemtsentences {
        context;
        constructor(context){
            this.context = context;
        }
        list(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://translation.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v3/{+parent}/adaptiveMtSentences').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'GET',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'parent'
                ],
                pathParams: [
                    'parent'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
    }
    translate_v3.Resource$Projects$Locations$Adaptivemtdatasets$Adaptivemtsentences = Resource$Projects$Locations$Adaptivemtdatasets$Adaptivemtsentences;
    class Resource$Projects$Locations$Datasets {
        context;
        examples;
        constructor(context){
            this.context = context;
            this.examples = new Resource$Projects$Locations$Datasets$Examples(this.context);
        }
        create(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://translation.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v3/{+parent}/datasets').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'POST',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'parent'
                ],
                pathParams: [
                    'parent'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        delete(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://translation.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v3/{+name}').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'DELETE',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'name'
                ],
                pathParams: [
                    'name'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        exportData(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://translation.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v3/{+dataset}:exportData').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'POST',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'dataset'
                ],
                pathParams: [
                    'dataset'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        get(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://translation.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v3/{+name}').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'GET',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'name'
                ],
                pathParams: [
                    'name'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        importData(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://translation.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v3/{+dataset}:importData').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'POST',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'dataset'
                ],
                pathParams: [
                    'dataset'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        list(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://translation.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v3/{+parent}/datasets').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'GET',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'parent'
                ],
                pathParams: [
                    'parent'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
    }
    translate_v3.Resource$Projects$Locations$Datasets = Resource$Projects$Locations$Datasets;
    class Resource$Projects$Locations$Datasets$Examples {
        context;
        constructor(context){
            this.context = context;
        }
        list(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://translation.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v3/{+parent}/examples').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'GET',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'parent'
                ],
                pathParams: [
                    'parent'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
    }
    translate_v3.Resource$Projects$Locations$Datasets$Examples = Resource$Projects$Locations$Datasets$Examples;
    class Resource$Projects$Locations$Glossaries {
        context;
        glossaryEntries;
        constructor(context){
            this.context = context;
            this.glossaryEntries = new Resource$Projects$Locations$Glossaries$Glossaryentries(this.context);
        }
        create(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://translation.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v3/{+parent}/glossaries').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'POST',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'parent'
                ],
                pathParams: [
                    'parent'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        delete(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://translation.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v3/{+name}').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'DELETE',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'name'
                ],
                pathParams: [
                    'name'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        get(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://translation.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v3/{+name}').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'GET',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'name'
                ],
                pathParams: [
                    'name'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        list(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://translation.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v3/{+parent}/glossaries').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'GET',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'parent'
                ],
                pathParams: [
                    'parent'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        patch(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://translation.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v3/{+name}').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'PATCH',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'name'
                ],
                pathParams: [
                    'name'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
    }
    translate_v3.Resource$Projects$Locations$Glossaries = Resource$Projects$Locations$Glossaries;
    class Resource$Projects$Locations$Glossaries$Glossaryentries {
        context;
        constructor(context){
            this.context = context;
        }
        create(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://translation.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v3/{+parent}/glossaryEntries').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'POST',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'parent'
                ],
                pathParams: [
                    'parent'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        delete(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://translation.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v3/{+name}').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'DELETE',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'name'
                ],
                pathParams: [
                    'name'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        get(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://translation.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v3/{+name}').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'GET',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'name'
                ],
                pathParams: [
                    'name'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        list(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://translation.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v3/{+parent}/glossaryEntries').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'GET',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'parent'
                ],
                pathParams: [
                    'parent'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        patch(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://translation.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v3/{+name}').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'PATCH',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'name'
                ],
                pathParams: [
                    'name'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
    }
    translate_v3.Resource$Projects$Locations$Glossaries$Glossaryentries = Resource$Projects$Locations$Glossaries$Glossaryentries;
    class Resource$Projects$Locations$Models {
        context;
        constructor(context){
            this.context = context;
        }
        create(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://translation.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v3/{+parent}/models').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'POST',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'parent'
                ],
                pathParams: [
                    'parent'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        delete(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://translation.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v3/{+name}').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'DELETE',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'name'
                ],
                pathParams: [
                    'name'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        get(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://translation.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v3/{+name}').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'GET',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'name'
                ],
                pathParams: [
                    'name'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        list(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://translation.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v3/{+parent}/models').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'GET',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'parent'
                ],
                pathParams: [
                    'parent'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
    }
    translate_v3.Resource$Projects$Locations$Models = Resource$Projects$Locations$Models;
    class Resource$Projects$Locations$Operations {
        context;
        constructor(context){
            this.context = context;
        }
        cancel(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://translation.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v3/{+name}:cancel').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'POST',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'name'
                ],
                pathParams: [
                    'name'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        delete(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://translation.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v3/{+name}').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'DELETE',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'name'
                ],
                pathParams: [
                    'name'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        get(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://translation.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v3/{+name}').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'GET',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'name'
                ],
                pathParams: [
                    'name'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        list(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://translation.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v3/{+name}/operations').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'GET',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'name'
                ],
                pathParams: [
                    'name'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        wait(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://translation.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v3/{+name}:wait').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'POST',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'name'
                ],
                pathParams: [
                    'name'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
    }
    translate_v3.Resource$Projects$Locations$Operations = Resource$Projects$Locations$Operations;
})(translate_v3 || (exports.translate_v3 = translate_v3 = {}));
}}),
"[project]/node_modules/googleapis/build/src/apis/translate/v3beta1.js [app-rsc] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
// Copyright 2020 Google LLC
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//    http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.translate_v3beta1 = void 0;
/* eslint-disable @typescript-eslint/no-explicit-any */ /* eslint-disable @typescript-eslint/no-unused-vars */ /* eslint-disable @typescript-eslint/no-empty-interface */ /* eslint-disable @typescript-eslint/no-namespace */ /* eslint-disable no-irregular-whitespace */ const googleapis_common_1 = __turbopack_context__.r("[project]/node_modules/googleapis-common/build/src/index.js [app-rsc] (ecmascript)");
var translate_v3beta1;
(function(translate_v3beta1) {
    /**
     * Cloud Translation API
     *
     * Integrates text translation into your website or application.
     *
     * @example
     * ```js
     * const {google} = require('googleapis');
     * const translate = google.translate('v3beta1');
     * ```
     */ class Translate {
        context;
        projects;
        constructor(options, google){
            this.context = {
                _options: options || {},
                google
            };
            this.projects = new Resource$Projects(this.context);
        }
    }
    translate_v3beta1.Translate = Translate;
    class Resource$Projects {
        context;
        locations;
        constructor(context){
            this.context = context;
            this.locations = new Resource$Projects$Locations(this.context);
        }
        detectLanguage(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://translation.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v3beta1/{+parent}:detectLanguage').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'POST',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'parent'
                ],
                pathParams: [
                    'parent'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        getSupportedLanguages(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://translation.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v3beta1/{+parent}/supportedLanguages').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'GET',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'parent'
                ],
                pathParams: [
                    'parent'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        translateText(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://translation.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v3beta1/{+parent}:translateText').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'POST',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'parent'
                ],
                pathParams: [
                    'parent'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
    }
    translate_v3beta1.Resource$Projects = Resource$Projects;
    class Resource$Projects$Locations {
        context;
        glossaries;
        operations;
        constructor(context){
            this.context = context;
            this.glossaries = new Resource$Projects$Locations$Glossaries(this.context);
            this.operations = new Resource$Projects$Locations$Operations(this.context);
        }
        batchTranslateDocument(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://translation.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v3beta1/{+parent}:batchTranslateDocument').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'POST',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'parent'
                ],
                pathParams: [
                    'parent'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        batchTranslateText(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://translation.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v3beta1/{+parent}:batchTranslateText').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'POST',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'parent'
                ],
                pathParams: [
                    'parent'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        detectLanguage(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://translation.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v3beta1/{+parent}:detectLanguage').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'POST',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'parent'
                ],
                pathParams: [
                    'parent'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        get(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://translation.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v3beta1/{+name}').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'GET',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'name'
                ],
                pathParams: [
                    'name'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        getSupportedLanguages(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://translation.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v3beta1/{+parent}/supportedLanguages').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'GET',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'parent'
                ],
                pathParams: [
                    'parent'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        list(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://translation.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v3beta1/{+name}/locations').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'GET',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'name'
                ],
                pathParams: [
                    'name'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        translateDocument(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://translation.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v3beta1/{+parent}:translateDocument').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'POST',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'parent'
                ],
                pathParams: [
                    'parent'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        translateText(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://translation.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v3beta1/{+parent}:translateText').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'POST',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'parent'
                ],
                pathParams: [
                    'parent'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
    }
    translate_v3beta1.Resource$Projects$Locations = Resource$Projects$Locations;
    class Resource$Projects$Locations$Glossaries {
        context;
        constructor(context){
            this.context = context;
        }
        create(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://translation.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v3beta1/{+parent}/glossaries').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'POST',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'parent'
                ],
                pathParams: [
                    'parent'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        delete(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://translation.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v3beta1/{+name}').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'DELETE',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'name'
                ],
                pathParams: [
                    'name'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        get(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://translation.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v3beta1/{+name}').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'GET',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'name'
                ],
                pathParams: [
                    'name'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        list(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://translation.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v3beta1/{+parent}/glossaries').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'GET',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'parent'
                ],
                pathParams: [
                    'parent'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
    }
    translate_v3beta1.Resource$Projects$Locations$Glossaries = Resource$Projects$Locations$Glossaries;
    class Resource$Projects$Locations$Operations {
        context;
        constructor(context){
            this.context = context;
        }
        cancel(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://translation.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v3beta1/{+name}:cancel').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'POST',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'name'
                ],
                pathParams: [
                    'name'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        delete(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://translation.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v3beta1/{+name}').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'DELETE',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'name'
                ],
                pathParams: [
                    'name'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        get(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://translation.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v3beta1/{+name}').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'GET',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'name'
                ],
                pathParams: [
                    'name'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        list(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://translation.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v3beta1/{+name}/operations').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'GET',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'name'
                ],
                pathParams: [
                    'name'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
        wait(paramsOrCallback, optionsOrCallback, callback) {
            let params = paramsOrCallback || {};
            let options = optionsOrCallback || {};
            if (typeof paramsOrCallback === 'function') {
                callback = paramsOrCallback;
                params = {};
                options = {};
            }
            if (typeof optionsOrCallback === 'function') {
                callback = optionsOrCallback;
                options = {};
            }
            const rootUrl = options.rootUrl || 'https://translation.googleapis.com/';
            const parameters = {
                options: Object.assign({
                    url: (rootUrl + '/v3beta1/{+name}:wait').replace(/([^:]\/)\/+/g, '$1'),
                    method: 'POST',
                    apiVersion: ''
                }, options),
                params,
                requiredParams: [
                    'name'
                ],
                pathParams: [
                    'name'
                ],
                context: this.context
            };
            if (callback) {
                (0, googleapis_common_1.createAPIRequest)(parameters, callback);
            } else {
                return (0, googleapis_common_1.createAPIRequest)(parameters);
            }
        }
    }
    translate_v3beta1.Resource$Projects$Locations$Operations = Resource$Projects$Locations$Operations;
})(translate_v3beta1 || (exports.translate_v3beta1 = translate_v3beta1 = {}));
}}),
"[project]/node_modules/googleapis/build/src/apis/translate/index.js [app-rsc] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
// Copyright 2020 Google LLC
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//    http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.AuthPlus = exports.translate_v3beta1 = exports.translate_v3 = exports.translate_v2 = exports.auth = exports.VERSIONS = void 0;
exports.translate = translate;
/*! THIS FILE IS AUTO-GENERATED */ const googleapis_common_1 = __turbopack_context__.r("[project]/node_modules/googleapis-common/build/src/index.js [app-rsc] (ecmascript)");
const v2_1 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/translate/v2.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "translate_v2", {
    enumerable: true,
    get: function() {
        return v2_1.translate_v2;
    }
});
const v3_1 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/translate/v3.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "translate_v3", {
    enumerable: true,
    get: function() {
        return v3_1.translate_v3;
    }
});
const v3beta1_1 = __turbopack_context__.r("[project]/node_modules/googleapis/build/src/apis/translate/v3beta1.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "translate_v3beta1", {
    enumerable: true,
    get: function() {
        return v3beta1_1.translate_v3beta1;
    }
});
exports.VERSIONS = {
    v2: v2_1.translate_v2.Translate,
    v3: v3_1.translate_v3.Translate,
    v3beta1: v3beta1_1.translate_v3beta1.Translate
};
function translate(versionOrOptions) {
    return (0, googleapis_common_1.getAPI)('translate', versionOrOptions, exports.VERSIONS, this);
}
const auth = new googleapis_common_1.AuthPlus();
exports.auth = auth;
var googleapis_common_2 = __turbopack_context__.r("[project]/node_modules/googleapis-common/build/src/index.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "AuthPlus", {
    enumerable: true,
    get: function() {
        return googleapis_common_2.AuthPlus;
    }
});
}}),

};

//# sourceMappingURL=node_modules_googleapis_build_src_apis_translate_f79b5f0c._.js.map
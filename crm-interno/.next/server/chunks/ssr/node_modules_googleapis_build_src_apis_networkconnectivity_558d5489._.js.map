{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/AI_Projects/CRMcriadores/crm-interno/node_modules/googleapis/build/src/apis/networkconnectivity/v1.js"], "sourcesContent": ["\"use strict\";\n// Copyright 2020 Google LLC\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//    http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.networkconnectivity_v1 = void 0;\n/* eslint-disable @typescript-eslint/no-explicit-any */\n/* eslint-disable @typescript-eslint/no-unused-vars */\n/* eslint-disable @typescript-eslint/no-empty-interface */\n/* eslint-disable @typescript-eslint/no-namespace */\n/* eslint-disable no-irregular-whitespace */\nconst googleapis_common_1 = require(\"googleapis-common\");\nvar networkconnectivity_v1;\n(function (networkconnectivity_v1) {\n    /**\n     * Network Connectivity API\n     *\n     * This API enables connectivity with and between Google Cloud resources.\n     *\n     * @example\n     * ```js\n     * const {google} = require('googleapis');\n     * const networkconnectivity = google.networkconnectivity('v1');\n     * ```\n     */\n    class Networkconnectivity {\n        context;\n        projects;\n        constructor(options, google) {\n            this.context = {\n                _options: options || {},\n                google,\n            };\n            this.projects = new Resource$Projects(this.context);\n        }\n    }\n    networkconnectivity_v1.Networkconnectivity = Networkconnectivity;\n    class Resource$Projects {\n        context;\n        locations;\n        constructor(context) {\n            this.context = context;\n            this.locations = new Resource$Projects$Locations(this.context);\n        }\n    }\n    networkconnectivity_v1.Resource$Projects = Resource$Projects;\n    class Resource$Projects$Locations {\n        context;\n        global;\n        internalRanges;\n        operations;\n        regionalEndpoints;\n        serviceClasses;\n        serviceConnectionMaps;\n        serviceConnectionPolicies;\n        serviceConnectionTokens;\n        spokes;\n        constructor(context) {\n            this.context = context;\n            this.global = new Resource$Projects$Locations$Global(this.context);\n            this.internalRanges = new Resource$Projects$Locations$Internalranges(this.context);\n            this.operations = new Resource$Projects$Locations$Operations(this.context);\n            this.regionalEndpoints =\n                new Resource$Projects$Locations$Regionalendpoints(this.context);\n            this.serviceClasses = new Resource$Projects$Locations$Serviceclasses(this.context);\n            this.serviceConnectionMaps =\n                new Resource$Projects$Locations$Serviceconnectionmaps(this.context);\n            this.serviceConnectionPolicies =\n                new Resource$Projects$Locations$Serviceconnectionpolicies(this.context);\n            this.serviceConnectionTokens =\n                new Resource$Projects$Locations$Serviceconnectiontokens(this.context);\n            this.spokes = new Resource$Projects$Locations$Spokes(this.context);\n        }\n        get(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://networkconnectivity.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1/{+name}').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'GET',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['name'],\n                pathParams: ['name'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        list(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://networkconnectivity.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1/{+name}/locations').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'GET',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['name'],\n                pathParams: ['name'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n    }\n    networkconnectivity_v1.Resource$Projects$Locations = Resource$Projects$Locations;\n    class Resource$Projects$Locations$Global {\n        context;\n        hubs;\n        policyBasedRoutes;\n        constructor(context) {\n            this.context = context;\n            this.hubs = new Resource$Projects$Locations$Global$Hubs(this.context);\n            this.policyBasedRoutes =\n                new Resource$Projects$Locations$Global$Policybasedroutes(this.context);\n        }\n    }\n    networkconnectivity_v1.Resource$Projects$Locations$Global = Resource$Projects$Locations$Global;\n    class Resource$Projects$Locations$Global$Hubs {\n        context;\n        groups;\n        routeTables;\n        constructor(context) {\n            this.context = context;\n            this.groups = new Resource$Projects$Locations$Global$Hubs$Groups(this.context);\n            this.routeTables =\n                new Resource$Projects$Locations$Global$Hubs$Routetables(this.context);\n        }\n        acceptSpoke(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params =\n                    {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://networkconnectivity.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1/{+name}:acceptSpoke').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'POST',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['name'],\n                pathParams: ['name'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        acceptSpokeUpdate(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params =\n                    {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://networkconnectivity.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1/{+name}:acceptSpokeUpdate').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'POST',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['name'],\n                pathParams: ['name'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        create(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://networkconnectivity.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1/{+parent}/hubs').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'POST',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['parent'],\n                pathParams: ['parent'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        delete(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://networkconnectivity.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1/{+name}').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'DELETE',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['name'],\n                pathParams: ['name'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        get(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://networkconnectivity.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1/{+name}').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'GET',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['name'],\n                pathParams: ['name'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        getIamPolicy(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params =\n                    {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://networkconnectivity.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1/{+resource}:getIamPolicy').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'GET',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['resource'],\n                pathParams: ['resource'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        list(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://networkconnectivity.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1/{+parent}/hubs').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'GET',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['parent'],\n                pathParams: ['parent'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        listSpokes(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params =\n                    {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://networkconnectivity.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1/{+name}:listSpokes').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'GET',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['name'],\n                pathParams: ['name'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        patch(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://networkconnectivity.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1/{+name}').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'PATCH',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['name'],\n                pathParams: ['name'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        queryStatus(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params =\n                    {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://networkconnectivity.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1/{+name}:queryStatus').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'GET',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['name'],\n                pathParams: ['name'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        rejectSpoke(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params =\n                    {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://networkconnectivity.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1/{+name}:rejectSpoke').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'POST',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['name'],\n                pathParams: ['name'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        rejectSpokeUpdate(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params =\n                    {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://networkconnectivity.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1/{+name}:rejectSpokeUpdate').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'POST',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['name'],\n                pathParams: ['name'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        setIamPolicy(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params =\n                    {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://networkconnectivity.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1/{+resource}:setIamPolicy').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'POST',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['resource'],\n                pathParams: ['resource'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        testIamPermissions(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params =\n                    {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://networkconnectivity.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1/{+resource}:testIamPermissions').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'POST',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['resource'],\n                pathParams: ['resource'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n    }\n    networkconnectivity_v1.Resource$Projects$Locations$Global$Hubs = Resource$Projects$Locations$Global$Hubs;\n    class Resource$Projects$Locations$Global$Hubs$Groups {\n        context;\n        constructor(context) {\n            this.context = context;\n        }\n        get(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params =\n                    {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://networkconnectivity.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1/{+name}').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'GET',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['name'],\n                pathParams: ['name'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        getIamPolicy(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params =\n                    {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://networkconnectivity.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1/{+resource}:getIamPolicy').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'GET',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['resource'],\n                pathParams: ['resource'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        list(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params =\n                    {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://networkconnectivity.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1/{+parent}/groups').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'GET',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['parent'],\n                pathParams: ['parent'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        patch(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params =\n                    {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://networkconnectivity.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1/{+name}').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'PATCH',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['name'],\n                pathParams: ['name'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        setIamPolicy(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params =\n                    {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://networkconnectivity.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1/{+resource}:setIamPolicy').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'POST',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['resource'],\n                pathParams: ['resource'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        testIamPermissions(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params =\n                    {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://networkconnectivity.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1/{+resource}:testIamPermissions').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'POST',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['resource'],\n                pathParams: ['resource'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n    }\n    networkconnectivity_v1.Resource$Projects$Locations$Global$Hubs$Groups = Resource$Projects$Locations$Global$Hubs$Groups;\n    class Resource$Projects$Locations$Global$Hubs$Routetables {\n        context;\n        routes;\n        constructor(context) {\n            this.context = context;\n            this.routes =\n                new Resource$Projects$Locations$Global$Hubs$Routetables$Routes(this.context);\n        }\n        get(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params =\n                    {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://networkconnectivity.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1/{+name}').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'GET',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['name'],\n                pathParams: ['name'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        list(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params =\n                    {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://networkconnectivity.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1/{+parent}/routeTables').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'GET',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['parent'],\n                pathParams: ['parent'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n    }\n    networkconnectivity_v1.Resource$Projects$Locations$Global$Hubs$Routetables = Resource$Projects$Locations$Global$Hubs$Routetables;\n    class Resource$Projects$Locations$Global$Hubs$Routetables$Routes {\n        context;\n        constructor(context) {\n            this.context = context;\n        }\n        get(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params =\n                    {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://networkconnectivity.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1/{+name}').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'GET',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['name'],\n                pathParams: ['name'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        list(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params =\n                    {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://networkconnectivity.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1/{+parent}/routes').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'GET',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['parent'],\n                pathParams: ['parent'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n    }\n    networkconnectivity_v1.Resource$Projects$Locations$Global$Hubs$Routetables$Routes = Resource$Projects$Locations$Global$Hubs$Routetables$Routes;\n    class Resource$Projects$Locations$Global$Policybasedroutes {\n        context;\n        constructor(context) {\n            this.context = context;\n        }\n        create(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params =\n                    {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://networkconnectivity.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1/{+parent}/policyBasedRoutes').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'POST',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['parent'],\n                pathParams: ['parent'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        delete(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params =\n                    {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://networkconnectivity.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1/{+name}').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'DELETE',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['name'],\n                pathParams: ['name'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        get(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params =\n                    {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://networkconnectivity.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1/{+name}').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'GET',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['name'],\n                pathParams: ['name'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        getIamPolicy(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params =\n                    {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://networkconnectivity.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1/{+resource}:getIamPolicy').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'GET',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['resource'],\n                pathParams: ['resource'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        list(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params =\n                    {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://networkconnectivity.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1/{+parent}/policyBasedRoutes').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'GET',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['parent'],\n                pathParams: ['parent'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        setIamPolicy(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params =\n                    {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://networkconnectivity.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1/{+resource}:setIamPolicy').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'POST',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['resource'],\n                pathParams: ['resource'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        testIamPermissions(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params =\n                    {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://networkconnectivity.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1/{+resource}:testIamPermissions').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'POST',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['resource'],\n                pathParams: ['resource'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n    }\n    networkconnectivity_v1.Resource$Projects$Locations$Global$Policybasedroutes = Resource$Projects$Locations$Global$Policybasedroutes;\n    class Resource$Projects$Locations$Internalranges {\n        context;\n        constructor(context) {\n            this.context = context;\n        }\n        create(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://networkconnectivity.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1/{+parent}/internalRanges').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'POST',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['parent'],\n                pathParams: ['parent'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        delete(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://networkconnectivity.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1/{+name}').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'DELETE',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['name'],\n                pathParams: ['name'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        get(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://networkconnectivity.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1/{+name}').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'GET',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['name'],\n                pathParams: ['name'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        getIamPolicy(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params =\n                    {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://networkconnectivity.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1/{+resource}:getIamPolicy').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'GET',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['resource'],\n                pathParams: ['resource'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        list(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://networkconnectivity.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1/{+parent}/internalRanges').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'GET',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['parent'],\n                pathParams: ['parent'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        patch(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://networkconnectivity.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1/{+name}').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'PATCH',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['name'],\n                pathParams: ['name'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        setIamPolicy(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params =\n                    {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://networkconnectivity.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1/{+resource}:setIamPolicy').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'POST',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['resource'],\n                pathParams: ['resource'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        testIamPermissions(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params =\n                    {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://networkconnectivity.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1/{+resource}:testIamPermissions').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'POST',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['resource'],\n                pathParams: ['resource'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n    }\n    networkconnectivity_v1.Resource$Projects$Locations$Internalranges = Resource$Projects$Locations$Internalranges;\n    class Resource$Projects$Locations$Operations {\n        context;\n        constructor(context) {\n            this.context = context;\n        }\n        cancel(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://networkconnectivity.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1/{+name}:cancel').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'POST',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['name'],\n                pathParams: ['name'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        delete(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://networkconnectivity.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1/{+name}').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'DELETE',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['name'],\n                pathParams: ['name'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        get(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://networkconnectivity.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1/{+name}').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'GET',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['name'],\n                pathParams: ['name'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        list(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://networkconnectivity.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1/{+name}/operations').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'GET',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['name'],\n                pathParams: ['name'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n    }\n    networkconnectivity_v1.Resource$Projects$Locations$Operations = Resource$Projects$Locations$Operations;\n    class Resource$Projects$Locations$Regionalendpoints {\n        context;\n        constructor(context) {\n            this.context = context;\n        }\n        create(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params =\n                    {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://networkconnectivity.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1/{+parent}/regionalEndpoints').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'POST',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['parent'],\n                pathParams: ['parent'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        delete(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params =\n                    {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://networkconnectivity.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1/{+name}').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'DELETE',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['name'],\n                pathParams: ['name'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        get(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://networkconnectivity.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1/{+name}').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'GET',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['name'],\n                pathParams: ['name'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        list(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params =\n                    {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://networkconnectivity.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1/{+parent}/regionalEndpoints').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'GET',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['parent'],\n                pathParams: ['parent'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n    }\n    networkconnectivity_v1.Resource$Projects$Locations$Regionalendpoints = Resource$Projects$Locations$Regionalendpoints;\n    class Resource$Projects$Locations$Serviceclasses {\n        context;\n        constructor(context) {\n            this.context = context;\n        }\n        delete(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://networkconnectivity.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1/{+name}').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'DELETE',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['name'],\n                pathParams: ['name'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        get(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://networkconnectivity.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1/{+name}').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'GET',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['name'],\n                pathParams: ['name'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        getIamPolicy(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params =\n                    {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://networkconnectivity.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1/{+resource}:getIamPolicy').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'GET',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['resource'],\n                pathParams: ['resource'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        list(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://networkconnectivity.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1/{+parent}/serviceClasses').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'GET',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['parent'],\n                pathParams: ['parent'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        patch(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://networkconnectivity.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1/{+name}').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'PATCH',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['name'],\n                pathParams: ['name'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        setIamPolicy(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params =\n                    {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://networkconnectivity.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1/{+resource}:setIamPolicy').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'POST',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['resource'],\n                pathParams: ['resource'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        testIamPermissions(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params =\n                    {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://networkconnectivity.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1/{+resource}:testIamPermissions').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'POST',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['resource'],\n                pathParams: ['resource'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n    }\n    networkconnectivity_v1.Resource$Projects$Locations$Serviceclasses = Resource$Projects$Locations$Serviceclasses;\n    class Resource$Projects$Locations$Serviceconnectionmaps {\n        context;\n        constructor(context) {\n            this.context = context;\n        }\n        create(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params =\n                    {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://networkconnectivity.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1/{+parent}/serviceConnectionMaps').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'POST',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['parent'],\n                pathParams: ['parent'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        delete(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params =\n                    {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://networkconnectivity.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1/{+name}').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'DELETE',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['name'],\n                pathParams: ['name'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        get(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params =\n                    {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://networkconnectivity.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1/{+name}').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'GET',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['name'],\n                pathParams: ['name'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        getIamPolicy(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params =\n                    {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://networkconnectivity.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1/{+resource}:getIamPolicy').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'GET',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['resource'],\n                pathParams: ['resource'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        list(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params =\n                    {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://networkconnectivity.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1/{+parent}/serviceConnectionMaps').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'GET',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['parent'],\n                pathParams: ['parent'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        patch(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params =\n                    {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://networkconnectivity.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1/{+name}').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'PATCH',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['name'],\n                pathParams: ['name'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        setIamPolicy(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params =\n                    {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://networkconnectivity.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1/{+resource}:setIamPolicy').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'POST',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['resource'],\n                pathParams: ['resource'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        testIamPermissions(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params =\n                    {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://networkconnectivity.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1/{+resource}:testIamPermissions').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'POST',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['resource'],\n                pathParams: ['resource'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n    }\n    networkconnectivity_v1.Resource$Projects$Locations$Serviceconnectionmaps = Resource$Projects$Locations$Serviceconnectionmaps;\n    class Resource$Projects$Locations$Serviceconnectionpolicies {\n        context;\n        constructor(context) {\n            this.context = context;\n        }\n        create(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params =\n                    {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://networkconnectivity.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1/{+parent}/serviceConnectionPolicies').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'POST',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['parent'],\n                pathParams: ['parent'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        delete(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params =\n                    {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://networkconnectivity.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1/{+name}').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'DELETE',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['name'],\n                pathParams: ['name'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        get(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params =\n                    {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://networkconnectivity.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1/{+name}').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'GET',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['name'],\n                pathParams: ['name'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        getIamPolicy(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params =\n                    {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://networkconnectivity.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1/{+resource}:getIamPolicy').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'GET',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['resource'],\n                pathParams: ['resource'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        list(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params =\n                    {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://networkconnectivity.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1/{+parent}/serviceConnectionPolicies').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'GET',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['parent'],\n                pathParams: ['parent'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        patch(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params =\n                    {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://networkconnectivity.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1/{+name}').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'PATCH',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['name'],\n                pathParams: ['name'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        setIamPolicy(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params =\n                    {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://networkconnectivity.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1/{+resource}:setIamPolicy').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'POST',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['resource'],\n                pathParams: ['resource'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        testIamPermissions(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params =\n                    {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://networkconnectivity.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1/{+resource}:testIamPermissions').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'POST',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['resource'],\n                pathParams: ['resource'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n    }\n    networkconnectivity_v1.Resource$Projects$Locations$Serviceconnectionpolicies = Resource$Projects$Locations$Serviceconnectionpolicies;\n    class Resource$Projects$Locations$Serviceconnectiontokens {\n        context;\n        constructor(context) {\n            this.context = context;\n        }\n        create(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params =\n                    {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://networkconnectivity.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1/{+parent}/serviceConnectionTokens').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'POST',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['parent'],\n                pathParams: ['parent'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        delete(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params =\n                    {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://networkconnectivity.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1/{+name}').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'DELETE',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['name'],\n                pathParams: ['name'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        get(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params =\n                    {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://networkconnectivity.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1/{+name}').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'GET',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['name'],\n                pathParams: ['name'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        list(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params =\n                    {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://networkconnectivity.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1/{+parent}/serviceConnectionTokens').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'GET',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['parent'],\n                pathParams: ['parent'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n    }\n    networkconnectivity_v1.Resource$Projects$Locations$Serviceconnectiontokens = Resource$Projects$Locations$Serviceconnectiontokens;\n    class Resource$Projects$Locations$Spokes {\n        context;\n        constructor(context) {\n            this.context = context;\n        }\n        create(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://networkconnectivity.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1/{+parent}/spokes').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'POST',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['parent'],\n                pathParams: ['parent'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        delete(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://networkconnectivity.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1/{+name}').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'DELETE',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['name'],\n                pathParams: ['name'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        get(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://networkconnectivity.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1/{+name}').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'GET',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['name'],\n                pathParams: ['name'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        getIamPolicy(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://networkconnectivity.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1/{+resource}:getIamPolicy').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'GET',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['resource'],\n                pathParams: ['resource'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        list(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://networkconnectivity.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1/{+parent}/spokes').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'GET',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['parent'],\n                pathParams: ['parent'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        patch(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://networkconnectivity.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1/{+name}').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'PATCH',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['name'],\n                pathParams: ['name'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        setIamPolicy(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://networkconnectivity.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1/{+resource}:setIamPolicy').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'POST',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['resource'],\n                pathParams: ['resource'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        testIamPermissions(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params =\n                    {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://networkconnectivity.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1/{+resource}:testIamPermissions').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'POST',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['resource'],\n                pathParams: ['resource'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n    }\n    networkconnectivity_v1.Resource$Projects$Locations$Spokes = Resource$Projects$Locations$Spokes;\n})(networkconnectivity_v1 || (exports.networkconnectivity_v1 = networkconnectivity_v1 = {}));\n"], "names": [], "mappings": "AAAA;AACA,4BAA4B;AAC5B,kEAAkE;AAClE,mEAAmE;AACnE,0CAA0C;AAC1C,EAAE;AACF,gDAAgD;AAChD,EAAE;AACF,sEAAsE;AACtE,oEAAoE;AACpE,2EAA2E;AAC3E,sEAAsE;AACtE,iCAAiC;AACjC,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,QAAQ,sBAAsB,GAAG,KAAK;AACtC,qDAAqD,GACrD,oDAAoD,GACpD,wDAAwD,GACxD,kDAAkD,GAClD,0CAA0C,GAC1C,MAAM;AACN,IAAI;AACJ,CAAC,SAAU,sBAAsB;IAC7B;;;;;;;;;;KAUC,GACD,MAAM;QACF,QAAQ;QACR,SAAS;QACT,YAAY,OAAO,EAAE,MAAM,CAAE;YACzB,IAAI,CAAC,OAAO,GAAG;gBACX,UAAU,WAAW,CAAC;gBACtB;YACJ;YACA,IAAI,CAAC,QAAQ,GAAG,IAAI,kBAAkB,IAAI,CAAC,OAAO;QACtD;IACJ;IACA,uBAAuB,mBAAmB,GAAG;IAC7C,MAAM;QACF,QAAQ;QACR,UAAU;QACV,YAAY,OAAO,CAAE;YACjB,IAAI,CAAC,OAAO,GAAG;YACf,IAAI,CAAC,SAAS,GAAG,IAAI,4BAA4B,IAAI,CAAC,OAAO;QACjE;IACJ;IACA,uBAAuB,iBAAiB,GAAG;IAC3C,MAAM;QACF,QAAQ;QACR,OAAO;QACP,eAAe;QACf,WAAW;QACX,kBAAkB;QAClB,eAAe;QACf,sBAAsB;QACtB,0BAA0B;QAC1B,wBAAwB;QACxB,OAAO;QACP,YAAY,OAAO,CAAE;YACjB,IAAI,CAAC,OAAO,GAAG;YACf,IAAI,CAAC,MAAM,GAAG,IAAI,mCAAmC,IAAI,CAAC,OAAO;YACjE,IAAI,CAAC,cAAc,GAAG,IAAI,2CAA2C,IAAI,CAAC,OAAO;YACjF,IAAI,CAAC,UAAU,GAAG,IAAI,uCAAuC,IAAI,CAAC,OAAO;YACzE,IAAI,CAAC,iBAAiB,GAClB,IAAI,8CAA8C,IAAI,CAAC,OAAO;YAClE,IAAI,CAAC,cAAc,GAAG,IAAI,2CAA2C,IAAI,CAAC,OAAO;YACjF,IAAI,CAAC,qBAAqB,GACtB,IAAI,kDAAkD,IAAI,CAAC,OAAO;YACtE,IAAI,CAAC,yBAAyB,GAC1B,IAAI,sDAAsD,IAAI,CAAC,OAAO;YAC1E,IAAI,CAAC,uBAAuB,GACxB,IAAI,oDAAoD,IAAI,CAAC,OAAO;YACxE,IAAI,CAAC,MAAM,GAAG,IAAI,mCAAmC,IAAI,CAAC,OAAO;QACrE;QACA,IAAI,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAC/C,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,aAAa,EAAE,OAAO,CAAC,gBAAgB;oBACvD,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAO;gBACxB,YAAY;oBAAC;iBAAO;gBACpB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,KAAK,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAChD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,uBAAuB,EAAE,OAAO,CAAC,gBAAgB;oBACjE,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAO;gBACxB,YAAY;oBAAC;iBAAO;gBACpB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;IACJ;IACA,uBAAuB,2BAA2B,GAAG;IACrD,MAAM;QACF,QAAQ;QACR,KAAK;QACL,kBAAkB;QAClB,YAAY,OAAO,CAAE;YACjB,IAAI,CAAC,OAAO,GAAG;YACf,IAAI,CAAC,IAAI,GAAG,IAAI,wCAAwC,IAAI,CAAC,OAAO;YACpE,IAAI,CAAC,iBAAiB,GAClB,IAAI,qDAAqD,IAAI,CAAC,OAAO;QAC7E;IACJ;IACA,uBAAuB,kCAAkC,GAAG;IAC5D,MAAM;QACF,QAAQ;QACR,OAAO;QACP,YAAY;QACZ,YAAY,OAAO,CAAE;YACjB,IAAI,CAAC,OAAO,GAAG;YACf,IAAI,CAAC,MAAM,GAAG,IAAI,+CAA+C,IAAI,CAAC,OAAO;YAC7E,IAAI,CAAC,WAAW,GACZ,IAAI,oDAAoD,IAAI,CAAC,OAAO;QAC5E;QACA,YAAY,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YACvD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SACI,CAAC;gBACL,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,yBAAyB,EAAE,OAAO,CAAC,gBAAgB;oBACnE,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAO;gBACxB,YAAY;oBAAC;iBAAO;gBACpB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,kBAAkB,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAC7D,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SACI,CAAC;gBACL,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,+BAA+B,EAAE,OAAO,CAAC,gBAAgB;oBACzE,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAO;gBACxB,YAAY;oBAAC;iBAAO;gBACpB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,OAAO,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAClD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,oBAAoB,EAAE,OAAO,CAAC,gBAAgB;oBAC9D,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAS;gBAC1B,YAAY;oBAAC;iBAAS;gBACtB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,OAAO,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAClD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,aAAa,EAAE,OAAO,CAAC,gBAAgB;oBACvD,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAO;gBACxB,YAAY;oBAAC;iBAAO;gBACpB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,IAAI,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAC/C,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,aAAa,EAAE,OAAO,CAAC,gBAAgB;oBACvD,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAO;gBACxB,YAAY;oBAAC;iBAAO;gBACpB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,aAAa,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YACxD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SACI,CAAC;gBACL,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,8BAA8B,EAAE,OAAO,CAAC,gBAAgB;oBACxE,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAW;gBAC5B,YAAY;oBAAC;iBAAW;gBACxB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,KAAK,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAChD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,oBAAoB,EAAE,OAAO,CAAC,gBAAgB;oBAC9D,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAS;gBAC1B,YAAY;oBAAC;iBAAS;gBACtB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,WAAW,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YACtD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SACI,CAAC;gBACL,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,wBAAwB,EAAE,OAAO,CAAC,gBAAgB;oBAClE,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAO;gBACxB,YAAY;oBAAC;iBAAO;gBACpB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,MAAM,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YACjD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,aAAa,EAAE,OAAO,CAAC,gBAAgB;oBACvD,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAO;gBACxB,YAAY;oBAAC;iBAAO;gBACpB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,YAAY,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YACvD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SACI,CAAC;gBACL,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,yBAAyB,EAAE,OAAO,CAAC,gBAAgB;oBACnE,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAO;gBACxB,YAAY;oBAAC;iBAAO;gBACpB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,YAAY,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YACvD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SACI,CAAC;gBACL,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,yBAAyB,EAAE,OAAO,CAAC,gBAAgB;oBACnE,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAO;gBACxB,YAAY;oBAAC;iBAAO;gBACpB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,kBAAkB,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAC7D,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SACI,CAAC;gBACL,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,+BAA+B,EAAE,OAAO,CAAC,gBAAgB;oBACzE,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAO;gBACxB,YAAY;oBAAC;iBAAO;gBACpB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,aAAa,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YACxD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SACI,CAAC;gBACL,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,8BAA8B,EAAE,OAAO,CAAC,gBAAgB;oBACxE,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAW;gBAC5B,YAAY;oBAAC;iBAAW;gBACxB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,mBAAmB,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAC9D,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SACI,CAAC;gBACL,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,oCAAoC,EAAE,OAAO,CAAC,gBAAgB;oBAC9E,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAW;gBAC5B,YAAY;oBAAC;iBAAW;gBACxB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;IACJ;IACA,uBAAuB,uCAAuC,GAAG;IACjE,MAAM;QACF,QAAQ;QACR,YAAY,OAAO,CAAE;YACjB,IAAI,CAAC,OAAO,GAAG;QACnB;QACA,IAAI,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAC/C,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SACI,CAAC;gBACL,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,aAAa,EAAE,OAAO,CAAC,gBAAgB;oBACvD,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAO;gBACxB,YAAY;oBAAC;iBAAO;gBACpB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,aAAa,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YACxD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SACI,CAAC;gBACL,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,8BAA8B,EAAE,OAAO,CAAC,gBAAgB;oBACxE,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAW;gBAC5B,YAAY;oBAAC;iBAAW;gBACxB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,KAAK,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAChD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SACI,CAAC;gBACL,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,sBAAsB,EAAE,OAAO,CAAC,gBAAgB;oBAChE,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAS;gBAC1B,YAAY;oBAAC;iBAAS;gBACtB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,MAAM,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YACjD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SACI,CAAC;gBACL,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,aAAa,EAAE,OAAO,CAAC,gBAAgB;oBACvD,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAO;gBACxB,YAAY;oBAAC;iBAAO;gBACpB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,aAAa,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YACxD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SACI,CAAC;gBACL,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,8BAA8B,EAAE,OAAO,CAAC,gBAAgB;oBACxE,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAW;gBAC5B,YAAY;oBAAC;iBAAW;gBACxB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,mBAAmB,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAC9D,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SACI,CAAC;gBACL,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,oCAAoC,EAAE,OAAO,CAAC,gBAAgB;oBAC9E,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAW;gBAC5B,YAAY;oBAAC;iBAAW;gBACxB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;IACJ;IACA,uBAAuB,8CAA8C,GAAG;IACxE,MAAM;QACF,QAAQ;QACR,OAAO;QACP,YAAY,OAAO,CAAE;YACjB,IAAI,CAAC,OAAO,GAAG;YACf,IAAI,CAAC,MAAM,GACP,IAAI,2DAA2D,IAAI,CAAC,OAAO;QACnF;QACA,IAAI,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAC/C,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SACI,CAAC;gBACL,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,aAAa,EAAE,OAAO,CAAC,gBAAgB;oBACvD,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAO;gBACxB,YAAY;oBAAC;iBAAO;gBACpB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,KAAK,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAChD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SACI,CAAC;gBACL,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,2BAA2B,EAAE,OAAO,CAAC,gBAAgB;oBACrE,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAS;gBAC1B,YAAY;oBAAC;iBAAS;gBACtB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;IACJ;IACA,uBAAuB,mDAAmD,GAAG;IAC7E,MAAM;QACF,QAAQ;QACR,YAAY,OAAO,CAAE;YACjB,IAAI,CAAC,OAAO,GAAG;QACnB;QACA,IAAI,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAC/C,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SACI,CAAC;gBACL,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,aAAa,EAAE,OAAO,CAAC,gBAAgB;oBACvD,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAO;gBACxB,YAAY;oBAAC;iBAAO;gBACpB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,KAAK,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAChD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SACI,CAAC;gBACL,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,sBAAsB,EAAE,OAAO,CAAC,gBAAgB;oBAChE,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAS;gBAC1B,YAAY;oBAAC;iBAAS;gBACtB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;IACJ;IACA,uBAAuB,0DAA0D,GAAG;IACpF,MAAM;QACF,QAAQ;QACR,YAAY,OAAO,CAAE;YACjB,IAAI,CAAC,OAAO,GAAG;QACnB;QACA,OAAO,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAClD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SACI,CAAC;gBACL,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,iCAAiC,EAAE,OAAO,CAAC,gBAAgB;oBAC3E,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAS;gBAC1B,YAAY;oBAAC;iBAAS;gBACtB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,OAAO,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAClD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SACI,CAAC;gBACL,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,aAAa,EAAE,OAAO,CAAC,gBAAgB;oBACvD,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAO;gBACxB,YAAY;oBAAC;iBAAO;gBACpB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,IAAI,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAC/C,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SACI,CAAC;gBACL,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,aAAa,EAAE,OAAO,CAAC,gBAAgB;oBACvD,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAO;gBACxB,YAAY;oBAAC;iBAAO;gBACpB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,aAAa,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YACxD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SACI,CAAC;gBACL,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,8BAA8B,EAAE,OAAO,CAAC,gBAAgB;oBACxE,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAW;gBAC5B,YAAY;oBAAC;iBAAW;gBACxB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,KAAK,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAChD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SACI,CAAC;gBACL,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,iCAAiC,EAAE,OAAO,CAAC,gBAAgB;oBAC3E,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAS;gBAC1B,YAAY;oBAAC;iBAAS;gBACtB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,aAAa,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YACxD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SACI,CAAC;gBACL,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,8BAA8B,EAAE,OAAO,CAAC,gBAAgB;oBACxE,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAW;gBAC5B,YAAY;oBAAC;iBAAW;gBACxB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,mBAAmB,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAC9D,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SACI,CAAC;gBACL,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,oCAAoC,EAAE,OAAO,CAAC,gBAAgB;oBAC9E,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAW;gBAC5B,YAAY;oBAAC;iBAAW;gBACxB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;IACJ;IACA,uBAAuB,oDAAoD,GAAG;IAC9E,MAAM;QACF,QAAQ;QACR,YAAY,OAAO,CAAE;YACjB,IAAI,CAAC,OAAO,GAAG;QACnB;QACA,OAAO,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAClD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,8BAA8B,EAAE,OAAO,CAAC,gBAAgB;oBACxE,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAS;gBAC1B,YAAY;oBAAC;iBAAS;gBACtB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,OAAO,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAClD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,aAAa,EAAE,OAAO,CAAC,gBAAgB;oBACvD,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAO;gBACxB,YAAY;oBAAC;iBAAO;gBACpB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,IAAI,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAC/C,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,aAAa,EAAE,OAAO,CAAC,gBAAgB;oBACvD,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAO;gBACxB,YAAY;oBAAC;iBAAO;gBACpB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,aAAa,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YACxD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SACI,CAAC;gBACL,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,8BAA8B,EAAE,OAAO,CAAC,gBAAgB;oBACxE,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAW;gBAC5B,YAAY;oBAAC;iBAAW;gBACxB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,KAAK,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAChD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,8BAA8B,EAAE,OAAO,CAAC,gBAAgB;oBACxE,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAS;gBAC1B,YAAY;oBAAC;iBAAS;gBACtB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,MAAM,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YACjD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,aAAa,EAAE,OAAO,CAAC,gBAAgB;oBACvD,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAO;gBACxB,YAAY;oBAAC;iBAAO;gBACpB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,aAAa,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YACxD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SACI,CAAC;gBACL,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,8BAA8B,EAAE,OAAO,CAAC,gBAAgB;oBACxE,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAW;gBAC5B,YAAY;oBAAC;iBAAW;gBACxB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,mBAAmB,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAC9D,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SACI,CAAC;gBACL,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,oCAAoC,EAAE,OAAO,CAAC,gBAAgB;oBAC9E,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAW;gBAC5B,YAAY;oBAAC;iBAAW;gBACxB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;IACJ;IACA,uBAAuB,0CAA0C,GAAG;IACpE,MAAM;QACF,QAAQ;QACR,YAAY,OAAO,CAAE;YACjB,IAAI,CAAC,OAAO,GAAG;QACnB;QACA,OAAO,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAClD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,oBAAoB,EAAE,OAAO,CAAC,gBAAgB;oBAC9D,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAO;gBACxB,YAAY;oBAAC;iBAAO;gBACpB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,OAAO,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAClD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,aAAa,EAAE,OAAO,CAAC,gBAAgB;oBACvD,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAO;gBACxB,YAAY;oBAAC;iBAAO;gBACpB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,IAAI,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAC/C,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,aAAa,EAAE,OAAO,CAAC,gBAAgB;oBACvD,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAO;gBACxB,YAAY;oBAAC;iBAAO;gBACpB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,KAAK,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAChD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,wBAAwB,EAAE,OAAO,CAAC,gBAAgB;oBAClE,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAO;gBACxB,YAAY;oBAAC;iBAAO;gBACpB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;IACJ;IACA,uBAAuB,sCAAsC,GAAG;IAChE,MAAM;QACF,QAAQ;QACR,YAAY,OAAO,CAAE;YACjB,IAAI,CAAC,OAAO,GAAG;QACnB;QACA,OAAO,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAClD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SACI,CAAC;gBACL,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,iCAAiC,EAAE,OAAO,CAAC,gBAAgB;oBAC3E,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAS;gBAC1B,YAAY;oBAAC;iBAAS;gBACtB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,OAAO,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAClD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SACI,CAAC;gBACL,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,aAAa,EAAE,OAAO,CAAC,gBAAgB;oBACvD,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAO;gBACxB,YAAY;oBAAC;iBAAO;gBACpB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,IAAI,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAC/C,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,aAAa,EAAE,OAAO,CAAC,gBAAgB;oBACvD,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAO;gBACxB,YAAY;oBAAC;iBAAO;gBACpB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,KAAK,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAChD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SACI,CAAC;gBACL,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,iCAAiC,EAAE,OAAO,CAAC,gBAAgB;oBAC3E,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAS;gBAC1B,YAAY;oBAAC;iBAAS;gBACtB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;IACJ;IACA,uBAAuB,6CAA6C,GAAG;IACvE,MAAM;QACF,QAAQ;QACR,YAAY,OAAO,CAAE;YACjB,IAAI,CAAC,OAAO,GAAG;QACnB;QACA,OAAO,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAClD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,aAAa,EAAE,OAAO,CAAC,gBAAgB;oBACvD,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAO;gBACxB,YAAY;oBAAC;iBAAO;gBACpB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,IAAI,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAC/C,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,aAAa,EAAE,OAAO,CAAC,gBAAgB;oBACvD,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAO;gBACxB,YAAY;oBAAC;iBAAO;gBACpB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,aAAa,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YACxD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SACI,CAAC;gBACL,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,8BAA8B,EAAE,OAAO,CAAC,gBAAgB;oBACxE,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAW;gBAC5B,YAAY;oBAAC;iBAAW;gBACxB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,KAAK,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAChD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,8BAA8B,EAAE,OAAO,CAAC,gBAAgB;oBACxE,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAS;gBAC1B,YAAY;oBAAC;iBAAS;gBACtB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,MAAM,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YACjD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,aAAa,EAAE,OAAO,CAAC,gBAAgB;oBACvD,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAO;gBACxB,YAAY;oBAAC;iBAAO;gBACpB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,aAAa,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YACxD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SACI,CAAC;gBACL,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,8BAA8B,EAAE,OAAO,CAAC,gBAAgB;oBACxE,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAW;gBAC5B,YAAY;oBAAC;iBAAW;gBACxB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,mBAAmB,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAC9D,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SACI,CAAC;gBACL,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,oCAAoC,EAAE,OAAO,CAAC,gBAAgB;oBAC9E,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAW;gBAC5B,YAAY;oBAAC;iBAAW;gBACxB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;IACJ;IACA,uBAAuB,0CAA0C,GAAG;IACpE,MAAM;QACF,QAAQ;QACR,YAAY,OAAO,CAAE;YACjB,IAAI,CAAC,OAAO,GAAG;QACnB;QACA,OAAO,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAClD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SACI,CAAC;gBACL,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,qCAAqC,EAAE,OAAO,CAAC,gBAAgB;oBAC/E,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAS;gBAC1B,YAAY;oBAAC;iBAAS;gBACtB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,OAAO,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAClD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SACI,CAAC;gBACL,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,aAAa,EAAE,OAAO,CAAC,gBAAgB;oBACvD,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAO;gBACxB,YAAY;oBAAC;iBAAO;gBACpB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,IAAI,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAC/C,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SACI,CAAC;gBACL,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,aAAa,EAAE,OAAO,CAAC,gBAAgB;oBACvD,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAO;gBACxB,YAAY;oBAAC;iBAAO;gBACpB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,aAAa,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YACxD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SACI,CAAC;gBACL,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,8BAA8B,EAAE,OAAO,CAAC,gBAAgB;oBACxE,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAW;gBAC5B,YAAY;oBAAC;iBAAW;gBACxB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,KAAK,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAChD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SACI,CAAC;gBACL,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,qCAAqC,EAAE,OAAO,CAAC,gBAAgB;oBAC/E,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAS;gBAC1B,YAAY;oBAAC;iBAAS;gBACtB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,MAAM,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YACjD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SACI,CAAC;gBACL,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,aAAa,EAAE,OAAO,CAAC,gBAAgB;oBACvD,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAO;gBACxB,YAAY;oBAAC;iBAAO;gBACpB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,aAAa,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YACxD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SACI,CAAC;gBACL,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,8BAA8B,EAAE,OAAO,CAAC,gBAAgB;oBACxE,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAW;gBAC5B,YAAY;oBAAC;iBAAW;gBACxB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,mBAAmB,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAC9D,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SACI,CAAC;gBACL,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,oCAAoC,EAAE,OAAO,CAAC,gBAAgB;oBAC9E,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAW;gBAC5B,YAAY;oBAAC;iBAAW;gBACxB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;IACJ;IACA,uBAAuB,iDAAiD,GAAG;IAC3E,MAAM;QACF,QAAQ;QACR,YAAY,OAAO,CAAE;YACjB,IAAI,CAAC,OAAO,GAAG;QACnB;QACA,OAAO,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAClD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SACI,CAAC;gBACL,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,yCAAyC,EAAE,OAAO,CAAC,gBAAgB;oBACnF,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAS;gBAC1B,YAAY;oBAAC;iBAAS;gBACtB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,OAAO,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAClD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SACI,CAAC;gBACL,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,aAAa,EAAE,OAAO,CAAC,gBAAgB;oBACvD,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAO;gBACxB,YAAY;oBAAC;iBAAO;gBACpB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,IAAI,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAC/C,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SACI,CAAC;gBACL,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,aAAa,EAAE,OAAO,CAAC,gBAAgB;oBACvD,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAO;gBACxB,YAAY;oBAAC;iBAAO;gBACpB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,aAAa,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YACxD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SACI,CAAC;gBACL,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,8BAA8B,EAAE,OAAO,CAAC,gBAAgB;oBACxE,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAW;gBAC5B,YAAY;oBAAC;iBAAW;gBACxB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,KAAK,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAChD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SACI,CAAC;gBACL,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,yCAAyC,EAAE,OAAO,CAAC,gBAAgB;oBACnF,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAS;gBAC1B,YAAY;oBAAC;iBAAS;gBACtB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,MAAM,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YACjD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SACI,CAAC;gBACL,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,aAAa,EAAE,OAAO,CAAC,gBAAgB;oBACvD,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAO;gBACxB,YAAY;oBAAC;iBAAO;gBACpB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,aAAa,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YACxD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SACI,CAAC;gBACL,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,8BAA8B,EAAE,OAAO,CAAC,gBAAgB;oBACxE,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAW;gBAC5B,YAAY;oBAAC;iBAAW;gBACxB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,mBAAmB,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAC9D,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SACI,CAAC;gBACL,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,oCAAoC,EAAE,OAAO,CAAC,gBAAgB;oBAC9E,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAW;gBAC5B,YAAY;oBAAC;iBAAW;gBACxB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;IACJ;IACA,uBAAuB,qDAAqD,GAAG;IAC/E,MAAM;QACF,QAAQ;QACR,YAAY,OAAO,CAAE;YACjB,IAAI,CAAC,OAAO,GAAG;QACnB;QACA,OAAO,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAClD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SACI,CAAC;gBACL,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,uCAAuC,EAAE,OAAO,CAAC,gBAAgB;oBACjF,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAS;gBAC1B,YAAY;oBAAC;iBAAS;gBACtB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,OAAO,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAClD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SACI,CAAC;gBACL,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,aAAa,EAAE,OAAO,CAAC,gBAAgB;oBACvD,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAO;gBACxB,YAAY;oBAAC;iBAAO;gBACpB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,IAAI,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAC/C,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SACI,CAAC;gBACL,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,aAAa,EAAE,OAAO,CAAC,gBAAgB;oBACvD,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAO;gBACxB,YAAY;oBAAC;iBAAO;gBACpB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,KAAK,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAChD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SACI,CAAC;gBACL,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,uCAAuC,EAAE,OAAO,CAAC,gBAAgB;oBACjF,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAS;gBAC1B,YAAY;oBAAC;iBAAS;gBACtB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;IACJ;IACA,uBAAuB,mDAAmD,GAAG;IAC7E,MAAM;QACF,QAAQ;QACR,YAAY,OAAO,CAAE;YACjB,IAAI,CAAC,OAAO,GAAG;QACnB;QACA,OAAO,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAClD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,sBAAsB,EAAE,OAAO,CAAC,gBAAgB;oBAChE,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAS;gBAC1B,YAAY;oBAAC;iBAAS;gBACtB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,OAAO,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAClD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,aAAa,EAAE,OAAO,CAAC,gBAAgB;oBACvD,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAO;gBACxB,YAAY;oBAAC;iBAAO;gBACpB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,IAAI,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAC/C,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,aAAa,EAAE,OAAO,CAAC,gBAAgB;oBACvD,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAO;gBACxB,YAAY;oBAAC;iBAAO;gBACpB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,aAAa,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YACxD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,8BAA8B,EAAE,OAAO,CAAC,gBAAgB;oBACxE,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAW;gBAC5B,YAAY;oBAAC;iBAAW;gBACxB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,KAAK,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAChD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,sBAAsB,EAAE,OAAO,CAAC,gBAAgB;oBAChE,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAS;gBAC1B,YAAY;oBAAC;iBAAS;gBACtB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,MAAM,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YACjD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,aAAa,EAAE,OAAO,CAAC,gBAAgB;oBACvD,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAO;gBACxB,YAAY;oBAAC;iBAAO;gBACpB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,aAAa,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YACxD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,8BAA8B,EAAE,OAAO,CAAC,gBAAgB;oBACxE,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAW;gBAC5B,YAAY;oBAAC;iBAAW;gBACxB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,mBAAmB,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAC9D,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SACI,CAAC;gBACL,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,oCAAoC,EAAE,OAAO,CAAC,gBAAgB;oBAC9E,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAW;gBAC5B,YAAY;oBAAC;iBAAW;gBACxB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;IACJ;IACA,uBAAuB,kCAAkC,GAAG;AAChE,CAAC,EAAE,0BAA0B,CAAC,QAAQ,sBAAsB,GAAG,yBAAyB,CAAC,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3052, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/AI_Projects/CRMcriadores/crm-interno/node_modules/googleapis/build/src/apis/networkconnectivity/v1alpha1.js"], "sourcesContent": ["\"use strict\";\n// Copyright 2020 Google LLC\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//    http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.networkconnectivity_v1alpha1 = void 0;\n/* eslint-disable @typescript-eslint/no-explicit-any */\n/* eslint-disable @typescript-eslint/no-unused-vars */\n/* eslint-disable @typescript-eslint/no-empty-interface */\n/* eslint-disable @typescript-eslint/no-namespace */\n/* eslint-disable no-irregular-whitespace */\nconst googleapis_common_1 = require(\"googleapis-common\");\nvar networkconnectivity_v1alpha1;\n(function (networkconnectivity_v1alpha1) {\n    /**\n     * Network Connectivity API\n     *\n     * This API enables connectivity with and between Google Cloud resources.\n     *\n     * @example\n     * ```js\n     * const {google} = require('googleapis');\n     * const networkconnectivity = google.networkconnectivity('v1alpha1');\n     * ```\n     */\n    class Networkconnectivity {\n        context;\n        projects;\n        constructor(options, google) {\n            this.context = {\n                _options: options || {},\n                google,\n            };\n            this.projects = new Resource$Projects(this.context);\n        }\n    }\n    networkconnectivity_v1alpha1.Networkconnectivity = Networkconnectivity;\n    class Resource$Projects {\n        context;\n        locations;\n        constructor(context) {\n            this.context = context;\n            this.locations = new Resource$Projects$Locations(this.context);\n        }\n    }\n    networkconnectivity_v1alpha1.Resource$Projects = Resource$Projects;\n    class Resource$Projects$Locations {\n        context;\n        global;\n        internalRanges;\n        operations;\n        spokes;\n        constructor(context) {\n            this.context = context;\n            this.global = new Resource$Projects$Locations$Global(this.context);\n            this.internalRanges = new Resource$Projects$Locations$Internalranges(this.context);\n            this.operations = new Resource$Projects$Locations$Operations(this.context);\n            this.spokes = new Resource$Projects$Locations$Spokes(this.context);\n        }\n        get(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://networkconnectivity.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1alpha1/{+name}').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'GET',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['name'],\n                pathParams: ['name'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        list(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://networkconnectivity.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1alpha1/{+name}/locations').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'GET',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['name'],\n                pathParams: ['name'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n    }\n    networkconnectivity_v1alpha1.Resource$Projects$Locations = Resource$Projects$Locations;\n    class Resource$Projects$Locations$Global {\n        context;\n        hubs;\n        constructor(context) {\n            this.context = context;\n            this.hubs = new Resource$Projects$Locations$Global$Hubs(this.context);\n        }\n    }\n    networkconnectivity_v1alpha1.Resource$Projects$Locations$Global = Resource$Projects$Locations$Global;\n    class Resource$Projects$Locations$Global$Hubs {\n        context;\n        constructor(context) {\n            this.context = context;\n        }\n        create(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://networkconnectivity.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1alpha1/{+parent}/hubs').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'POST',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['parent'],\n                pathParams: ['parent'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        delete(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://networkconnectivity.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1alpha1/{+name}').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'DELETE',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['name'],\n                pathParams: ['name'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        get(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://networkconnectivity.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1alpha1/{+name}').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'GET',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['name'],\n                pathParams: ['name'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        getIamPolicy(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params =\n                    {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://networkconnectivity.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1alpha1/{+resource}:getIamPolicy').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'GET',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['resource'],\n                pathParams: ['resource'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        list(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://networkconnectivity.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1alpha1/{+parent}/hubs').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'GET',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['parent'],\n                pathParams: ['parent'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        patch(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://networkconnectivity.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1alpha1/{+name}').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'PATCH',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['name'],\n                pathParams: ['name'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        setIamPolicy(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params =\n                    {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://networkconnectivity.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1alpha1/{+resource}:setIamPolicy').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'POST',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['resource'],\n                pathParams: ['resource'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        testIamPermissions(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params =\n                    {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://networkconnectivity.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1alpha1/{+resource}:testIamPermissions').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'POST',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['resource'],\n                pathParams: ['resource'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n    }\n    networkconnectivity_v1alpha1.Resource$Projects$Locations$Global$Hubs = Resource$Projects$Locations$Global$Hubs;\n    class Resource$Projects$Locations$Internalranges {\n        context;\n        constructor(context) {\n            this.context = context;\n        }\n        create(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://networkconnectivity.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1alpha1/{+parent}/internalRanges').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'POST',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['parent'],\n                pathParams: ['parent'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        delete(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://networkconnectivity.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1alpha1/{+name}').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'DELETE',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['name'],\n                pathParams: ['name'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        get(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://networkconnectivity.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1alpha1/{+name}').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'GET',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['name'],\n                pathParams: ['name'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        getIamPolicy(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params =\n                    {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://networkconnectivity.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1alpha1/{+resource}:getIamPolicy').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'GET',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['resource'],\n                pathParams: ['resource'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        list(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://networkconnectivity.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1alpha1/{+parent}/internalRanges').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'GET',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['parent'],\n                pathParams: ['parent'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        patch(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://networkconnectivity.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1alpha1/{+name}').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'PATCH',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['name'],\n                pathParams: ['name'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        setIamPolicy(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params =\n                    {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://networkconnectivity.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1alpha1/{+resource}:setIamPolicy').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'POST',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['resource'],\n                pathParams: ['resource'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        testIamPermissions(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params =\n                    {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://networkconnectivity.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1alpha1/{+resource}:testIamPermissions').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'POST',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['resource'],\n                pathParams: ['resource'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n    }\n    networkconnectivity_v1alpha1.Resource$Projects$Locations$Internalranges = Resource$Projects$Locations$Internalranges;\n    class Resource$Projects$Locations$Operations {\n        context;\n        constructor(context) {\n            this.context = context;\n        }\n        cancel(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://networkconnectivity.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1alpha1/{+name}:cancel').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'POST',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['name'],\n                pathParams: ['name'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        delete(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://networkconnectivity.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1alpha1/{+name}').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'DELETE',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['name'],\n                pathParams: ['name'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        get(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://networkconnectivity.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1alpha1/{+name}').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'GET',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['name'],\n                pathParams: ['name'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        list(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://networkconnectivity.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1alpha1/{+name}/operations').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'GET',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['name'],\n                pathParams: ['name'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n    }\n    networkconnectivity_v1alpha1.Resource$Projects$Locations$Operations = Resource$Projects$Locations$Operations;\n    class Resource$Projects$Locations$Spokes {\n        context;\n        constructor(context) {\n            this.context = context;\n        }\n        create(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://networkconnectivity.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1alpha1/{+parent}/spokes').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'POST',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['parent'],\n                pathParams: ['parent'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        delete(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://networkconnectivity.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1alpha1/{+name}').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'DELETE',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['name'],\n                pathParams: ['name'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        get(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://networkconnectivity.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1alpha1/{+name}').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'GET',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['name'],\n                pathParams: ['name'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        getIamPolicy(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://networkconnectivity.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1alpha1/{+resource}:getIamPolicy').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'GET',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['resource'],\n                pathParams: ['resource'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        list(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://networkconnectivity.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1alpha1/{+parent}/spokes').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'GET',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['parent'],\n                pathParams: ['parent'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        patch(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://networkconnectivity.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1alpha1/{+name}').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'PATCH',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['name'],\n                pathParams: ['name'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        setIamPolicy(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params = {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://networkconnectivity.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1alpha1/{+resource}:setIamPolicy').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'POST',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['resource'],\n                pathParams: ['resource'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n        testIamPermissions(paramsOrCallback, optionsOrCallback, callback) {\n            let params = (paramsOrCallback ||\n                {});\n            let options = (optionsOrCallback || {});\n            if (typeof paramsOrCallback === 'function') {\n                callback = paramsOrCallback;\n                params =\n                    {};\n                options = {};\n            }\n            if (typeof optionsOrCallback === 'function') {\n                callback = optionsOrCallback;\n                options = {};\n            }\n            const rootUrl = options.rootUrl || 'https://networkconnectivity.googleapis.com/';\n            const parameters = {\n                options: Object.assign({\n                    url: (rootUrl + '/v1alpha1/{+resource}:testIamPermissions').replace(/([^:]\\/)\\/+/g, '$1'),\n                    method: 'POST',\n                    apiVersion: '',\n                }, options),\n                params,\n                requiredParams: ['resource'],\n                pathParams: ['resource'],\n                context: this.context,\n            };\n            if (callback) {\n                (0, googleapis_common_1.createAPIRequest)(parameters, callback);\n            }\n            else {\n                return (0, googleapis_common_1.createAPIRequest)(parameters);\n            }\n        }\n    }\n    networkconnectivity_v1alpha1.Resource$Projects$Locations$Spokes = Resource$Projects$Locations$Spokes;\n})(networkconnectivity_v1alpha1 || (exports.networkconnectivity_v1alpha1 = networkconnectivity_v1alpha1 = {}));\n"], "names": [], "mappings": "AAAA;AACA,4BAA4B;AAC5B,kEAAkE;AAClE,mEAAmE;AACnE,0CAA0C;AAC1C,EAAE;AACF,gDAAgD;AAChD,EAAE;AACF,sEAAsE;AACtE,oEAAoE;AACpE,2EAA2E;AAC3E,sEAAsE;AACtE,iCAAiC;AACjC,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,QAAQ,4BAA4B,GAAG,KAAK;AAC5C,qDAAqD,GACrD,oDAAoD,GACpD,wDAAwD,GACxD,kDAAkD,GAClD,0CAA0C,GAC1C,MAAM;AACN,IAAI;AACJ,CAAC,SAAU,4BAA4B;IACnC;;;;;;;;;;KAUC,GACD,MAAM;QACF,QAAQ;QACR,SAAS;QACT,YAAY,OAAO,EAAE,MAAM,CAAE;YACzB,IAAI,CAAC,OAAO,GAAG;gBACX,UAAU,WAAW,CAAC;gBACtB;YACJ;YACA,IAAI,CAAC,QAAQ,GAAG,IAAI,kBAAkB,IAAI,CAAC,OAAO;QACtD;IACJ;IACA,6BAA6B,mBAAmB,GAAG;IACnD,MAAM;QACF,QAAQ;QACR,UAAU;QACV,YAAY,OAAO,CAAE;YACjB,IAAI,CAAC,OAAO,GAAG;YACf,IAAI,CAAC,SAAS,GAAG,IAAI,4BAA4B,IAAI,CAAC,OAAO;QACjE;IACJ;IACA,6BAA6B,iBAAiB,GAAG;IACjD,MAAM;QACF,QAAQ;QACR,OAAO;QACP,eAAe;QACf,WAAW;QACX,OAAO;QACP,YAAY,OAAO,CAAE;YACjB,IAAI,CAAC,OAAO,GAAG;YACf,IAAI,CAAC,MAAM,GAAG,IAAI,mCAAmC,IAAI,CAAC,OAAO;YACjE,IAAI,CAAC,cAAc,GAAG,IAAI,2CAA2C,IAAI,CAAC,OAAO;YACjF,IAAI,CAAC,UAAU,GAAG,IAAI,uCAAuC,IAAI,CAAC,OAAO;YACzE,IAAI,CAAC,MAAM,GAAG,IAAI,mCAAmC,IAAI,CAAC,OAAO;QACrE;QACA,IAAI,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAC/C,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,mBAAmB,EAAE,OAAO,CAAC,gBAAgB;oBAC7D,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAO;gBACxB,YAAY;oBAAC;iBAAO;gBACpB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,KAAK,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAChD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,6BAA6B,EAAE,OAAO,CAAC,gBAAgB;oBACvE,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAO;gBACxB,YAAY;oBAAC;iBAAO;gBACpB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;IACJ;IACA,6BAA6B,2BAA2B,GAAG;IAC3D,MAAM;QACF,QAAQ;QACR,KAAK;QACL,YAAY,OAAO,CAAE;YACjB,IAAI,CAAC,OAAO,GAAG;YACf,IAAI,CAAC,IAAI,GAAG,IAAI,wCAAwC,IAAI,CAAC,OAAO;QACxE;IACJ;IACA,6BAA6B,kCAAkC,GAAG;IAClE,MAAM;QACF,QAAQ;QACR,YAAY,OAAO,CAAE;YACjB,IAAI,CAAC,OAAO,GAAG;QACnB;QACA,OAAO,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAClD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,0BAA0B,EAAE,OAAO,CAAC,gBAAgB;oBACpE,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAS;gBAC1B,YAAY;oBAAC;iBAAS;gBACtB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,OAAO,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAClD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,mBAAmB,EAAE,OAAO,CAAC,gBAAgB;oBAC7D,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAO;gBACxB,YAAY;oBAAC;iBAAO;gBACpB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,IAAI,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAC/C,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,mBAAmB,EAAE,OAAO,CAAC,gBAAgB;oBAC7D,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAO;gBACxB,YAAY;oBAAC;iBAAO;gBACpB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,aAAa,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YACxD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SACI,CAAC;gBACL,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,oCAAoC,EAAE,OAAO,CAAC,gBAAgB;oBAC9E,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAW;gBAC5B,YAAY;oBAAC;iBAAW;gBACxB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,KAAK,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAChD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,0BAA0B,EAAE,OAAO,CAAC,gBAAgB;oBACpE,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAS;gBAC1B,YAAY;oBAAC;iBAAS;gBACtB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,MAAM,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YACjD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,mBAAmB,EAAE,OAAO,CAAC,gBAAgB;oBAC7D,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAO;gBACxB,YAAY;oBAAC;iBAAO;gBACpB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,aAAa,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YACxD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SACI,CAAC;gBACL,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,oCAAoC,EAAE,OAAO,CAAC,gBAAgB;oBAC9E,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAW;gBAC5B,YAAY;oBAAC;iBAAW;gBACxB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,mBAAmB,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAC9D,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SACI,CAAC;gBACL,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,0CAA0C,EAAE,OAAO,CAAC,gBAAgB;oBACpF,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAW;gBAC5B,YAAY;oBAAC;iBAAW;gBACxB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;IACJ;IACA,6BAA6B,uCAAuC,GAAG;IACvE,MAAM;QACF,QAAQ;QACR,YAAY,OAAO,CAAE;YACjB,IAAI,CAAC,OAAO,GAAG;QACnB;QACA,OAAO,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAClD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,oCAAoC,EAAE,OAAO,CAAC,gBAAgB;oBAC9E,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAS;gBAC1B,YAAY;oBAAC;iBAAS;gBACtB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,OAAO,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAClD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,mBAAmB,EAAE,OAAO,CAAC,gBAAgB;oBAC7D,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAO;gBACxB,YAAY;oBAAC;iBAAO;gBACpB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,IAAI,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAC/C,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,mBAAmB,EAAE,OAAO,CAAC,gBAAgB;oBAC7D,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAO;gBACxB,YAAY;oBAAC;iBAAO;gBACpB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,aAAa,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YACxD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SACI,CAAC;gBACL,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,oCAAoC,EAAE,OAAO,CAAC,gBAAgB;oBAC9E,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAW;gBAC5B,YAAY;oBAAC;iBAAW;gBACxB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,KAAK,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAChD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,oCAAoC,EAAE,OAAO,CAAC,gBAAgB;oBAC9E,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAS;gBAC1B,YAAY;oBAAC;iBAAS;gBACtB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,MAAM,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YACjD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,mBAAmB,EAAE,OAAO,CAAC,gBAAgB;oBAC7D,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAO;gBACxB,YAAY;oBAAC;iBAAO;gBACpB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,aAAa,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YACxD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SACI,CAAC;gBACL,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,oCAAoC,EAAE,OAAO,CAAC,gBAAgB;oBAC9E,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAW;gBAC5B,YAAY;oBAAC;iBAAW;gBACxB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,mBAAmB,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAC9D,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SACI,CAAC;gBACL,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,0CAA0C,EAAE,OAAO,CAAC,gBAAgB;oBACpF,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAW;gBAC5B,YAAY;oBAAC;iBAAW;gBACxB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;IACJ;IACA,6BAA6B,0CAA0C,GAAG;IAC1E,MAAM;QACF,QAAQ;QACR,YAAY,OAAO,CAAE;YACjB,IAAI,CAAC,OAAO,GAAG;QACnB;QACA,OAAO,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAClD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,0BAA0B,EAAE,OAAO,CAAC,gBAAgB;oBACpE,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAO;gBACxB,YAAY;oBAAC;iBAAO;gBACpB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,OAAO,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAClD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,mBAAmB,EAAE,OAAO,CAAC,gBAAgB;oBAC7D,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAO;gBACxB,YAAY;oBAAC;iBAAO;gBACpB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,IAAI,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAC/C,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,mBAAmB,EAAE,OAAO,CAAC,gBAAgB;oBAC7D,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAO;gBACxB,YAAY;oBAAC;iBAAO;gBACpB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,KAAK,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAChD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,8BAA8B,EAAE,OAAO,CAAC,gBAAgB;oBACxE,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAO;gBACxB,YAAY;oBAAC;iBAAO;gBACpB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;IACJ;IACA,6BAA6B,sCAAsC,GAAG;IACtE,MAAM;QACF,QAAQ;QACR,YAAY,OAAO,CAAE;YACjB,IAAI,CAAC,OAAO,GAAG;QACnB;QACA,OAAO,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAClD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,4BAA4B,EAAE,OAAO,CAAC,gBAAgB;oBACtE,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAS;gBAC1B,YAAY;oBAAC;iBAAS;gBACtB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,OAAO,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAClD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,mBAAmB,EAAE,OAAO,CAAC,gBAAgB;oBAC7D,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAO;gBACxB,YAAY;oBAAC;iBAAO;gBACpB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,IAAI,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAC/C,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,mBAAmB,EAAE,OAAO,CAAC,gBAAgB;oBAC7D,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAO;gBACxB,YAAY;oBAAC;iBAAO;gBACpB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,aAAa,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YACxD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,oCAAoC,EAAE,OAAO,CAAC,gBAAgB;oBAC9E,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAW;gBAC5B,YAAY;oBAAC;iBAAW;gBACxB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,KAAK,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAChD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,4BAA4B,EAAE,OAAO,CAAC,gBAAgB;oBACtE,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAS;gBAC1B,YAAY;oBAAC;iBAAS;gBACtB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,MAAM,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YACjD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,mBAAmB,EAAE,OAAO,CAAC,gBAAgB;oBAC7D,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAO;gBACxB,YAAY;oBAAC;iBAAO;gBACpB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,aAAa,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YACxD,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SAAS,CAAC;gBACV,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,oCAAoC,EAAE,OAAO,CAAC,gBAAgB;oBAC9E,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAW;gBAC5B,YAAY;oBAAC;iBAAW;gBACxB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;QACA,mBAAmB,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE;YAC9D,IAAI,SAAU,oBACV,CAAC;YACL,IAAI,UAAW,qBAAqB,CAAC;YACrC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,WAAW;gBACX,SACI,CAAC;gBACL,UAAU,CAAC;YACf;YACA,IAAI,OAAO,sBAAsB,YAAY;gBACzC,WAAW;gBACX,UAAU,CAAC;YACf;YACA,MAAM,UAAU,QAAQ,OAAO,IAAI;YACnC,MAAM,aAAa;gBACf,SAAS,OAAO,MAAM,CAAC;oBACnB,KAAK,CAAC,UAAU,0CAA0C,EAAE,OAAO,CAAC,gBAAgB;oBACpF,QAAQ;oBACR,YAAY;gBAChB,GAAG;gBACH;gBACA,gBAAgB;oBAAC;iBAAW;gBAC5B,YAAY;oBAAC;iBAAW;gBACxB,SAAS,IAAI,CAAC,OAAO;YACzB;YACA,IAAI,UAAU;gBACV,CAAC,GAAG,oBAAoB,gBAAgB,EAAE,YAAY;YAC1D,OACK;gBACD,OAAO,CAAC,GAAG,oBAAoB,gBAAgB,EAAE;YACrD;QACJ;IACJ;IACA,6BAA6B,kCAAkC,GAAG;AACtE,CAAC,EAAE,gCAAgC,CAAC,QAAQ,4BAA4B,GAAG,+BAA+B,CAAC,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4181, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/AI_Projects/CRMcriadores/crm-interno/node_modules/googleapis/build/src/apis/networkconnectivity/index.js"], "sourcesContent": ["\"use strict\";\n// Copyright 2020 Google LLC\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//    http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.AuthPlus = exports.networkconnectivity_v1alpha1 = exports.networkconnectivity_v1 = exports.auth = exports.VERSIONS = void 0;\nexports.networkconnectivity = networkconnectivity;\n/*! THIS FILE IS AUTO-GENERATED */\nconst googleapis_common_1 = require(\"googleapis-common\");\nconst v1_1 = require(\"./v1\");\nObject.defineProperty(exports, \"networkconnectivity_v1\", { enumerable: true, get: function () { return v1_1.networkconnectivity_v1; } });\nconst v1alpha1_1 = require(\"./v1alpha1\");\nObject.defineProperty(exports, \"networkconnectivity_v1alpha1\", { enumerable: true, get: function () { return v1alpha1_1.networkconnectivity_v1alpha1; } });\nexports.VERSIONS = {\n    v1: v1_1.networkconnectivity_v1.Networkconnectivity,\n    v1alpha1: v1alpha1_1.networkconnectivity_v1alpha1.Networkconnectivity,\n};\nfunction networkconnectivity(versionOrOptions) {\n    return (0, googleapis_common_1.getAPI)('networkconnectivity', versionOrOptions, exports.VERSIONS, this);\n}\nconst auth = new googleapis_common_1.AuthPlus();\nexports.auth = auth;\nvar googleapis_common_2 = require(\"googleapis-common\");\nObject.defineProperty(exports, \"AuthPlus\", { enumerable: true, get: function () { return googleapis_common_2.AuthPlus; } });\n"], "names": [], "mappings": "AAAA;AACA,4BAA4B;AAC5B,kEAAkE;AAClE,mEAAmE;AACnE,0CAA0C;AAC1C,EAAE;AACF,gDAAgD;AAChD,EAAE;AACF,sEAAsE;AACtE,oEAAoE;AACpE,2EAA2E;AAC3E,sEAAsE;AACtE,iCAAiC;AACjC,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,QAAQ,QAAQ,GAAG,QAAQ,4BAA4B,GAAG,QAAQ,sBAAsB,GAAG,QAAQ,IAAI,GAAG,QAAQ,QAAQ,GAAG,KAAK;AAClI,QAAQ,mBAAmB,GAAG;AAC9B,gCAAgC,GAChC,MAAM;AACN,MAAM;AACN,OAAO,cAAc,CAAC,SAAS,0BAA0B;IAAE,YAAY;IAAM,KAAK;QAAc,OAAO,KAAK,sBAAsB;IAAE;AAAE;AACtI,MAAM;AACN,OAAO,cAAc,CAAC,SAAS,gCAAgC;IAAE,YAAY;IAAM,KAAK;QAAc,OAAO,WAAW,4BAA4B;IAAE;AAAE;AACxJ,QAAQ,QAAQ,GAAG;IACf,IAAI,KAAK,sBAAsB,CAAC,mBAAmB;IACnD,UAAU,WAAW,4BAA4B,CAAC,mBAAmB;AACzE;AACA,SAAS,oBAAoB,gBAAgB;IACzC,OAAO,CAAC,GAAG,oBAAoB,MAAM,EAAE,uBAAuB,kBAAkB,QAAQ,QAAQ,EAAE,IAAI;AAC1G;AACA,MAAM,OAAO,IAAI,oBAAoB,QAAQ;AAC7C,QAAQ,IAAI,GAAG;AACf,IAAI;AACJ,OAAO,cAAc,CAAC,SAAS,YAAY;IAAE,YAAY;IAAM,KAAK;QAAc,OAAO,oBAAoB,QAAQ;IAAE;AAAE", "ignoreList": [0], "debugId": null}}]}
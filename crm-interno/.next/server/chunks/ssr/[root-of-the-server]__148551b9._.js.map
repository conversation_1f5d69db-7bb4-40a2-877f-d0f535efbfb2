{"version": 3, "sources": [], "sections": [{"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/AI_Projects/CRMcriadores/crm-interno/app/%28dashboard%29/layout.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport Link from 'next/link';\nimport { usePathname } from 'next/navigation';\n\ninterface DashboardLayoutProps {\n  children: React.ReactNode;\n}\n\nexport default function DashboardLayout({ children }: DashboardLayoutProps) {\n  const pathname = usePathname();\n  const [activeSection, setActiveSection] = useState('businesses');\n\n  // Determinar seção ativa baseada na URL\n  React.useEffect(() => {\n    if (pathname.includes('businesses')) setActiveSection('businesses');\n    else if (pathname.includes('influencers')) setActiveSection('influencers');\n    else if (pathname.includes('campaigns')) setActiveSection('campaigns');\n    else if (pathname.includes('jornada')) setActiveSection('jornada');\n    else setActiveSection('businesses');\n  }, [pathname]);\n\n  const navigationItems = [\n    {\n      id: 'businesses',\n      label: 'Negócios',\n      icon: '🏢',\n      href: '/businesses',\n      count: 12\n    },\n    {\n      id: 'influencers',\n      label: 'Influenciadores',\n      icon: '👥',\n      href: '/influencers',\n      count: 8\n    },\n    {\n      id: 'campaigns',\n      label: 'Campanhas',\n      icon: '📢',\n      href: '/campaigns',\n      count: 5\n    },\n    {\n      id: 'jornada',\n      label: 'Jornada',\n      icon: '🛤️',\n      href: '/jornada',\n      count: 12\n    }\n  ];\n\n  return (\n    <div className=\"min-h-screen bg-surface-dim\">\n      {/* Top Header */}\n      <header className=\"bg-surface shadow-sm\">\n        <div className=\"px-6 py-4\">\n          <div className=\"flex items-center justify-between mb-4\">\n            <div className=\"flex items-center space-x-4\">\n              <h1 className=\"text-2xl font-bold text-on-surface\">CRM Criadores</h1>\n              <span className=\"text-sm text-on-surface-variant\">Gestão Inteligente</span>\n            </div>\n\n            <div className=\"flex items-center space-x-3\">\n              <button className=\"btn-text\">\n                <span className=\"mr-2\">🔍</span>\n                Buscar\n              </button>\n              <button className=\"btn-outlined\">\n                <span className=\"mr-2\">📥</span>\n                Importar\n              </button>\n              <button className=\"btn-primary\">\n                <span className=\"mr-2\">➕</span>\n                Novo\n              </button>\n              <div className=\"w-8 h-8 bg-primary rounded-full flex items-center justify-center ml-4\">\n                <span className=\"text-on-primary text-sm font-medium\">U</span>\n              </div>\n            </div>\n          </div>\n\n          {/* Navigation Tabs */}\n          <nav className=\"flex space-x-1\">\n            {navigationItems.map((item) => (\n              <Link\n                key={item.id}\n                href={item.href}\n                onClick={() => setActiveSection(item.id)}\n                className={`flex items-center px-6 py-3 rounded-lg font-medium transition-all duration-200 ${\n                  activeSection === item.id\n                    ? 'bg-primary-container text-on-primary-container'\n                    : 'text-on-surface-variant hover:bg-surface-container hover:text-on-surface'\n                }`}\n              >\n                <span className=\"text-lg mr-3\">{item.icon}</span>\n                <span>{item.label}</span>\n                <span className=\"ml-2 text-xs bg-surface-container-high px-2 py-1 rounded-full\">\n                  {item.count}\n                </span>\n              </Link>\n            ))}\n          </nav>\n        </div>\n      </header>\n\n      {/* Main Content Area */}\n      <main className=\"p-6\">\n        <div className=\"max-w-7xl mx-auto\">\n          {children}\n        </div>\n      </main>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAUe,SAAS,gBAAgB,EAAE,QAAQ,EAAwB;IACxE,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEnD,wCAAwC;IACxC,qMAAA,CAAA,UAAK,CAAC,SAAS,CAAC;QACd,IAAI,SAAS,QAAQ,CAAC,eAAe,iBAAiB;aACjD,IAAI,SAAS,QAAQ,CAAC,gBAAgB,iBAAiB;aACvD,IAAI,SAAS,QAAQ,CAAC,cAAc,iBAAiB;aACrD,IAAI,SAAS,QAAQ,CAAC,YAAY,iBAAiB;aACnD,iBAAiB;IACxB,GAAG;QAAC;KAAS;IAEb,MAAM,kBAAkB;QACtB;YACE,IAAI;YACJ,OAAO;YACP,MAAM;YACN,MAAM;YACN,OAAO;QACT;QACA;YACE,IAAI;YACJ,OAAO;YACP,MAAM;YACN,MAAM;YACN,OAAO;QACT;QACA;YACE,IAAI;YACJ,OAAO;YACP,MAAM;YACN,MAAM;YACN,OAAO;QACT;QACA;YACE,IAAI;YACJ,OAAO;YACP,MAAM;YACN,MAAM;YACN,OAAO;QACT;KACD;IAED,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAO,WAAU;0BAChB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAqC;;;;;;sDACnD,8OAAC;4CAAK,WAAU;sDAAkC;;;;;;;;;;;;8CAGpD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAO,WAAU;;8DAChB,8OAAC;oDAAK,WAAU;8DAAO;;;;;;gDAAS;;;;;;;sDAGlC,8OAAC;4CAAO,WAAU;;8DAChB,8OAAC;oDAAK,WAAU;8DAAO;;;;;;gDAAS;;;;;;;sDAGlC,8OAAC;4CAAO,WAAU;;8DAChB,8OAAC;oDAAK,WAAU;8DAAO;;;;;;gDAAQ;;;;;;;sDAGjC,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAK,WAAU;0DAAsC;;;;;;;;;;;;;;;;;;;;;;;sCAM5D,8OAAC;4BAAI,WAAU;sCACZ,gBAAgB,GAAG,CAAC,CAAC,qBACpB,8OAAC,4JAAA,CAAA,UAAI;oCAEH,MAAM,KAAK,IAAI;oCACf,SAAS,IAAM,iBAAiB,KAAK,EAAE;oCACvC,WAAW,CAAC,+EAA+E,EACzF,kBAAkB,KAAK,EAAE,GACrB,mDACA,4EACJ;;sDAEF,8OAAC;4CAAK,WAAU;sDAAgB,KAAK,IAAI;;;;;;sDACzC,8OAAC;sDAAM,KAAK,KAAK;;;;;;sDACjB,8OAAC;4CAAK,WAAU;sDACb,KAAK,KAAK;;;;;;;mCAZR,KAAK,EAAE;;;;;;;;;;;;;;;;;;;;;0BAqBtB,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC;oBAAI,WAAU;8BACZ;;;;;;;;;;;;;;;;;AAKX", "debugId": null}}]}
{"version": 3, "sources": [], "sections": [{"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/AI_Projects/CRMcriadores/crm-interno/app/%28dashboard%29/layout.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport Link from 'next/link';\nimport { usePathname } from 'next/navigation';\n\ninterface DashboardLayoutProps {\n  children: React.ReactNode;\n}\n\nexport default function DashboardLayout({ children }: DashboardLayoutProps) {\n  const pathname = usePathname();\n  const [activeSection, setActiveSection] = useState('businesses');\n\n  // Determinar seção ativa baseada na URL\n  React.useEffect(() => {\n    if (pathname.includes('businesses')) setActiveSection('businesses');\n    else if (pathname.includes('influencers')) setActiveSection('influencers');\n    else if (pathname.includes('campaigns')) setActiveSection('campaigns');\n    else setActiveSection('businesses');\n  }, [pathname]);\n\n  const navigationItems = [\n    {\n      id: 'businesses',\n      label: 'Negócios',\n      icon: '🏢',\n      href: '/dashboard/businesses',\n      count: 12\n    },\n    {\n      id: 'influencers',\n      label: 'Influenciadores',\n      icon: '👥',\n      href: '/dashboard/influencers',\n      count: 8\n    },\n    {\n      id: 'campaigns',\n      label: 'Campanhas',\n      icon: '📢',\n      href: '/dashboard/campaigns',\n      count: 5\n    }\n  ];\n\n  return (\n    <div className=\"min-h-screen bg-surface-dim flex\">\n      {/* Sidebar Navigation - Gmail Style */}\n      <div className=\"w-64 bg-surface shadow-sm flex flex-col\">\n        {/* Header */}\n        <div className=\"p-6\">\n          <h1 className=\"text-2xl font-bold text-on-surface\">CRM Criadores</h1>\n          <p className=\"text-sm text-on-surface-variant mt-1\">Gestão Inteligente</p>\n        </div>\n\n        {/* Navigation Menu */}\n        <nav className=\"flex-1 px-3\">\n          <div className=\"space-y-1\">\n            {navigationItems.map((item) => (\n              <Link\n                key={item.id}\n                href={item.href}\n                onClick={() => setActiveSection(item.id)}\n                className={`nav-item ${activeSection === item.id ? 'active' : ''}`}\n              >\n                <span className=\"text-xl mr-3\">{item.icon}</span>\n                <span className=\"flex-1\">{item.label}</span>\n                <span className=\"text-xs bg-surface-container px-2 py-1 rounded-full\">\n                  {item.count}\n                </span>\n              </Link>\n            ))}\n          </div>\n\n          {/* Divider */}\n          <div className=\"my-6 h-px bg-outline-variant mx-4\"></div>\n\n          {/* Quick Actions */}\n          <div className=\"space-y-1\">\n            <button className=\"nav-item w-full text-left\">\n              <span className=\"text-xl mr-3\">⭐</span>\n              <span className=\"flex-1\">Favoritos</span>\n            </button>\n            <button className=\"nav-item w-full text-left\">\n              <span className=\"text-xl mr-3\">📊</span>\n              <span className=\"flex-1\">Relatórios</span>\n            </button>\n            <button className=\"nav-item w-full text-left\">\n              <span className=\"text-xl mr-3\">⚙️</span>\n              <span className=\"flex-1\">Configurações</span>\n            </button>\n          </div>\n        </nav>\n\n        {/* Footer */}\n        <div className=\"p-4 border-t border-outline-variant\">\n          <div className=\"flex items-center space-x-3\">\n            <div className=\"w-8 h-8 bg-primary rounded-full flex items-center justify-center\">\n              <span className=\"text-on-primary text-sm font-medium\">U</span>\n            </div>\n            <div className=\"flex-1\">\n              <p className=\"text-sm font-medium text-on-surface\">Usuário</p>\n              <p className=\"text-xs text-on-surface-variant\">Admin</p>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Main Content Area */}\n      <div className=\"flex-1 flex flex-col\">\n        {/* Top Bar */}\n        <header className=\"bg-surface shadow-sm px-6 py-4\">\n          <div className=\"flex items-center justify-between\">\n            <div className=\"flex items-center space-x-4\">\n              <h2 className=\"text-xl font-semibold text-on-surface\">\n                {navigationItems.find(item => item.id === activeSection)?.label || 'Dashboard'}\n              </h2>\n              <span className=\"text-sm text-on-surface-variant\">\n                {navigationItems.find(item => item.id === activeSection)?.count || 0} itens\n              </span>\n            </div>\n            \n            <div className=\"flex items-center space-x-3\">\n              <button className=\"btn-text\">\n                <span className=\"mr-2\">🔍</span>\n                Buscar\n              </button>\n              <button className=\"btn-outlined\">\n                <span className=\"mr-2\">📥</span>\n                Importar\n              </button>\n              <button className=\"btn-primary\">\n                <span className=\"mr-2\">➕</span>\n                Novo\n              </button>\n            </div>\n          </div>\n        </header>\n\n        {/* Content Area */}\n        <main className=\"flex-1 p-6 overflow-auto\">\n          {children}\n        </main>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAUe,SAAS,gBAAgB,EAAE,QAAQ,EAAwB;IACxE,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEnD,wCAAwC;IACxC,qMAAA,CAAA,UAAK,CAAC,SAAS,CAAC;QACd,IAAI,SAAS,QAAQ,CAAC,eAAe,iBAAiB;aACjD,IAAI,SAAS,QAAQ,CAAC,gBAAgB,iBAAiB;aACvD,IAAI,SAAS,QAAQ,CAAC,cAAc,iBAAiB;aACrD,iBAAiB;IACxB,GAAG;QAAC;KAAS;IAEb,MAAM,kBAAkB;QACtB;YACE,IAAI;YACJ,OAAO;YACP,MAAM;YACN,MAAM;YACN,OAAO;QACT;QACA;YACE,IAAI;YACJ,OAAO;YACP,MAAM;YACN,MAAM;YACN,OAAO;QACT;QACA;YACE,IAAI;YACJ,OAAO;YACP,MAAM;YACN,MAAM;YACN,OAAO;QACT;KACD;IAED,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAqC;;;;;;0CACnD,8OAAC;gCAAE,WAAU;0CAAuC;;;;;;;;;;;;kCAItD,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACZ,gBAAgB,GAAG,CAAC,CAAC,qBACpB,8OAAC,4JAAA,CAAA,UAAI;wCAEH,MAAM,KAAK,IAAI;wCACf,SAAS,IAAM,iBAAiB,KAAK,EAAE;wCACvC,WAAW,CAAC,SAAS,EAAE,kBAAkB,KAAK,EAAE,GAAG,WAAW,IAAI;;0DAElE,8OAAC;gDAAK,WAAU;0DAAgB,KAAK,IAAI;;;;;;0DACzC,8OAAC;gDAAK,WAAU;0DAAU,KAAK,KAAK;;;;;;0DACpC,8OAAC;gDAAK,WAAU;0DACb,KAAK,KAAK;;;;;;;uCARR,KAAK,EAAE;;;;;;;;;;0CAelB,8OAAC;gCAAI,WAAU;;;;;;0CAGf,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAO,WAAU;;0DAChB,8OAAC;gDAAK,WAAU;0DAAe;;;;;;0DAC/B,8OAAC;gDAAK,WAAU;0DAAS;;;;;;;;;;;;kDAE3B,8OAAC;wCAAO,WAAU;;0DAChB,8OAAC;gDAAK,WAAU;0DAAe;;;;;;0DAC/B,8OAAC;gDAAK,WAAU;0DAAS;;;;;;;;;;;;kDAE3B,8OAAC;wCAAO,WAAU;;0DAChB,8OAAC;gDAAK,WAAU;0DAAe;;;;;;0DAC/B,8OAAC;gDAAK,WAAU;0DAAS;;;;;;;;;;;;;;;;;;;;;;;;kCAM/B,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAK,WAAU;kDAAsC;;;;;;;;;;;8CAExD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAE,WAAU;sDAAsC;;;;;;sDACnD,8OAAC;4CAAE,WAAU;sDAAkC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAOvD,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAO,WAAU;kCAChB,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDACX,gBAAgB,IAAI,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK,gBAAgB,SAAS;;;;;;sDAErE,8OAAC;4CAAK,WAAU;;gDACb,gBAAgB,IAAI,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK,gBAAgB,SAAS;gDAAE;;;;;;;;;;;;;8CAIzE,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAO,WAAU;;8DAChB,8OAAC;oDAAK,WAAU;8DAAO;;;;;;gDAAS;;;;;;;sDAGlC,8OAAC;4CAAO,WAAU;;8DAChB,8OAAC;oDAAK,WAAU;8DAAO;;;;;;gDAAS;;;;;;;sDAGlC,8OAAC;4CAAO,WAAU;;8DAChB,8OAAC;oDAAK,WAAU;8DAAO;;;;;;gDAAQ;;;;;;;;;;;;;;;;;;;;;;;;kCAQvC,8OAAC;wBAAK,WAAU;kCACb;;;;;;;;;;;;;;;;;;AAKX", "debugId": null}}]}
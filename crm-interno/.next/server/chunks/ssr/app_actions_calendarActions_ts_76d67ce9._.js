module.exports = {

"[project]/app/actions/calendarActions.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/* __next_internal_action_entry_do_not_use__ [{"4065d208bb127f3649bec43a1d30b211f5598ff96c":"createSchedulingEvent","406a8e6df362697922cdb6277d7cc5bfceb14b817a":"deleteCalendarEvent","40b9886ee6eff374e831bc09f243f729deeddd84a3":"createCalendarEvent","60866bed54a51640ac21f2293a398c31a013526ac7":"updateCalendarEvent","60a260f981de4be87965322f8cb81878793ff4836b":"listCalendarEvents"},"",""] */ __turbopack_context__.s({
    "createCalendarEvent": (()=>createCalendarEvent),
    "createSchedulingEvent": (()=>createSchedulingEvent),
    "deleteCalendarEvent": (()=>deleteCalendarEvent),
    "listCalendarEvents": (()=>listCalendarEvents),
    "updateCalendarEvent": (()=>updateCalendarEvent)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/webpack/loaders/next-flight-loader/server-reference.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$app$2d$render$2f$encryption$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/app-render/encryption.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$googleapis$2f$build$2f$src$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/googleapis/build/src/index.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$google$2d$auth$2d$library$2f$build$2f$src$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/google-auth-library/build/src/index.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$validate$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/webpack/loaders/next-flight-loader/action-validate.js [app-rsc] (ecmascript)");
;
;
;
;
// Configuração de autenticação do Google Calendar
function getGoogleCalendarAuth() {
    const credentials = {
        client_email: process.env.GOOGLE_CLIENT_EMAIL,
        private_key: process.env.GOOGLE_PRIVATE_KEY?.replace(/\\n/g, '\n')
    };
    if (!credentials.client_email || !credentials.private_key) {
        throw new Error('Credenciais do Google Calendar não configuradas');
    }
    return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$google$2d$auth$2d$library$2f$build$2f$src$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["JWT"]({
        email: credentials.client_email,
        key: credentials.private_key,
        scopes: [
            'https://www.googleapis.com/auth/calendar'
        ]
    });
}
async function createCalendarEvent(event) {
    try {
        const auth = getGoogleCalendarAuth();
        const calendar = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$googleapis$2f$build$2f$src$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["google"].calendar({
            version: 'v3',
            auth
        });
        const calendarId = process.env.GOOGLE_CALENDAR_ID || 'primary';
        if (!calendarId || calendarId === 'primary') {
            console.log('Simulando criação de evento no calendário:', event.summary);
            return {
                success: true,
                message: 'Evento simulado criado com sucesso',
                eventId: `simulated-${Date.now()}`
            };
        }
        const calendarEvent = {
            summary: event.summary,
            description: event.description,
            start: {
                dateTime: event.startDateTime,
                timeZone: 'America/Sao_Paulo'
            },
            end: {
                dateTime: event.endDateTime,
                timeZone: 'America/Sao_Paulo'
            },
            attendees: event.attendees?.map((email)=>({
                    email
                })),
            location: event.location,
            reminders: {
                useDefault: false,
                overrides: [
                    {
                        method: 'email',
                        minutes: 24 * 60
                    },
                    {
                        method: 'popup',
                        minutes: 60
                    }
                ]
            }
        };
        const response = await calendar.events.insert({
            calendarId,
            requestBody: calendarEvent
        });
        return {
            success: true,
            data: response.data,
            eventId: response.data.id
        };
    } catch (error) {
        console.error('Erro ao criar evento no calendário:', error);
        throw new Error('Falha ao criar evento no calendário');
    }
}
async function updateCalendarEvent(eventId, event) {
    try {
        const auth = getGoogleCalendarAuth();
        const calendar = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$googleapis$2f$build$2f$src$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["google"].calendar({
            version: 'v3',
            auth
        });
        const calendarId = process.env.GOOGLE_CALENDAR_ID || 'primary';
        if (!calendarId || calendarId === 'primary') {
            console.log('Simulando atualização de evento no calendário:', eventId);
            return {
                success: true,
                message: 'Evento simulado atualizado com sucesso'
            };
        }
        const updateData = {};
        if (event.summary) updateData.summary = event.summary;
        if (event.description) updateData.description = event.description;
        if (event.startDateTime) {
            updateData.start = {
                dateTime: event.startDateTime,
                timeZone: 'America/Sao_Paulo'
            };
        }
        if (event.endDateTime) {
            updateData.end = {
                dateTime: event.endDateTime,
                timeZone: 'America/Sao_Paulo'
            };
        }
        if (event.attendees) {
            updateData.attendees = event.attendees.map((email)=>({
                    email
                }));
        }
        if (event.location) updateData.location = event.location;
        const response = await calendar.events.update({
            calendarId,
            eventId,
            requestBody: updateData
        });
        return {
            success: true,
            data: response.data
        };
    } catch (error) {
        console.error('Erro ao atualizar evento no calendário:', error);
        throw new Error('Falha ao atualizar evento no calendário');
    }
}
async function deleteCalendarEvent(eventId) {
    try {
        const auth = getGoogleCalendarAuth();
        const calendar = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$googleapis$2f$build$2f$src$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["google"].calendar({
            version: 'v3',
            auth
        });
        const calendarId = process.env.GOOGLE_CALENDAR_ID || 'primary';
        if (!calendarId || calendarId === 'primary') {
            console.log('Simulando exclusão de evento no calendário:', eventId);
            return {
                success: true,
                message: 'Evento simulado excluído com sucesso'
            };
        }
        await calendar.events.delete({
            calendarId,
            eventId
        });
        return {
            success: true
        };
    } catch (error) {
        console.error('Erro ao deletar evento no calendário:', error);
        throw new Error('Falha ao deletar evento do calendário');
    }
}
async function createSchedulingEvent(businessData) {
    try {
        const startDate = new Date();
        startDate.setDate(startDate.getDate() + 2); // 2 dias a partir de hoje
        startDate.setHours(14, 0, 0, 0); // 14:00
        const endDate = new Date(startDate);
        endDate.setHours(15, 0, 0, 0); // 15:00
        const event = {
            summary: `Agendamento: ${businessData.businessName}`,
            description: `
Agendamento para coordenação com criadores
        
Negócio: ${businessData.businessName}
Valor: R$ ${(businessData.value / 1000).toFixed(0)}K
Criadores: ${businessData.creators?.length || 0}
        
Próxima ação: ${businessData.nextAction}
        
Descrição: ${businessData.description}
      `.trim(),
            startDateTime: startDate.toISOString(),
            endDateTime: endDate.toISOString(),
            location: 'Reunião Online',
            attendees: businessData.creators?.map((creator)=>creator.email).filter(Boolean) || []
        };
        const result = await createCalendarEvent(event);
        console.log(`Evento criado para ${businessData.businessName}:`, result.eventId);
        return result;
    } catch (error) {
        console.error('Erro ao criar agendamento automático:', error);
        throw error;
    }
}
async function listCalendarEvents(timeMin, timeMax) {
    try {
        const auth = getGoogleCalendarAuth();
        const calendar = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$googleapis$2f$build$2f$src$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["google"].calendar({
            version: 'v3',
            auth
        });
        const calendarId = process.env.GOOGLE_CALENDAR_ID || 'primary';
        if (!calendarId || calendarId === 'primary') {
            console.log('Simulando listagem de eventos do calendário');
            return {
                success: true,
                events: [
                    {
                        id: 'simulated-1',
                        summary: 'Agendamento: Loja de Roupas Fashion',
                        start: {
                            dateTime: new Date().toISOString()
                        },
                        end: {
                            dateTime: new Date(Date.now() + 3600000).toISOString()
                        }
                    }
                ]
            };
        }
        const response = await calendar.events.list({
            calendarId,
            timeMin: timeMin || new Date().toISOString(),
            timeMax: timeMax,
            maxResults: 50,
            singleEvents: true,
            orderBy: 'startTime'
        });
        return {
            success: true,
            events: response.data.items || []
        };
    } catch (error) {
        console.error('Erro ao listar eventos do calendário:', error);
        throw new Error('Falha ao listar eventos do calendário');
    }
}
;
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$validate$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["ensureServerEntryExports"])([
    createCalendarEvent,
    updateCalendarEvent,
    deleteCalendarEvent,
    createSchedulingEvent,
    listCalendarEvents
]);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(createCalendarEvent, "40b9886ee6eff374e831bc09f243f729deeddd84a3", null);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(updateCalendarEvent, "60866bed54a51640ac21f2293a398c31a013526ac7", null);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(deleteCalendarEvent, "406a8e6df362697922cdb6277d7cc5bfceb14b817a", null);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(createSchedulingEvent, "4065d208bb127f3649bec43a1d30b211f5598ff96c", null);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(listCalendarEvents, "60a260f981de4be87965322f8cb81878793ff4836b", null);
}}),

};

//# sourceMappingURL=app_actions_calendarActions_ts_76d67ce9._.js.map
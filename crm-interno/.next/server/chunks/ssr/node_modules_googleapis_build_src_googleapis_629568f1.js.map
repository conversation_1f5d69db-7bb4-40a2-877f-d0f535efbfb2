{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/AI_Projects/CRMcriadores/crm-interno/node_modules/googleapis/build/src/googleapis.js"], "sourcesContent": ["\"use strict\";\n// Copyright 2012 Google LLC\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//    http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.GoogleApis = exports.AuthPlus = void 0;\nconst apis_1 = require(\"./apis\");\nconst googleapis_common_1 = require(\"googleapis-common\");\nObject.defineProperty(exports, \"AuthPlus\", { enumerable: true, get: function () { return googleapis_common_1.AuthPlus; } });\nclass GoogleApis extends apis_1.GeneratedAPIs {\n    _discovery = new googleapis_common_1.Discovery({ debug: false, includePrivate: false });\n    auth = new googleapis_common_1.AuthPlus();\n    _options = {};\n    /**\n     * GoogleApis constructor.\n     *\n     * @example\n     * ```js\n     * const GoogleApis = require('googleapis').GoogleApis;\n     * const google = new GoogleApis();\n     * ```\n     *\n     * @param options - Configuration options.\n     */\n    constructor(options) {\n        super();\n        this.options(options);\n    }\n    /**\n     * Obtain a Map of supported APIs, along with included API versions.\n     */\n    getSupportedAPIs() {\n        const apiMap = {};\n        Object.keys(apis_1.APIS).forEach(a => {\n            apiMap[a] = Object.keys(apis_1.APIS[a]);\n        });\n        return apiMap;\n    }\n    /**\n     * Set options.\n     *\n     * @param options - Configuration options.\n     */\n    options(options) {\n        this._options = options || {};\n    }\n    /**\n     * Add APIs endpoints to googleapis object\n     * E.g. googleapis.drive and googleapis.datastore\n     *\n     * @param apisToAdd - Apis to be added to this GoogleApis instance.\n     */\n    addAPIs(apisToAdd) {\n        for (const apiName in apisToAdd) {\n            // eslint-disable-next-line no-prototype-builtins\n            if (apisToAdd.hasOwnProperty(apiName)) {\n                // eslint-disable-next-line @typescript-eslint/no-explicit-any\n                this[apiName] = apisToAdd[apiName].bind(this);\n            }\n        }\n    }\n    discover(url, callback) {\n        if (callback) {\n            this.discoverAsync(url)\n                .then(() => callback())\n                .catch(callback);\n        }\n        else {\n            return this.discoverAsync(url);\n        }\n    }\n    async discoverAsync(url) {\n        const allApis = await this._discovery.discoverAllAPIs(url);\n        this.addAPIs(allApis);\n    }\n    /**\n     * Dynamically generate an Endpoint object from a discovery doc.\n     *\n     * @param path - Url or file path to discover doc for a single API.\n     * @param options - Options to configure the Endpoint object generated from the discovery doc.\n     * @returns A promise that resolves with the configured endpoint.\n     */\n    async discoverAPI(apiPath, options = {}) {\n        const endpointCreator = await this._discovery.discoverAPI(apiPath);\n        const ep = endpointCreator(options, this);\n        ep.google = this; // for drive.google.transporter\n        return Object.freeze(ep);\n    }\n}\nexports.GoogleApis = GoogleApis;\n"], "names": [], "mappings": "AAAA;AACA,4BAA4B;AAC5B,kEAAkE;AAClE,mEAAmE;AACnE,0CAA0C;AAC1C,EAAE;AACF,gDAAgD;AAChD,EAAE;AACF,sEAAsE;AACtE,oEAAoE;AACpE,2EAA2E;AAC3E,sEAAsE;AACtE,iCAAiC;AACjC,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,QAAQ,UAAU,GAAG,QAAQ,QAAQ,GAAG,KAAK;AAC7C,MAAM;AACN,MAAM;AACN,OAAO,cAAc,CAAC,SAAS,YAAY;IAAE,YAAY;IAAM,KAAK;QAAc,OAAO,oBAAoB,QAAQ;IAAE;AAAE;AACzH,MAAM,mBAAmB,OAAO,aAAa;IACzC,aAAa,IAAI,oBAAoB,SAAS,CAAC;QAAE,OAAO;QAAO,gBAAgB;IAAM,GAAG;IACxF,OAAO,IAAI,oBAAoB,QAAQ,GAAG;IAC1C,WAAW,CAAC,EAAE;IACd;;;;;;;;;;KAUC,GACD,YAAY,OAAO,CAAE;QACjB,KAAK;QACL,IAAI,CAAC,OAAO,CAAC;IACjB;IACA;;KAEC,GACD,mBAAmB;QACf,MAAM,SAAS,CAAC;QAChB,OAAO,IAAI,CAAC,OAAO,IAAI,EAAE,OAAO,CAAC,CAAA;YAC7B,MAAM,CAAC,EAAE,GAAG,OAAO,IAAI,CAAC,OAAO,IAAI,CAAC,EAAE;QAC1C;QACA,OAAO;IACX;IACA;;;;KAIC,GACD,QAAQ,OAAO,EAAE;QACb,IAAI,CAAC,QAAQ,GAAG,WAAW,CAAC;IAChC;IACA;;;;;KAKC,GACD,QAAQ,SAAS,EAAE;QACf,IAAK,MAAM,WAAW,UAAW;YAC7B,iDAAiD;YACjD,IAAI,UAAU,cAAc,CAAC,UAAU;gBACnC,8DAA8D;gBAC9D,IAAI,CAAC,QAAQ,GAAG,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI;YAChD;QACJ;IACJ;IACA,SAAS,GAAG,EAAE,QAAQ,EAAE;QACpB,IAAI,UAAU;YACV,IAAI,CAAC,aAAa,CAAC,KACd,IAAI,CAAC,IAAM,YACX,KAAK,CAAC;QACf,OACK;YACD,OAAO,IAAI,CAAC,aAAa,CAAC;QAC9B;IACJ;IACA,MAAM,cAAc,GAAG,EAAE;QACrB,MAAM,UAAU,MAAM,IAAI,CAAC,UAAU,CAAC,eAAe,CAAC;QACtD,IAAI,CAAC,OAAO,CAAC;IACjB;IACA;;;;;;KAMC,GACD,MAAM,YAAY,OAAO,EAAE,UAAU,CAAC,CAAC,EAAE;QACrC,MAAM,kBAAkB,MAAM,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC;QAC1D,MAAM,KAAK,gBAAgB,SAAS,IAAI;QACxC,GAAG,MAAM,GAAG,IAAI,EAAE,+BAA+B;QACjD,OAAO,OAAO,MAAM,CAAC;IACzB;AACJ;AACA,QAAQ,UAAU,GAAG", "ignoreList": [0], "debugId": null}}]}
{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 36, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/AI_Projects/CRMcriadores/crm-interno/app/%28dashboard%29/dashboard/page.tsx"], "sourcesContent": ["import React from 'react';\nimport Link from 'next/link';\n\nexport default function DashboardPage() {\n  return (\n    <div>\n      <h1 className=\"text-3xl font-bold text-on-surface mb-6\">Dashboard CRM</h1>\n      <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6\">\n        <Link \n          href=\"/dashboard/businesses\" \n          className=\"block p-6 bg-primary-container rounded-xl border border-outline-variant hover:shadow-md transition-shadow\"\n        >\n          <h2 className=\"text-xl font-semibold text-on-primary-container mb-2\">Neg<PERSON><PERSON>s</h2>\n          <p className=\"text-on-primary-container/70\">Gerencie seus negócios e clientes</p>\n        </Link>\n        \n        <Link \n          href=\"/dashboard/influencers\" \n          className=\"block p-6 bg-secondary-container rounded-xl border border-outline-variant hover:shadow-md transition-shadow\"\n        >\n          <h2 className=\"text-xl font-semibold text-on-secondary-container mb-2\">Influenciadores</h2>\n          <p className=\"text-on-secondary-container/70\">Gerencie sua rede de influenciadores</p>\n        </Link>\n        \n        <Link \n          href=\"/dashboard/campaigns\" \n          className=\"block p-6 bg-tertiary-container rounded-xl border border-outline-variant hover:shadow-md transition-shadow\"\n        >\n          <h2 className=\"text-xl font-semibold text-on-tertiary-container mb-2\">Campanhas</h2>\n          <p className=\"text-on-tertiary-container/70\">Gerencie suas campanhas ativas</p>\n        </Link>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AACA;;;AAEe,SAAS;IACtB,qBACE,8OAAC;;0BACC,8OAAC;gBAAG,WAAU;0BAA0C;;;;;;0BACxD,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,4JAAA,CAAA,UAAI;wBACH,MAAK;wBACL,WAAU;;0CAEV,8OAAC;gCAAG,WAAU;0CAAuD;;;;;;0CACrE,8OAAC;gCAAE,WAAU;0CAA+B;;;;;;;;;;;;kCAG9C,8OAAC,4JAAA,CAAA,UAAI;wBACH,MAAK;wBACL,WAAU;;0CAEV,8OAAC;gCAAG,WAAU;0CAAyD;;;;;;0CACvE,8OAAC;gCAAE,WAAU;0CAAiC;;;;;;;;;;;;kCAGhD,8OAAC,4JAAA,CAAA,UAAI;wBACH,MAAK;wBACL,WAAU;;0CAEV,8OAAC;gCAAG,WAAU;0CAAwD;;;;;;0CACtE,8OAAC;gCAAE,WAAU;0CAAgC;;;;;;;;;;;;;;;;;;;;;;;;AAKvD", "debugId": null}}]}
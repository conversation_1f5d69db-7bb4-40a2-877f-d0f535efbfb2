{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/AI_Projects/CRMcriadores/crm-interno/node_modules/next/src/build/webpack/loaders/next-flight-loader/action-client-wrapper.ts"], "sourcesContent": ["// This file must be bundled in the app's client layer, it shouldn't be directly\n// imported by the server.\n\nexport { callServer } from 'next/dist/client/app-call-server'\nexport { findSourceMapURL } from 'next/dist/client/app-find-source-map-url'\n\n// A noop wrapper to let the Flight client create the server reference.\n// See also: https://github.com/facebook/react/pull/26632\n// Since we're using the Edge build of Flight client for SSR [1], here we need to\n// also use the same Edge build to create the reference. For the client bundle,\n// we use the default and let Webpack to resolve it to the correct version.\n// 1: https://github.com/vercel/next.js/blob/16eb80b0b0be13f04a6407943664b5efd8f3d7d0/packages/next/src/server/app-render/use-flight-response.tsx#L24-L26\nexport const createServerReference = (\n  (!!process.env.NEXT_RUNTIME\n    ? // eslint-disable-next-line import/no-extraneous-dependencies\n      require('react-server-dom-webpack/client.edge')\n    : // eslint-disable-next-line import/no-extraneous-dependencies\n      require('react-server-dom-webpack/client')) as typeof import('react-server-dom-webpack/client')\n).createServerReference\n"], "names": ["callServer", "createServerReference", "findSourceMapURL", "process", "env", "NEXT_RUNTIME", "require"], "mappings": "AAAA,gFAAgF;AAChF,0BAA0B;AAYrBG,QAAQC,GAAG,CAACC,YAAY,GAEvBC,QAAQ,0CAERA,QAAQ;;;;;;;;;;;;;;;;;IAdLN,UAAU,EAAA;eAAVA,eAAAA,UAAU;;IASNC,qBAAqB,EAAA;eAArBA;;IARJC,gBAAgB,EAAA;eAAhBA,qBAAAA,gBAAgB;;;+BADE;qCACM;AAQ1B,MAAMD,wBACV,CAAA,CAAC,+MAI2C,EAC7CA,qBAAqB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 43, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/AI_Projects/CRMcriadores/crm-interno/node_modules/next/src/shared/lib/image-blur-svg.ts"], "sourcesContent": ["/**\n * A shared function, used on both client and server, to generate a SVG blur placeholder.\n */\nexport function getImageBlurSvg({\n  widthInt,\n  heightInt,\n  blurWidth,\n  blurHeight,\n  blurDataURL,\n  objectFit,\n}: {\n  widthInt?: number\n  heightInt?: number\n  blurWidth?: number\n  blurHeight?: number\n  blurDataURL: string\n  objectFit?: string\n}): string {\n  const std = 20\n  const svgWidth = blurWidth ? blurWidth * 40 : widthInt\n  const svgHeight = blurHeight ? blurHeight * 40 : heightInt\n\n  const viewBox =\n    svgWidth && svgHeight ? `viewBox='0 0 ${svgWidth} ${svgHeight}'` : ''\n  const preserveAspectRatio = viewBox\n    ? 'none'\n    : objectFit === 'contain'\n      ? 'xMidYMid'\n      : objectFit === 'cover'\n        ? 'xMidYMid slice'\n        : 'none'\n\n  return `%3Csvg xmlns='http://www.w3.org/2000/svg' ${viewBox}%3E%3Cfilter id='b' color-interpolation-filters='sRGB'%3E%3CfeGaussianBlur stdDeviation='${std}'/%3E%3CfeColorMatrix values='1 0 0 0 0 0 1 0 0 0 0 0 1 0 0 0 0 0 100 -1' result='s'/%3E%3CfeFlood x='0' y='0' width='100%25' height='100%25'/%3E%3CfeComposite operator='out' in='s'/%3E%3CfeComposite in2='SourceGraphic'/%3E%3CfeGaussianBlur stdDeviation='${std}'/%3E%3C/filter%3E%3Cimage width='100%25' height='100%25' x='0' y='0' preserveAspectRatio='${preserveAspectRatio}' style='filter: url(%23b);' href='${blurDataURL}'/%3E%3C/svg%3E`\n}\n"], "names": ["getImageBlurSvg", "widthInt", "heightInt", "blur<PERSON>idth", "blurHeight", "blurDataURL", "objectFit", "std", "svgWidth", "svgHeight", "viewBox", "preserveAspectRatio"], "mappings": "AAAA;;CAEC,GAAA;;;;+BACeA,mBAAAA;;;eAAAA;;;AAAT,SAASA,gBAAgB,KAc/B;IAd+B,IAAA,EAC9BC,QAAQ,EACRC,SAAS,EACTC,SAAS,EACTC,UAAU,EACVC,WAAW,EACXC,SAAS,EAQV,GAd+B;IAe9B,MAAMC,MAAM;IACZ,MAAMC,WAAWL,YAAYA,YAAY,KAAKF;IAC9C,MAAMQ,YAAYL,aAAaA,aAAa,KAAKF;IAEjD,MAAMQ,UACJF,YAAYC,YAAa,kBAAeD,WAAS,MAAGC,YAAU,MAAK;IACrE,MAAME,sBAAsBD,UACxB,SACAJ,cAAc,YACZ,aACAA,cAAc,UACZ,mBACA;IAER,OAAQ,+CAA4CI,UAAQ,8FAA2FH,MAAI,oQAAiQA,MAAI,gGAA6FI,sBAAoB,wCAAqCN,cAAY;AACpkB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 69, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/AI_Projects/CRMcriadores/crm-interno/node_modules/next/src/shared/lib/image-config.ts"], "sourcesContent": ["export const VALID_LOADERS = [\n  'default',\n  'imgix',\n  'cloudinary',\n  'akamai',\n  'custom',\n] as const\n\nexport type LoaderValue = (typeof VALID_LOADERS)[number]\n\nexport type ImageLoaderProps = {\n  src: string\n  width: number\n  quality?: number\n}\n\nexport type ImageLoaderPropsWithConfig = ImageLoaderProps & {\n  config: Readonly<ImageConfig>\n}\n\nexport type LocalPattern = {\n  /**\n   * Can be literal or wildcard.\n   * Single `*` matches a single path segment.\n   * Double `**` matches any number of path segments.\n   */\n  pathname?: string\n\n  /**\n   * Can be literal query string such as `?v=1` or\n   * empty string meaning no query string.\n   */\n  search?: string\n}\n\nexport type RemotePattern = {\n  /**\n   * Must be `http` or `https`.\n   */\n  protocol?: 'http' | 'https'\n\n  /**\n   * Can be literal or wildcard.\n   * Single `*` matches a single subdomain.\n   * Double `**` matches any number of subdomains.\n   */\n  hostname: string\n\n  /**\n   * Can be literal port such as `8080` or empty string\n   * meaning no port.\n   */\n  port?: string\n\n  /**\n   * Can be literal or wildcard.\n   * Single `*` matches a single path segment.\n   * Double `**` matches any number of path segments.\n   */\n  pathname?: string\n\n  /**\n   * Can be literal query string such as `?v=1` or\n   * empty string meaning no query string.\n   */\n  search?: string\n}\n\ntype ImageFormat = 'image/avif' | 'image/webp'\n\n/**\n * Image configurations\n *\n * @see [Image configuration options](https://nextjs.org/docs/api-reference/next/image#configuration-options)\n */\nexport type ImageConfigComplete = {\n  /** @see [Device sizes documentation](https://nextjs.org/docs/api-reference/next/image#device-sizes) */\n  deviceSizes: number[]\n\n  /** @see [Image sizing documentation](https://nextjs.org/docs/app/building-your-application/optimizing/images#image-sizing) */\n  imageSizes: number[]\n\n  /** @see [Image loaders configuration](https://nextjs.org/docs/api-reference/next/legacy/image#loader) */\n  loader: LoaderValue\n\n  /** @see [Image loader configuration](https://nextjs.org/docs/api-reference/next/legacy/image#loader-configuration) */\n  path: string\n\n  /** @see [Image loader configuration](https://nextjs.org/docs/api-reference/next/image#loader-configuration) */\n  loaderFile: string\n\n  /**\n   * @deprecated Use `remotePatterns` instead.\n   */\n  domains: string[]\n\n  /** @see [Disable static image import configuration](https://nextjs.org/docs/api-reference/next/image#disable-static-imports) */\n  disableStaticImages: boolean\n\n  /** @see [Cache behavior](https://nextjs.org/docs/api-reference/next/image#caching-behavior) */\n  minimumCacheTTL: number\n\n  /** @see [Acceptable formats](https://nextjs.org/docs/api-reference/next/image#acceptable-formats) */\n  formats: ImageFormat[]\n\n  /** @see [Dangerously Allow SVG](https://nextjs.org/docs/api-reference/next/image#dangerously-allow-svg) */\n  dangerouslyAllowSVG: boolean\n\n  /** @see [Dangerously Allow SVG](https://nextjs.org/docs/api-reference/next/image#dangerously-allow-svg) */\n  contentSecurityPolicy: string\n\n  /** @see [Dangerously Allow SVG](https://nextjs.org/docs/api-reference/next/image#dangerously-allow-svg) */\n  contentDispositionType: 'inline' | 'attachment'\n\n  /** @see [Remote Patterns](https://nextjs.org/docs/api-reference/next/image#remotepatterns) */\n  remotePatterns: Array<URL | RemotePattern>\n\n  /** @see [Remote Patterns](https://nextjs.org/docs/api-reference/next/image#localPatterns) */\n  localPatterns: LocalPattern[] | undefined\n\n  /** @see [Qualities](https://nextjs.org/docs/api-reference/next/image#qualities) */\n  qualities: number[] | undefined\n\n  /** @see [Unoptimized](https://nextjs.org/docs/api-reference/next/image#unoptimized) */\n  unoptimized: boolean\n}\n\nexport type ImageConfig = Partial<ImageConfigComplete>\n\nexport const imageConfigDefault: ImageConfigComplete = {\n  deviceSizes: [640, 750, 828, 1080, 1200, 1920, 2048, 3840],\n  imageSizes: [16, 32, 48, 64, 96, 128, 256, 384],\n  path: '/_next/image',\n  loader: 'default',\n  loaderFile: '',\n  domains: [],\n  disableStaticImages: false,\n  minimumCacheTTL: 60,\n  formats: ['image/webp'],\n  dangerouslyAllowSVG: false,\n  contentSecurityPolicy: `script-src 'none'; frame-src 'none'; sandbox;`,\n  contentDispositionType: 'attachment',\n  localPatterns: undefined, // default: allow all local images\n  remotePatterns: [], // default: allow no remote images\n  qualities: undefined, // default: allow all qualities\n  unoptimized: false,\n}\n"], "names": ["VALID_LOADERS", "imageConfigDefault", "deviceSizes", "imageSizes", "path", "loader", "loaderFile", "domains", "disableStaticImages", "minimumCacheTTL", "formats", "dangerouslyAllowSVG", "contentSecurityPolicy", "contentDispositionType", "localPatterns", "undefined", "remotePatterns", "qualities", "unoptimized"], "mappings": ";;;;;;;;;;;;;;;IAAaA,aAAa,EAAA;eAAbA;;IAiIAC,kBAAkB,EAAA;eAAlBA;;;AAjIN,MAAMD,gBAAgB;IAC3B;IACA;IACA;IACA;IACA;CACD;AA2HM,MAAMC,qBAA0C;IACrDC,aAAa;QAAC;QAAK;QAAK;QAAK;QAAM;QAAM;QAAM;QAAM;KAAK;IAC1DC,YAAY;QAAC;QAAI;QAAI;QAAI;QAAI;QAAI;QAAK;QAAK;KAAI;IAC/CC,MAAM;IACNC,QAAQ;IACRC,YAAY;IACZC,SAAS,EAAE;IACXC,qBAAqB;IACrBC,iBAAiB;IACjBC,SAAS;QAAC;KAAa;IACvBC,qBAAqB;IACrBC,uBAAwB;IACxBC,wBAAwB;IACxBC,eAAeC;IACfC,gBAAgB,EAAE;IAClBC,WAAWF;IACXG,aAAa;AACf", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 141, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/AI_Projects/CRMcriadores/crm-interno/node_modules/next/src/shared/lib/get-img-props.ts"], "sourcesContent": ["import { warnOnce } from './utils/warn-once'\nimport { getImageBlurSvg } from './image-blur-svg'\nimport { imageConfigDefault } from './image-config'\nimport type {\n  ImageConfigComplete,\n  ImageLoaderProps,\n  ImageLoaderPropsWithConfig,\n} from './image-config'\n\nimport type { CSSProperties, JSX } from 'react'\n\nexport interface StaticImageData {\n  src: string\n  height: number\n  width: number\n  blurDataURL?: string\n  blurWidth?: number\n  blurHeight?: number\n}\n\nexport interface StaticRequire {\n  default: StaticImageData\n}\n\nexport type StaticImport = StaticRequire | StaticImageData\n\nexport type ImageProps = Omit<\n  JSX.IntrinsicElements['img'],\n  'src' | 'srcSet' | 'ref' | 'alt' | 'width' | 'height' | 'loading'\n> & {\n  src: string | StaticImport\n  alt: string\n  width?: number | `${number}`\n  height?: number | `${number}`\n  fill?: boolean\n  loader?: ImageLoader\n  quality?: number | `${number}`\n  priority?: boolean\n  loading?: LoadingValue\n  placeholder?: PlaceholderValue\n  blurDataURL?: string\n  unoptimized?: boolean\n  overrideSrc?: string\n  /**\n   * @deprecated Use `onLoad` instead.\n   * @see https://nextjs.org/docs/app/api-reference/components/image#onload\n   */\n  onLoadingComplete?: OnLoadingComplete\n  /**\n   * @deprecated Use `fill` prop instead of `layout=\"fill\"` or change import to `next/legacy/image`.\n   * @see https://nextjs.org/docs/api-reference/next/legacy/image\n   */\n  layout?: string\n  /**\n   * @deprecated Use `style` prop instead.\n   */\n  objectFit?: string\n  /**\n   * @deprecated Use `style` prop instead.\n   */\n  objectPosition?: string\n  /**\n   * @deprecated This prop does not do anything.\n   */\n  lazyBoundary?: string\n  /**\n   * @deprecated This prop does not do anything.\n   */\n  lazyRoot?: string\n}\n\nexport type ImgProps = Omit<ImageProps, 'src' | 'loader'> & {\n  loading: LoadingValue\n  width: number | undefined\n  height: number | undefined\n  style: NonNullable<JSX.IntrinsicElements['img']['style']>\n  sizes: string | undefined\n  srcSet: string | undefined\n  src: string\n}\n\nconst VALID_LOADING_VALUES = ['lazy', 'eager', undefined] as const\n\n// Object-fit values that are not valid background-size values\nconst INVALID_BACKGROUND_SIZE_VALUES = [\n  '-moz-initial',\n  'fill',\n  'none',\n  'scale-down',\n  undefined,\n]\ntype LoadingValue = (typeof VALID_LOADING_VALUES)[number]\ntype ImageConfig = ImageConfigComplete & {\n  allSizes: number[]\n  output?: 'standalone' | 'export'\n}\n\nexport type ImageLoader = (p: ImageLoaderProps) => string\n\n// Do not export - this is an internal type only\n// because `next.config.js` is only meant for the\n// built-in loaders, not for a custom loader() prop.\ntype ImageLoaderWithConfig = (p: ImageLoaderPropsWithConfig) => string\n\nexport type PlaceholderValue = 'blur' | 'empty' | `data:image/${string}`\nexport type OnLoad = React.ReactEventHandler<HTMLImageElement> | undefined\nexport type OnLoadingComplete = (img: HTMLImageElement) => void\n\nexport type PlaceholderStyle = Partial<\n  Pick<\n    CSSProperties,\n    | 'backgroundSize'\n    | 'backgroundPosition'\n    | 'backgroundRepeat'\n    | 'backgroundImage'\n  >\n>\n\nfunction isStaticRequire(\n  src: StaticRequire | StaticImageData\n): src is StaticRequire {\n  return (src as StaticRequire).default !== undefined\n}\n\nfunction isStaticImageData(\n  src: StaticRequire | StaticImageData\n): src is StaticImageData {\n  return (src as StaticImageData).src !== undefined\n}\n\nfunction isStaticImport(src: string | StaticImport): src is StaticImport {\n  return (\n    !!src &&\n    typeof src === 'object' &&\n    (isStaticRequire(src as StaticImport) ||\n      isStaticImageData(src as StaticImport))\n  )\n}\n\nconst allImgs = new Map<\n  string,\n  { src: string; priority: boolean; placeholder: PlaceholderValue }\n>()\nlet perfObserver: PerformanceObserver | undefined\n\nfunction getInt(x: unknown): number | undefined {\n  if (typeof x === 'undefined') {\n    return x\n  }\n  if (typeof x === 'number') {\n    return Number.isFinite(x) ? x : NaN\n  }\n  if (typeof x === 'string' && /^[0-9]+$/.test(x)) {\n    return parseInt(x, 10)\n  }\n  return NaN\n}\n\nfunction getWidths(\n  { deviceSizes, allSizes }: ImageConfig,\n  width: number | undefined,\n  sizes: string | undefined\n): { widths: number[]; kind: 'w' | 'x' } {\n  if (sizes) {\n    // Find all the \"vw\" percent sizes used in the sizes prop\n    const viewportWidthRe = /(^|\\s)(1?\\d?\\d)vw/g\n    const percentSizes = []\n    for (let match; (match = viewportWidthRe.exec(sizes)); match) {\n      percentSizes.push(parseInt(match[2]))\n    }\n    if (percentSizes.length) {\n      const smallestRatio = Math.min(...percentSizes) * 0.01\n      return {\n        widths: allSizes.filter((s) => s >= deviceSizes[0] * smallestRatio),\n        kind: 'w',\n      }\n    }\n    return { widths: allSizes, kind: 'w' }\n  }\n  if (typeof width !== 'number') {\n    return { widths: deviceSizes, kind: 'w' }\n  }\n\n  const widths = [\n    ...new Set(\n      // > This means that most OLED screens that say they are 3x resolution,\n      // > are actually 3x in the green color, but only 1.5x in the red and\n      // > blue colors. Showing a 3x resolution image in the app vs a 2x\n      // > resolution image will be visually the same, though the 3x image\n      // > takes significantly more data. Even true 3x resolution screens are\n      // > wasteful as the human eye cannot see that level of detail without\n      // > something like a magnifying glass.\n      // https://blog.twitter.com/engineering/en_us/topics/infrastructure/2019/capping-image-fidelity-on-ultra-high-resolution-devices.html\n      [width, width * 2 /*, width * 3*/].map(\n        (w) => allSizes.find((p) => p >= w) || allSizes[allSizes.length - 1]\n      )\n    ),\n  ]\n  return { widths, kind: 'x' }\n}\n\ntype GenImgAttrsData = {\n  config: ImageConfig\n  src: string\n  unoptimized: boolean\n  loader: ImageLoaderWithConfig\n  width?: number\n  quality?: number\n  sizes?: string\n}\n\ntype GenImgAttrsResult = {\n  src: string\n  srcSet: string | undefined\n  sizes: string | undefined\n}\n\nfunction generateImgAttrs({\n  config,\n  src,\n  unoptimized,\n  width,\n  quality,\n  sizes,\n  loader,\n}: GenImgAttrsData): GenImgAttrsResult {\n  if (unoptimized) {\n    return { src, srcSet: undefined, sizes: undefined }\n  }\n\n  const { widths, kind } = getWidths(config, width, sizes)\n  const last = widths.length - 1\n\n  return {\n    sizes: !sizes && kind === 'w' ? '100vw' : sizes,\n    srcSet: widths\n      .map(\n        (w, i) =>\n          `${loader({ config, src, quality, width: w })} ${\n            kind === 'w' ? w : i + 1\n          }${kind}`\n      )\n      .join(', '),\n\n    // It's intended to keep `src` the last attribute because React updates\n    // attributes in order. If we keep `src` the first one, Safari will\n    // immediately start to fetch `src`, before `sizes` and `srcSet` are even\n    // updated by React. That causes multiple unnecessary requests if `srcSet`\n    // and `sizes` are defined.\n    // This bug cannot be reproduced in Chrome or Firefox.\n    src: loader({ config, src, quality, width: widths[last] }),\n  }\n}\n\n/**\n * A shared function, used on both client and server, to generate the props for <img>.\n */\nexport function getImgProps(\n  {\n    src,\n    sizes,\n    unoptimized = false,\n    priority = false,\n    loading,\n    className,\n    quality,\n    width,\n    height,\n    fill = false,\n    style,\n    overrideSrc,\n    onLoad,\n    onLoadingComplete,\n    placeholder = 'empty',\n    blurDataURL,\n    fetchPriority,\n    decoding = 'async',\n    layout,\n    objectFit,\n    objectPosition,\n    lazyBoundary,\n    lazyRoot,\n    ...rest\n  }: ImageProps,\n  _state: {\n    defaultLoader: ImageLoaderWithConfig\n    imgConf: ImageConfigComplete\n    showAltText?: boolean\n    blurComplete?: boolean\n  }\n): {\n  props: ImgProps\n  meta: {\n    unoptimized: boolean\n    priority: boolean\n    placeholder: NonNullable<ImageProps['placeholder']>\n    fill: boolean\n  }\n} {\n  const { imgConf, showAltText, blurComplete, defaultLoader } = _state\n  let config: ImageConfig\n  let c = imgConf || imageConfigDefault\n  if ('allSizes' in c) {\n    config = c as ImageConfig\n  } else {\n    const allSizes = [...c.deviceSizes, ...c.imageSizes].sort((a, b) => a - b)\n    const deviceSizes = c.deviceSizes.sort((a, b) => a - b)\n    const qualities = c.qualities?.sort((a, b) => a - b)\n    config = { ...c, allSizes, deviceSizes, qualities }\n  }\n\n  if (typeof defaultLoader === 'undefined') {\n    throw new Error(\n      'images.loaderFile detected but the file is missing default export.\\nRead more: https://nextjs.org/docs/messages/invalid-images-config'\n    )\n  }\n  let loader: ImageLoaderWithConfig = rest.loader || defaultLoader\n\n  // Remove property so it's not spread on <img> element\n  delete rest.loader\n  delete (rest as any).srcSet\n\n  // This special value indicates that the user\n  // didn't define a \"loader\" prop or \"loader\" config.\n  const isDefaultLoader = '__next_img_default' in loader\n\n  if (isDefaultLoader) {\n    if (config.loader === 'custom') {\n      throw new Error(\n        `Image with src \"${src}\" is missing \"loader\" prop.` +\n          `\\nRead more: https://nextjs.org/docs/messages/next-image-missing-loader`\n      )\n    }\n  } else {\n    // The user defined a \"loader\" prop or config.\n    // Since the config object is internal only, we\n    // must not pass it to the user-defined \"loader\".\n    const customImageLoader = loader as ImageLoader\n    loader = (obj) => {\n      const { config: _, ...opts } = obj\n      return customImageLoader(opts)\n    }\n  }\n\n  if (layout) {\n    if (layout === 'fill') {\n      fill = true\n    }\n    const layoutToStyle: Record<string, Record<string, string> | undefined> = {\n      intrinsic: { maxWidth: '100%', height: 'auto' },\n      responsive: { width: '100%', height: 'auto' },\n    }\n    const layoutToSizes: Record<string, string | undefined> = {\n      responsive: '100vw',\n      fill: '100vw',\n    }\n    const layoutStyle = layoutToStyle[layout]\n    if (layoutStyle) {\n      style = { ...style, ...layoutStyle }\n    }\n    const layoutSizes = layoutToSizes[layout]\n    if (layoutSizes && !sizes) {\n      sizes = layoutSizes\n    }\n  }\n\n  let staticSrc = ''\n  let widthInt = getInt(width)\n  let heightInt = getInt(height)\n  let blurWidth: number | undefined\n  let blurHeight: number | undefined\n  if (isStaticImport(src)) {\n    const staticImageData = isStaticRequire(src) ? src.default : src\n\n    if (!staticImageData.src) {\n      throw new Error(\n        `An object should only be passed to the image component src parameter if it comes from a static image import. It must include src. Received ${JSON.stringify(\n          staticImageData\n        )}`\n      )\n    }\n    if (!staticImageData.height || !staticImageData.width) {\n      throw new Error(\n        `An object should only be passed to the image component src parameter if it comes from a static image import. It must include height and width. Received ${JSON.stringify(\n          staticImageData\n        )}`\n      )\n    }\n\n    blurWidth = staticImageData.blurWidth\n    blurHeight = staticImageData.blurHeight\n    blurDataURL = blurDataURL || staticImageData.blurDataURL\n    staticSrc = staticImageData.src\n\n    if (!fill) {\n      if (!widthInt && !heightInt) {\n        widthInt = staticImageData.width\n        heightInt = staticImageData.height\n      } else if (widthInt && !heightInt) {\n        const ratio = widthInt / staticImageData.width\n        heightInt = Math.round(staticImageData.height * ratio)\n      } else if (!widthInt && heightInt) {\n        const ratio = heightInt / staticImageData.height\n        widthInt = Math.round(staticImageData.width * ratio)\n      }\n    }\n  }\n  src = typeof src === 'string' ? src : staticSrc\n\n  let isLazy =\n    !priority && (loading === 'lazy' || typeof loading === 'undefined')\n  if (!src || src.startsWith('data:') || src.startsWith('blob:')) {\n    // https://developer.mozilla.org/docs/Web/HTTP/Basics_of_HTTP/Data_URIs\n    unoptimized = true\n    isLazy = false\n  }\n  if (config.unoptimized) {\n    unoptimized = true\n  }\n  if (\n    isDefaultLoader &&\n    !config.dangerouslyAllowSVG &&\n    src.split('?', 1)[0].endsWith('.svg')\n  ) {\n    // Special case to make svg serve as-is to avoid proxying\n    // through the built-in Image Optimization API.\n    unoptimized = true\n  }\n\n  const qualityInt = getInt(quality)\n\n  if (process.env.NODE_ENV !== 'production') {\n    if (config.output === 'export' && isDefaultLoader && !unoptimized) {\n      throw new Error(\n        `Image Optimization using the default loader is not compatible with \\`{ output: 'export' }\\`.\n  Possible solutions:\n    - Remove \\`{ output: 'export' }\\` and run \"next start\" to run server mode including the Image Optimization API.\n    - Configure \\`{ images: { unoptimized: true } }\\` in \\`next.config.js\\` to disable the Image Optimization API.\n  Read more: https://nextjs.org/docs/messages/export-image-api`\n      )\n    }\n    if (!src) {\n      // React doesn't show the stack trace and there's\n      // no `src` to help identify which image, so we\n      // instead console.error(ref) during mount.\n      unoptimized = true\n    } else {\n      if (fill) {\n        if (width) {\n          throw new Error(\n            `Image with src \"${src}\" has both \"width\" and \"fill\" properties. Only one should be used.`\n          )\n        }\n        if (height) {\n          throw new Error(\n            `Image with src \"${src}\" has both \"height\" and \"fill\" properties. Only one should be used.`\n          )\n        }\n        if (style?.position && style.position !== 'absolute') {\n          throw new Error(\n            `Image with src \"${src}\" has both \"fill\" and \"style.position\" properties. Images with \"fill\" always use position absolute - it cannot be modified.`\n          )\n        }\n        if (style?.width && style.width !== '100%') {\n          throw new Error(\n            `Image with src \"${src}\" has both \"fill\" and \"style.width\" properties. Images with \"fill\" always use width 100% - it cannot be modified.`\n          )\n        }\n        if (style?.height && style.height !== '100%') {\n          throw new Error(\n            `Image with src \"${src}\" has both \"fill\" and \"style.height\" properties. Images with \"fill\" always use height 100% - it cannot be modified.`\n          )\n        }\n      } else {\n        if (typeof widthInt === 'undefined') {\n          throw new Error(\n            `Image with src \"${src}\" is missing required \"width\" property.`\n          )\n        } else if (isNaN(widthInt)) {\n          throw new Error(\n            `Image with src \"${src}\" has invalid \"width\" property. Expected a numeric value in pixels but received \"${width}\".`\n          )\n        }\n        if (typeof heightInt === 'undefined') {\n          throw new Error(\n            `Image with src \"${src}\" is missing required \"height\" property.`\n          )\n        } else if (isNaN(heightInt)) {\n          throw new Error(\n            `Image with src \"${src}\" has invalid \"height\" property. Expected a numeric value in pixels but received \"${height}\".`\n          )\n        }\n        // eslint-disable-next-line no-control-regex\n        if (/^[\\x00-\\x20]/.test(src)) {\n          throw new Error(\n            `Image with src \"${src}\" cannot start with a space or control character. Use src.trimStart() to remove it or encodeURIComponent(src) to keep it.`\n          )\n        }\n        // eslint-disable-next-line no-control-regex\n        if (/[\\x00-\\x20]$/.test(src)) {\n          throw new Error(\n            `Image with src \"${src}\" cannot end with a space or control character. Use src.trimEnd() to remove it or encodeURIComponent(src) to keep it.`\n          )\n        }\n      }\n    }\n    if (!VALID_LOADING_VALUES.includes(loading)) {\n      throw new Error(\n        `Image with src \"${src}\" has invalid \"loading\" property. Provided \"${loading}\" should be one of ${VALID_LOADING_VALUES.map(\n          String\n        ).join(',')}.`\n      )\n    }\n    if (priority && loading === 'lazy') {\n      throw new Error(\n        `Image with src \"${src}\" has both \"priority\" and \"loading='lazy'\" properties. Only one should be used.`\n      )\n    }\n    if (\n      placeholder !== 'empty' &&\n      placeholder !== 'blur' &&\n      !placeholder.startsWith('data:image/')\n    ) {\n      throw new Error(\n        `Image with src \"${src}\" has invalid \"placeholder\" property \"${placeholder}\".`\n      )\n    }\n    if (placeholder !== 'empty') {\n      if (widthInt && heightInt && widthInt * heightInt < 1600) {\n        warnOnce(\n          `Image with src \"${src}\" is smaller than 40x40. Consider removing the \"placeholder\" property to improve performance.`\n        )\n      }\n    }\n    if (placeholder === 'blur' && !blurDataURL) {\n      const VALID_BLUR_EXT = ['jpeg', 'png', 'webp', 'avif'] // should match next-image-loader\n\n      throw new Error(\n        `Image with src \"${src}\" has \"placeholder='blur'\" property but is missing the \"blurDataURL\" property.\n        Possible solutions:\n          - Add a \"blurDataURL\" property, the contents should be a small Data URL to represent the image\n          - Change the \"src\" property to a static import with one of the supported file types: ${VALID_BLUR_EXT.join(\n            ','\n          )} (animated images not supported)\n          - Remove the \"placeholder\" property, effectively no blur effect\n        Read more: https://nextjs.org/docs/messages/placeholder-blur-data-url`\n      )\n    }\n    if ('ref' in rest) {\n      warnOnce(\n        `Image with src \"${src}\" is using unsupported \"ref\" property. Consider using the \"onLoad\" property instead.`\n      )\n    }\n\n    if (!unoptimized && !isDefaultLoader) {\n      const urlStr = loader({\n        config,\n        src,\n        width: widthInt || 400,\n        quality: qualityInt || 75,\n      })\n      let url: URL | undefined\n      try {\n        url = new URL(urlStr)\n      } catch (err) {}\n      if (urlStr === src || (url && url.pathname === src && !url.search)) {\n        warnOnce(\n          `Image with src \"${src}\" has a \"loader\" property that does not implement width. Please implement it or use the \"unoptimized\" property instead.` +\n            `\\nRead more: https://nextjs.org/docs/messages/next-image-missing-loader-width`\n        )\n      }\n    }\n\n    if (onLoadingComplete) {\n      warnOnce(\n        `Image with src \"${src}\" is using deprecated \"onLoadingComplete\" property. Please use the \"onLoad\" property instead.`\n      )\n    }\n\n    for (const [legacyKey, legacyValue] of Object.entries({\n      layout,\n      objectFit,\n      objectPosition,\n      lazyBoundary,\n      lazyRoot,\n    })) {\n      if (legacyValue) {\n        warnOnce(\n          `Image with src \"${src}\" has legacy prop \"${legacyKey}\". Did you forget to run the codemod?` +\n            `\\nRead more: https://nextjs.org/docs/messages/next-image-upgrade-to-13`\n        )\n      }\n    }\n\n    if (\n      typeof window !== 'undefined' &&\n      !perfObserver &&\n      window.PerformanceObserver\n    ) {\n      perfObserver = new PerformanceObserver((entryList) => {\n        for (const entry of entryList.getEntries()) {\n          // @ts-ignore - missing \"LargestContentfulPaint\" class with \"element\" prop\n          const imgSrc = entry?.element?.src || ''\n          const lcpImage = allImgs.get(imgSrc)\n          if (\n            lcpImage &&\n            !lcpImage.priority &&\n            lcpImage.placeholder === 'empty' &&\n            !lcpImage.src.startsWith('data:') &&\n            !lcpImage.src.startsWith('blob:')\n          ) {\n            // https://web.dev/lcp/#measure-lcp-in-javascript\n            warnOnce(\n              `Image with src \"${lcpImage.src}\" was detected as the Largest Contentful Paint (LCP). Please add the \"priority\" property if this image is above the fold.` +\n                `\\nRead more: https://nextjs.org/docs/api-reference/next/image#priority`\n            )\n          }\n        }\n      })\n      try {\n        perfObserver.observe({\n          type: 'largest-contentful-paint',\n          buffered: true,\n        })\n      } catch (err) {\n        // Log error but don't crash the app\n        console.error(err)\n      }\n    }\n  }\n  const imgStyle = Object.assign(\n    fill\n      ? {\n          position: 'absolute',\n          height: '100%',\n          width: '100%',\n          left: 0,\n          top: 0,\n          right: 0,\n          bottom: 0,\n          objectFit,\n          objectPosition,\n        }\n      : {},\n    showAltText ? {} : { color: 'transparent' },\n    style\n  )\n\n  const backgroundImage =\n    !blurComplete && placeholder !== 'empty'\n      ? placeholder === 'blur'\n        ? `url(\"data:image/svg+xml;charset=utf-8,${getImageBlurSvg({\n            widthInt,\n            heightInt,\n            blurWidth,\n            blurHeight,\n            blurDataURL: blurDataURL || '', // assume not undefined\n            objectFit: imgStyle.objectFit,\n          })}\")`\n        : `url(\"${placeholder}\")` // assume `data:image/`\n      : null\n\n  const backgroundSize = !INVALID_BACKGROUND_SIZE_VALUES.includes(\n    imgStyle.objectFit\n  )\n    ? imgStyle.objectFit\n    : imgStyle.objectFit === 'fill'\n      ? '100% 100%' // the background-size equivalent of `fill`\n      : 'cover'\n\n  let placeholderStyle: PlaceholderStyle = backgroundImage\n    ? {\n        backgroundSize,\n        backgroundPosition: imgStyle.objectPosition || '50% 50%',\n        backgroundRepeat: 'no-repeat',\n        backgroundImage,\n      }\n    : {}\n\n  if (process.env.NODE_ENV === 'development') {\n    if (\n      placeholderStyle.backgroundImage &&\n      placeholder === 'blur' &&\n      blurDataURL?.startsWith('/')\n    ) {\n      // During `next dev`, we don't want to generate blur placeholders with webpack\n      // because it can delay starting the dev server. Instead, `next-image-loader.js`\n      // will inline a special url to lazily generate the blur placeholder at request time.\n      placeholderStyle.backgroundImage = `url(\"${blurDataURL}\")`\n    }\n  }\n\n  const imgAttributes = generateImgAttrs({\n    config,\n    src,\n    unoptimized,\n    width: widthInt,\n    quality: qualityInt,\n    sizes,\n    loader,\n  })\n\n  if (process.env.NODE_ENV !== 'production') {\n    if (typeof window !== 'undefined') {\n      let fullUrl: URL\n      try {\n        fullUrl = new URL(imgAttributes.src)\n      } catch (e) {\n        fullUrl = new URL(imgAttributes.src, window.location.href)\n      }\n      allImgs.set(fullUrl.href, { src, priority, placeholder })\n    }\n  }\n\n  const props: ImgProps = {\n    ...rest,\n    loading: isLazy ? 'lazy' : loading,\n    fetchPriority,\n    width: widthInt,\n    height: heightInt,\n    decoding,\n    className,\n    style: { ...imgStyle, ...placeholderStyle },\n    sizes: imgAttributes.sizes,\n    srcSet: imgAttributes.srcSet,\n    src: overrideSrc || imgAttributes.src,\n  }\n  const meta = { unoptimized, priority, placeholder, fill }\n  return { props, meta }\n}\n"], "names": ["getImgProps", "VALID_LOADING_VALUES", "undefined", "INVALID_BACKGROUND_SIZE_VALUES", "isStaticRequire", "src", "default", "isStaticImageData", "isStaticImport", "allImgs", "Map", "perfObserver", "getInt", "x", "Number", "isFinite", "NaN", "test", "parseInt", "getWidths", "width", "sizes", "deviceSizes", "allSizes", "viewportWidthRe", "percentSizes", "match", "exec", "push", "length", "smallestRatio", "Math", "min", "widths", "filter", "s", "kind", "Set", "map", "w", "find", "p", "generateImgAttrs", "config", "unoptimized", "quality", "loader", "srcSet", "last", "i", "join", "_state", "priority", "loading", "className", "height", "fill", "style", "overrideSrc", "onLoad", "onLoadingComplete", "placeholder", "blurDataURL", "fetchPriority", "decoding", "layout", "objectFit", "objectPosition", "lazyBoundary", "lazyRoot", "rest", "imgConf", "showAltText", "blurComplete", "defaultLoader", "c", "imageConfigDefault", "imageSizes", "sort", "a", "b", "qualities", "Error", "isDefaultLoader", "customImageLoader", "obj", "_", "opts", "layoutToStyle", "intrinsic", "max<PERSON><PERSON><PERSON>", "responsive", "layoutToSizes", "layoutStyle", "layoutSizes", "staticSrc", "widthInt", "heightInt", "blur<PERSON>idth", "blurHeight", "staticImageData", "JSON", "stringify", "ratio", "round", "isLazy", "startsWith", "dangerouslyAllowSVG", "split", "endsWith", "qualityInt", "process", "env", "NODE_ENV", "output", "position", "isNaN", "includes", "String", "warnOnce", "VALID_BLUR_EXT", "urlStr", "url", "URL", "err", "pathname", "search", "<PERSON><PERSON><PERSON>", "legacyValue", "Object", "entries", "window", "PerformanceObserver", "entryList", "entry", "getEntries", "imgSrc", "element", "lcpImage", "get", "observe", "type", "buffered", "console", "error", "imgStyle", "assign", "left", "top", "right", "bottom", "color", "backgroundImage", "getImageBlurSvg", "backgroundSize", "placeholder<PERSON><PERSON><PERSON>", "backgroundPosition", "backgroundRepeat", "imgAttributes", "fullUrl", "e", "location", "href", "set", "props", "meta"], "mappings": "AA+aM+G,QAAQC,GAAG,CAACC,QAAQ,KAAK;;;;;+BA9KfjH,eAAAA;;;eAAAA;;;0BAjQS;8BACO;6BACG;AA+EnC,MAAMC,uBAAuB;IAAC;IAAQ;IAASC;CAAU;AAEzD,8DAA8D;AAC9D,MAAMC,iCAAiC;IACrC;IACA;IACA;IACA;IACAD;CACD;AA4BD,SAASE,gBACPC,GAAoC;IAEpC,OAAQA,IAAsBC,OAAO,KAAKJ;AAC5C;AAEA,SAASK,kBACPF,GAAoC;IAEpC,OAAQA,IAAwBA,GAAG,KAAKH;AAC1C;AAEA,SAASM,eAAeH,GAA0B;IAChD,OACE,CAAC,CAACA,OACF,OAAOA,QAAQ,YACdD,CAAAA,gBAAgBC,QACfE,kBAAkBF,IAAmB;AAE3C;AAEA,MAAMI,UAAU,IAAIC;AAIpB,IAAIC;AAEJ,SAASC,OAAOC,CAAU;IACxB,IAAI,OAAOA,MAAM,aAAa;QAC5B,OAAOA;IACT;IACA,IAAI,OAAOA,MAAM,UAAU;QACzB,OAAOC,OAAOC,QAAQ,CAACF,KAAKA,IAAIG;IAClC;IACA,IAAI,OAAOH,MAAM,YAAY,WAAWI,IAAI,CAACJ,IAAI;QAC/C,OAAOK,SAASL,GAAG;IACrB;IACA,OAAOG;AACT;AAEA,SAASG,UACP,KAAsC,EACtCC,KAAyB,EACzBC,KAAyB;IAFzB,IAAA,EAAEC,WAAW,EAAEC,QAAQ,EAAe,GAAtC;IAIA,IAAIF,OAAO;QACT,yDAAyD;QACzD,MAAMG,kBAAkB;QACxB,MAAMC,eAAe,EAAE;QACvB,IAAK,IAAIC,OAAQA,QAAQF,gBAAgBG,IAAI,CAACN,QAASK,MAAO;YAC5DD,aAAaG,IAAI,CAACV,SAASQ,KAAK,CAAC,EAAE;QACrC;QACA,IAAID,aAAaI,MAAM,EAAE;YACvB,MAAMC,gBAAgBC,KAAKC,GAAG,IAAIP,gBAAgB;YAClD,OAAO;gBACLQ,QAAQV,SAASW,MAAM,CAAC,CAACC,IAAMA,KAAKb,WAAW,CAAC,EAAE,GAAGQ;gBACrDM,MAAM;YACR;QACF;QACA,OAAO;YAAEH,QAAQV;YAAUa,MAAM;QAAI;IACvC;IACA,IAAI,OAAOhB,UAAU,UAAU;QAC7B,OAAO;YAAEa,QAAQX;YAAac,MAAM;QAAI;IAC1C;IAEA,MAAMH,SAAS;WACV,IAAII,IACL,AACA,qEAAqE,EADE;QAEvE,kEAAkE;QAClE,oEAAoE;QACpE,uEAAuE;QACvE,sEAAsE;QACtE,uCAAuC;QACvC,qIAAqI;QACrI;YAACjB;YAAOA,QAAQ,EAAE,aAAa;SAAG,CAACkB,GAAG,CACpC,CAACC,IAAMhB,SAASiB,IAAI,CAAC,CAACC,IAAMA,KAAKF,MAAMhB,QAAQ,CAACA,SAASM,MAAM,GAAG,EAAE;KAGzE;IACD,OAAO;QAAEI;QAAQG,MAAM;IAAI;AAC7B;AAkBA,SAASM,iBAAiB,KAQR;IARQ,IAAA,EACxBC,MAAM,EACNtC,GAAG,EACHuC,WAAW,EACXxB,KAAK,EACLyB,OAAO,EACPxB,KAAK,EACLyB,MAAM,EACU,GARQ;IASxB,IAAIF,aAAa;QACf,OAAO;YAAEvC;YAAK0C,QAAQ7C;YAAWmB,OAAOnB;QAAU;IACpD;IAEA,MAAM,EAAE+B,MAAM,EAAEG,IAAI,EAAE,GAAGjB,UAAUwB,QAAQvB,OAAOC;IAClD,MAAM2B,OAAOf,OAAOJ,MAAM,GAAG;IAE7B,OAAO;QACLR,OAAO,CAACA,SAASe,SAAS,MAAM,UAAUf;QAC1C0B,QAAQd,OACLK,GAAG,CACF,CAACC,GAAGU,IACCH,OAAO;gBAAEH;gBAAQtC;gBAAKwC;gBAASzB,OAAOmB;YAAE,KAAG,MAC5CH,CAAAA,SAAS,MAAMG,IAAIU,IAAI,CAAA,IACtBb,MAENc,IAAI,CAAC;QAER,uEAAuE;QACvE,mEAAmE;QACnE,yEAAyE;QACzE,0EAA0E;QAC1E,2BAA2B;QAC3B,sDAAsD;QACtD7C,KAAKyC,OAAO;YAAEH;YAAQtC;YAAKwC;YAASzB,OAAOa,MAAM,CAACe,KAAK;QAAC;IAC1D;AACF;AAKO,SAAShD,YACd,KAyBa,EACbmD,MAKC;IA/BD,IAAA,EACE9C,GAAG,EACHgB,KAAK,EACLuB,cAAc,KAAK,EACnBQ,WAAW,KAAK,EAChBC,OAAO,EACPC,SAAS,EACTT,OAAO,EACPzB,KAAK,EACLmC,MAAM,EACNC,OAAO,KAAK,EACZC,KAAK,EACLC,WAAW,EACXC,MAAM,EACNC,iBAAiB,EACjBC,cAAc,OAAO,EACrBC,WAAW,EACXC,aAAa,EACbC,WAAW,OAAO,EAClBC,MAAM,EACNC,SAAS,EACTC,cAAc,EACdC,YAAY,EACZC,QAAQ,EACR,GAAGC,MACQ,GAzBb;IAyCA,MAAM,EAAEC,OAAO,EAAEC,WAAW,EAAEC,YAAY,EAAEC,aAAa,EAAE,GAAGvB;IAC9D,IAAIR;IACJ,IAAIgC,IAAIJ,WAAWK,aAAAA,kBAAkB;IACrC,IAAI,cAAcD,GAAG;QACnBhC,SAASgC;IACX,OAAO;YAGaA;QAFlB,MAAMpD,WAAW;eAAIoD,EAAErD,WAAW;eAAKqD,EAAEE,UAAU;SAAC,CAACC,IAAI,CAAC,CAACC,GAAGC,IAAMD,IAAIC;QACxE,MAAM1D,cAAcqD,EAAErD,WAAW,CAACwD,IAAI,CAAC,CAACC,GAAGC,IAAMD,IAAIC;QACrD,MAAMC,YAAAA,CAAYN,eAAAA,EAAEM,SAAS,KAAA,OAAA,KAAA,IAAXN,aAAaG,IAAI,CAAC,CAACC,GAAGC,IAAMD,IAAIC;QAClDrC,SAAS;YAAE,GAAGgC,CAAC;YAAEpD;YAAUD;YAAa2D;QAAU;IACpD;IAEA,IAAI,OAAOP,kBAAkB,aAAa;QACxC,MAAM,OAAA,cAEL,CAFK,IAAIQ,MACR,0IADI,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;IACA,IAAIpC,SAAgCwB,KAAKxB,MAAM,IAAI4B;IAEnD,sDAAsD;IACtD,OAAOJ,KAAKxB,MAAM;IAClB,OAAQwB,KAAavB,MAAM;IAE3B,6CAA6C;IAC7C,oDAAoD;IACpD,MAAMoC,kBAAkB,wBAAwBrC;IAEhD,IAAIqC,iBAAiB;QACnB,IAAIxC,OAAOG,MAAM,KAAK,UAAU;YAC9B,MAAM,OAAA,cAGL,CAHK,IAAIoC,MACP,qBAAkB7E,MAAI,gCACpB,4EAFC,qBAAA;uBAAA;4BAAA;8BAAA;YAGN;QACF;IACF,OAAO;QACL,8CAA8C;QAC9C,+CAA+C;QAC/C,iDAAiD;QACjD,MAAM+E,oBAAoBtC;QAC1BA,SAAS,CAACuC;YACR,MAAM,EAAE1C,QAAQ2C,CAAC,EAAE,GAAGC,MAAM,GAAGF;YAC/B,OAAOD,kBAAkBG;QAC3B;IACF;IAEA,IAAItB,QAAQ;QACV,IAAIA,WAAW,QAAQ;YACrBT,OAAO;QACT;QACA,MAAMgC,gBAAoE;YACxEC,WAAW;gBAAEC,UAAU;gBAAQnC,QAAQ;YAAO;YAC9CoC,YAAY;gBAAEvE,OAAO;gBAAQmC,QAAQ;YAAO;QAC9C;QACA,MAAMqC,gBAAoD;YACxDD,YAAY;YACZnC,MAAM;QACR;QACA,MAAMqC,cAAcL,aAAa,CAACvB,OAAO;QACzC,IAAI4B,aAAa;YACfpC,QAAQ;gBAAE,GAAGA,KAAK;gBAAE,GAAGoC,WAAW;YAAC;QACrC;QACA,MAAMC,cAAcF,aAAa,CAAC3B,OAAO;QACzC,IAAI6B,eAAe,CAACzE,OAAO;YACzBA,QAAQyE;QACV;IACF;IAEA,IAAIC,YAAY;IAChB,IAAIC,WAAWpF,OAAOQ;IACtB,IAAI6E,YAAYrF,OAAO2C;IACvB,IAAI2C;IACJ,IAAIC;IACJ,IAAI3F,eAAeH,MAAM;QACvB,MAAM+F,kBAAkBhG,gBAAgBC,OAAOA,IAAIC,OAAO,GAAGD;QAE7D,IAAI,CAAC+F,gBAAgB/F,GAAG,EAAE;YACxB,MAAM,OAAA,cAIL,CAJK,IAAI6E,MACP,gJAA6ImB,KAAKC,SAAS,CAC1JF,mBAFE,qBAAA;uBAAA;4BAAA;8BAAA;YAIN;QACF;QACA,IAAI,CAACA,gBAAgB7C,MAAM,IAAI,CAAC6C,gBAAgBhF,KAAK,EAAE;YACrD,MAAM,OAAA,cAIL,CAJK,IAAI8D,MACP,6JAA0JmB,KAAKC,SAAS,CACvKF,mBAFE,qBAAA;uBAAA;4BAAA;8BAAA;YAIN;QACF;QAEAF,YAAYE,gBAAgBF,SAAS;QACrCC,aAAaC,gBAAgBD,UAAU;QACvCrC,cAAcA,eAAesC,gBAAgBtC,WAAW;QACxDiC,YAAYK,gBAAgB/F,GAAG;QAE/B,IAAI,CAACmD,MAAM;YACT,IAAI,CAACwC,YAAY,CAACC,WAAW;gBAC3BD,WAAWI,gBAAgBhF,KAAK;gBAChC6E,YAAYG,gBAAgB7C,MAAM;YACpC,OAAO,IAAIyC,YAAY,CAACC,WAAW;gBACjC,MAAMM,QAAQP,WAAWI,gBAAgBhF,KAAK;gBAC9C6E,YAAYlE,KAAKyE,KAAK,CAACJ,gBAAgB7C,MAAM,GAAGgD;YAClD,OAAO,IAAI,CAACP,YAAYC,WAAW;gBACjC,MAAMM,QAAQN,YAAYG,gBAAgB7C,MAAM;gBAChDyC,WAAWjE,KAAKyE,KAAK,CAACJ,gBAAgBhF,KAAK,GAAGmF;YAChD;QACF;IACF;IACAlG,MAAM,OAAOA,QAAQ,WAAWA,MAAM0F;IAEtC,IAAIU,SACF,CAACrD,YAAaC,CAAAA,YAAY,UAAU,OAAOA,YAAY,WAAU;IACnE,IAAI,CAAChD,OAAOA,IAAIqG,UAAU,CAAC,YAAYrG,IAAIqG,UAAU,CAAC,UAAU;QAC9D,uEAAuE;QACvE9D,cAAc;QACd6D,SAAS;IACX;IACA,IAAI9D,OAAOC,WAAW,EAAE;QACtBA,cAAc;IAChB;IACA,IACEuC,mBACA,CAACxC,OAAOgE,mBAAmB,IAC3BtG,IAAIuG,KAAK,CAAC,KAAK,EAAE,CAAC,EAAE,CAACC,QAAQ,CAAC,SAC9B;QACA,yDAAyD;QACzD,+CAA+C;QAC/CjE,cAAc;IAChB;IAEA,MAAMkE,aAAalG,OAAOiC;IAE1B,wCAA2C;QACzC,IAAIF,OAAOuE,MAAM,KAAK,YAAY/B,mBAAmB,CAACvC,aAAa;YACjE,MAAM,OAAA,cAML,CANK,IAAIsC,MACP,2ZADG,qBAAA;uBAAA;4BAAA;8BAAA;YAMN;QACF;QACA,IAAI,CAAC7E,KAAK;YACR,iDAAiD;YACjD,+CAA+C;YAC/C,2CAA2C;YAC3CuC,cAAc;QAChB,OAAO;YACL,IAAIY,MAAM;gBACR,IAAIpC,OAAO;oBACT,MAAM,OAAA,cAEL,CAFK,IAAI8D,MACP,qBAAkB7E,MAAI,uEADnB,qBAAA;+BAAA;oCAAA;sCAAA;oBAEN;gBACF;gBACA,IAAIkD,QAAQ;oBACV,MAAM,OAAA,cAEL,CAFK,IAAI2B,MACP,qBAAkB7E,MAAI,wEADnB,qBAAA;+BAAA;oCAAA;sCAAA;oBAEN;gBACF;gBACA,IAAIoD,CAAAA,SAAAA,OAAAA,KAAAA,IAAAA,MAAO0D,QAAQ,KAAI1D,MAAM0D,QAAQ,KAAK,YAAY;oBACpD,MAAM,OAAA,cAEL,CAFK,IAAIjC,MACP,qBAAkB7E,MAAI,gIADnB,qBAAA;+BAAA;oCAAA;sCAAA;oBAEN;gBACF;gBACA,IAAIoD,CAAAA,SAAAA,OAAAA,KAAAA,IAAAA,MAAOrC,KAAK,KAAIqC,MAAMrC,KAAK,KAAK,QAAQ;oBAC1C,MAAM,OAAA,cAEL,CAFK,IAAI8D,MACP,qBAAkB7E,MAAI,sHADnB,qBAAA;+BAAA;oCAAA;sCAAA;oBAEN;gBACF;gBACA,IAAIoD,CAAAA,SAAAA,OAAAA,KAAAA,IAAAA,MAAOF,MAAM,KAAIE,MAAMF,MAAM,KAAK,QAAQ;oBAC5C,MAAM,OAAA,cAEL,CAFK,IAAI2B,MACP,qBAAkB7E,MAAI,wHADnB,qBAAA;+BAAA;oCAAA;sCAAA;oBAEN;gBACF;YACF,OAAO;gBACL,IAAI,OAAO2F,aAAa,aAAa;oBACnC,MAAM,OAAA,cAEL,CAFK,IAAId,MACP,qBAAkB7E,MAAI,4CADnB,qBAAA;+BAAA;oCAAA;sCAAA;oBAEN;gBACF,OAAO,IAAI+G,MAAMpB,WAAW;oBAC1B,MAAM,OAAA,cAEL,CAFK,IAAId,MACP,qBAAkB7E,MAAI,sFAAmFe,QAAM,OAD5G,qBAAA;+BAAA;oCAAA;sCAAA;oBAEN;gBACF;gBACA,IAAI,OAAO6E,cAAc,aAAa;oBACpC,MAAM,OAAA,cAEL,CAFK,IAAIf,MACP,qBAAkB7E,MAAI,6CADnB,qBAAA;+BAAA;oCAAA;sCAAA;oBAEN;gBACF,OAAO,IAAI+G,MAAMnB,YAAY;oBAC3B,MAAM,OAAA,cAEL,CAFK,IAAIf,MACP,qBAAkB7E,MAAI,uFAAoFkD,SAAO,OAD9G,qBAAA;+BAAA;oCAAA;sCAAA;oBAEN;gBACF;gBACA,4CAA4C;gBAC5C,IAAI,eAAetC,IAAI,CAACZ,MAAM;oBAC5B,MAAM,OAAA,cAEL,CAFK,IAAI6E,MACP,qBAAkB7E,MAAI,8HADnB,qBAAA;+BAAA;oCAAA;sCAAA;oBAEN;gBACF;gBACA,4CAA4C;gBAC5C,IAAI,eAAeY,IAAI,CAACZ,MAAM;oBAC5B,MAAM,OAAA,cAEL,CAFK,IAAI6E,MACP,qBAAkB7E,MAAI,0HADnB,qBAAA;+BAAA;oCAAA;sCAAA;oBAEN;gBACF;YACF;QACF;QACA,IAAI,CAACJ,qBAAqBoH,QAAQ,CAAChE,UAAU;YAC3C,MAAM,OAAA,cAIL,CAJK,IAAI6B,MACP,qBAAkB7E,MAAI,iDAA8CgD,UAAQ,wBAAqBpD,qBAAqBqC,GAAG,CACxHgF,QACApE,IAAI,CAAC,OAAK,MAHR,qBAAA;uBAAA;4BAAA;8BAAA;YAIN;QACF;QACA,IAAIE,YAAYC,YAAY,QAAQ;YAClC,MAAM,OAAA,cAEL,CAFK,IAAI6B,MACP,qBAAkB7E,MAAI,sFADnB,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;QACA,IACEwD,gBAAgB,WAChBA,gBAAgB,UAChB,CAACA,YAAY6C,UAAU,CAAC,gBACxB;YACA,MAAM,OAAA,cAEL,CAFK,IAAIxB,MACP,qBAAkB7E,MAAI,2CAAwCwD,cAAY,OADvE,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;QACA,IAAIA,gBAAgB,SAAS;YAC3B,IAAImC,YAAYC,aAAaD,WAAWC,YAAY,MAAM;gBACxDsB,CAAAA,GAAAA,UAAAA,QAAQ,EACL,qBAAkBlH,MAAI;YAE3B;QACF;QACA,IAAIwD,gBAAgB,UAAU,CAACC,aAAa;YAC1C,MAAM0D,iBAAiB;gBAAC;gBAAQ;gBAAO;gBAAQ;aAAO,CAAC,iCAAiC;;YAExF,MAAM,OAAA,cASL,CATK,IAAItC,MACP,qBAAkB7E,MAAI,6TAGkEmH,eAAetE,IAAI,CACxG,OACA,+LANA,qBAAA;uBAAA;4BAAA;8BAAA;YASN;QACF;QACA,IAAI,SAASoB,MAAM;YACjBiD,CAAAA,GAAAA,UAAAA,QAAQ,EACL,qBAAkBlH,MAAI;QAE3B;QAEA,IAAI,CAACuC,eAAe,CAACuC,iBAAiB;YACpC,MAAMsC,SAAS3E,OAAO;gBACpBH;gBACAtC;gBACAe,OAAO4E,YAAY;gBACnBnD,SAASiE,cAAc;YACzB;YACA,IAAIY;YACJ,IAAI;gBACFA,MAAM,IAAIC,IAAIF;YAChB,EAAE,OAAOG,KAAK,CAAC;YACf,IAAIH,WAAWpH,OAAQqH,OAAOA,IAAIG,QAAQ,KAAKxH,OAAO,CAACqH,IAAII,MAAM,EAAG;gBAClEP,CAAAA,GAAAA,UAAAA,QAAQ,EACL,qBAAkBlH,MAAI,4HACpB;YAEP;QACF;QAEA,IAAIuD,mBAAmB;YACrB2D,CAAAA,GAAAA,UAAAA,QAAQ,EACL,qBAAkBlH,MAAI;QAE3B;QAEA,KAAK,MAAM,CAAC0H,WAAWC,YAAY,IAAIC,OAAOC,OAAO,CAAC;YACpDjE;YACAC;YACAC;YACAC;YACAC;QACF,GAAI;YACF,IAAI2D,aAAa;gBACfT,CAAAA,GAAAA,UAAAA,QAAQ,EACL,qBAAkBlH,MAAI,wBAAqB0H,YAAU,0CACnD;YAEP;QACF;QAEA,IACE,OAAOI,WAAW,eAClB,CAACxH,gBACDwH,OAAOC,mBAAmB,EAC1B;YACAzH,eAAe,IAAIyH,oBAAoB,CAACC;gBACtC,KAAK,MAAMC,SAASD,UAAUE,UAAU,GAAI;wBAE3BD;oBADf,0EAA0E;oBAC1E,MAAME,SAASF,CAAAA,SAAAA,OAAAA,KAAAA,IAAAA,CAAAA,iBAAAA,MAAOG,OAAO,KAAA,OAAA,KAAA,IAAdH,eAAgBjI,GAAG,KAAI;oBACtC,MAAMqI,WAAWjI,QAAQkI,GAAG,CAACH;oBAC7B,IACEE,YACA,CAACA,SAAStF,QAAQ,IAClBsF,SAAS7E,WAAW,KAAK,WACzB,CAAC6E,SAASrI,GAAG,CAACqG,UAAU,CAAC,YACzB,CAACgC,SAASrI,GAAG,CAACqG,UAAU,CAAC,UACzB;wBACA,iDAAiD;wBACjDa,CAAAA,GAAAA,UAAAA,QAAQ,EACL,qBAAkBmB,SAASrI,GAAG,GAAC,8HAC7B;oBAEP;gBACF;YACF;YACA,IAAI;gBACFM,aAAaiI,OAAO,CAAC;oBACnBC,MAAM;oBACNC,UAAU;gBACZ;YACF,EAAE,OAAOlB,KAAK;gBACZ,oCAAoC;gBACpCmB,QAAQC,KAAK,CAACpB;YAChB;QACF;IACF;IACA,MAAMqB,WAAWhB,OAAOiB,MAAM,CAC5B1F,OACI;QACE2D,UAAU;QACV5D,QAAQ;QACRnC,OAAO;QACP+H,MAAM;QACNC,KAAK;QACLC,OAAO;QACPC,QAAQ;QACRpF;QACAC;IACF,IACA,CAAC,GACLK,cAAc,CAAC,IAAI;QAAE+E,OAAO;IAAc,GAC1C9F;IAGF,MAAM+F,kBACJ,CAAC/E,gBAAgBZ,gBAAgB,UAC7BA,gBAAgB,SACb,2CAAwC4F,CAAAA,GAAAA,cAAAA,eAAe,EAAC;QACvDzD;QACAC;QACAC;QACAC;QACArC,aAAaA,eAAe;QAC5BI,WAAW+E,SAAS/E,SAAS;IAC/B,KAAG,OACF,UAAOL,cAAY,KAAI,uBAAuB;OACjD;IAEN,MAAM6F,iBAAiB,CAACvJ,+BAA+BkH,QAAQ,CAC7D4B,SAAS/E,SAAS,IAEhB+E,SAAS/E,SAAS,GAClB+E,SAAS/E,SAAS,KAAK,SACrB,YAAY,2CAA2C;OACvD;IAEN,IAAIyF,mBAAqCH,kBACrC;QACEE;QACAE,oBAAoBX,SAAS9E,cAAc,IAAI;QAC/C0F,kBAAkB;QAClBL;IACF,IACA,CAAC;IAEL,IAAIzC,QAAQC,GAAG,CAACC,QAAQ,KAAK,WAAe;QAC1C,IACE0C,iBAAiBH,eAAe,IAChC3F,gBAAgB,UAAA,CAChBC,eAAAA,OAAAA,KAAAA,IAAAA,YAAa4C,UAAU,CAAC,IAAA,GACxB;YACA,8EAA8E;YAC9E,gFAAgF;YAChF,qFAAqF;YACrFiD,iBAAiBH,eAAe,GAAI,UAAO1F,cAAY;QACzD;IACF;IAEA,MAAMgG,gBAAgBpH,iBAAiB;QACrCC;QACAtC;QACAuC;QACAxB,OAAO4E;QACPnD,SAASiE;QACTzF;QACAyB;IACF;IAEA,IAAIiE,QAAQC,GAAG,CAACC,QAAQ,KAAK,WAAc;QACzC,IAAI,OAAOkB,WAAW,aAAa;YACjC,IAAI4B;YACJ,IAAI;gBACFA,UAAU,IAAIpC,IAAImC,cAAczJ,GAAG;YACrC,EAAE,OAAO2J,GAAG;gBACVD,UAAU,IAAIpC,IAAImC,cAAczJ,GAAG,EAAE8H,OAAO8B,QAAQ,CAACC,IAAI;YAC3D;YACAzJ,QAAQ0J,GAAG,CAACJ,QAAQG,IAAI,EAAE;gBAAE7J;gBAAK+C;gBAAUS;YAAY;QACzD;IACF;IAEA,MAAMuG,QAAkB;QACtB,GAAG9F,IAAI;QACPjB,SAASoD,SAAS,SAASpD;QAC3BU;QACA3C,OAAO4E;QACPzC,QAAQ0C;QACRjC;QACAV;QACAG,OAAO;YAAE,GAAGwF,QAAQ;YAAE,GAAGU,gBAAgB;QAAC;QAC1CtI,OAAOyI,cAAczI,KAAK;QAC1B0B,QAAQ+G,cAAc/G,MAAM;QAC5B1C,KAAKqD,eAAeoG,cAAczJ,GAAG;IACvC;IACA,MAAMgK,OAAO;QAAEzH;QAAaQ;QAAUS;QAAaL;IAAK;IACxD,OAAO;QAAE4G;QAAOC;IAAK;AACvB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 692, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/AI_Projects/CRMcriadores/crm-interno/node_modules/next/src/shared/lib/side-effect.tsx"], "sourcesContent": ["import type React from 'react'\nimport { Children, useEffect, useLayoutEffect, type JSX } from 'react'\n\ntype State = JSX.Element[] | undefined\n\nexport type SideEffectProps = {\n  reduceComponentsToState: <T extends {}>(\n    components: Array<React.ReactElement<any>>,\n    props: T\n  ) => State\n  handleStateChange?: (state: State) => void\n  headManager: any\n  inAmpMode?: boolean\n  children: React.ReactNode\n}\n\nconst isServer = typeof window === 'undefined'\nconst useClientOnlyLayoutEffect = isServer ? () => {} : useLayoutEffect\nconst useClientOnlyEffect = isServer ? () => {} : useEffect\n\nexport default function SideEffect(props: SideEffectProps) {\n  const { headManager, reduceComponentsToState } = props\n\n  function emitChange() {\n    if (headManager && headManager.mountedInstances) {\n      const headElements = Children.toArray(\n        Array.from(headManager.mountedInstances as Set<React.ReactNode>).filter(\n          Boolean\n        )\n      ) as React.ReactElement[]\n      headManager.updateHead(reduceComponentsToState(headElements, props))\n    }\n  }\n\n  if (isServer) {\n    headManager?.mountedInstances?.add(props.children)\n    emitChange()\n  }\n\n  useClientOnlyLayoutEffect(() => {\n    headManager?.mountedInstances?.add(props.children)\n    return () => {\n      headManager?.mountedInstances?.delete(props.children)\n    }\n  })\n\n  // We need to call `updateHead` method whenever the `SideEffect` is trigger in all\n  // life-cycles: mount, update, unmount. However, if there are multiple `SideEffect`s\n  // being rendered, we only trigger the method from the last one.\n  // This is ensured by keeping the last unflushed `updateHead` in the `_pendingUpdate`\n  // singleton in the layout effect pass, and actually trigger it in the effect pass.\n  useClientOnlyLayoutEffect(() => {\n    if (headManager) {\n      headManager._pendingUpdate = emitChange\n    }\n    return () => {\n      if (headManager) {\n        headManager._pendingUpdate = emitChange\n      }\n    }\n  })\n\n  useClientOnlyEffect(() => {\n    if (headManager && headManager._pendingUpdate) {\n      headManager._pendingUpdate()\n      headManager._pendingUpdate = null\n    }\n    return () => {\n      if (headManager && headManager._pendingUpdate) {\n        headManager._pendingUpdate()\n        headManager._pendingUpdate = null\n      }\n    }\n  })\n\n  return null\n}\n"], "names": ["SideEffect", "isServer", "window", "useClientOnlyLayoutEffect", "useLayoutEffect", "useClientOnlyEffect", "useEffect", "props", "headManager", "reduceComponentsToState", "emitChange", "mountedInstances", "headElements", "Children", "toArray", "Array", "from", "filter", "Boolean", "updateHead", "add", "children", "delete", "_pendingUpdate"], "mappings": ";;;;+BAoBA,WAAA;;;eAAwBA;;;uBAnBuC;AAe/D,MAAMC,WAAW,OAAOC,WAAW;AACnC,MAAMC,4BAA4BF,WAAW,KAAO,IAAIG,OAAAA,eAAe;AACvE,MAAMC,sBAAsBJ,WAAW,KAAO,IAAIK,OAAAA,SAAS;AAE5C,SAASN,WAAWO,KAAsB;IACvD,MAAM,EAAEC,WAAW,EAAEC,uBAAuB,EAAE,GAAGF;IAEjD,SAASG;QACP,IAAIF,eAAeA,YAAYG,gBAAgB,EAAE;YAC/C,MAAMC,eAAeC,OAAAA,QAAQ,CAACC,OAAO,CACnCC,MAAMC,IAAI,CAACR,YAAYG,gBAAgB,EAA0BM,MAAM,CACrEC;YAGJV,YAAYW,UAAU,CAACV,wBAAwBG,cAAcL;QAC/D;IACF;IAEA,IAAIN,UAAU;YACZO;QAAAA,eAAAA,OAAAA,KAAAA,IAAAA,CAAAA,gCAAAA,YAAaG,gBAAgB,KAAA,OAAA,KAAA,IAA7BH,8BAA+BY,GAAG,CAACb,MAAMc,QAAQ;QACjDX;IACF;IAEAP;gDAA0B;gBACxBK;YAAAA,eAAAA,OAAAA,KAAAA,IAAAA,CAAAA,gCAAAA,YAAaG,gBAAgB,KAAA,OAAA,KAAA,IAA7BH,8BAA+BY,GAAG,CAACb,MAAMc,QAAQ;YACjD;wDAAO;wBACLb;oBAAAA,eAAAA,OAAAA,KAAAA,IAAAA,CAAAA,gCAAAA,YAAaG,gBAAgB,KAAA,OAAA,KAAA,IAA7BH,8BAA+Bc,MAAM,CAACf,MAAMc,QAAQ;gBACtD;;QACF;;IAEA,kFAAkF;IAClF,oFAAoF;IACpF,gEAAgE;IAChE,qFAAqF;IACrF,mFAAmF;IACnFlB;gDAA0B;YACxB,IAAIK,aAAa;gBACfA,YAAYe,cAAc,GAAGb;YAC/B;YACA;wDAAO;oBACL,IAAIF,aAAa;wBACfA,YAAYe,cAAc,GAAGb;oBAC/B;gBACF;;QACF;;IAEAL;0CAAoB;YAClB,IAAIG,eAAeA,YAAYe,cAAc,EAAE;gBAC7Cf,YAAYe,cAAc;gBAC1Bf,YAAYe,cAAc,GAAG;YAC/B;YACA;kDAAO;oBACL,IAAIf,eAAeA,YAAYe,cAAc,EAAE;wBAC7Cf,YAAYe,cAAc;wBAC1Bf,YAAYe,cAAc,GAAG;oBAC/B;gBACF;;QACF;;IAEA,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 773, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/AI_Projects/CRMcriadores/crm-interno/node_modules/next/src/shared/lib/amp-context.shared-runtime.ts"], "sourcesContent": ["import React from 'react'\n\nexport const AmpStateContext: React.Context<any> = React.createContext({})\n\nif (process.env.NODE_ENV !== 'production') {\n  AmpStateContext.displayName = 'AmpStateContext'\n}\n"], "names": ["AmpStateContext", "React", "createContext", "process", "env", "NODE_ENV", "displayName"], "mappings": "AAIIG,QAAQC,GAAG,CAACC,QAAQ,KAAK;;;;;+BAFhBL,mBAAAA;;;eAAAA;;;;gEAFK;AAEX,MAAMA,kBAAsCC,OAAAA,OAAK,CAACC,aAAa,CAAC,CAAC;AAExE,wCAA2C;IACzCF,gBAAgBM,WAAW,GAAG;AAChC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 795, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/AI_Projects/CRMcriadores/crm-interno/node_modules/next/src/shared/lib/amp-mode.ts"], "sourcesContent": ["export function isInAmpMode({\n  ampFirst = false,\n  hybrid = false,\n  hasQuery = false,\n} = {}): boolean {\n  return ampFirst || (hybrid && hasQuery)\n}\n"], "names": ["isInAmpMode", "ampFirs<PERSON>", "hybrid", "<PERSON><PERSON><PERSON><PERSON>"], "mappings": ";;;;+BAAgBA,eAAAA;;;eAAAA;;;AAAT,SAASA,YAAY,KAAA;IAAA,IAAA,EAC1BC,WAAW,KAAK,EAChBC,SAAS,KAAK,EACdC,WAAW,KAAK,EACjB,GAJ2B,UAAA,KAAA,IAIxB,CAAC,IAJuB;IAK1B,OAAOF,YAAaC,UAAUC;AAChC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 814, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/AI_Projects/CRMcriadores/crm-interno/node_modules/next/src/shared/lib/head.tsx"], "sourcesContent": ["'use client'\n\nimport React, { useContext, type JSX } from 'react'\nimport Effect from './side-effect'\nimport { AmpStateContext } from './amp-context.shared-runtime'\nimport { HeadManagerContext } from './head-manager-context.shared-runtime'\nimport { isInAmpMode } from './amp-mode'\nimport { warnOnce } from './utils/warn-once'\n\ntype WithInAmpMode = {\n  inAmpMode?: boolean\n}\n\nexport function defaultHead(inAmpMode = false): JSX.Element[] {\n  const head = [<meta charSet=\"utf-8\" key=\"charset\" />]\n  if (!inAmpMode) {\n    head.push(\n      <meta name=\"viewport\" content=\"width=device-width\" key=\"viewport\" />\n    )\n  }\n  return head\n}\n\nfunction onlyReactElement(\n  list: Array<React.ReactElement<any>>,\n  child: React.ReactElement | number | string\n): Array<React.ReactElement<any>> {\n  // React children can be \"string\" or \"number\" in this case we ignore them for backwards compat\n  if (typeof child === 'string' || typeof child === 'number') {\n    return list\n  }\n  // Adds support for React.Fragment\n  if (child.type === React.Fragment) {\n    return list.concat(\n      // @ts-expect-error @types/react does not remove fragments but this could also return ReactPortal[]\n      React.Children.toArray(child.props.children).reduce(\n        // @ts-expect-error @types/react does not remove fragments but this could also return ReactPortal[]\n        (\n          fragmentList: Array<React.ReactElement<any>>,\n          fragmentChild: React.ReactElement | number | string\n        ): Array<React.ReactElement<any>> => {\n          if (\n            typeof fragmentChild === 'string' ||\n            typeof fragmentChild === 'number'\n          ) {\n            return fragmentList\n          }\n          return fragmentList.concat(fragmentChild)\n        },\n        []\n      )\n    )\n  }\n  return list.concat(child)\n}\n\nconst METATYPES = ['name', 'httpEquiv', 'charSet', 'itemProp']\n\n/*\n returns a function for filtering head child elements\n which shouldn't be duplicated, like <title/>\n Also adds support for deduplicated `key` properties\n*/\nfunction unique() {\n  const keys = new Set()\n  const tags = new Set()\n  const metaTypes = new Set()\n  const metaCategories: { [metatype: string]: Set<string> } = {}\n\n  return (h: React.ReactElement<any>) => {\n    let isUnique = true\n    let hasKey = false\n\n    if (h.key && typeof h.key !== 'number' && h.key.indexOf('$') > 0) {\n      hasKey = true\n      const key = h.key.slice(h.key.indexOf('$') + 1)\n      if (keys.has(key)) {\n        isUnique = false\n      } else {\n        keys.add(key)\n      }\n    }\n\n    // eslint-disable-next-line default-case\n    switch (h.type) {\n      case 'title':\n      case 'base':\n        if (tags.has(h.type)) {\n          isUnique = false\n        } else {\n          tags.add(h.type)\n        }\n        break\n      case 'meta':\n        for (let i = 0, len = METATYPES.length; i < len; i++) {\n          const metatype = METATYPES[i]\n          if (!h.props.hasOwnProperty(metatype)) continue\n\n          if (metatype === 'charSet') {\n            if (metaTypes.has(metatype)) {\n              isUnique = false\n            } else {\n              metaTypes.add(metatype)\n            }\n          } else {\n            const category = h.props[metatype]\n            const categories = metaCategories[metatype] || new Set()\n            if ((metatype !== 'name' || !hasKey) && categories.has(category)) {\n              isUnique = false\n            } else {\n              categories.add(category)\n              metaCategories[metatype] = categories\n            }\n          }\n        }\n        break\n    }\n\n    return isUnique\n  }\n}\n\n/**\n *\n * @param headChildrenElements List of children of <Head>\n */\nfunction reduceComponents<T extends {} & WithInAmpMode>(\n  headChildrenElements: Array<React.ReactElement<any>>,\n  props: T\n) {\n  const { inAmpMode } = props\n  return headChildrenElements\n    .reduce(onlyReactElement, [])\n    .reverse()\n    .concat(defaultHead(inAmpMode).reverse())\n    .filter(unique())\n    .reverse()\n    .map((c: React.ReactElement<any>, i: number) => {\n      const key = c.key || i\n      if (\n        process.env.NODE_ENV !== 'development' &&\n        process.env.__NEXT_OPTIMIZE_FONTS &&\n        !inAmpMode\n      ) {\n        if (\n          c.type === 'link' &&\n          c.props['href'] &&\n          // TODO(prateekbh@): Replace this with const from `constants` when the tree shaking works.\n          ['https://fonts.googleapis.com/css', 'https://use.typekit.net/'].some(\n            (url) => c.props['href'].startsWith(url)\n          )\n        ) {\n          const newProps = { ...(c.props || {}) }\n          newProps['data-href'] = newProps['href']\n          newProps['href'] = undefined\n\n          // Add this attribute to make it easy to identify optimized tags\n          newProps['data-optimized-fonts'] = true\n\n          return React.cloneElement(c, newProps)\n        }\n      }\n      if (process.env.NODE_ENV === 'development') {\n        // omit JSON-LD structured data snippets from the warning\n        if (c.type === 'script' && c.props['type'] !== 'application/ld+json') {\n          const srcMessage = c.props['src']\n            ? `<script> tag with src=\"${c.props['src']}\"`\n            : `inline <script>`\n          warnOnce(\n            `Do not add <script> tags using next/head (see ${srcMessage}). Use next/script instead. \\nSee more info here: https://nextjs.org/docs/messages/no-script-tags-in-head-component`\n          )\n        } else if (c.type === 'link' && c.props['rel'] === 'stylesheet') {\n          warnOnce(\n            `Do not add stylesheets using next/head (see <link rel=\"stylesheet\"> tag with href=\"${c.props['href']}\"). Use Document instead. \\nSee more info here: https://nextjs.org/docs/messages/no-stylesheets-in-head-component`\n          )\n        }\n      }\n      return React.cloneElement(c, { key })\n    })\n}\n\n/**\n * This component injects elements to `<head>` of your page.\n * To avoid duplicated `tags` in `<head>` you can use the `key` property, which will make sure every tag is only rendered once.\n */\nfunction Head({ children }: { children: React.ReactNode }) {\n  const ampState = useContext(AmpStateContext)\n  const headManager = useContext(HeadManagerContext)\n  return (\n    <Effect\n      reduceComponentsToState={reduceComponents}\n      headManager={headManager}\n      inAmpMode={isInAmpMode(ampState)}\n    >\n      {children}\n    </Effect>\n  )\n}\n\nexport default Head\n"], "names": ["defaultHead", "inAmpMode", "head", "meta", "charSet", "push", "name", "content", "onlyReactElement", "list", "child", "type", "React", "Fragment", "concat", "Children", "toArray", "props", "children", "reduce", "fragmentList", "fragmentChild", "METATYPES", "unique", "keys", "Set", "tags", "metaTypes", "metaCategories", "h", "isUnique", "<PERSON><PERSON><PERSON>", "key", "indexOf", "slice", "has", "add", "i", "len", "length", "metatype", "hasOwnProperty", "category", "categories", "reduceComponents", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "reverse", "filter", "map", "c", "process", "env", "NODE_ENV", "__NEXT_OPTIMIZE_FONTS", "some", "url", "startsWith", "newProps", "undefined", "cloneElement", "srcMessage", "warnOnce", "Head", "ampState", "useContext", "AmpStateContext", "headManager", "HeadManagerContext", "Effect", "reduceComponentsToState", "isInAmpMode"], "mappings": "AA4IQkD,QAAQC,GAAG,CAACC,QAAQ,KAAK,iBACzBF,QAAQC,GAAG,CAACE,qBAAqB,IACjC,CAACpD;AA9IT;;;;;;;;;;;;;;;;IAuMA,OAAmB,EAAA;eAAnB;;IA1LgBD,WAAW,EAAA;eAAXA;;;;;;iEAX4B;qEACzB;yCACa;iDACG;yBACP;0BACH;AAMlB,SAASA,YAAYC,SAAiB;IAAjBA,IAAAA,cAAAA,KAAAA,GAAAA,YAAY;IACtC,MAAMC,OAAO;sBAAC,CAAA,GAAA,YAAA,GAAA,EAACC,QAAAA;YAAKC,SAAQ;WAAY;KAAa;IACrD,IAAI,CAACH,WAAW;QACdC,KAAKG,IAAI,CAAA,WAAA,GACP,CAAA,GAAA,YAAA,GAAA,EAACF,QAAAA;YAAKG,MAAK;YAAWC,SAAQ;WAAyB;IAE3D;IACA,OAAOL;AACT;AAEA,SAASM,iBACPC,IAAoC,EACpCC,KAA2C;IAE3C,8FAA8F;IAC9F,IAAI,OAAOA,UAAU,YAAY,OAAOA,UAAU,UAAU;QAC1D,OAAOD;IACT;IACA,kCAAkC;IAClC,IAAIC,MAAMC,IAAI,KAAKC,OAAAA,OAAK,CAACC,QAAQ,EAAE;QACjC,OAAOJ,KAAKK,MAAM,CAChB,AACAF,OAAAA,OAAK,CAACG,QAAQ,CAACC,OAAO,CAACN,MAAMO,KAAK,CAACC,QAAQ,EAAEC,MAAM,CACjD,AACA,CACEC,cACAC,uBAL+F,6DAEE;YAKjG,IACE,OAAOA,kBAAkB,YACzB,OAAOA,kBAAkB,UACzB;gBACA,OAAOD;YACT;YACA,OAAOA,aAAaN,MAAM,CAACO;QAC7B,GACA,EAAE;IAGR;IACA,OAAOZ,KAAKK,MAAM,CAACJ;AACrB;AAEA,MAAMY,YAAY;IAAC;IAAQ;IAAa;IAAW;CAAW;AAE9D;;;;AAIA,GACA,SAASC;IACP,MAAMC,OAAO,IAAIC;IACjB,MAAMC,OAAO,IAAID;IACjB,MAAME,YAAY,IAAIF;IACtB,MAAMG,iBAAsD,CAAC;IAE7D,OAAO,CAACC;QACN,IAAIC,WAAW;QACf,IAAIC,SAAS;QAEb,IAAIF,EAAEG,GAAG,IAAI,OAAOH,EAAEG,GAAG,KAAK,YAAYH,EAAEG,GAAG,CAACC,OAAO,CAAC,OAAO,GAAG;YAChEF,SAAS;YACT,MAAMC,MAAMH,EAAEG,GAAG,CAACE,KAAK,CAACL,EAAEG,GAAG,CAACC,OAAO,CAAC,OAAO;YAC7C,IAAIT,KAAKW,GAAG,CAACH,MAAM;gBACjBF,WAAW;YACb,OAAO;gBACLN,KAAKY,GAAG,CAACJ;YACX;QACF;QAEA,wCAAwC;QACxC,OAAQH,EAAElB,IAAI;YACZ,KAAK;YACL,KAAK;gBACH,IAAIe,KAAKS,GAAG,CAACN,EAAElB,IAAI,GAAG;oBACpBmB,WAAW;gBACb,OAAO;oBACLJ,KAAKU,GAAG,CAACP,EAAElB,IAAI;gBACjB;gBACA;YACF,KAAK;gBACH,IAAK,IAAI0B,IAAI,GAAGC,MAAMhB,UAAUiB,MAAM,EAAEF,IAAIC,KAAKD,IAAK;oBACpD,MAAMG,WAAWlB,SAAS,CAACe,EAAE;oBAC7B,IAAI,CAACR,EAAEZ,KAAK,CAACwB,cAAc,CAACD,WAAW;oBAEvC,IAAIA,aAAa,WAAW;wBAC1B,IAAIb,UAAUQ,GAAG,CAACK,WAAW;4BAC3BV,WAAW;wBACb,OAAO;4BACLH,UAAUS,GAAG,CAACI;wBAChB;oBACF,OAAO;wBACL,MAAME,WAAWb,EAAEZ,KAAK,CAACuB,SAAS;wBAClC,MAAMG,aAAaf,cAAc,CAACY,SAAS,IAAI,IAAIf;wBACnD,IAAKe,CAAAA,aAAa,UAAU,CAACT,MAAK,KAAMY,WAAWR,GAAG,CAACO,WAAW;4BAChEZ,WAAW;wBACb,OAAO;4BACLa,WAAWP,GAAG,CAACM;4BACfd,cAAc,CAACY,SAAS,GAAGG;wBAC7B;oBACF;gBACF;gBACA;QACJ;QAEA,OAAOb;IACT;AACF;AAEA;;;CAGC,GACD,SAASc,iBACPC,oBAAoD,EACpD5B,KAAQ;IAER,MAAM,EAAEhB,SAAS,EAAE,GAAGgB;IACtB,OAAO4B,qBACJ1B,MAAM,CAACX,kBAAkB,EAAE,EAC3BsC,OAAO,GACPhC,MAAM,CAACd,YAAYC,WAAW6C,OAAO,IACrCC,MAAM,CAACxB,UACPuB,OAAO,GACPE,GAAG,CAAC,CAACC,GAA4BZ;QAChC,MAAML,MAAMiB,EAAEjB,GAAG,IAAIK;QACrB,uCAIE;;QAkBF;QACA,IAAIa,QAAQC,GAAG,CAACC,QAAQ,KAAK,WAAe;YAC1C,yDAAyD;YACzD,IAAIH,EAAEtC,IAAI,KAAK,YAAYsC,EAAEhC,KAAK,CAAC,OAAO,KAAK,uBAAuB;gBACpE,MAAM2C,aAAaX,EAAEhC,KAAK,CAAC,MAAM,GAC5B,4BAAyBgC,EAAEhC,KAAK,CAAC,MAAM,GAAC,MACxC;gBACL4C,CAAAA,GAAAA,UAAAA,QAAQ,EACL,mDAAgDD,aAAW;YAEhE,OAAO,IAAIX,EAAEtC,IAAI,KAAK,UAAUsC,EAAEhC,KAAK,CAAC,MAAM,KAAK,cAAc;gBAC/D4C,CAAAA,GAAAA,UAAAA,QAAQ,EACL,wFAAqFZ,EAAEhC,KAAK,CAAC,OAAO,GAAC;YAE1G;QACF;QACA,OAAA,WAAA,GAAOL,OAAAA,OAAK,CAAC+C,YAAY,CAACV,GAAG;YAAEjB;QAAI;IACrC;AACJ;AAEA;;;CAGC,GACD,SAAS8B,KAAK,KAA2C;IAA3C,IAAA,EAAE5C,QAAQ,EAAiC,GAA3C;IACZ,MAAM6C,WAAWC,CAAAA,GAAAA,OAAAA,UAAU,EAACC,yBAAAA,eAAe;IAC3C,MAAMC,cAAcF,CAAAA,GAAAA,OAAAA,UAAU,EAACG,iCAAAA,kBAAkB;IACjD,OAAA,WAAA,GACE,CAAA,GAAA,YAAA,GAAA,EAACC,YAAAA,OAAM,EAAA;QACLC,yBAAyBzB;QACzBsB,aAAaA;QACbjE,WAAWqE,CAAAA,GAAAA,SAAAA,WAAW,EAACP;kBAEtB7C;;AAGP;MAEA,WAAe4C", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 992, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/AI_Projects/CRMcriadores/crm-interno/node_modules/next/src/shared/lib/image-config-context.shared-runtime.ts"], "sourcesContent": ["import React from 'react'\nimport type { ImageConfigComplete } from './image-config'\nimport { imageConfigDefault } from './image-config'\n\nexport const ImageConfigContext =\n  React.createContext<ImageConfigComplete>(imageConfigDefault)\n\nif (process.env.NODE_ENV !== 'production') {\n  ImageConfigContext.displayName = 'ImageConfigContext'\n}\n"], "names": ["ImageConfigContext", "React", "createContext", "imageConfigDefault", "process", "env", "NODE_ENV", "displayName"], "mappings": "AAOII,QAAQC,GAAG,CAACC,QAAQ,KAAK;;;;;+BAHhBN,sBAAAA;;;eAAAA;;;;gEAJK;6BAEiB;AAE5B,MAAMA,qBACXC,OAAAA,OAAK,CAACC,aAAa,CAAsBC,aAAAA,kBAAkB;AAE7D,wCAA2C;IACzCH,mBAAmBO,WAAW,GAAG;AACnC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1015, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/AI_Projects/CRMcriadores/crm-interno/node_modules/next/src/shared/lib/router-context.shared-runtime.ts"], "sourcesContent": ["import React from 'react'\nimport type { NextRouter } from './router/router'\n\nexport const RouterContext = React.createContext<NextRouter | null>(null)\n\nif (process.env.NODE_ENV !== 'production') {\n  RouterContext.displayName = 'RouterContext'\n}\n"], "names": ["RouterContext", "React", "createContext", "process", "env", "NODE_ENV", "displayName"], "mappings": "AAKIG,QAAQC,GAAG,CAACC,QAAQ,KAAK;;;;;+BAFhBL,iBAAAA;;;eAAAA;;;;gEAHK;AAGX,MAAMA,gBAAgBC,OAAAA,OAAK,CAACC,aAAa,CAAoB;AAEpE,wCAA2C;IACzCF,cAAcM,WAAW,GAAG;AAC9B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1037, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/AI_Projects/CRMcriadores/crm-interno/node_modules/next/dist/compiled/picomatch/index.js"], "sourcesContent": ["(()=>{\"use strict\";var t={170:(t,e,u)=>{const n=u(510);const isWindows=()=>{if(typeof navigator!==\"undefined\"&&navigator.platform){const t=navigator.platform.toLowerCase();return t===\"win32\"||t===\"windows\"}if(typeof process!==\"undefined\"&&process.platform){return process.platform===\"win32\"}return false};function picomatch(t,e,u=false){if(e&&(e.windows===null||e.windows===undefined)){e={...e,windows:isWindows()}}return n(t,e,u)}Object.assign(picomatch,n);t.exports=picomatch},154:t=>{const e=\"\\\\\\\\/\";const u=`[^${e}]`;const n=\"\\\\.\";const o=\"\\\\+\";const s=\"\\\\?\";const r=\"\\\\/\";const a=\"(?=.)\";const i=\"[^/]\";const c=`(?:${r}|$)`;const p=`(?:^|${r})`;const l=`${n}{1,2}${c}`;const f=`(?!${n})`;const A=`(?!${p}${l})`;const _=`(?!${n}{0,1}${c})`;const R=`(?!${l})`;const E=`[^.${r}]`;const h=`${i}*?`;const g=\"/\";const b={DOT_LITERAL:n,PLUS_LITERAL:o,QMARK_LITERAL:s,SLASH_LITERAL:r,ONE_CHAR:a,QMARK:i,END_ANCHOR:c,DOTS_SLASH:l,NO_DOT:f,NO_DOTS:A,NO_DOT_SLASH:_,NO_DOTS_SLASH:R,QMARK_NO_DOT:E,STAR:h,START_ANCHOR:p,SEP:g};const C={...b,SLASH_LITERAL:`[${e}]`,QMARK:u,STAR:`${u}*?`,DOTS_SLASH:`${n}{1,2}(?:[${e}]|$)`,NO_DOT:`(?!${n})`,NO_DOTS:`(?!(?:^|[${e}])${n}{1,2}(?:[${e}]|$))`,NO_DOT_SLASH:`(?!${n}{0,1}(?:[${e}]|$))`,NO_DOTS_SLASH:`(?!${n}{1,2}(?:[${e}]|$))`,QMARK_NO_DOT:`[^.${e}]`,START_ANCHOR:`(?:^|[${e}])`,END_ANCHOR:`(?:[${e}]|$)`,SEP:\"\\\\\"};const y={alnum:\"a-zA-Z0-9\",alpha:\"a-zA-Z\",ascii:\"\\\\x00-\\\\x7F\",blank:\" \\\\t\",cntrl:\"\\\\x00-\\\\x1F\\\\x7F\",digit:\"0-9\",graph:\"\\\\x21-\\\\x7E\",lower:\"a-z\",print:\"\\\\x20-\\\\x7E \",punct:\"\\\\-!\\\"#$%&'()\\\\*+,./:;<=>?@[\\\\]^_`{|}~\",space:\" \\\\t\\\\r\\\\n\\\\v\\\\f\",upper:\"A-Z\",word:\"A-Za-z0-9_\",xdigit:\"A-Fa-f0-9\"};t.exports={MAX_LENGTH:1024*64,POSIX_REGEX_SOURCE:y,REGEX_BACKSLASH:/\\\\(?![*+?^${}(|)[\\]])/g,REGEX_NON_SPECIAL_CHARS:/^[^@![\\].,$*+?^{}()|\\\\/]+/,REGEX_SPECIAL_CHARS:/[-*+?.^${}(|)[\\]]/,REGEX_SPECIAL_CHARS_BACKREF:/(\\\\?)((\\W)(\\3*))/g,REGEX_SPECIAL_CHARS_GLOBAL:/([-*+?.^${}(|)[\\]])/g,REGEX_REMOVE_BACKSLASH:/(?:\\[.*?[^\\\\]\\]|\\\\(?=.))/g,REPLACEMENTS:{\"***\":\"*\",\"**/**\":\"**\",\"**/**/**\":\"**\"},CHAR_0:48,CHAR_9:57,CHAR_UPPERCASE_A:65,CHAR_LOWERCASE_A:97,CHAR_UPPERCASE_Z:90,CHAR_LOWERCASE_Z:122,CHAR_LEFT_PARENTHESES:40,CHAR_RIGHT_PARENTHESES:41,CHAR_ASTERISK:42,CHAR_AMPERSAND:38,CHAR_AT:64,CHAR_BACKWARD_SLASH:92,CHAR_CARRIAGE_RETURN:13,CHAR_CIRCUMFLEX_ACCENT:94,CHAR_COLON:58,CHAR_COMMA:44,CHAR_DOT:46,CHAR_DOUBLE_QUOTE:34,CHAR_EQUAL:61,CHAR_EXCLAMATION_MARK:33,CHAR_FORM_FEED:12,CHAR_FORWARD_SLASH:47,CHAR_GRAVE_ACCENT:96,CHAR_HASH:35,CHAR_HYPHEN_MINUS:45,CHAR_LEFT_ANGLE_BRACKET:60,CHAR_LEFT_CURLY_BRACE:123,CHAR_LEFT_SQUARE_BRACKET:91,CHAR_LINE_FEED:10,CHAR_NO_BREAK_SPACE:160,CHAR_PERCENT:37,CHAR_PLUS:43,CHAR_QUESTION_MARK:63,CHAR_RIGHT_ANGLE_BRACKET:62,CHAR_RIGHT_CURLY_BRACE:125,CHAR_RIGHT_SQUARE_BRACKET:93,CHAR_SEMICOLON:59,CHAR_SINGLE_QUOTE:39,CHAR_SPACE:32,CHAR_TAB:9,CHAR_UNDERSCORE:95,CHAR_VERTICAL_LINE:124,CHAR_ZERO_WIDTH_NOBREAK_SPACE:65279,extglobChars(t){return{\"!\":{type:\"negate\",open:\"(?:(?!(?:\",close:`))${t.STAR})`},\"?\":{type:\"qmark\",open:\"(?:\",close:\")?\"},\"+\":{type:\"plus\",open:\"(?:\",close:\")+\"},\"*\":{type:\"star\",open:\"(?:\",close:\")*\"},\"@\":{type:\"at\",open:\"(?:\",close:\")\"}}},globChars(t){return t===true?C:b}}},697:(t,e,u)=>{const n=u(154);const o=u(96);const{MAX_LENGTH:s,POSIX_REGEX_SOURCE:r,REGEX_NON_SPECIAL_CHARS:a,REGEX_SPECIAL_CHARS_BACKREF:i,REPLACEMENTS:c}=n;const expandRange=(t,e)=>{if(typeof e.expandRange===\"function\"){return e.expandRange(...t,e)}t.sort();const u=`[${t.join(\"-\")}]`;try{new RegExp(u)}catch(e){return t.map((t=>o.escapeRegex(t))).join(\"..\")}return u};const syntaxError=(t,e)=>`Missing ${t}: \"${e}\" - use \"\\\\\\\\${e}\" to match literal characters`;const parse=(t,e)=>{if(typeof t!==\"string\"){throw new TypeError(\"Expected a string\")}t=c[t]||t;const u={...e};const p=typeof u.maxLength===\"number\"?Math.min(s,u.maxLength):s;let l=t.length;if(l>p){throw new SyntaxError(`Input length: ${l}, exceeds maximum allowed length: ${p}`)}const f={type:\"bos\",value:\"\",output:u.prepend||\"\"};const A=[f];const _=u.capture?\"\":\"?:\";const R=n.globChars(u.windows);const E=n.extglobChars(R);const{DOT_LITERAL:h,PLUS_LITERAL:g,SLASH_LITERAL:b,ONE_CHAR:C,DOTS_SLASH:y,NO_DOT:$,NO_DOT_SLASH:x,NO_DOTS_SLASH:S,QMARK:H,QMARK_NO_DOT:v,STAR:d,START_ANCHOR:L}=R;const globstar=t=>`(${_}(?:(?!${L}${t.dot?y:h}).)*?)`;const T=u.dot?\"\":$;const O=u.dot?H:v;let k=u.bash===true?globstar(u):d;if(u.capture){k=`(${k})`}if(typeof u.noext===\"boolean\"){u.noextglob=u.noext}const m={input:t,index:-1,start:0,dot:u.dot===true,consumed:\"\",output:\"\",prefix:\"\",backtrack:false,negated:false,brackets:0,braces:0,parens:0,quotes:0,globstar:false,tokens:A};t=o.removePrefix(t,m);l=t.length;const w=[];const N=[];const I=[];let B=f;let G;const eos=()=>m.index===l-1;const D=m.peek=(e=1)=>t[m.index+e];const M=m.advance=()=>t[++m.index]||\"\";const remaining=()=>t.slice(m.index+1);const consume=(t=\"\",e=0)=>{m.consumed+=t;m.index+=e};const append=t=>{m.output+=t.output!=null?t.output:t.value;consume(t.value)};const negate=()=>{let t=1;while(D()===\"!\"&&(D(2)!==\"(\"||D(3)===\"?\")){M();m.start++;t++}if(t%2===0){return false}m.negated=true;m.start++;return true};const increment=t=>{m[t]++;I.push(t)};const decrement=t=>{m[t]--;I.pop()};const push=t=>{if(B.type===\"globstar\"){const e=m.braces>0&&(t.type===\"comma\"||t.type===\"brace\");const u=t.extglob===true||w.length&&(t.type===\"pipe\"||t.type===\"paren\");if(t.type!==\"slash\"&&t.type!==\"paren\"&&!e&&!u){m.output=m.output.slice(0,-B.output.length);B.type=\"star\";B.value=\"*\";B.output=k;m.output+=B.output}}if(w.length&&t.type!==\"paren\"){w[w.length-1].inner+=t.value}if(t.value||t.output)append(t);if(B&&B.type===\"text\"&&t.type===\"text\"){B.output=(B.output||B.value)+t.value;B.value+=t.value;return}t.prev=B;A.push(t);B=t};const extglobOpen=(t,e)=>{const n={...E[e],conditions:1,inner:\"\"};n.prev=B;n.parens=m.parens;n.output=m.output;const o=(u.capture?\"(\":\"\")+n.open;increment(\"parens\");push({type:t,value:e,output:m.output?\"\":C});push({type:\"paren\",extglob:true,value:M(),output:o});w.push(n)};const extglobClose=t=>{let n=t.close+(u.capture?\")\":\"\");let o;if(t.type===\"negate\"){let s=k;if(t.inner&&t.inner.length>1&&t.inner.includes(\"/\")){s=globstar(u)}if(s!==k||eos()||/^\\)+$/.test(remaining())){n=t.close=`)$))${s}`}if(t.inner.includes(\"*\")&&(o=remaining())&&/^\\.[^\\\\/.]+$/.test(o)){const u=parse(o,{...e,fastpaths:false}).output;n=t.close=`)${u})${s})`}if(t.prev.type===\"bos\"){m.negatedExtglob=true}}push({type:\"paren\",extglob:true,value:G,output:n});decrement(\"parens\")};if(u.fastpaths!==false&&!/(^[*!]|[/()[\\]{}\"])/.test(t)){let n=false;let s=t.replace(i,((t,e,u,o,s,r)=>{if(o===\"\\\\\"){n=true;return t}if(o===\"?\"){if(e){return e+o+(s?H.repeat(s.length):\"\")}if(r===0){return O+(s?H.repeat(s.length):\"\")}return H.repeat(u.length)}if(o===\".\"){return h.repeat(u.length)}if(o===\"*\"){if(e){return e+o+(s?k:\"\")}return k}return e?t:`\\\\${t}`}));if(n===true){if(u.unescape===true){s=s.replace(/\\\\/g,\"\")}else{s=s.replace(/\\\\+/g,(t=>t.length%2===0?\"\\\\\\\\\":t?\"\\\\\":\"\"))}}if(s===t&&u.contains===true){m.output=t;return m}m.output=o.wrapOutput(s,m,e);return m}while(!eos()){G=M();if(G===\"\\0\"){continue}if(G===\"\\\\\"){const t=D();if(t===\"/\"&&u.bash!==true){continue}if(t===\".\"||t===\";\"){continue}if(!t){G+=\"\\\\\";push({type:\"text\",value:G});continue}const e=/^\\\\+/.exec(remaining());let n=0;if(e&&e[0].length>2){n=e[0].length;m.index+=n;if(n%2!==0){G+=\"\\\\\"}}if(u.unescape===true){G=M()}else{G+=M()}if(m.brackets===0){push({type:\"text\",value:G});continue}}if(m.brackets>0&&(G!==\"]\"||B.value===\"[\"||B.value===\"[^\")){if(u.posix!==false&&G===\":\"){const t=B.value.slice(1);if(t.includes(\"[\")){B.posix=true;if(t.includes(\":\")){const t=B.value.lastIndexOf(\"[\");const e=B.value.slice(0,t);const u=B.value.slice(t+2);const n=r[u];if(n){B.value=e+n;m.backtrack=true;M();if(!f.output&&A.indexOf(B)===1){f.output=C}continue}}}}if(G===\"[\"&&D()!==\":\"||G===\"-\"&&D()===\"]\"){G=`\\\\${G}`}if(G===\"]\"&&(B.value===\"[\"||B.value===\"[^\")){G=`\\\\${G}`}if(u.posix===true&&G===\"!\"&&B.value===\"[\"){G=\"^\"}B.value+=G;append({value:G});continue}if(m.quotes===1&&G!=='\"'){G=o.escapeRegex(G);B.value+=G;append({value:G});continue}if(G==='\"'){m.quotes=m.quotes===1?0:1;if(u.keepQuotes===true){push({type:\"text\",value:G})}continue}if(G===\"(\"){increment(\"parens\");push({type:\"paren\",value:G});continue}if(G===\")\"){if(m.parens===0&&u.strictBrackets===true){throw new SyntaxError(syntaxError(\"opening\",\"(\"))}const t=w[w.length-1];if(t&&m.parens===t.parens+1){extglobClose(w.pop());continue}push({type:\"paren\",value:G,output:m.parens?\")\":\"\\\\)\"});decrement(\"parens\");continue}if(G===\"[\"){if(u.nobracket===true||!remaining().includes(\"]\")){if(u.nobracket!==true&&u.strictBrackets===true){throw new SyntaxError(syntaxError(\"closing\",\"]\"))}G=`\\\\${G}`}else{increment(\"brackets\")}push({type:\"bracket\",value:G});continue}if(G===\"]\"){if(u.nobracket===true||B&&B.type===\"bracket\"&&B.value.length===1){push({type:\"text\",value:G,output:`\\\\${G}`});continue}if(m.brackets===0){if(u.strictBrackets===true){throw new SyntaxError(syntaxError(\"opening\",\"[\"))}push({type:\"text\",value:G,output:`\\\\${G}`});continue}decrement(\"brackets\");const t=B.value.slice(1);if(B.posix!==true&&t[0]===\"^\"&&!t.includes(\"/\")){G=`/${G}`}B.value+=G;append({value:G});if(u.literalBrackets===false||o.hasRegexChars(t)){continue}const e=o.escapeRegex(B.value);m.output=m.output.slice(0,-B.value.length);if(u.literalBrackets===true){m.output+=e;B.value=e;continue}B.value=`(${_}${e}|${B.value})`;m.output+=B.value;continue}if(G===\"{\"&&u.nobrace!==true){increment(\"braces\");const t={type:\"brace\",value:G,output:\"(\",outputIndex:m.output.length,tokensIndex:m.tokens.length};N.push(t);push(t);continue}if(G===\"}\"){const t=N[N.length-1];if(u.nobrace===true||!t){push({type:\"text\",value:G,output:G});continue}let e=\")\";if(t.dots===true){const t=A.slice();const n=[];for(let e=t.length-1;e>=0;e--){A.pop();if(t[e].type===\"brace\"){break}if(t[e].type!==\"dots\"){n.unshift(t[e].value)}}e=expandRange(n,u);m.backtrack=true}if(t.comma!==true&&t.dots!==true){const u=m.output.slice(0,t.outputIndex);const n=m.tokens.slice(t.tokensIndex);t.value=t.output=\"\\\\{\";G=e=\"\\\\}\";m.output=u;for(const t of n){m.output+=t.output||t.value}}push({type:\"brace\",value:G,output:e});decrement(\"braces\");N.pop();continue}if(G===\"|\"){if(w.length>0){w[w.length-1].conditions++}push({type:\"text\",value:G});continue}if(G===\",\"){let t=G;const e=N[N.length-1];if(e&&I[I.length-1]===\"braces\"){e.comma=true;t=\"|\"}push({type:\"comma\",value:G,output:t});continue}if(G===\"/\"){if(B.type===\"dot\"&&m.index===m.start+1){m.start=m.index+1;m.consumed=\"\";m.output=\"\";A.pop();B=f;continue}push({type:\"slash\",value:G,output:b});continue}if(G===\".\"){if(m.braces>0&&B.type===\"dot\"){if(B.value===\".\")B.output=h;const t=N[N.length-1];B.type=\"dots\";B.output+=G;B.value+=G;t.dots=true;continue}if(m.braces+m.parens===0&&B.type!==\"bos\"&&B.type!==\"slash\"){push({type:\"text\",value:G,output:h});continue}push({type:\"dot\",value:G,output:h});continue}if(G===\"?\"){const t=B&&B.value===\"(\";if(!t&&u.noextglob!==true&&D()===\"(\"&&D(2)!==\"?\"){extglobOpen(\"qmark\",G);continue}if(B&&B.type===\"paren\"){const t=D();let e=G;if(B.value===\"(\"&&!/[!=<:]/.test(t)||t===\"<\"&&!/<([!=]|\\w+>)/.test(remaining())){e=`\\\\${G}`}push({type:\"text\",value:G,output:e});continue}if(u.dot!==true&&(B.type===\"slash\"||B.type===\"bos\")){push({type:\"qmark\",value:G,output:v});continue}push({type:\"qmark\",value:G,output:H});continue}if(G===\"!\"){if(u.noextglob!==true&&D()===\"(\"){if(D(2)!==\"?\"||!/[!=<:]/.test(D(3))){extglobOpen(\"negate\",G);continue}}if(u.nonegate!==true&&m.index===0){negate();continue}}if(G===\"+\"){if(u.noextglob!==true&&D()===\"(\"&&D(2)!==\"?\"){extglobOpen(\"plus\",G);continue}if(B&&B.value===\"(\"||u.regex===false){push({type:\"plus\",value:G,output:g});continue}if(B&&(B.type===\"bracket\"||B.type===\"paren\"||B.type===\"brace\")||m.parens>0){push({type:\"plus\",value:G});continue}push({type:\"plus\",value:g});continue}if(G===\"@\"){if(u.noextglob!==true&&D()===\"(\"&&D(2)!==\"?\"){push({type:\"at\",extglob:true,value:G,output:\"\"});continue}push({type:\"text\",value:G});continue}if(G!==\"*\"){if(G===\"$\"||G===\"^\"){G=`\\\\${G}`}const t=a.exec(remaining());if(t){G+=t[0];m.index+=t[0].length}push({type:\"text\",value:G});continue}if(B&&(B.type===\"globstar\"||B.star===true)){B.type=\"star\";B.star=true;B.value+=G;B.output=k;m.backtrack=true;m.globstar=true;consume(G);continue}let e=remaining();if(u.noextglob!==true&&/^\\([^?]/.test(e)){extglobOpen(\"star\",G);continue}if(B.type===\"star\"){if(u.noglobstar===true){consume(G);continue}const n=B.prev;const o=n.prev;const s=n.type===\"slash\"||n.type===\"bos\";const r=o&&(o.type===\"star\"||o.type===\"globstar\");if(u.bash===true&&(!s||e[0]&&e[0]!==\"/\")){push({type:\"star\",value:G,output:\"\"});continue}const a=m.braces>0&&(n.type===\"comma\"||n.type===\"brace\");const i=w.length&&(n.type===\"pipe\"||n.type===\"paren\");if(!s&&n.type!==\"paren\"&&!a&&!i){push({type:\"star\",value:G,output:\"\"});continue}while(e.slice(0,3)===\"/**\"){const u=t[m.index+4];if(u&&u!==\"/\"){break}e=e.slice(3);consume(\"/**\",3)}if(n.type===\"bos\"&&eos()){B.type=\"globstar\";B.value+=G;B.output=globstar(u);m.output=B.output;m.globstar=true;consume(G);continue}if(n.type===\"slash\"&&n.prev.type!==\"bos\"&&!r&&eos()){m.output=m.output.slice(0,-(n.output+B.output).length);n.output=`(?:${n.output}`;B.type=\"globstar\";B.output=globstar(u)+(u.strictSlashes?\")\":\"|$)\");B.value+=G;m.globstar=true;m.output+=n.output+B.output;consume(G);continue}if(n.type===\"slash\"&&n.prev.type!==\"bos\"&&e[0]===\"/\"){const t=e[1]!==void 0?\"|$\":\"\";m.output=m.output.slice(0,-(n.output+B.output).length);n.output=`(?:${n.output}`;B.type=\"globstar\";B.output=`${globstar(u)}${b}|${b}${t})`;B.value+=G;m.output+=n.output+B.output;m.globstar=true;consume(G+M());push({type:\"slash\",value:\"/\",output:\"\"});continue}if(n.type===\"bos\"&&e[0]===\"/\"){B.type=\"globstar\";B.value+=G;B.output=`(?:^|${b}|${globstar(u)}${b})`;m.output=B.output;m.globstar=true;consume(G+M());push({type:\"slash\",value:\"/\",output:\"\"});continue}m.output=m.output.slice(0,-B.output.length);B.type=\"globstar\";B.output=globstar(u);B.value+=G;m.output+=B.output;m.globstar=true;consume(G);continue}const n={type:\"star\",value:G,output:k};if(u.bash===true){n.output=\".*?\";if(B.type===\"bos\"||B.type===\"slash\"){n.output=T+n.output}push(n);continue}if(B&&(B.type===\"bracket\"||B.type===\"paren\")&&u.regex===true){n.output=G;push(n);continue}if(m.index===m.start||B.type===\"slash\"||B.type===\"dot\"){if(B.type===\"dot\"){m.output+=x;B.output+=x}else if(u.dot===true){m.output+=S;B.output+=S}else{m.output+=T;B.output+=T}if(D()!==\"*\"){m.output+=C;B.output+=C}}push(n)}while(m.brackets>0){if(u.strictBrackets===true)throw new SyntaxError(syntaxError(\"closing\",\"]\"));m.output=o.escapeLast(m.output,\"[\");decrement(\"brackets\")}while(m.parens>0){if(u.strictBrackets===true)throw new SyntaxError(syntaxError(\"closing\",\")\"));m.output=o.escapeLast(m.output,\"(\");decrement(\"parens\")}while(m.braces>0){if(u.strictBrackets===true)throw new SyntaxError(syntaxError(\"closing\",\"}\"));m.output=o.escapeLast(m.output,\"{\");decrement(\"braces\")}if(u.strictSlashes!==true&&(B.type===\"star\"||B.type===\"bracket\")){push({type:\"maybe_slash\",value:\"\",output:`${b}?`})}if(m.backtrack===true){m.output=\"\";for(const t of m.tokens){m.output+=t.output!=null?t.output:t.value;if(t.suffix){m.output+=t.suffix}}}return m};parse.fastpaths=(t,e)=>{const u={...e};const r=typeof u.maxLength===\"number\"?Math.min(s,u.maxLength):s;const a=t.length;if(a>r){throw new SyntaxError(`Input length: ${a}, exceeds maximum allowed length: ${r}`)}t=c[t]||t;const{DOT_LITERAL:i,SLASH_LITERAL:p,ONE_CHAR:l,DOTS_SLASH:f,NO_DOT:A,NO_DOTS:_,NO_DOTS_SLASH:R,STAR:E,START_ANCHOR:h}=n.globChars(u.windows);const g=u.dot?_:A;const b=u.dot?R:A;const C=u.capture?\"\":\"?:\";const y={negated:false,prefix:\"\"};let $=u.bash===true?\".*?\":E;if(u.capture){$=`(${$})`}const globstar=t=>{if(t.noglobstar===true)return $;return`(${C}(?:(?!${h}${t.dot?f:i}).)*?)`};const create=t=>{switch(t){case\"*\":return`${g}${l}${$}`;case\".*\":return`${i}${l}${$}`;case\"*.*\":return`${g}${$}${i}${l}${$}`;case\"*/*\":return`${g}${$}${p}${l}${b}${$}`;case\"**\":return g+globstar(u);case\"**/*\":return`(?:${g}${globstar(u)}${p})?${b}${l}${$}`;case\"**/*.*\":return`(?:${g}${globstar(u)}${p})?${b}${$}${i}${l}${$}`;case\"**/.*\":return`(?:${g}${globstar(u)}${p})?${i}${l}${$}`;default:{const e=/^(.*?)\\.(\\w+)$/.exec(t);if(!e)return;const u=create(e[1]);if(!u)return;return u+i+e[2]}}};const x=o.removePrefix(t,y);let S=create(x);if(S&&u.strictSlashes!==true){S+=`${p}?`}return S};t.exports=parse},510:(t,e,u)=>{const n=u(716);const o=u(697);const s=u(96);const r=u(154);const isObject=t=>t&&typeof t===\"object\"&&!Array.isArray(t);const picomatch=(t,e,u=false)=>{if(Array.isArray(t)){const n=t.map((t=>picomatch(t,e,u)));const arrayMatcher=t=>{for(const e of n){const u=e(t);if(u)return u}return false};return arrayMatcher}const n=isObject(t)&&t.tokens&&t.input;if(t===\"\"||typeof t!==\"string\"&&!n){throw new TypeError(\"Expected pattern to be a non-empty string\")}const o=e||{};const s=o.windows;const r=n?picomatch.compileRe(t,e):picomatch.makeRe(t,e,false,true);const a=r.state;delete r.state;let isIgnored=()=>false;if(o.ignore){const t={...e,ignore:null,onMatch:null,onResult:null};isIgnored=picomatch(o.ignore,t,u)}const matcher=(u,n=false)=>{const{isMatch:i,match:c,output:p}=picomatch.test(u,r,e,{glob:t,posix:s});const l={glob:t,state:a,regex:r,posix:s,input:u,output:p,match:c,isMatch:i};if(typeof o.onResult===\"function\"){o.onResult(l)}if(i===false){l.isMatch=false;return n?l:false}if(isIgnored(u)){if(typeof o.onIgnore===\"function\"){o.onIgnore(l)}l.isMatch=false;return n?l:false}if(typeof o.onMatch===\"function\"){o.onMatch(l)}return n?l:true};if(u){matcher.state=a}return matcher};picomatch.test=(t,e,u,{glob:n,posix:o}={})=>{if(typeof t!==\"string\"){throw new TypeError(\"Expected input to be a string\")}if(t===\"\"){return{isMatch:false,output:\"\"}}const r=u||{};const a=r.format||(o?s.toPosixSlashes:null);let i=t===n;let c=i&&a?a(t):t;if(i===false){c=a?a(t):t;i=c===n}if(i===false||r.capture===true){if(r.matchBase===true||r.basename===true){i=picomatch.matchBase(t,e,u,o)}else{i=e.exec(c)}}return{isMatch:Boolean(i),match:i,output:c}};picomatch.matchBase=(t,e,u)=>{const n=e instanceof RegExp?e:picomatch.makeRe(e,u);return n.test(s.basename(t))};picomatch.isMatch=(t,e,u)=>picomatch(e,u)(t);picomatch.parse=(t,e)=>{if(Array.isArray(t))return t.map((t=>picomatch.parse(t,e)));return o(t,{...e,fastpaths:false})};picomatch.scan=(t,e)=>n(t,e);picomatch.compileRe=(t,e,u=false,n=false)=>{if(u===true){return t.output}const o=e||{};const s=o.contains?\"\":\"^\";const r=o.contains?\"\":\"$\";let a=`${s}(?:${t.output})${r}`;if(t&&t.negated===true){a=`^(?!${a}).*$`}const i=picomatch.toRegex(a,e);if(n===true){i.state=t}return i};picomatch.makeRe=(t,e={},u=false,n=false)=>{if(!t||typeof t!==\"string\"){throw new TypeError(\"Expected a non-empty string\")}let s={negated:false,fastpaths:true};if(e.fastpaths!==false&&(t[0]===\".\"||t[0]===\"*\")){s.output=o.fastpaths(t,e)}if(!s.output){s=o(t,e)}return picomatch.compileRe(s,e,u,n)};picomatch.toRegex=(t,e)=>{try{const u=e||{};return new RegExp(t,u.flags||(u.nocase?\"i\":\"\"))}catch(t){if(e&&e.debug===true)throw t;return/$^/}};picomatch.constants=r;t.exports=picomatch},716:(t,e,u)=>{const n=u(96);const{CHAR_ASTERISK:o,CHAR_AT:s,CHAR_BACKWARD_SLASH:r,CHAR_COMMA:a,CHAR_DOT:i,CHAR_EXCLAMATION_MARK:c,CHAR_FORWARD_SLASH:p,CHAR_LEFT_CURLY_BRACE:l,CHAR_LEFT_PARENTHESES:f,CHAR_LEFT_SQUARE_BRACKET:A,CHAR_PLUS:_,CHAR_QUESTION_MARK:R,CHAR_RIGHT_CURLY_BRACE:E,CHAR_RIGHT_PARENTHESES:h,CHAR_RIGHT_SQUARE_BRACKET:g}=u(154);const isPathSeparator=t=>t===p||t===r;const depth=t=>{if(t.isPrefix!==true){t.depth=t.isGlobstar?Infinity:1}};const scan=(t,e)=>{const u=e||{};const b=t.length-1;const C=u.parts===true||u.scanToEnd===true;const y=[];const $=[];const x=[];let S=t;let H=-1;let v=0;let d=0;let L=false;let T=false;let O=false;let k=false;let m=false;let w=false;let N=false;let I=false;let B=false;let G=false;let D=0;let M;let P;let K={value:\"\",depth:0,isGlob:false};const eos=()=>H>=b;const peek=()=>S.charCodeAt(H+1);const advance=()=>{M=P;return S.charCodeAt(++H)};while(H<b){P=advance();let t;if(P===r){N=K.backslashes=true;P=advance();if(P===l){w=true}continue}if(w===true||P===l){D++;while(eos()!==true&&(P=advance())){if(P===r){N=K.backslashes=true;advance();continue}if(P===l){D++;continue}if(w!==true&&P===i&&(P=advance())===i){L=K.isBrace=true;O=K.isGlob=true;G=true;if(C===true){continue}break}if(w!==true&&P===a){L=K.isBrace=true;O=K.isGlob=true;G=true;if(C===true){continue}break}if(P===E){D--;if(D===0){w=false;L=K.isBrace=true;G=true;break}}}if(C===true){continue}break}if(P===p){y.push(H);$.push(K);K={value:\"\",depth:0,isGlob:false};if(G===true)continue;if(M===i&&H===v+1){v+=2;continue}d=H+1;continue}if(u.noext!==true){const t=P===_||P===s||P===o||P===R||P===c;if(t===true&&peek()===f){O=K.isGlob=true;k=K.isExtglob=true;G=true;if(P===c&&H===v){B=true}if(C===true){while(eos()!==true&&(P=advance())){if(P===r){N=K.backslashes=true;P=advance();continue}if(P===h){O=K.isGlob=true;G=true;break}}continue}break}}if(P===o){if(M===o)m=K.isGlobstar=true;O=K.isGlob=true;G=true;if(C===true){continue}break}if(P===R){O=K.isGlob=true;G=true;if(C===true){continue}break}if(P===A){while(eos()!==true&&(t=advance())){if(t===r){N=K.backslashes=true;advance();continue}if(t===g){T=K.isBracket=true;O=K.isGlob=true;G=true;break}}if(C===true){continue}break}if(u.nonegate!==true&&P===c&&H===v){I=K.negated=true;v++;continue}if(u.noparen!==true&&P===f){O=K.isGlob=true;if(C===true){while(eos()!==true&&(P=advance())){if(P===f){N=K.backslashes=true;P=advance();continue}if(P===h){G=true;break}}continue}break}if(O===true){G=true;if(C===true){continue}break}}if(u.noext===true){k=false;O=false}let U=S;let X=\"\";let F=\"\";if(v>0){X=S.slice(0,v);S=S.slice(v);d-=v}if(U&&O===true&&d>0){U=S.slice(0,d);F=S.slice(d)}else if(O===true){U=\"\";F=S}else{U=S}if(U&&U!==\"\"&&U!==\"/\"&&U!==S){if(isPathSeparator(U.charCodeAt(U.length-1))){U=U.slice(0,-1)}}if(u.unescape===true){if(F)F=n.removeBackslashes(F);if(U&&N===true){U=n.removeBackslashes(U)}}const Q={prefix:X,input:t,start:v,base:U,glob:F,isBrace:L,isBracket:T,isGlob:O,isExtglob:k,isGlobstar:m,negated:I,negatedExtglob:B};if(u.tokens===true){Q.maxDepth=0;if(!isPathSeparator(P)){$.push(K)}Q.tokens=$}if(u.parts===true||u.tokens===true){let e;for(let n=0;n<y.length;n++){const o=e?e+1:v;const s=y[n];const r=t.slice(o,s);if(u.tokens){if(n===0&&v!==0){$[n].isPrefix=true;$[n].value=X}else{$[n].value=r}depth($[n]);Q.maxDepth+=$[n].depth}if(n!==0||r!==\"\"){x.push(r)}e=s}if(e&&e+1<t.length){const n=t.slice(e+1);x.push(n);if(u.tokens){$[$.length-1].value=n;depth($[$.length-1]);Q.maxDepth+=$[$.length-1].depth}}Q.slashes=y;Q.parts=x}return Q};t.exports=scan},96:(t,e,u)=>{const{REGEX_BACKSLASH:n,REGEX_REMOVE_BACKSLASH:o,REGEX_SPECIAL_CHARS:s,REGEX_SPECIAL_CHARS_GLOBAL:r}=u(154);e.isObject=t=>t!==null&&typeof t===\"object\"&&!Array.isArray(t);e.hasRegexChars=t=>s.test(t);e.isRegexChar=t=>t.length===1&&e.hasRegexChars(t);e.escapeRegex=t=>t.replace(r,\"\\\\$1\");e.toPosixSlashes=t=>t.replace(n,\"/\");e.removeBackslashes=t=>t.replace(o,(t=>t===\"\\\\\"?\"\":t));e.escapeLast=(t,u,n)=>{const o=t.lastIndexOf(u,n);if(o===-1)return t;if(t[o-1]===\"\\\\\")return e.escapeLast(t,u,o-1);return`${t.slice(0,o)}\\\\${t.slice(o)}`};e.removePrefix=(t,e={})=>{let u=t;if(u.startsWith(\"./\")){u=u.slice(2);e.prefix=\"./\"}return u};e.wrapOutput=(t,e={},u={})=>{const n=u.contains?\"\":\"^\";const o=u.contains?\"\":\"$\";let s=`${n}(?:${t})${o}`;if(e.negated===true){s=`(?:^(?!${s}).*$)`}return s};e.basename=(t,{windows:e}={})=>{const u=t.split(e?/[\\\\/]/:\"/\");const n=u[u.length-1];if(n===\"\"){return u[u.length-2]}return n}}};var e={};function __nccwpck_require__(u){var n=e[u];if(n!==undefined){return n.exports}var o=e[u]={exports:{}};var s=true;try{t[u](o,o.exports,__nccwpck_require__);s=false}finally{if(s)delete e[u]}return o.exports}if(typeof __nccwpck_require__!==\"undefined\")__nccwpck_require__.ab=__dirname+\"/\";var u=__nccwpck_require__(170);module.exports=u})();"], "names": [], "mappings": "AAAwN;AAAxN,CAAC;IAAK;IAAa,IAAI,IAAE;QAAC,KAAI,CAAC,GAAE,GAAE;YAAK,MAAM,IAAE,EAAE;YAAK,MAAM,YAAU;gBAAK,IAAG,OAAO,cAAY,eAAa,UAAU,QAAQ,EAAC;oBAAC,MAAM,IAAE,UAAU,QAAQ,CAAC,WAAW;oBAAG,OAAO,MAAI,WAAS,MAAI;gBAAS;gBAAC,IAAG,OAAO,gKAAA,CAAA,UAAO,KAAG,eAAa,gKAAA,CAAA,UAAO,CAAC,QAAQ,EAAC;oBAAC,OAAO,gKAAA,CAAA,UAAO,CAAC,QAAQ,KAAG;gBAAO;gBAAC,OAAO;YAAK;YAAE,SAAS,UAAU,CAAC,EAAC,CAAC,EAAC,IAAE,KAAK;gBAAE,IAAG,KAAG,CAAC,EAAE,OAAO,KAAG,QAAM,EAAE,OAAO,KAAG,SAAS,GAAE;oBAAC,IAAE;wBAAC,GAAG,CAAC;wBAAC,SAAQ;oBAAW;gBAAC;gBAAC,OAAO,EAAE,GAAE,GAAE;YAAE;YAAC,OAAO,MAAM,CAAC,WAAU;YAAG,EAAE,OAAO,GAAC;QAAS;QAAE,KAAI,CAAA;YAAI,MAAM,IAAE;YAAQ,MAAM,IAAE,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;YAAC,MAAM,IAAE;YAAM,MAAM,IAAE;YAAM,MAAM,IAAE;YAAM,MAAM,IAAE;YAAM,MAAM,IAAE;YAAQ,MAAM,IAAE;YAAO,MAAM,IAAE,CAAC,GAAG,EAAE,EAAE,GAAG,CAAC;YAAC,MAAM,IAAE,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC;YAAC,MAAM,IAAE,GAAG,EAAE,KAAK,EAAE,GAAG;YAAC,MAAM,IAAE,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC;YAAC,MAAM,IAAE,CAAC,GAAG,EAAE,IAAI,EAAE,CAAC,CAAC;YAAC,MAAM,IAAE,CAAC,GAAG,EAAE,EAAE,KAAK,EAAE,EAAE,CAAC,CAAC;YAAC,MAAM,IAAE,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC;YAAC,MAAM,IAAE,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC;YAAC,MAAM,IAAE,GAAG,EAAE,EAAE,CAAC;YAAC,MAAM,IAAE;YAAI,MAAM,IAAE;gBAAC,aAAY;gBAAE,cAAa;gBAAE,eAAc;gBAAE,eAAc;gBAAE,UAAS;gBAAE,OAAM;gBAAE,YAAW;gBAAE,YAAW;gBAAE,QAAO;gBAAE,SAAQ;gBAAE,cAAa;gBAAE,eAAc;gBAAE,cAAa;gBAAE,MAAK;gBAAE,cAAa;gBAAE,KAAI;YAAC;YAAE,MAAM,IAAE;gBAAC,GAAG,CAAC;gBAAC,eAAc,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;gBAAC,OAAM;gBAAE,MAAK,GAAG,EAAE,EAAE,CAAC;gBAAC,YAAW,GAAG,EAAE,SAAS,EAAE,EAAE,IAAI,CAAC;gBAAC,QAAO,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC;gBAAC,SAAQ,CAAC,SAAS,EAAE,EAAE,EAAE,EAAE,EAAE,SAAS,EAAE,EAAE,KAAK,CAAC;gBAAC,cAAa,CAAC,GAAG,EAAE,EAAE,SAAS,EAAE,EAAE,KAAK,CAAC;gBAAC,eAAc,CAAC,GAAG,EAAE,EAAE,SAAS,EAAE,EAAE,KAAK,CAAC;gBAAC,cAAa,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC;gBAAC,cAAa,CAAC,MAAM,EAAE,EAAE,EAAE,CAAC;gBAAC,YAAW,CAAC,IAAI,EAAE,EAAE,IAAI,CAAC;gBAAC,KAAI;YAAI;YAAE,MAAM,IAAE;gBAAC,OAAM;gBAAY,OAAM;gBAAS,OAAM;gBAAc,OAAM;gBAAO,OAAM;gBAAmB,OAAM;gBAAM,OAAM;gBAAc,OAAM;gBAAM,OAAM;gBAAe,OAAM;gBAAyC,OAAM;gBAAmB,OAAM;gBAAM,MAAK;gBAAa,QAAO;YAAW;YAAE,EAAE,OAAO,GAAC;gBAAC,YAAW,OAAK;gBAAG,oBAAmB;gBAAE,iBAAgB;gBAAyB,yBAAwB;gBAA4B,qBAAoB;gBAAoB,6BAA4B;gBAAoB,4BAA2B;gBAAuB,wBAAuB;gBAA4B,cAAa;oBAAC,OAAM;oBAAI,SAAQ;oBAAK,YAAW;gBAAI;gBAAE,QAAO;gBAAG,QAAO;gBAAG,kBAAiB;gBAAG,kBAAiB;gBAAG,kBAAiB;gBAAG,kBAAiB;gBAAI,uBAAsB;gBAAG,wBAAuB;gBAAG,eAAc;gBAAG,gBAAe;gBAAG,SAAQ;gBAAG,qBAAoB;gBAAG,sBAAqB;gBAAG,wBAAuB;gBAAG,YAAW;gBAAG,YAAW;gBAAG,UAAS;gBAAG,mBAAkB;gBAAG,YAAW;gBAAG,uBAAsB;gBAAG,gBAAe;gBAAG,oBAAmB;gBAAG,mBAAkB;gBAAG,WAAU;gBAAG,mBAAkB;gBAAG,yBAAwB;gBAAG,uBAAsB;gBAAI,0BAAyB;gBAAG,gBAAe;gBAAG,qBAAoB;gBAAI,cAAa;gBAAG,WAAU;gBAAG,oBAAmB;gBAAG,0BAAyB;gBAAG,wBAAuB;gBAAI,2BAA0B;gBAAG,gBAAe;gBAAG,mBAAkB;gBAAG,YAAW;gBAAG,UAAS;gBAAE,iBAAgB;gBAAG,oBAAmB;gBAAI,+BAA8B;gBAAM,cAAa,CAAC;oBAAE,OAAM;wBAAC,KAAI;4BAAC,MAAK;4BAAS,MAAK;4BAAY,OAAM,CAAC,EAAE,EAAE,EAAE,IAAI,CAAC,CAAC,CAAC;wBAAA;wBAAE,KAAI;4BAAC,MAAK;4BAAQ,MAAK;4BAAM,OAAM;wBAAI;wBAAE,KAAI;4BAAC,MAAK;4BAAO,MAAK;4BAAM,OAAM;wBAAI;wBAAE,KAAI;4BAAC,MAAK;4BAAO,MAAK;4BAAM,OAAM;wBAAI;wBAAE,KAAI;4BAAC,MAAK;4BAAK,MAAK;4BAAM,OAAM;wBAAG;oBAAC;gBAAC;gBAAE,WAAU,CAAC;oBAAE,OAAO,MAAI,OAAK,IAAE;gBAAC;YAAC;QAAC;QAAE,KAAI,CAAC,GAAE,GAAE;YAAK,MAAM,IAAE,EAAE;YAAK,MAAM,IAAE,EAAE;YAAI,MAAK,EAAC,YAAW,CAAC,EAAC,oBAAmB,CAAC,EAAC,yBAAwB,CAAC,EAAC,6BAA4B,CAAC,EAAC,cAAa,CAAC,EAAC,GAAC;YAAE,MAAM,cAAY,CAAC,GAAE;gBAAK,IAAG,OAAO,EAAE,WAAW,KAAG,YAAW;oBAAC,OAAO,EAAE,WAAW,IAAI,GAAE;gBAAE;gBAAC,EAAE,IAAI;gBAAG,MAAM,IAAE,CAAC,CAAC,EAAE,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC;gBAAC,IAAG;oBAAC,IAAI,OAAO;gBAAE,EAAC,OAAM,GAAE;oBAAC,OAAO,EAAE,GAAG,CAAE,CAAA,IAAG,EAAE,WAAW,CAAC,IAAK,IAAI,CAAC;gBAAK;gBAAC,OAAO;YAAC;YAAE,MAAM,cAAY,CAAC,GAAE,IAAI,CAAC,QAAQ,EAAE,EAAE,GAAG,EAAE,EAAE,aAAa,EAAE,EAAE,6BAA6B,CAAC;YAAC,MAAM,QAAM,CAAC,GAAE;gBAAK,IAAG,OAAO,MAAI,UAAS;oBAAC,MAAM,IAAI,UAAU;gBAAoB;gBAAC,IAAE,CAAC,CAAC,EAAE,IAAE;gBAAE,MAAM,IAAE;oBAAC,GAAG,CAAC;gBAAA;gBAAE,MAAM,IAAE,OAAO,EAAE,SAAS,KAAG,WAAS,KAAK,GAAG,CAAC,GAAE,EAAE,SAAS,IAAE;gBAAE,IAAI,IAAE,EAAE,MAAM;gBAAC,IAAG,IAAE,GAAE;oBAAC,MAAM,IAAI,YAAY,CAAC,cAAc,EAAE,EAAE,kCAAkC,EAAE,GAAG;gBAAC;gBAAC,MAAM,IAAE;oBAAC,MAAK;oBAAM,OAAM;oBAAG,QAAO,EAAE,OAAO,IAAE;gBAAE;gBAAE,MAAM,IAAE;oBAAC;iBAAE;gBAAC,MAAM,IAAE,EAAE,OAAO,GAAC,KAAG;gBAAK,MAAM,IAAE,EAAE,SAAS,CAAC,EAAE,OAAO;gBAAE,MAAM,IAAE,EAAE,YAAY,CAAC;gBAAG,MAAK,EAAC,aAAY,CAAC,EAAC,cAAa,CAAC,EAAC,eAAc,CAAC,EAAC,UAAS,CAAC,EAAC,YAAW,CAAC,EAAC,QAAO,CAAC,EAAC,cAAa,CAAC,EAAC,eAAc,CAAC,EAAC,OAAM,CAAC,EAAC,cAAa,CAAC,EAAC,MAAK,CAAC,EAAC,cAAa,CAAC,EAAC,GAAC;gBAAE,MAAM,WAAS,CAAA,IAAG,CAAC,CAAC,EAAE,EAAE,MAAM,EAAE,IAAI,EAAE,GAAG,GAAC,IAAE,EAAE,MAAM,CAAC;gBAAC,MAAM,IAAE,EAAE,GAAG,GAAC,KAAG;gBAAE,MAAM,IAAE,EAAE,GAAG,GAAC,IAAE;gBAAE,IAAI,IAAE,EAAE,IAAI,KAAG,OAAK,SAAS,KAAG;gBAAE,IAAG,EAAE,OAAO,EAAC;oBAAC,IAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;gBAAA;gBAAC,IAAG,OAAO,EAAE,KAAK,KAAG,WAAU;oBAAC,EAAE,SAAS,GAAC,EAAE,KAAK;gBAAA;gBAAC,MAAM,IAAE;oBAAC,OAAM;oBAAE,OAAM,CAAC;oBAAE,OAAM;oBAAE,KAAI,EAAE,GAAG,KAAG;oBAAK,UAAS;oBAAG,QAAO;oBAAG,QAAO;oBAAG,WAAU;oBAAM,SAAQ;oBAAM,UAAS;oBAAE,QAAO;oBAAE,QAAO;oBAAE,QAAO;oBAAE,UAAS;oBAAM,QAAO;gBAAC;gBAAE,IAAE,EAAE,YAAY,CAAC,GAAE;gBAAG,IAAE,EAAE,MAAM;gBAAC,MAAM,IAAE,EAAE;gBAAC,MAAM,IAAE,EAAE;gBAAC,MAAM,IAAE,EAAE;gBAAC,IAAI,IAAE;gBAAE,IAAI;gBAAE,MAAM,MAAI,IAAI,EAAE,KAAK,KAAG,IAAE;gBAAE,MAAM,IAAE,EAAE,IAAI,GAAC,CAAC,IAAE,CAAC,GAAG,CAAC,CAAC,EAAE,KAAK,GAAC,EAAE;gBAAC,MAAM,IAAE,EAAE,OAAO,GAAC,IAAI,CAAC,CAAC,EAAE,EAAE,KAAK,CAAC,IAAE;gBAAG,MAAM,YAAU,IAAI,EAAE,KAAK,CAAC,EAAE,KAAK,GAAC;gBAAG,MAAM,UAAQ,CAAC,IAAE,EAAE,EAAC,IAAE,CAAC;oBAAI,EAAE,QAAQ,IAAE;oBAAE,EAAE,KAAK,IAAE;gBAAC;gBAAE,MAAM,SAAO,CAAA;oBAAI,EAAE,MAAM,IAAE,EAAE,MAAM,IAAE,OAAK,EAAE,MAAM,GAAC,EAAE,KAAK;oBAAC,QAAQ,EAAE,KAAK;gBAAC;gBAAE,MAAM,SAAO;oBAAK,IAAI,IAAE;oBAAE,MAAM,QAAM,OAAK,CAAC,EAAE,OAAK,OAAK,EAAE,OAAK,GAAG,EAAE;wBAAC;wBAAI,EAAE,KAAK;wBAAG;oBAAG;oBAAC,IAAG,IAAE,MAAI,GAAE;wBAAC,OAAO;oBAAK;oBAAC,EAAE,OAAO,GAAC;oBAAK,EAAE,KAAK;oBAAG,OAAO;gBAAI;gBAAE,MAAM,YAAU,CAAA;oBAAI,CAAC,CAAC,EAAE;oBAAG,EAAE,IAAI,CAAC;gBAAE;gBAAE,MAAM,YAAU,CAAA;oBAAI,CAAC,CAAC,EAAE;oBAAG,EAAE,GAAG;gBAAE;gBAAE,MAAM,OAAK,CAAA;oBAAI,IAAG,EAAE,IAAI,KAAG,YAAW;wBAAC,MAAM,IAAE,EAAE,MAAM,GAAC,KAAG,CAAC,EAAE,IAAI,KAAG,WAAS,EAAE,IAAI,KAAG,OAAO;wBAAE,MAAM,IAAE,EAAE,OAAO,KAAG,QAAM,EAAE,MAAM,IAAE,CAAC,EAAE,IAAI,KAAG,UAAQ,EAAE,IAAI,KAAG,OAAO;wBAAE,IAAG,EAAE,IAAI,KAAG,WAAS,EAAE,IAAI,KAAG,WAAS,CAAC,KAAG,CAAC,GAAE;4BAAC,EAAE,MAAM,GAAC,EAAE,MAAM,CAAC,KAAK,CAAC,GAAE,CAAC,EAAE,MAAM,CAAC,MAAM;4BAAE,EAAE,IAAI,GAAC;4BAAO,EAAE,KAAK,GAAC;4BAAI,EAAE,MAAM,GAAC;4BAAE,EAAE,MAAM,IAAE,EAAE,MAAM;wBAAA;oBAAC;oBAAC,IAAG,EAAE,MAAM,IAAE,EAAE,IAAI,KAAG,SAAQ;wBAAC,CAAC,CAAC,EAAE,MAAM,GAAC,EAAE,CAAC,KAAK,IAAE,EAAE,KAAK;oBAAA;oBAAC,IAAG,EAAE,KAAK,IAAE,EAAE,MAAM,EAAC,OAAO;oBAAG,IAAG,KAAG,EAAE,IAAI,KAAG,UAAQ,EAAE,IAAI,KAAG,QAAO;wBAAC,EAAE,MAAM,GAAC,CAAC,EAAE,MAAM,IAAE,EAAE,KAAK,IAAE,EAAE,KAAK;wBAAC,EAAE,KAAK,IAAE,EAAE,KAAK;wBAAC;oBAAM;oBAAC,EAAE,IAAI,GAAC;oBAAE,EAAE,IAAI,CAAC;oBAAG,IAAE;gBAAC;gBAAE,MAAM,cAAY,CAAC,GAAE;oBAAK,MAAM,IAAE;wBAAC,GAAG,CAAC,CAAC,EAAE;wBAAC,YAAW;wBAAE,OAAM;oBAAE;oBAAE,EAAE,IAAI,GAAC;oBAAE,EAAE,MAAM,GAAC,EAAE,MAAM;oBAAC,EAAE,MAAM,GAAC,EAAE,MAAM;oBAAC,MAAM,IAAE,CAAC,EAAE,OAAO,GAAC,MAAI,EAAE,IAAE,EAAE,IAAI;oBAAC,UAAU;oBAAU,KAAK;wBAAC,MAAK;wBAAE,OAAM;wBAAE,QAAO,EAAE,MAAM,GAAC,KAAG;oBAAC;oBAAG,KAAK;wBAAC,MAAK;wBAAQ,SAAQ;wBAAK,OAAM;wBAAI,QAAO;oBAAC;oBAAG,EAAE,IAAI,CAAC;gBAAE;gBAAE,MAAM,eAAa,CAAA;oBAAI,IAAI,IAAE,EAAE,KAAK,GAAC,CAAC,EAAE,OAAO,GAAC,MAAI,EAAE;oBAAE,IAAI;oBAAE,IAAG,EAAE,IAAI,KAAG,UAAS;wBAAC,IAAI,IAAE;wBAAE,IAAG,EAAE,KAAK,IAAE,EAAE,KAAK,CAAC,MAAM,GAAC,KAAG,EAAE,KAAK,CAAC,QAAQ,CAAC,MAAK;4BAAC,IAAE,SAAS;wBAAE;wBAAC,IAAG,MAAI,KAAG,SAAO,QAAQ,IAAI,CAAC,cAAa;4BAAC,IAAE,EAAE,KAAK,GAAC,CAAC,IAAI,EAAE,GAAG;wBAAA;wBAAC,IAAG,EAAE,KAAK,CAAC,QAAQ,CAAC,QAAM,CAAC,IAAE,WAAW,KAAG,eAAe,IAAI,CAAC,IAAG;4BAAC,MAAM,IAAE,MAAM,GAAE;gCAAC,GAAG,CAAC;gCAAC,WAAU;4BAAK,GAAG,MAAM;4BAAC,IAAE,EAAE,KAAK,GAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC;wBAAA;wBAAC,IAAG,EAAE,IAAI,CAAC,IAAI,KAAG,OAAM;4BAAC,EAAE,cAAc,GAAC;wBAAI;oBAAC;oBAAC,KAAK;wBAAC,MAAK;wBAAQ,SAAQ;wBAAK,OAAM;wBAAE,QAAO;oBAAC;oBAAG,UAAU;gBAAS;gBAAE,IAAG,EAAE,SAAS,KAAG,SAAO,CAAC,sBAAsB,IAAI,CAAC,IAAG;oBAAC,IAAI,IAAE;oBAAM,IAAI,IAAE,EAAE,OAAO,CAAC,GAAG,CAAC,GAAE,GAAE,GAAE,GAAE,GAAE;wBAAK,IAAG,MAAI,MAAK;4BAAC,IAAE;4BAAK,OAAO;wBAAC;wBAAC,IAAG,MAAI,KAAI;4BAAC,IAAG,GAAE;gCAAC,OAAO,IAAE,IAAE,CAAC,IAAE,EAAE,MAAM,CAAC,EAAE,MAAM,IAAE,EAAE;4BAAC;4BAAC,IAAG,MAAI,GAAE;gCAAC,OAAO,IAAE,CAAC,IAAE,EAAE,MAAM,CAAC,EAAE,MAAM,IAAE,EAAE;4BAAC;4BAAC,OAAO,EAAE,MAAM,CAAC,EAAE,MAAM;wBAAC;wBAAC,IAAG,MAAI,KAAI;4BAAC,OAAO,EAAE,MAAM,CAAC,EAAE,MAAM;wBAAC;wBAAC,IAAG,MAAI,KAAI;4BAAC,IAAG,GAAE;gCAAC,OAAO,IAAE,IAAE,CAAC,IAAE,IAAE,EAAE;4BAAC;4BAAC,OAAO;wBAAC;wBAAC,OAAO,IAAE,IAAE,CAAC,EAAE,EAAE,GAAG;oBAAA;oBAAI,IAAG,MAAI,MAAK;wBAAC,IAAG,EAAE,QAAQ,KAAG,MAAK;4BAAC,IAAE,EAAE,OAAO,CAAC,OAAM;wBAAG,OAAK;4BAAC,IAAE,EAAE,OAAO,CAAC,QAAQ,CAAA,IAAG,EAAE,MAAM,GAAC,MAAI,IAAE,SAAO,IAAE,OAAK;wBAAI;oBAAC;oBAAC,IAAG,MAAI,KAAG,EAAE,QAAQ,KAAG,MAAK;wBAAC,EAAE,MAAM,GAAC;wBAAE,OAAO;oBAAC;oBAAC,EAAE,MAAM,GAAC,EAAE,UAAU,CAAC,GAAE,GAAE;oBAAG,OAAO;gBAAC;gBAAC,MAAM,CAAC,MAAM;oBAAC,IAAE;oBAAI,IAAG,MAAI,MAAK;wBAAC;oBAAQ;oBAAC,IAAG,MAAI,MAAK;wBAAC,MAAM,IAAE;wBAAI,IAAG,MAAI,OAAK,EAAE,IAAI,KAAG,MAAK;4BAAC;wBAAQ;wBAAC,IAAG,MAAI,OAAK,MAAI,KAAI;4BAAC;wBAAQ;wBAAC,IAAG,CAAC,GAAE;4BAAC,KAAG;4BAAK,KAAK;gCAAC,MAAK;gCAAO,OAAM;4BAAC;4BAAG;wBAAQ;wBAAC,MAAM,IAAE,OAAO,IAAI,CAAC;wBAAa,IAAI,IAAE;wBAAE,IAAG,KAAG,CAAC,CAAC,EAAE,CAAC,MAAM,GAAC,GAAE;4BAAC,IAAE,CAAC,CAAC,EAAE,CAAC,MAAM;4BAAC,EAAE,KAAK,IAAE;4BAAE,IAAG,IAAE,MAAI,GAAE;gCAAC,KAAG;4BAAI;wBAAC;wBAAC,IAAG,EAAE,QAAQ,KAAG,MAAK;4BAAC,IAAE;wBAAG,OAAK;4BAAC,KAAG;wBAAG;wBAAC,IAAG,EAAE,QAAQ,KAAG,GAAE;4BAAC,KAAK;gCAAC,MAAK;gCAAO,OAAM;4BAAC;4BAAG;wBAAQ;oBAAC;oBAAC,IAAG,EAAE,QAAQ,GAAC,KAAG,CAAC,MAAI,OAAK,EAAE,KAAK,KAAG,OAAK,EAAE,KAAK,KAAG,IAAI,GAAE;wBAAC,IAAG,EAAE,KAAK,KAAG,SAAO,MAAI,KAAI;4BAAC,MAAM,IAAE,EAAE,KAAK,CAAC,KAAK,CAAC;4BAAG,IAAG,EAAE,QAAQ,CAAC,MAAK;gCAAC,EAAE,KAAK,GAAC;gCAAK,IAAG,EAAE,QAAQ,CAAC,MAAK;oCAAC,MAAM,IAAE,EAAE,KAAK,CAAC,WAAW,CAAC;oCAAK,MAAM,IAAE,EAAE,KAAK,CAAC,KAAK,CAAC,GAAE;oCAAG,MAAM,IAAE,EAAE,KAAK,CAAC,KAAK,CAAC,IAAE;oCAAG,MAAM,IAAE,CAAC,CAAC,EAAE;oCAAC,IAAG,GAAE;wCAAC,EAAE,KAAK,GAAC,IAAE;wCAAE,EAAE,SAAS,GAAC;wCAAK;wCAAI,IAAG,CAAC,EAAE,MAAM,IAAE,EAAE,OAAO,CAAC,OAAK,GAAE;4CAAC,EAAE,MAAM,GAAC;wCAAC;wCAAC;oCAAQ;gCAAC;4BAAC;wBAAC;wBAAC,IAAG,MAAI,OAAK,QAAM,OAAK,MAAI,OAAK,QAAM,KAAI;4BAAC,IAAE,CAAC,EAAE,EAAE,GAAG;wBAAA;wBAAC,IAAG,MAAI,OAAK,CAAC,EAAE,KAAK,KAAG,OAAK,EAAE,KAAK,KAAG,IAAI,GAAE;4BAAC,IAAE,CAAC,EAAE,EAAE,GAAG;wBAAA;wBAAC,IAAG,EAAE,KAAK,KAAG,QAAM,MAAI,OAAK,EAAE,KAAK,KAAG,KAAI;4BAAC,IAAE;wBAAG;wBAAC,EAAE,KAAK,IAAE;wBAAE,OAAO;4BAAC,OAAM;wBAAC;wBAAG;oBAAQ;oBAAC,IAAG,EAAE,MAAM,KAAG,KAAG,MAAI,KAAI;wBAAC,IAAE,EAAE,WAAW,CAAC;wBAAG,EAAE,KAAK,IAAE;wBAAE,OAAO;4BAAC,OAAM;wBAAC;wBAAG;oBAAQ;oBAAC,IAAG,MAAI,KAAI;wBAAC,EAAE,MAAM,GAAC,EAAE,MAAM,KAAG,IAAE,IAAE;wBAAE,IAAG,EAAE,UAAU,KAAG,MAAK;4BAAC,KAAK;gCAAC,MAAK;gCAAO,OAAM;4BAAC;wBAAE;wBAAC;oBAAQ;oBAAC,IAAG,MAAI,KAAI;wBAAC,UAAU;wBAAU,KAAK;4BAAC,MAAK;4BAAQ,OAAM;wBAAC;wBAAG;oBAAQ;oBAAC,IAAG,MAAI,KAAI;wBAAC,IAAG,EAAE,MAAM,KAAG,KAAG,EAAE,cAAc,KAAG,MAAK;4BAAC,MAAM,IAAI,YAAY,YAAY,WAAU;wBAAK;wBAAC,MAAM,IAAE,CAAC,CAAC,EAAE,MAAM,GAAC,EAAE;wBAAC,IAAG,KAAG,EAAE,MAAM,KAAG,EAAE,MAAM,GAAC,GAAE;4BAAC,aAAa,EAAE,GAAG;4BAAI;wBAAQ;wBAAC,KAAK;4BAAC,MAAK;4BAAQ,OAAM;4BAAE,QAAO,EAAE,MAAM,GAAC,MAAI;wBAAK;wBAAG,UAAU;wBAAU;oBAAQ;oBAAC,IAAG,MAAI,KAAI;wBAAC,IAAG,EAAE,SAAS,KAAG,QAAM,CAAC,YAAY,QAAQ,CAAC,MAAK;4BAAC,IAAG,EAAE,SAAS,KAAG,QAAM,EAAE,cAAc,KAAG,MAAK;gCAAC,MAAM,IAAI,YAAY,YAAY,WAAU;4BAAK;4BAAC,IAAE,CAAC,EAAE,EAAE,GAAG;wBAAA,OAAK;4BAAC,UAAU;wBAAW;wBAAC,KAAK;4BAAC,MAAK;4BAAU,OAAM;wBAAC;wBAAG;oBAAQ;oBAAC,IAAG,MAAI,KAAI;wBAAC,IAAG,EAAE,SAAS,KAAG,QAAM,KAAG,EAAE,IAAI,KAAG,aAAW,EAAE,KAAK,CAAC,MAAM,KAAG,GAAE;4BAAC,KAAK;gCAAC,MAAK;gCAAO,OAAM;gCAAE,QAAO,CAAC,EAAE,EAAE,GAAG;4BAAA;4BAAG;wBAAQ;wBAAC,IAAG,EAAE,QAAQ,KAAG,GAAE;4BAAC,IAAG,EAAE,cAAc,KAAG,MAAK;gCAAC,MAAM,IAAI,YAAY,YAAY,WAAU;4BAAK;4BAAC,KAAK;gCAAC,MAAK;gCAAO,OAAM;gCAAE,QAAO,CAAC,EAAE,EAAE,GAAG;4BAAA;4BAAG;wBAAQ;wBAAC,UAAU;wBAAY,MAAM,IAAE,EAAE,KAAK,CAAC,KAAK,CAAC;wBAAG,IAAG,EAAE,KAAK,KAAG,QAAM,CAAC,CAAC,EAAE,KAAG,OAAK,CAAC,EAAE,QAAQ,CAAC,MAAK;4BAAC,IAAE,CAAC,CAAC,EAAE,GAAG;wBAAA;wBAAC,EAAE,KAAK,IAAE;wBAAE,OAAO;4BAAC,OAAM;wBAAC;wBAAG,IAAG,EAAE,eAAe,KAAG,SAAO,EAAE,aAAa,CAAC,IAAG;4BAAC;wBAAQ;wBAAC,MAAM,IAAE,EAAE,WAAW,CAAC,EAAE,KAAK;wBAAE,EAAE,MAAM,GAAC,EAAE,MAAM,CAAC,KAAK,CAAC,GAAE,CAAC,EAAE,KAAK,CAAC,MAAM;wBAAE,IAAG,EAAE,eAAe,KAAG,MAAK;4BAAC,EAAE,MAAM,IAAE;4BAAE,EAAE,KAAK,GAAC;4BAAE;wBAAQ;wBAAC,EAAE,KAAK,GAAC,CAAC,CAAC,EAAE,IAAI,EAAE,CAAC,EAAE,EAAE,KAAK,CAAC,CAAC,CAAC;wBAAC,EAAE,MAAM,IAAE,EAAE,KAAK;wBAAC;oBAAQ;oBAAC,IAAG,MAAI,OAAK,EAAE,OAAO,KAAG,MAAK;wBAAC,UAAU;wBAAU,MAAM,IAAE;4BAAC,MAAK;4BAAQ,OAAM;4BAAE,QAAO;4BAAI,aAAY,EAAE,MAAM,CAAC,MAAM;4BAAC,aAAY,EAAE,MAAM,CAAC,MAAM;wBAAA;wBAAE,EAAE,IAAI,CAAC;wBAAG,KAAK;wBAAG;oBAAQ;oBAAC,IAAG,MAAI,KAAI;wBAAC,MAAM,IAAE,CAAC,CAAC,EAAE,MAAM,GAAC,EAAE;wBAAC,IAAG,EAAE,OAAO,KAAG,QAAM,CAAC,GAAE;4BAAC,KAAK;gCAAC,MAAK;gCAAO,OAAM;gCAAE,QAAO;4BAAC;4BAAG;wBAAQ;wBAAC,IAAI,IAAE;wBAAI,IAAG,EAAE,IAAI,KAAG,MAAK;4BAAC,MAAM,IAAE,EAAE,KAAK;4BAAG,MAAM,IAAE,EAAE;4BAAC,IAAI,IAAI,IAAE,EAAE,MAAM,GAAC,GAAE,KAAG,GAAE,IAAI;gCAAC,EAAE,GAAG;gCAAG,IAAG,CAAC,CAAC,EAAE,CAAC,IAAI,KAAG,SAAQ;oCAAC;gCAAK;gCAAC,IAAG,CAAC,CAAC,EAAE,CAAC,IAAI,KAAG,QAAO;oCAAC,EAAE,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,KAAK;gCAAC;4BAAC;4BAAC,IAAE,YAAY,GAAE;4BAAG,EAAE,SAAS,GAAC;wBAAI;wBAAC,IAAG,EAAE,KAAK,KAAG,QAAM,EAAE,IAAI,KAAG,MAAK;4BAAC,MAAM,IAAE,EAAE,MAAM,CAAC,KAAK,CAAC,GAAE,EAAE,WAAW;4BAAE,MAAM,IAAE,EAAE,MAAM,CAAC,KAAK,CAAC,EAAE,WAAW;4BAAE,EAAE,KAAK,GAAC,EAAE,MAAM,GAAC;4BAAM,IAAE,IAAE;4BAAM,EAAE,MAAM,GAAC;4BAAE,KAAI,MAAM,KAAK,EAAE;gCAAC,EAAE,MAAM,IAAE,EAAE,MAAM,IAAE,EAAE,KAAK;4BAAA;wBAAC;wBAAC,KAAK;4BAAC,MAAK;4BAAQ,OAAM;4BAAE,QAAO;wBAAC;wBAAG,UAAU;wBAAU,EAAE,GAAG;wBAAG;oBAAQ;oBAAC,IAAG,MAAI,KAAI;wBAAC,IAAG,EAAE,MAAM,GAAC,GAAE;4BAAC,CAAC,CAAC,EAAE,MAAM,GAAC,EAAE,CAAC,UAAU;wBAAE;wBAAC,KAAK;4BAAC,MAAK;4BAAO,OAAM;wBAAC;wBAAG;oBAAQ;oBAAC,IAAG,MAAI,KAAI;wBAAC,IAAI,IAAE;wBAAE,MAAM,IAAE,CAAC,CAAC,EAAE,MAAM,GAAC,EAAE;wBAAC,IAAG,KAAG,CAAC,CAAC,EAAE,MAAM,GAAC,EAAE,KAAG,UAAS;4BAAC,EAAE,KAAK,GAAC;4BAAK,IAAE;wBAAG;wBAAC,KAAK;4BAAC,MAAK;4BAAQ,OAAM;4BAAE,QAAO;wBAAC;wBAAG;oBAAQ;oBAAC,IAAG,MAAI,KAAI;wBAAC,IAAG,EAAE,IAAI,KAAG,SAAO,EAAE,KAAK,KAAG,EAAE,KAAK,GAAC,GAAE;4BAAC,EAAE,KAAK,GAAC,EAAE,KAAK,GAAC;4BAAE,EAAE,QAAQ,GAAC;4BAAG,EAAE,MAAM,GAAC;4BAAG,EAAE,GAAG;4BAAG,IAAE;4BAAE;wBAAQ;wBAAC,KAAK;4BAAC,MAAK;4BAAQ,OAAM;4BAAE,QAAO;wBAAC;wBAAG;oBAAQ;oBAAC,IAAG,MAAI,KAAI;wBAAC,IAAG,EAAE,MAAM,GAAC,KAAG,EAAE,IAAI,KAAG,OAAM;4BAAC,IAAG,EAAE,KAAK,KAAG,KAAI,EAAE,MAAM,GAAC;4BAAE,MAAM,IAAE,CAAC,CAAC,EAAE,MAAM,GAAC,EAAE;4BAAC,EAAE,IAAI,GAAC;4BAAO,EAAE,MAAM,IAAE;4BAAE,EAAE,KAAK,IAAE;4BAAE,EAAE,IAAI,GAAC;4BAAK;wBAAQ;wBAAC,IAAG,EAAE,MAAM,GAAC,EAAE,MAAM,KAAG,KAAG,EAAE,IAAI,KAAG,SAAO,EAAE,IAAI,KAAG,SAAQ;4BAAC,KAAK;gCAAC,MAAK;gCAAO,OAAM;gCAAE,QAAO;4BAAC;4BAAG;wBAAQ;wBAAC,KAAK;4BAAC,MAAK;4BAAM,OAAM;4BAAE,QAAO;wBAAC;wBAAG;oBAAQ;oBAAC,IAAG,MAAI,KAAI;wBAAC,MAAM,IAAE,KAAG,EAAE,KAAK,KAAG;wBAAI,IAAG,CAAC,KAAG,EAAE,SAAS,KAAG,QAAM,QAAM,OAAK,EAAE,OAAK,KAAI;4BAAC,YAAY,SAAQ;4BAAG;wBAAQ;wBAAC,IAAG,KAAG,EAAE,IAAI,KAAG,SAAQ;4BAAC,MAAM,IAAE;4BAAI,IAAI,IAAE;4BAAE,IAAG,EAAE,KAAK,KAAG,OAAK,CAAC,SAAS,IAAI,CAAC,MAAI,MAAI,OAAK,CAAC,eAAe,IAAI,CAAC,cAAa;gCAAC,IAAE,CAAC,EAAE,EAAE,GAAG;4BAAA;4BAAC,KAAK;gCAAC,MAAK;gCAAO,OAAM;gCAAE,QAAO;4BAAC;4BAAG;wBAAQ;wBAAC,IAAG,EAAE,GAAG,KAAG,QAAM,CAAC,EAAE,IAAI,KAAG,WAAS,EAAE,IAAI,KAAG,KAAK,GAAE;4BAAC,KAAK;gCAAC,MAAK;gCAAQ,OAAM;gCAAE,QAAO;4BAAC;4BAAG;wBAAQ;wBAAC,KAAK;4BAAC,MAAK;4BAAQ,OAAM;4BAAE,QAAO;wBAAC;wBAAG;oBAAQ;oBAAC,IAAG,MAAI,KAAI;wBAAC,IAAG,EAAE,SAAS,KAAG,QAAM,QAAM,KAAI;4BAAC,IAAG,EAAE,OAAK,OAAK,CAAC,SAAS,IAAI,CAAC,EAAE,KAAI;gCAAC,YAAY,UAAS;gCAAG;4BAAQ;wBAAC;wBAAC,IAAG,EAAE,QAAQ,KAAG,QAAM,EAAE,KAAK,KAAG,GAAE;4BAAC;4BAAS;wBAAQ;oBAAC;oBAAC,IAAG,MAAI,KAAI;wBAAC,IAAG,EAAE,SAAS,KAAG,QAAM,QAAM,OAAK,EAAE,OAAK,KAAI;4BAAC,YAAY,QAAO;4BAAG;wBAAQ;wBAAC,IAAG,KAAG,EAAE,KAAK,KAAG,OAAK,EAAE,KAAK,KAAG,OAAM;4BAAC,KAAK;gCAAC,MAAK;gCAAO,OAAM;gCAAE,QAAO;4BAAC;4BAAG;wBAAQ;wBAAC,IAAG,KAAG,CAAC,EAAE,IAAI,KAAG,aAAW,EAAE,IAAI,KAAG,WAAS,EAAE,IAAI,KAAG,OAAO,KAAG,EAAE,MAAM,GAAC,GAAE;4BAAC,KAAK;gCAAC,MAAK;gCAAO,OAAM;4BAAC;4BAAG;wBAAQ;wBAAC,KAAK;4BAAC,MAAK;4BAAO,OAAM;wBAAC;wBAAG;oBAAQ;oBAAC,IAAG,MAAI,KAAI;wBAAC,IAAG,EAAE,SAAS,KAAG,QAAM,QAAM,OAAK,EAAE,OAAK,KAAI;4BAAC,KAAK;gCAAC,MAAK;gCAAK,SAAQ;gCAAK,OAAM;gCAAE,QAAO;4BAAE;4BAAG;wBAAQ;wBAAC,KAAK;4BAAC,MAAK;4BAAO,OAAM;wBAAC;wBAAG;oBAAQ;oBAAC,IAAG,MAAI,KAAI;wBAAC,IAAG,MAAI,OAAK,MAAI,KAAI;4BAAC,IAAE,CAAC,EAAE,EAAE,GAAG;wBAAA;wBAAC,MAAM,IAAE,EAAE,IAAI,CAAC;wBAAa,IAAG,GAAE;4BAAC,KAAG,CAAC,CAAC,EAAE;4BAAC,EAAE,KAAK,IAAE,CAAC,CAAC,EAAE,CAAC,MAAM;wBAAA;wBAAC,KAAK;4BAAC,MAAK;4BAAO,OAAM;wBAAC;wBAAG;oBAAQ;oBAAC,IAAG,KAAG,CAAC,EAAE,IAAI,KAAG,cAAY,EAAE,IAAI,KAAG,IAAI,GAAE;wBAAC,EAAE,IAAI,GAAC;wBAAO,EAAE,IAAI,GAAC;wBAAK,EAAE,KAAK,IAAE;wBAAE,EAAE,MAAM,GAAC;wBAAE,EAAE,SAAS,GAAC;wBAAK,EAAE,QAAQ,GAAC;wBAAK,QAAQ;wBAAG;oBAAQ;oBAAC,IAAI,IAAE;oBAAY,IAAG,EAAE,SAAS,KAAG,QAAM,UAAU,IAAI,CAAC,IAAG;wBAAC,YAAY,QAAO;wBAAG;oBAAQ;oBAAC,IAAG,EAAE,IAAI,KAAG,QAAO;wBAAC,IAAG,EAAE,UAAU,KAAG,MAAK;4BAAC,QAAQ;4BAAG;wBAAQ;wBAAC,MAAM,IAAE,EAAE,IAAI;wBAAC,MAAM,IAAE,EAAE,IAAI;wBAAC,MAAM,IAAE,EAAE,IAAI,KAAG,WAAS,EAAE,IAAI,KAAG;wBAAM,MAAM,IAAE,KAAG,CAAC,EAAE,IAAI,KAAG,UAAQ,EAAE,IAAI,KAAG,UAAU;wBAAE,IAAG,EAAE,IAAI,KAAG,QAAM,CAAC,CAAC,KAAG,CAAC,CAAC,EAAE,IAAE,CAAC,CAAC,EAAE,KAAG,GAAG,GAAE;4BAAC,KAAK;gCAAC,MAAK;gCAAO,OAAM;gCAAE,QAAO;4BAAE;4BAAG;wBAAQ;wBAAC,MAAM,IAAE,EAAE,MAAM,GAAC,KAAG,CAAC,EAAE,IAAI,KAAG,WAAS,EAAE,IAAI,KAAG,OAAO;wBAAE,MAAM,IAAE,EAAE,MAAM,IAAE,CAAC,EAAE,IAAI,KAAG,UAAQ,EAAE,IAAI,KAAG,OAAO;wBAAE,IAAG,CAAC,KAAG,EAAE,IAAI,KAAG,WAAS,CAAC,KAAG,CAAC,GAAE;4BAAC,KAAK;gCAAC,MAAK;gCAAO,OAAM;gCAAE,QAAO;4BAAE;4BAAG;wBAAQ;wBAAC,MAAM,EAAE,KAAK,CAAC,GAAE,OAAK,MAAM;4BAAC,MAAM,IAAE,CAAC,CAAC,EAAE,KAAK,GAAC,EAAE;4BAAC,IAAG,KAAG,MAAI,KAAI;gCAAC;4BAAK;4BAAC,IAAE,EAAE,KAAK,CAAC;4BAAG,QAAQ,OAAM;wBAAE;wBAAC,IAAG,EAAE,IAAI,KAAG,SAAO,OAAM;4BAAC,EAAE,IAAI,GAAC;4BAAW,EAAE,KAAK,IAAE;4BAAE,EAAE,MAAM,GAAC,SAAS;4BAAG,EAAE,MAAM,GAAC,EAAE,MAAM;4BAAC,EAAE,QAAQ,GAAC;4BAAK,QAAQ;4BAAG;wBAAQ;wBAAC,IAAG,EAAE,IAAI,KAAG,WAAS,EAAE,IAAI,CAAC,IAAI,KAAG,SAAO,CAAC,KAAG,OAAM;4BAAC,EAAE,MAAM,GAAC,EAAE,MAAM,CAAC,KAAK,CAAC,GAAE,CAAC,CAAC,EAAE,MAAM,GAAC,EAAE,MAAM,EAAE,MAAM;4BAAE,EAAE,MAAM,GAAC,CAAC,GAAG,EAAE,EAAE,MAAM,EAAE;4BAAC,EAAE,IAAI,GAAC;4BAAW,EAAE,MAAM,GAAC,SAAS,KAAG,CAAC,EAAE,aAAa,GAAC,MAAI,KAAK;4BAAE,EAAE,KAAK,IAAE;4BAAE,EAAE,QAAQ,GAAC;4BAAK,EAAE,MAAM,IAAE,EAAE,MAAM,GAAC,EAAE,MAAM;4BAAC,QAAQ;4BAAG;wBAAQ;wBAAC,IAAG,EAAE,IAAI,KAAG,WAAS,EAAE,IAAI,CAAC,IAAI,KAAG,SAAO,CAAC,CAAC,EAAE,KAAG,KAAI;4BAAC,MAAM,IAAE,CAAC,CAAC,EAAE,KAAG,KAAK,IAAE,OAAK;4BAAG,EAAE,MAAM,GAAC,EAAE,MAAM,CAAC,KAAK,CAAC,GAAE,CAAC,CAAC,EAAE,MAAM,GAAC,EAAE,MAAM,EAAE,MAAM;4BAAE,EAAE,MAAM,GAAC,CAAC,GAAG,EAAE,EAAE,MAAM,EAAE;4BAAC,EAAE,IAAI,GAAC;4BAAW,EAAE,MAAM,GAAC,GAAG,SAAS,KAAK,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC;4BAAC,EAAE,KAAK,IAAE;4BAAE,EAAE,MAAM,IAAE,EAAE,MAAM,GAAC,EAAE,MAAM;4BAAC,EAAE,QAAQ,GAAC;4BAAK,QAAQ,IAAE;4BAAK,KAAK;gCAAC,MAAK;gCAAQ,OAAM;gCAAI,QAAO;4BAAE;4BAAG;wBAAQ;wBAAC,IAAG,EAAE,IAAI,KAAG,SAAO,CAAC,CAAC,EAAE,KAAG,KAAI;4BAAC,EAAE,IAAI,GAAC;4BAAW,EAAE,KAAK,IAAE;4BAAE,EAAE,MAAM,GAAC,CAAC,KAAK,EAAE,EAAE,CAAC,EAAE,SAAS,KAAK,EAAE,CAAC,CAAC;4BAAC,EAAE,MAAM,GAAC,EAAE,MAAM;4BAAC,EAAE,QAAQ,GAAC;4BAAK,QAAQ,IAAE;4BAAK,KAAK;gCAAC,MAAK;gCAAQ,OAAM;gCAAI,QAAO;4BAAE;4BAAG;wBAAQ;wBAAC,EAAE,MAAM,GAAC,EAAE,MAAM,CAAC,KAAK,CAAC,GAAE,CAAC,EAAE,MAAM,CAAC,MAAM;wBAAE,EAAE,IAAI,GAAC;wBAAW,EAAE,MAAM,GAAC,SAAS;wBAAG,EAAE,KAAK,IAAE;wBAAE,EAAE,MAAM,IAAE,EAAE,MAAM;wBAAC,EAAE,QAAQ,GAAC;wBAAK,QAAQ;wBAAG;oBAAQ;oBAAC,MAAM,IAAE;wBAAC,MAAK;wBAAO,OAAM;wBAAE,QAAO;oBAAC;oBAAE,IAAG,EAAE,IAAI,KAAG,MAAK;wBAAC,EAAE,MAAM,GAAC;wBAAM,IAAG,EAAE,IAAI,KAAG,SAAO,EAAE,IAAI,KAAG,SAAQ;4BAAC,EAAE,MAAM,GAAC,IAAE,EAAE,MAAM;wBAAA;wBAAC,KAAK;wBAAG;oBAAQ;oBAAC,IAAG,KAAG,CAAC,EAAE,IAAI,KAAG,aAAW,EAAE,IAAI,KAAG,OAAO,KAAG,EAAE,KAAK,KAAG,MAAK;wBAAC,EAAE,MAAM,GAAC;wBAAE,KAAK;wBAAG;oBAAQ;oBAAC,IAAG,EAAE,KAAK,KAAG,EAAE,KAAK,IAAE,EAAE,IAAI,KAAG,WAAS,EAAE,IAAI,KAAG,OAAM;wBAAC,IAAG,EAAE,IAAI,KAAG,OAAM;4BAAC,EAAE,MAAM,IAAE;4BAAE,EAAE,MAAM,IAAE;wBAAC,OAAM,IAAG,EAAE,GAAG,KAAG,MAAK;4BAAC,EAAE,MAAM,IAAE;4BAAE,EAAE,MAAM,IAAE;wBAAC,OAAK;4BAAC,EAAE,MAAM,IAAE;4BAAE,EAAE,MAAM,IAAE;wBAAC;wBAAC,IAAG,QAAM,KAAI;4BAAC,EAAE,MAAM,IAAE;4BAAE,EAAE,MAAM,IAAE;wBAAC;oBAAC;oBAAC,KAAK;gBAAE;gBAAC,MAAM,EAAE,QAAQ,GAAC,EAAE;oBAAC,IAAG,EAAE,cAAc,KAAG,MAAK,MAAM,IAAI,YAAY,YAAY,WAAU;oBAAM,EAAE,MAAM,GAAC,EAAE,UAAU,CAAC,EAAE,MAAM,EAAC;oBAAK,UAAU;gBAAW;gBAAC,MAAM,EAAE,MAAM,GAAC,EAAE;oBAAC,IAAG,EAAE,cAAc,KAAG,MAAK,MAAM,IAAI,YAAY,YAAY,WAAU;oBAAM,EAAE,MAAM,GAAC,EAAE,UAAU,CAAC,EAAE,MAAM,EAAC;oBAAK,UAAU;gBAAS;gBAAC,MAAM,EAAE,MAAM,GAAC,EAAE;oBAAC,IAAG,EAAE,cAAc,KAAG,MAAK,MAAM,IAAI,YAAY,YAAY,WAAU;oBAAM,EAAE,MAAM,GAAC,EAAE,UAAU,CAAC,EAAE,MAAM,EAAC;oBAAK,UAAU;gBAAS;gBAAC,IAAG,EAAE,aAAa,KAAG,QAAM,CAAC,EAAE,IAAI,KAAG,UAAQ,EAAE,IAAI,KAAG,SAAS,GAAE;oBAAC,KAAK;wBAAC,MAAK;wBAAc,OAAM;wBAAG,QAAO,GAAG,EAAE,CAAC,CAAC;oBAAA;gBAAE;gBAAC,IAAG,EAAE,SAAS,KAAG,MAAK;oBAAC,EAAE,MAAM,GAAC;oBAAG,KAAI,MAAM,KAAK,EAAE,MAAM,CAAC;wBAAC,EAAE,MAAM,IAAE,EAAE,MAAM,IAAE,OAAK,EAAE,MAAM,GAAC,EAAE,KAAK;wBAAC,IAAG,EAAE,MAAM,EAAC;4BAAC,EAAE,MAAM,IAAE,EAAE,MAAM;wBAAA;oBAAC;gBAAC;gBAAC,OAAO;YAAC;YAAE,MAAM,SAAS,GAAC,CAAC,GAAE;gBAAK,MAAM,IAAE;oBAAC,GAAG,CAAC;gBAAA;gBAAE,MAAM,IAAE,OAAO,EAAE,SAAS,KAAG,WAAS,KAAK,GAAG,CAAC,GAAE,EAAE,SAAS,IAAE;gBAAE,MAAM,IAAE,EAAE,MAAM;gBAAC,IAAG,IAAE,GAAE;oBAAC,MAAM,IAAI,YAAY,CAAC,cAAc,EAAE,EAAE,kCAAkC,EAAE,GAAG;gBAAC;gBAAC,IAAE,CAAC,CAAC,EAAE,IAAE;gBAAE,MAAK,EAAC,aAAY,CAAC,EAAC,eAAc,CAAC,EAAC,UAAS,CAAC,EAAC,YAAW,CAAC,EAAC,QAAO,CAAC,EAAC,SAAQ,CAAC,EAAC,eAAc,CAAC,EAAC,MAAK,CAAC,EAAC,cAAa,CAAC,EAAC,GAAC,EAAE,SAAS,CAAC,EAAE,OAAO;gBAAE,MAAM,IAAE,EAAE,GAAG,GAAC,IAAE;gBAAE,MAAM,IAAE,EAAE,GAAG,GAAC,IAAE;gBAAE,MAAM,IAAE,EAAE,OAAO,GAAC,KAAG;gBAAK,MAAM,IAAE;oBAAC,SAAQ;oBAAM,QAAO;gBAAE;gBAAE,IAAI,IAAE,EAAE,IAAI,KAAG,OAAK,QAAM;gBAAE,IAAG,EAAE,OAAO,EAAC;oBAAC,IAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;gBAAA;gBAAC,MAAM,WAAS,CAAA;oBAAI,IAAG,EAAE,UAAU,KAAG,MAAK,OAAO;oBAAE,OAAM,CAAC,CAAC,EAAE,EAAE,MAAM,EAAE,IAAI,EAAE,GAAG,GAAC,IAAE,EAAE,MAAM,CAAC;gBAAA;gBAAE,MAAM,SAAO,CAAA;oBAAI,OAAO;wBAAG,KAAI;4BAAI,OAAM,GAAG,IAAI,IAAI,GAAG;wBAAC,KAAI;4BAAK,OAAM,GAAG,IAAI,IAAI,GAAG;wBAAC,KAAI;4BAAM,OAAM,GAAG,IAAI,IAAI,IAAI,IAAI,GAAG;wBAAC,KAAI;4BAAM,OAAM,GAAG,IAAI,IAAI,IAAI,IAAI,IAAI,GAAG;wBAAC,KAAI;4BAAK,OAAO,IAAE,SAAS;wBAAG,KAAI;4BAAO,OAAM,CAAC,GAAG,EAAE,IAAI,SAAS,KAAK,EAAE,EAAE,EAAE,IAAI,IAAI,GAAG;wBAAC,KAAI;4BAAS,OAAM,CAAC,GAAG,EAAE,IAAI,SAAS,KAAK,EAAE,EAAE,EAAE,IAAI,IAAI,IAAI,IAAI,GAAG;wBAAC,KAAI;4BAAQ,OAAM,CAAC,GAAG,EAAE,IAAI,SAAS,KAAK,EAAE,EAAE,EAAE,IAAI,IAAI,GAAG;wBAAC;4BAAQ;gCAAC,MAAM,IAAE,iBAAiB,IAAI,CAAC;gCAAG,IAAG,CAAC,GAAE;gCAAO,MAAM,IAAE,OAAO,CAAC,CAAC,EAAE;gCAAE,IAAG,CAAC,GAAE;gCAAO,OAAO,IAAE,IAAE,CAAC,CAAC,EAAE;4BAAA;oBAAC;gBAAC;gBAAE,MAAM,IAAE,EAAE,YAAY,CAAC,GAAE;gBAAG,IAAI,IAAE,OAAO;gBAAG,IAAG,KAAG,EAAE,aAAa,KAAG,MAAK;oBAAC,KAAG,GAAG,EAAE,CAAC,CAAC;gBAAA;gBAAC,OAAO;YAAC;YAAE,EAAE,OAAO,GAAC;QAAK;QAAE,KAAI,CAAC,GAAE,GAAE;YAAK,MAAM,IAAE,EAAE;YAAK,MAAM,IAAE,EAAE;YAAK,MAAM,IAAE,EAAE;YAAI,MAAM,IAAE,EAAE;YAAK,MAAM,WAAS,CAAA,IAAG,KAAG,OAAO,MAAI,YAAU,CAAC,MAAM,OAAO,CAAC;YAAG,MAAM,YAAU,CAAC,GAAE,GAAE,IAAE,KAAK;gBAAI,IAAG,MAAM,OAAO,CAAC,IAAG;oBAAC,MAAM,IAAE,EAAE,GAAG,CAAE,CAAA,IAAG,UAAU,GAAE,GAAE;oBAAK,MAAM,eAAa,CAAA;wBAAI,KAAI,MAAM,KAAK,EAAE;4BAAC,MAAM,IAAE,EAAE;4BAAG,IAAG,GAAE,OAAO;wBAAC;wBAAC,OAAO;oBAAK;oBAAE,OAAO;gBAAY;gBAAC,MAAM,IAAE,SAAS,MAAI,EAAE,MAAM,IAAE,EAAE,KAAK;gBAAC,IAAG,MAAI,MAAI,OAAO,MAAI,YAAU,CAAC,GAAE;oBAAC,MAAM,IAAI,UAAU;gBAA4C;gBAAC,MAAM,IAAE,KAAG,CAAC;gBAAE,MAAM,IAAE,EAAE,OAAO;gBAAC,MAAM,IAAE,IAAE,UAAU,SAAS,CAAC,GAAE,KAAG,UAAU,MAAM,CAAC,GAAE,GAAE,OAAM;gBAAM,MAAM,IAAE,EAAE,KAAK;gBAAC,OAAO,EAAE,KAAK;gBAAC,IAAI,YAAU,IAAI;gBAAM,IAAG,EAAE,MAAM,EAAC;oBAAC,MAAM,IAAE;wBAAC,GAAG,CAAC;wBAAC,QAAO;wBAAK,SAAQ;wBAAK,UAAS;oBAAI;oBAAE,YAAU,UAAU,EAAE,MAAM,EAAC,GAAE;gBAAE;gBAAC,MAAM,UAAQ,CAAC,GAAE,IAAE,KAAK;oBAAI,MAAK,EAAC,SAAQ,CAAC,EAAC,OAAM,CAAC,EAAC,QAAO,CAAC,EAAC,GAAC,UAAU,IAAI,CAAC,GAAE,GAAE,GAAE;wBAAC,MAAK;wBAAE,OAAM;oBAAC;oBAAG,MAAM,IAAE;wBAAC,MAAK;wBAAE,OAAM;wBAAE,OAAM;wBAAE,OAAM;wBAAE,OAAM;wBAAE,QAAO;wBAAE,OAAM;wBAAE,SAAQ;oBAAC;oBAAE,IAAG,OAAO,EAAE,QAAQ,KAAG,YAAW;wBAAC,EAAE,QAAQ,CAAC;oBAAE;oBAAC,IAAG,MAAI,OAAM;wBAAC,EAAE,OAAO,GAAC;wBAAM,OAAO,IAAE,IAAE;oBAAK;oBAAC,IAAG,UAAU,IAAG;wBAAC,IAAG,OAAO,EAAE,QAAQ,KAAG,YAAW;4BAAC,EAAE,QAAQ,CAAC;wBAAE;wBAAC,EAAE,OAAO,GAAC;wBAAM,OAAO,IAAE,IAAE;oBAAK;oBAAC,IAAG,OAAO,EAAE,OAAO,KAAG,YAAW;wBAAC,EAAE,OAAO,CAAC;oBAAE;oBAAC,OAAO,IAAE,IAAE;gBAAI;gBAAE,IAAG,GAAE;oBAAC,QAAQ,KAAK,GAAC;gBAAC;gBAAC,OAAO;YAAO;YAAE,UAAU,IAAI,GAAC,CAAC,GAAE,GAAE,GAAE,EAAC,MAAK,CAAC,EAAC,OAAM,CAAC,EAAC,GAAC,CAAC,CAAC;gBAAI,IAAG,OAAO,MAAI,UAAS;oBAAC,MAAM,IAAI,UAAU;gBAAgC;gBAAC,IAAG,MAAI,IAAG;oBAAC,OAAM;wBAAC,SAAQ;wBAAM,QAAO;oBAAE;gBAAC;gBAAC,MAAM,IAAE,KAAG,CAAC;gBAAE,MAAM,IAAE,EAAE,MAAM,IAAE,CAAC,IAAE,EAAE,cAAc,GAAC,IAAI;gBAAE,IAAI,IAAE,MAAI;gBAAE,IAAI,IAAE,KAAG,IAAE,EAAE,KAAG;gBAAE,IAAG,MAAI,OAAM;oBAAC,IAAE,IAAE,EAAE,KAAG;oBAAE,IAAE,MAAI;gBAAC;gBAAC,IAAG,MAAI,SAAO,EAAE,OAAO,KAAG,MAAK;oBAAC,IAAG,EAAE,SAAS,KAAG,QAAM,EAAE,QAAQ,KAAG,MAAK;wBAAC,IAAE,UAAU,SAAS,CAAC,GAAE,GAAE,GAAE;oBAAE,OAAK;wBAAC,IAAE,EAAE,IAAI,CAAC;oBAAE;gBAAC;gBAAC,OAAM;oBAAC,SAAQ,QAAQ;oBAAG,OAAM;oBAAE,QAAO;gBAAC;YAAC;YAAE,UAAU,SAAS,GAAC,CAAC,GAAE,GAAE;gBAAK,MAAM,IAAE,aAAa,SAAO,IAAE,UAAU,MAAM,CAAC,GAAE;gBAAG,OAAO,EAAE,IAAI,CAAC,EAAE,QAAQ,CAAC;YAAG;YAAE,UAAU,OAAO,GAAC,CAAC,GAAE,GAAE,IAAI,UAAU,GAAE,GAAG;YAAG,UAAU,KAAK,GAAC,CAAC,GAAE;gBAAK,IAAG,MAAM,OAAO,CAAC,IAAG,OAAO,EAAE,GAAG,CAAE,CAAA,IAAG,UAAU,KAAK,CAAC,GAAE;gBAAK,OAAO,EAAE,GAAE;oBAAC,GAAG,CAAC;oBAAC,WAAU;gBAAK;YAAE;YAAE,UAAU,IAAI,GAAC,CAAC,GAAE,IAAI,EAAE,GAAE;YAAG,UAAU,SAAS,GAAC,CAAC,GAAE,GAAE,IAAE,KAAK,EAAC,IAAE,KAAK;gBAAI,IAAG,MAAI,MAAK;oBAAC,OAAO,EAAE,MAAM;gBAAA;gBAAC,MAAM,IAAE,KAAG,CAAC;gBAAE,MAAM,IAAE,EAAE,QAAQ,GAAC,KAAG;gBAAI,MAAM,IAAE,EAAE,QAAQ,GAAC,KAAG;gBAAI,IAAI,IAAE,GAAG,EAAE,GAAG,EAAE,EAAE,MAAM,CAAC,CAAC,EAAE,GAAG;gBAAC,IAAG,KAAG,EAAE,OAAO,KAAG,MAAK;oBAAC,IAAE,CAAC,IAAI,EAAE,EAAE,IAAI,CAAC;gBAAA;gBAAC,MAAM,IAAE,UAAU,OAAO,CAAC,GAAE;gBAAG,IAAG,MAAI,MAAK;oBAAC,EAAE,KAAK,GAAC;gBAAC;gBAAC,OAAO;YAAC;YAAE,UAAU,MAAM,GAAC,CAAC,GAAE,IAAE,CAAC,CAAC,EAAC,IAAE,KAAK,EAAC,IAAE,KAAK;gBAAI,IAAG,CAAC,KAAG,OAAO,MAAI,UAAS;oBAAC,MAAM,IAAI,UAAU;gBAA8B;gBAAC,IAAI,IAAE;oBAAC,SAAQ;oBAAM,WAAU;gBAAI;gBAAE,IAAG,EAAE,SAAS,KAAG,SAAO,CAAC,CAAC,CAAC,EAAE,KAAG,OAAK,CAAC,CAAC,EAAE,KAAG,GAAG,GAAE;oBAAC,EAAE,MAAM,GAAC,EAAE,SAAS,CAAC,GAAE;gBAAE;gBAAC,IAAG,CAAC,EAAE,MAAM,EAAC;oBAAC,IAAE,EAAE,GAAE;gBAAE;gBAAC,OAAO,UAAU,SAAS,CAAC,GAAE,GAAE,GAAE;YAAE;YAAE,UAAU,OAAO,GAAC,CAAC,GAAE;gBAAK,IAAG;oBAAC,MAAM,IAAE,KAAG,CAAC;oBAAE,OAAO,IAAI,OAAO,GAAE,EAAE,KAAK,IAAE,CAAC,EAAE,MAAM,GAAC,MAAI,EAAE;gBAAE,EAAC,OAAM,GAAE;oBAAC,IAAG,KAAG,EAAE,KAAK,KAAG,MAAK,MAAM;oBAAE,OAAM;gBAAI;YAAC;YAAE,UAAU,SAAS,GAAC;YAAE,EAAE,OAAO,GAAC;QAAS;QAAE,KAAI,CAAC,GAAE,GAAE;YAAK,MAAM,IAAE,EAAE;YAAI,MAAK,EAAC,eAAc,CAAC,EAAC,SAAQ,CAAC,EAAC,qBAAoB,CAAC,EAAC,YAAW,CAAC,EAAC,UAAS,CAAC,EAAC,uBAAsB,CAAC,EAAC,oBAAmB,CAAC,EAAC,uBAAsB,CAAC,EAAC,uBAAsB,CAAC,EAAC,0BAAyB,CAAC,EAAC,WAAU,CAAC,EAAC,oBAAmB,CAAC,EAAC,wBAAuB,CAAC,EAAC,wBAAuB,CAAC,EAAC,2BAA0B,CAAC,EAAC,GAAC,EAAE;YAAK,MAAM,kBAAgB,CAAA,IAAG,MAAI,KAAG,MAAI;YAAE,MAAM,QAAM,CAAA;gBAAI,IAAG,EAAE,QAAQ,KAAG,MAAK;oBAAC,EAAE,KAAK,GAAC,EAAE,UAAU,GAAC,WAAS;gBAAC;YAAC;YAAE,MAAM,OAAK,CAAC,GAAE;gBAAK,MAAM,IAAE,KAAG,CAAC;gBAAE,MAAM,IAAE,EAAE,MAAM,GAAC;gBAAE,MAAM,IAAE,EAAE,KAAK,KAAG,QAAM,EAAE,SAAS,KAAG;gBAAK,MAAM,IAAE,EAAE;gBAAC,MAAM,IAAE,EAAE;gBAAC,MAAM,IAAE,EAAE;gBAAC,IAAI,IAAE;gBAAE,IAAI,IAAE,CAAC;gBAAE,IAAI,IAAE;gBAAE,IAAI,IAAE;gBAAE,IAAI,IAAE;gBAAM,IAAI,IAAE;gBAAM,IAAI,IAAE;gBAAM,IAAI,IAAE;gBAAM,IAAI,IAAE;gBAAM,IAAI,IAAE;gBAAM,IAAI,IAAE;gBAAM,IAAI,IAAE;gBAAM,IAAI,IAAE;gBAAM,IAAI,IAAE;gBAAM,IAAI,IAAE;gBAAE,IAAI;gBAAE,IAAI;gBAAE,IAAI,IAAE;oBAAC,OAAM;oBAAG,OAAM;oBAAE,QAAO;gBAAK;gBAAE,MAAM,MAAI,IAAI,KAAG;gBAAE,MAAM,OAAK,IAAI,EAAE,UAAU,CAAC,IAAE;gBAAG,MAAM,UAAQ;oBAAK,IAAE;oBAAE,OAAO,EAAE,UAAU,CAAC,EAAE;gBAAE;gBAAE,MAAM,IAAE,EAAE;oBAAC,IAAE;oBAAU,IAAI;oBAAE,IAAG,MAAI,GAAE;wBAAC,IAAE,EAAE,WAAW,GAAC;wBAAK,IAAE;wBAAU,IAAG,MAAI,GAAE;4BAAC,IAAE;wBAAI;wBAAC;oBAAQ;oBAAC,IAAG,MAAI,QAAM,MAAI,GAAE;wBAAC;wBAAI,MAAM,UAAQ,QAAM,CAAC,IAAE,SAAS,EAAE;4BAAC,IAAG,MAAI,GAAE;gCAAC,IAAE,EAAE,WAAW,GAAC;gCAAK;gCAAU;4BAAQ;4BAAC,IAAG,MAAI,GAAE;gCAAC;gCAAI;4BAAQ;4BAAC,IAAG,MAAI,QAAM,MAAI,KAAG,CAAC,IAAE,SAAS,MAAI,GAAE;gCAAC,IAAE,EAAE,OAAO,GAAC;gCAAK,IAAE,EAAE,MAAM,GAAC;gCAAK,IAAE;gCAAK,IAAG,MAAI,MAAK;oCAAC;gCAAQ;gCAAC;4BAAK;4BAAC,IAAG,MAAI,QAAM,MAAI,GAAE;gCAAC,IAAE,EAAE,OAAO,GAAC;gCAAK,IAAE,EAAE,MAAM,GAAC;gCAAK,IAAE;gCAAK,IAAG,MAAI,MAAK;oCAAC;gCAAQ;gCAAC;4BAAK;4BAAC,IAAG,MAAI,GAAE;gCAAC;gCAAI,IAAG,MAAI,GAAE;oCAAC,IAAE;oCAAM,IAAE,EAAE,OAAO,GAAC;oCAAK,IAAE;oCAAK;gCAAK;4BAAC;wBAAC;wBAAC,IAAG,MAAI,MAAK;4BAAC;wBAAQ;wBAAC;oBAAK;oBAAC,IAAG,MAAI,GAAE;wBAAC,EAAE,IAAI,CAAC;wBAAG,EAAE,IAAI,CAAC;wBAAG,IAAE;4BAAC,OAAM;4BAAG,OAAM;4BAAE,QAAO;wBAAK;wBAAE,IAAG,MAAI,MAAK;wBAAS,IAAG,MAAI,KAAG,MAAI,IAAE,GAAE;4BAAC,KAAG;4BAAE;wBAAQ;wBAAC,IAAE,IAAE;wBAAE;oBAAQ;oBAAC,IAAG,EAAE,KAAK,KAAG,MAAK;wBAAC,MAAM,IAAE,MAAI,KAAG,MAAI,KAAG,MAAI,KAAG,MAAI,KAAG,MAAI;wBAAE,IAAG,MAAI,QAAM,WAAS,GAAE;4BAAC,IAAE,EAAE,MAAM,GAAC;4BAAK,IAAE,EAAE,SAAS,GAAC;4BAAK,IAAE;4BAAK,IAAG,MAAI,KAAG,MAAI,GAAE;gCAAC,IAAE;4BAAI;4BAAC,IAAG,MAAI,MAAK;gCAAC,MAAM,UAAQ,QAAM,CAAC,IAAE,SAAS,EAAE;oCAAC,IAAG,MAAI,GAAE;wCAAC,IAAE,EAAE,WAAW,GAAC;wCAAK,IAAE;wCAAU;oCAAQ;oCAAC,IAAG,MAAI,GAAE;wCAAC,IAAE,EAAE,MAAM,GAAC;wCAAK,IAAE;wCAAK;oCAAK;gCAAC;gCAAC;4BAAQ;4BAAC;wBAAK;oBAAC;oBAAC,IAAG,MAAI,GAAE;wBAAC,IAAG,MAAI,GAAE,IAAE,EAAE,UAAU,GAAC;wBAAK,IAAE,EAAE,MAAM,GAAC;wBAAK,IAAE;wBAAK,IAAG,MAAI,MAAK;4BAAC;wBAAQ;wBAAC;oBAAK;oBAAC,IAAG,MAAI,GAAE;wBAAC,IAAE,EAAE,MAAM,GAAC;wBAAK,IAAE;wBAAK,IAAG,MAAI,MAAK;4BAAC;wBAAQ;wBAAC;oBAAK;oBAAC,IAAG,MAAI,GAAE;wBAAC,MAAM,UAAQ,QAAM,CAAC,IAAE,SAAS,EAAE;4BAAC,IAAG,MAAI,GAAE;gCAAC,IAAE,EAAE,WAAW,GAAC;gCAAK;gCAAU;4BAAQ;4BAAC,IAAG,MAAI,GAAE;gCAAC,IAAE,EAAE,SAAS,GAAC;gCAAK,IAAE,EAAE,MAAM,GAAC;gCAAK,IAAE;gCAAK;4BAAK;wBAAC;wBAAC,IAAG,MAAI,MAAK;4BAAC;wBAAQ;wBAAC;oBAAK;oBAAC,IAAG,EAAE,QAAQ,KAAG,QAAM,MAAI,KAAG,MAAI,GAAE;wBAAC,IAAE,EAAE,OAAO,GAAC;wBAAK;wBAAI;oBAAQ;oBAAC,IAAG,EAAE,OAAO,KAAG,QAAM,MAAI,GAAE;wBAAC,IAAE,EAAE,MAAM,GAAC;wBAAK,IAAG,MAAI,MAAK;4BAAC,MAAM,UAAQ,QAAM,CAAC,IAAE,SAAS,EAAE;gCAAC,IAAG,MAAI,GAAE;oCAAC,IAAE,EAAE,WAAW,GAAC;oCAAK,IAAE;oCAAU;gCAAQ;gCAAC,IAAG,MAAI,GAAE;oCAAC,IAAE;oCAAK;gCAAK;4BAAC;4BAAC;wBAAQ;wBAAC;oBAAK;oBAAC,IAAG,MAAI,MAAK;wBAAC,IAAE;wBAAK,IAAG,MAAI,MAAK;4BAAC;wBAAQ;wBAAC;oBAAK;gBAAC;gBAAC,IAAG,EAAE,KAAK,KAAG,MAAK;oBAAC,IAAE;oBAAM,IAAE;gBAAK;gBAAC,IAAI,IAAE;gBAAE,IAAI,IAAE;gBAAG,IAAI,IAAE;gBAAG,IAAG,IAAE,GAAE;oBAAC,IAAE,EAAE,KAAK,CAAC,GAAE;oBAAG,IAAE,EAAE,KAAK,CAAC;oBAAG,KAAG;gBAAC;gBAAC,IAAG,KAAG,MAAI,QAAM,IAAE,GAAE;oBAAC,IAAE,EAAE,KAAK,CAAC,GAAE;oBAAG,IAAE,EAAE,KAAK,CAAC;gBAAE,OAAM,IAAG,MAAI,MAAK;oBAAC,IAAE;oBAAG,IAAE;gBAAC,OAAK;oBAAC,IAAE;gBAAC;gBAAC,IAAG,KAAG,MAAI,MAAI,MAAI,OAAK,MAAI,GAAE;oBAAC,IAAG,gBAAgB,EAAE,UAAU,CAAC,EAAE,MAAM,GAAC,KAAI;wBAAC,IAAE,EAAE,KAAK,CAAC,GAAE,CAAC;oBAAE;gBAAC;gBAAC,IAAG,EAAE,QAAQ,KAAG,MAAK;oBAAC,IAAG,GAAE,IAAE,EAAE,iBAAiB,CAAC;oBAAG,IAAG,KAAG,MAAI,MAAK;wBAAC,IAAE,EAAE,iBAAiB,CAAC;oBAAE;gBAAC;gBAAC,MAAM,IAAE;oBAAC,QAAO;oBAAE,OAAM;oBAAE,OAAM;oBAAE,MAAK;oBAAE,MAAK;oBAAE,SAAQ;oBAAE,WAAU;oBAAE,QAAO;oBAAE,WAAU;oBAAE,YAAW;oBAAE,SAAQ;oBAAE,gBAAe;gBAAC;gBAAE,IAAG,EAAE,MAAM,KAAG,MAAK;oBAAC,EAAE,QAAQ,GAAC;oBAAE,IAAG,CAAC,gBAAgB,IAAG;wBAAC,EAAE,IAAI,CAAC;oBAAE;oBAAC,EAAE,MAAM,GAAC;gBAAC;gBAAC,IAAG,EAAE,KAAK,KAAG,QAAM,EAAE,MAAM,KAAG,MAAK;oBAAC,IAAI;oBAAE,IAAI,IAAI,IAAE,GAAE,IAAE,EAAE,MAAM,EAAC,IAAI;wBAAC,MAAM,IAAE,IAAE,IAAE,IAAE;wBAAE,MAAM,IAAE,CAAC,CAAC,EAAE;wBAAC,MAAM,IAAE,EAAE,KAAK,CAAC,GAAE;wBAAG,IAAG,EAAE,MAAM,EAAC;4BAAC,IAAG,MAAI,KAAG,MAAI,GAAE;gCAAC,CAAC,CAAC,EAAE,CAAC,QAAQ,GAAC;gCAAK,CAAC,CAAC,EAAE,CAAC,KAAK,GAAC;4BAAC,OAAK;gCAAC,CAAC,CAAC,EAAE,CAAC,KAAK,GAAC;4BAAC;4BAAC,MAAM,CAAC,CAAC,EAAE;4BAAE,EAAE,QAAQ,IAAE,CAAC,CAAC,EAAE,CAAC,KAAK;wBAAA;wBAAC,IAAG,MAAI,KAAG,MAAI,IAAG;4BAAC,EAAE,IAAI,CAAC;wBAAE;wBAAC,IAAE;oBAAC;oBAAC,IAAG,KAAG,IAAE,IAAE,EAAE,MAAM,EAAC;wBAAC,MAAM,IAAE,EAAE,KAAK,CAAC,IAAE;wBAAG,EAAE,IAAI,CAAC;wBAAG,IAAG,EAAE,MAAM,EAAC;4BAAC,CAAC,CAAC,EAAE,MAAM,GAAC,EAAE,CAAC,KAAK,GAAC;4BAAE,MAAM,CAAC,CAAC,EAAE,MAAM,GAAC,EAAE;4BAAE,EAAE,QAAQ,IAAE,CAAC,CAAC,EAAE,MAAM,GAAC,EAAE,CAAC,KAAK;wBAAA;oBAAC;oBAAC,EAAE,OAAO,GAAC;oBAAE,EAAE,KAAK,GAAC;gBAAC;gBAAC,OAAO;YAAC;YAAE,EAAE,OAAO,GAAC;QAAI;QAAE,IAAG,CAAC,GAAE,GAAE;YAAK,MAAK,EAAC,iBAAgB,CAAC,EAAC,wBAAuB,CAAC,EAAC,qBAAoB,CAAC,EAAC,4BAA2B,CAAC,EAAC,GAAC,EAAE;YAAK,EAAE,QAAQ,GAAC,CAAA,IAAG,MAAI,QAAM,OAAO,MAAI,YAAU,CAAC,MAAM,OAAO,CAAC;YAAG,EAAE,aAAa,GAAC,CAAA,IAAG,EAAE,IAAI,CAAC;YAAG,EAAE,WAAW,GAAC,CAAA,IAAG,EAAE,MAAM,KAAG,KAAG,EAAE,aAAa,CAAC;YAAG,EAAE,WAAW,GAAC,CAAA,IAAG,EAAE,OAAO,CAAC,GAAE;YAAQ,EAAE,cAAc,GAAC,CAAA,IAAG,EAAE,OAAO,CAAC,GAAE;YAAK,EAAE,iBAAiB,GAAC,CAAA,IAAG,EAAE,OAAO,CAAC,GAAG,CAAA,IAAG,MAAI,OAAK,KAAG;YAAI,EAAE,UAAU,GAAC,CAAC,GAAE,GAAE;gBAAK,MAAM,IAAE,EAAE,WAAW,CAAC,GAAE;gBAAG,IAAG,MAAI,CAAC,GAAE,OAAO;gBAAE,IAAG,CAAC,CAAC,IAAE,EAAE,KAAG,MAAK,OAAO,EAAE,UAAU,CAAC,GAAE,GAAE,IAAE;gBAAG,OAAM,GAAG,EAAE,KAAK,CAAC,GAAE,GAAG,EAAE,EAAE,EAAE,KAAK,CAAC,IAAI;YAAA;YAAE,EAAE,YAAY,GAAC,CAAC,GAAE,IAAE,CAAC,CAAC;gBAAI,IAAI,IAAE;gBAAE,IAAG,EAAE,UAAU,CAAC,OAAM;oBAAC,IAAE,EAAE,KAAK,CAAC;oBAAG,EAAE,MAAM,GAAC;gBAAI;gBAAC,OAAO;YAAC;YAAE,EAAE,UAAU,GAAC,CAAC,GAAE,IAAE,CAAC,CAAC,EAAC,IAAE,CAAC,CAAC;gBAAI,MAAM,IAAE,EAAE,QAAQ,GAAC,KAAG;gBAAI,MAAM,IAAE,EAAE,QAAQ,GAAC,KAAG;gBAAI,IAAI,IAAE,GAAG,EAAE,GAAG,EAAE,EAAE,CAAC,EAAE,GAAG;gBAAC,IAAG,EAAE,OAAO,KAAG,MAAK;oBAAC,IAAE,CAAC,OAAO,EAAE,EAAE,KAAK,CAAC;gBAAA;gBAAC,OAAO;YAAC;YAAE,EAAE,QAAQ,GAAC,CAAC,GAAE,EAAC,SAAQ,CAAC,EAAC,GAAC,CAAC,CAAC;gBAAI,MAAM,IAAE,EAAE,KAAK,CAAC,IAAE,UAAQ;gBAAK,MAAM,IAAE,CAAC,CAAC,EAAE,MAAM,GAAC,EAAE;gBAAC,IAAG,MAAI,IAAG;oBAAC,OAAO,CAAC,CAAC,EAAE,MAAM,GAAC,EAAE;gBAAA;gBAAC,OAAO;YAAC;QAAC;IAAC;IAAE,IAAI,IAAE,CAAC;IAAE,SAAS,oBAAoB,CAAC;QAAE,IAAI,IAAE,CAAC,CAAC,EAAE;QAAC,IAAG,MAAI,WAAU;YAAC,OAAO,EAAE,OAAO;QAAA;QAAC,IAAI,IAAE,CAAC,CAAC,EAAE,GAAC;YAAC,SAAQ,CAAC;QAAC;QAAE,IAAI,IAAE;QAAK,IAAG;YAAC,CAAC,CAAC,EAAE,CAAC,GAAE,EAAE,OAAO,EAAC;YAAqB,IAAE;QAAK,SAAQ;YAAC,IAAG,GAAE,OAAO,CAAC,CAAC,EAAE;QAAA;QAAC,OAAO,EAAE,OAAO;IAAA;IAAC,IAAG,OAAO,wBAAsB,aAAY,oBAAoB,EAAE,GAAC,YAAU;IAAI,IAAI,IAAE,oBAAoB;IAAK,OAAO,OAAO,GAAC;AAAC,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2637, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/AI_Projects/CRMcriadores/crm-interno/node_modules/next/src/shared/lib/match-local-pattern.ts"], "sourcesContent": ["import type { LocalPattern } from './image-config'\nimport { makeRe } from 'next/dist/compiled/picomatch'\n\n// Modifying this function should also modify writeImagesManifest()\nexport function matchLocalPattern(pattern: LocalPattern, url: URL): boolean {\n  if (pattern.search !== undefined) {\n    if (pattern.search !== url.search) {\n      return false\n    }\n  }\n\n  if (!makeRe(pattern.pathname ?? '**', { dot: true }).test(url.pathname)) {\n    return false\n  }\n\n  return true\n}\n\nexport function hasLocalMatch(\n  localPatterns: LocalPattern[] | undefined,\n  urlPathAndQuery: string\n): boolean {\n  if (!localPatterns) {\n    // if the user didn't define \"localPatterns\", we allow all local images\n    return true\n  }\n  const url = new URL(urlPathAndQuery, 'http://n')\n  return localPatterns.some((p) => matchLocalPattern(p, url))\n}\n"], "names": ["hasLocalMatch", "matchLocalPattern", "pattern", "url", "search", "undefined", "makeRe", "pathname", "dot", "test", "localPatterns", "urlPathAndQuery", "URL", "some", "p"], "mappings": ";;;;;;;;;;;;;;;IAkBgBA,aAAa,EAAA;eAAbA;;IAdAC,iBAAiB,EAAA;eAAjBA;;;2BAHO;AAGhB,SAASA,kBAAkBC,OAAqB,EAAEC,GAAQ;IAC/D,IAAID,QAAQE,MAAM,KAAKC,WAAW;QAChC,IAAIH,QAAQE,MAAM,KAAKD,IAAIC,MAAM,EAAE;YACjC,OAAO;QACT;IACF;QAEYF;IAAZ,IAAI,CAACI,CAAAA,GAAAA,WAAAA,MAAM,EAACJ,CAAAA,oBAAAA,QAAQK,QAAQ,KAAA,OAAhBL,oBAAoB,MAAM;QAAEM,KAAK;IAAK,GAAGC,IAAI,CAACN,IAAII,QAAQ,GAAG;QACvE,OAAO;IACT;IAEA,OAAO;AACT;AAEO,SAASP,cACdU,aAAyC,EACzCC,eAAuB;IAEvB,IAAI,CAACD,eAAe;QAClB,uEAAuE;QACvE,OAAO;IACT;IACA,MAAMP,MAAM,IAAIS,IAAID,iBAAiB;IACrC,OAAOD,cAAcG,IAAI,CAAC,CAACC,IAAMb,kBAAkBa,GAAGX;AACxD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2687, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/AI_Projects/CRMcriadores/crm-interno/node_modules/next/src/shared/lib/match-remote-pattern.ts"], "sourcesContent": ["import type { RemotePattern } from './image-config'\nimport { makeRe } from 'next/dist/compiled/picomatch'\n\n// Modifying this function should also modify writeImagesManifest()\nexport function matchRemotePattern(\n  pattern: RemotePattern | URL,\n  url: URL\n): boolean {\n  if (pattern.protocol !== undefined) {\n    if (pattern.protocol.replace(/:$/, '') !== url.protocol.replace(/:$/, '')) {\n      return false\n    }\n  }\n  if (pattern.port !== undefined) {\n    if (pattern.port !== url.port) {\n      return false\n    }\n  }\n\n  if (pattern.hostname === undefined) {\n    throw new Error(\n      `Pattern should define hostname but found\\n${JSON.stringify(pattern)}`\n    )\n  } else {\n    if (!makeRe(pattern.hostname).test(url.hostname)) {\n      return false\n    }\n  }\n\n  if (pattern.search !== undefined) {\n    if (pattern.search !== url.search) {\n      return false\n    }\n  }\n\n  // Should be the same as writeImagesManifest()\n  if (!makeRe(pattern.pathname ?? '**', { dot: true }).test(url.pathname)) {\n    return false\n  }\n\n  return true\n}\n\nexport function hasRemoteMatch(\n  domains: string[],\n  remotePatterns: Array<RemotePattern | URL>,\n  url: URL\n): boolean {\n  return (\n    domains.some((domain) => url.hostname === domain) ||\n    remotePatterns.some((p) => matchRemotePattern(p, url))\n  )\n}\n"], "names": ["hasRemoteMatch", "matchRemotePattern", "pattern", "url", "protocol", "undefined", "replace", "port", "hostname", "Error", "JSON", "stringify", "makeRe", "test", "search", "pathname", "dot", "domains", "remotePatterns", "some", "domain", "p"], "mappings": ";;;;;;;;;;;;;;;IA2CgBA,cAAc,EAAA;eAAdA;;IAvCAC,kBAAkB,EAAA;eAAlBA;;;2BAHO;AAGhB,SAASA,mBACdC,OAA4B,EAC5BC,GAAQ;IAER,IAAID,QAAQE,QAAQ,KAAKC,WAAW;QAClC,IAAIH,QAAQE,QAAQ,CAACE,OAAO,CAAC,MAAM,QAAQH,IAAIC,QAAQ,CAACE,OAAO,CAAC,MAAM,KAAK;YACzE,OAAO;QACT;IACF;IACA,IAAIJ,QAAQK,IAAI,KAAKF,WAAW;QAC9B,IAAIH,QAAQK,IAAI,KAAKJ,IAAII,IAAI,EAAE;YAC7B,OAAO;QACT;IACF;IAEA,IAAIL,QAAQM,QAAQ,KAAKH,WAAW;QAClC,MAAM,OAAA,cAEL,CAFK,IAAII,MACP,+CAA4CC,KAAKC,SAAS,CAACT,WADxD,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF,OAAO;QACL,IAAI,CAACU,CAAAA,GAAAA,WAAAA,MAAM,EAACV,QAAQM,QAAQ,EAAEK,IAAI,CAACV,IAAIK,QAAQ,GAAG;YAChD,OAAO;QACT;IACF;IAEA,IAAIN,QAAQY,MAAM,KAAKT,WAAW;QAChC,IAAIH,QAAQY,MAAM,KAAKX,IAAIW,MAAM,EAAE;YACjC,OAAO;QACT;IACF;QAGYZ;IADZ,8CAA8C;IAC9C,IAAI,CAACU,CAAAA,GAAAA,WAAAA,MAAM,EAACV,CAAAA,oBAAAA,QAAQa,QAAQ,KAAA,OAAhBb,oBAAoB,MAAM;QAAEc,KAAK;IAAK,GAAGH,IAAI,CAACV,IAAIY,QAAQ,GAAG;QACvE,OAAO;IACT;IAEA,OAAO;AACT;AAEO,SAASf,eACdiB,OAAiB,EACjBC,cAA0C,EAC1Cf,GAAQ;IAER,OACEc,QAAQE,IAAI,CAAC,CAACC,SAAWjB,IAAIK,QAAQ,KAAKY,WAC1CF,eAAeC,IAAI,CAAC,CAACE,IAAMpB,mBAAmBoB,GAAGlB;AAErD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2754, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/AI_Projects/CRMcriadores/crm-interno/node_modules/next/src/shared/lib/image-loader.ts"], "sourcesContent": ["import type { ImageLoaderPropsWithConfig } from './image-config'\n\nconst DEFAULT_Q = 75\n\nfunction defaultLoader({\n  config,\n  src,\n  width,\n  quality,\n}: ImageLoaderPropsWithConfig): string {\n  if (process.env.NODE_ENV !== 'production') {\n    const missingValues = []\n\n    // these should always be provided but make sure they are\n    if (!src) missingValues.push('src')\n    if (!width) missingValues.push('width')\n\n    if (missingValues.length > 0) {\n      throw new Error(\n        `Next Image Optimization requires ${missingValues.join(\n          ', '\n        )} to be provided. Make sure you pass them as props to the \\`next/image\\` component. Received: ${JSON.stringify(\n          { src, width, quality }\n        )}`\n      )\n    }\n\n    if (src.startsWith('//')) {\n      throw new Error(\n        `Failed to parse src \"${src}\" on \\`next/image\\`, protocol-relative URL (//) must be changed to an absolute URL (http:// or https://)`\n      )\n    }\n\n    if (src.startsWith('/') && config.localPatterns) {\n      if (\n        process.env.NODE_ENV !== 'test' &&\n        // micromatch isn't compatible with edge runtime\n        process.env.NEXT_RUNTIME !== 'edge'\n      ) {\n        // We use dynamic require because this should only error in development\n        const { hasLocalMatch } = require('./match-local-pattern')\n        if (!hasLocalMatch(config.localPatterns, src)) {\n          throw new Error(\n            `Invalid src prop (${src}) on \\`next/image\\` does not match \\`images.localPatterns\\` configured in your \\`next.config.js\\`\\n` +\n              `See more info: https://nextjs.org/docs/messages/next-image-unconfigured-localpatterns`\n          )\n        }\n      }\n    }\n\n    if (!src.startsWith('/') && (config.domains || config.remotePatterns)) {\n      let parsedSrc: URL\n      try {\n        parsedSrc = new URL(src)\n      } catch (err) {\n        console.error(err)\n        throw new Error(\n          `Failed to parse src \"${src}\" on \\`next/image\\`, if using relative image it must start with a leading slash \"/\" or be an absolute URL (http:// or https://)`\n        )\n      }\n\n      if (\n        process.env.NODE_ENV !== 'test' &&\n        // micromatch isn't compatible with edge runtime\n        process.env.NEXT_RUNTIME !== 'edge'\n      ) {\n        // We use dynamic require because this should only error in development\n        const { hasRemoteMatch } = require('./match-remote-pattern')\n        if (!hasRemoteMatch(config.domains, config.remotePatterns, parsedSrc)) {\n          throw new Error(\n            `Invalid src prop (${src}) on \\`next/image\\`, hostname \"${parsedSrc.hostname}\" is not configured under images in your \\`next.config.js\\`\\n` +\n              `See more info: https://nextjs.org/docs/messages/next-image-unconfigured-host`\n          )\n        }\n      }\n    }\n\n    if (quality && config.qualities && !config.qualities.includes(quality)) {\n      throw new Error(\n        `Invalid quality prop (${quality}) on \\`next/image\\` does not match \\`images.qualities\\` configured in your \\`next.config.js\\`\\n` +\n          `See more info: https://nextjs.org/docs/messages/next-image-unconfigured-qualities`\n      )\n    }\n  }\n\n  const q =\n    quality ||\n    config.qualities?.reduce((prev, cur) =>\n      Math.abs(cur - DEFAULT_Q) < Math.abs(prev - DEFAULT_Q) ? cur : prev\n    ) ||\n    DEFAULT_Q\n\n  return `${config.path}?url=${encodeURIComponent(src)}&w=${width}&q=${q}${\n    src.startsWith('/_next/static/media/') && process.env.NEXT_DEPLOYMENT_ID\n      ? `&dpl=${process.env.NEXT_DEPLOYMENT_ID}`\n      : ''\n  }`\n}\n\n// We use this to determine if the import is the default loader\n// or a custom loader defined by the user in next.config.js\ndefaultLoader.__next_img_default = true\n\nexport default defaultLoader\n"], "names": ["DEFAULT_Q", "defaultLoader", "config", "src", "width", "quality", "process", "env", "NODE_ENV", "<PERSON><PERSON><PERSON><PERSON>", "push", "length", "Error", "join", "JSON", "stringify", "startsWith", "localPatterns", "NEXT_RUNTIME", "hasLocalMatch", "require", "domains", "remotePatterns", "parsedSrc", "URL", "err", "console", "error", "hasRemoteMatch", "hostname", "qualities", "includes", "q", "reduce", "prev", "cur", "Math", "abs", "path", "encodeURIComponent", "NEXT_DEPLOYMENT_ID", "__next_img_default"], "mappings": "AAUMM,QAAQC,GAAG,CAACC,QAAQ,KAAK;;;;;+BA6F/B,WAAA;;;eAAA;;;AArGA,MAAMR,YAAY;AAElB,SAASC,cAAc,KAKM;IALN,IAAA,EACrBC,MAAM,EACNC,GAAG,EACHC,KAAK,EACLC,OAAO,EACoB,GALN;QAmFnBH;IA7EF,wCAA2C;QACzC,MAAMO,gBAAgB,EAAE;QAExB,yDAAyD;QACzD,IAAI,CAACN,KAAKM,cAAcC,IAAI,CAAC;QAC7B,IAAI,CAACN,OAAOK,cAAcC,IAAI,CAAC;QAE/B,IAAID,cAAcE,MAAM,GAAG,GAAG;YAC5B,MAAM,OAAA,cAML,CANK,IAAIC,MACP,sCAAmCH,cAAcI,IAAI,CACpD,QACA,gGAA+FC,KAAKC,SAAS,CAC7G;gBAAEZ;gBAAKC;gBAAOC;YAAQ,KAJpB,qBAAA;uBAAA;4BAAA;8BAAA;YAMN;QACF;QAEA,IAAIF,IAAIa,UAAU,CAAC,OAAO;YACxB,MAAM,OAAA,cAEL,CAFK,IAAIJ,MACP,0BAAuBT,MAAI,2GADxB,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;QAEA,IAAIA,IAAIa,UAAU,CAAC,QAAQd,OAAOe,aAAa,EAAE;YAC/C,IACEX,QAAQC,GAAG,CAACC,QAAQ,KAAK,UACzB,CAEA,+CAFgD;gBAGhD,uEAAuE;gBACvE,MAAM,EAAEW,aAAa,EAAE,GAAGC,QAAQ;gBAClC,IAAI,CAACD,cAAcjB,OAAOe,aAAa,EAAEd,MAAM;oBAC7C,MAAM,OAAA,cAGL,CAHK,IAAIS,MACP,uBAAoBT,MAAI,kGACtB,0FAFC,qBAAA;+BAAA;oCAAA;sCAAA;oBAGN;gBACF;YACF;QACF;QAEA,IAAI,CAACA,IAAIa,UAAU,CAAC,QAASd,CAAAA,OAAOmB,OAAO,IAAInB,OAAOoB,cAAa,GAAI;YACrE,IAAIC;YACJ,IAAI;gBACFA,YAAY,IAAIC,IAAIrB;YACtB,EAAE,OAAOsB,KAAK;gBACZC,QAAQC,KAAK,CAACF;gBACd,MAAM,OAAA,cAEL,CAFK,IAAIb,MACP,0BAAuBT,MAAI,kIADxB,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF;YAEA,IACEG,QAAQC,GAAG,CAACC,QAAQ,KAAK,UACzB,CAEA,+CAFgD;gBAGhD,uEAAuE;gBACvE,MAAM,EAAEoB,cAAc,EAAE,GAAGR,QAAQ;gBACnC,IAAI,CAACQ,eAAe1B,OAAOmB,OAAO,EAAEnB,OAAOoB,cAAc,EAAEC,YAAY;oBACrE,MAAM,OAAA,cAGL,CAHK,IAAIX,MACP,uBAAoBT,MAAI,kCAAiCoB,UAAUM,QAAQ,GAAC,gEAC1E,iFAFC,qBAAA;+BAAA;oCAAA;sCAAA;oBAGN;gBACF;YACF;QACF;QAEA,IAAIxB,WAAWH,OAAO4B,SAAS,IAAI,CAAC5B,OAAO4B,SAAS,CAACC,QAAQ,CAAC1B,UAAU;YACtE,MAAM,OAAA,cAGL,CAHK,IAAIO,MACP,2BAAwBP,UAAQ,8FAC9B,sFAFC,qBAAA;uBAAA;4BAAA;8BAAA;YAGN;QACF;IACF;IAEA,MAAM2B,IACJ3B,WAAAA,CAAAA,CACAH,oBAAAA,OAAO4B,SAAS,KAAA,OAAA,KAAA,IAAhB5B,kBAAkB+B,MAAM,CAAC,CAACC,MAAMC,MAC9BC,KAAKC,GAAG,CAACF,MAAMnC,aAAaoC,KAAKC,GAAG,CAACH,OAAOlC,aAAamC,MAAMD,KAAAA,KAEjElC;IAEF,OAAUE,OAAOoC,IAAI,GAAC,UAAOC,mBAAmBpC,OAAK,QAAKC,QAAM,QAAK4B,IACnE7B,CAAAA,IAAIa,UAAU,CAAC,2BAA2BV,QAAQC,GAAG,CAACiC,kBAAkB,GACpE,AAAC,UAAOlC,QAAQC,GAAG,CAACiC,kBAAkB,QACtC,EAAC;AAET;AAEA,+DAA+D;AAC/D,2DAA2D;AAC3DvC,cAAcwC,kBAAkB,GAAG;MAEnC,WAAexC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2849, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/AI_Projects/CRMcriadores/crm-interno/node_modules/next/src/client/image-component.tsx"], "sourcesContent": ["'use client'\n\nimport React, {\n  useRef,\n  useEffect,\n  useCallback,\n  useContext,\n  useMemo,\n  useState,\n  forwardRef,\n  use,\n} from 'react'\nimport ReactDOM from 'react-dom'\nimport Head from '../shared/lib/head'\nimport { getImgProps } from '../shared/lib/get-img-props'\nimport type {\n  ImageProps,\n  ImgProps,\n  OnLoad,\n  OnLoadingComplete,\n  PlaceholderValue,\n} from '../shared/lib/get-img-props'\nimport type {\n  ImageConfigComplete,\n  ImageLoaderProps,\n} from '../shared/lib/image-config'\nimport { imageConfigDefault } from '../shared/lib/image-config'\nimport { ImageConfigContext } from '../shared/lib/image-config-context.shared-runtime'\nimport { warnOnce } from '../shared/lib/utils/warn-once'\nimport { RouterContext } from '../shared/lib/router-context.shared-runtime'\n\n// @ts-ignore - This is replaced by webpack alias\nimport defaultLoader from 'next/dist/shared/lib/image-loader'\nimport { useMergedRef } from './use-merged-ref'\n\n// This is replaced by webpack define plugin\nconst configEnv = process.env.__NEXT_IMAGE_OPTS as any as ImageConfigComplete\n\nif (typeof window === 'undefined') {\n  ;(globalThis as any).__NEXT_IMAGE_IMPORTED = true\n}\n\nexport type { ImageLoaderProps }\nexport type ImageLoader = (p: ImageLoaderProps) => string\n\ntype ImgElementWithDataProp = HTMLImageElement & {\n  'data-loaded-src': string | undefined\n}\n\ntype ImageElementProps = ImgProps & {\n  unoptimized: boolean\n  placeholder: PlaceholderValue\n  onLoadRef: React.MutableRefObject<OnLoad | undefined>\n  onLoadingCompleteRef: React.MutableRefObject<OnLoadingComplete | undefined>\n  setBlurComplete: (b: boolean) => void\n  setShowAltText: (b: boolean) => void\n  sizesInput: string | undefined\n}\n\n// See https://stackoverflow.com/q/39777833/266535 for why we use this ref\n// handler instead of the img's onLoad attribute.\nfunction handleLoading(\n  img: ImgElementWithDataProp,\n  placeholder: PlaceholderValue,\n  onLoadRef: React.MutableRefObject<OnLoad | undefined>,\n  onLoadingCompleteRef: React.MutableRefObject<OnLoadingComplete | undefined>,\n  setBlurComplete: (b: boolean) => void,\n  unoptimized: boolean,\n  sizesInput: string | undefined\n) {\n  const src = img?.src\n  if (!img || img['data-loaded-src'] === src) {\n    return\n  }\n  img['data-loaded-src'] = src\n  const p = 'decode' in img ? img.decode() : Promise.resolve()\n  p.catch(() => {}).then(() => {\n    if (!img.parentElement || !img.isConnected) {\n      // Exit early in case of race condition:\n      // - onload() is called\n      // - decode() is called but incomplete\n      // - unmount is called\n      // - decode() completes\n      return\n    }\n    if (placeholder !== 'empty') {\n      setBlurComplete(true)\n    }\n    if (onLoadRef?.current) {\n      // Since we don't have the SyntheticEvent here,\n      // we must create one with the same shape.\n      // See https://reactjs.org/docs/events.html\n      const event = new Event('load')\n      Object.defineProperty(event, 'target', { writable: false, value: img })\n      let prevented = false\n      let stopped = false\n      onLoadRef.current({\n        ...event,\n        nativeEvent: event,\n        currentTarget: img,\n        target: img,\n        isDefaultPrevented: () => prevented,\n        isPropagationStopped: () => stopped,\n        persist: () => {},\n        preventDefault: () => {\n          prevented = true\n          event.preventDefault()\n        },\n        stopPropagation: () => {\n          stopped = true\n          event.stopPropagation()\n        },\n      })\n    }\n    if (onLoadingCompleteRef?.current) {\n      onLoadingCompleteRef.current(img)\n    }\n    if (process.env.NODE_ENV !== 'production') {\n      const origSrc = new URL(src, 'http://n').searchParams.get('url') || src\n      if (img.getAttribute('data-nimg') === 'fill') {\n        if (!unoptimized && (!sizesInput || sizesInput === '100vw')) {\n          let widthViewportRatio =\n            img.getBoundingClientRect().width / window.innerWidth\n          if (widthViewportRatio < 0.6) {\n            if (sizesInput === '100vw') {\n              warnOnce(\n                `Image with src \"${origSrc}\" has \"fill\" prop and \"sizes\" prop of \"100vw\", but image is not rendered at full viewport width. Please adjust \"sizes\" to improve page performance. Read more: https://nextjs.org/docs/api-reference/next/image#sizes`\n              )\n            } else {\n              warnOnce(\n                `Image with src \"${origSrc}\" has \"fill\" but is missing \"sizes\" prop. Please add it to improve page performance. Read more: https://nextjs.org/docs/api-reference/next/image#sizes`\n              )\n            }\n          }\n        }\n        if (img.parentElement) {\n          const { position } = window.getComputedStyle(img.parentElement)\n          const valid = ['absolute', 'fixed', 'relative']\n          if (!valid.includes(position)) {\n            warnOnce(\n              `Image with src \"${origSrc}\" has \"fill\" and parent element with invalid \"position\". Provided \"${position}\" should be one of ${valid\n                .map(String)\n                .join(',')}.`\n            )\n          }\n        }\n        if (img.height === 0) {\n          warnOnce(\n            `Image with src \"${origSrc}\" has \"fill\" and a height value of 0. This is likely because the parent element of the image has not been styled to have a set height.`\n          )\n        }\n      }\n\n      const heightModified =\n        img.height.toString() !== img.getAttribute('height')\n      const widthModified = img.width.toString() !== img.getAttribute('width')\n      if (\n        (heightModified && !widthModified) ||\n        (!heightModified && widthModified)\n      ) {\n        warnOnce(\n          `Image with src \"${origSrc}\" has either width or height modified, but not the other. If you use CSS to change the size of your image, also include the styles 'width: \"auto\"' or 'height: \"auto\"' to maintain the aspect ratio.`\n        )\n      }\n    }\n  })\n}\n\nfunction getDynamicProps(\n  fetchPriority?: string\n): Record<string, string | undefined> {\n  if (Boolean(use)) {\n    // In React 19.0.0 or newer, we must use camelCase\n    // prop to avoid \"Warning: Invalid DOM property\".\n    // See https://github.com/facebook/react/pull/25927\n    return { fetchPriority }\n  }\n  // In React 18.2.0 or older, we must use lowercase prop\n  // to avoid \"Warning: Invalid DOM property\".\n  return { fetchpriority: fetchPriority }\n}\n\nconst ImageElement = forwardRef<HTMLImageElement | null, ImageElementProps>(\n  (\n    {\n      src,\n      srcSet,\n      sizes,\n      height,\n      width,\n      decoding,\n      className,\n      style,\n      fetchPriority,\n      placeholder,\n      loading,\n      unoptimized,\n      fill,\n      onLoadRef,\n      onLoadingCompleteRef,\n      setBlurComplete,\n      setShowAltText,\n      sizesInput,\n      onLoad,\n      onError,\n      ...rest\n    },\n    forwardedRef\n  ) => {\n    const ownRef = useCallback(\n      (img: ImgElementWithDataProp | null) => {\n        if (!img) {\n          return\n        }\n        if (onError) {\n          // If the image has an error before react hydrates, then the error is lost.\n          // The workaround is to wait until the image is mounted which is after hydration,\n          // then we set the src again to trigger the error handler (if there was an error).\n          // eslint-disable-next-line no-self-assign\n          img.src = img.src\n        }\n        if (process.env.NODE_ENV !== 'production') {\n          if (!src) {\n            console.error(`Image is missing required \"src\" property:`, img)\n          }\n          if (img.getAttribute('alt') === null) {\n            console.error(\n              `Image is missing required \"alt\" property. Please add Alternative Text to describe the image for screen readers and search engines.`\n            )\n          }\n        }\n        if (img.complete) {\n          handleLoading(\n            img,\n            placeholder,\n            onLoadRef,\n            onLoadingCompleteRef,\n            setBlurComplete,\n            unoptimized,\n            sizesInput\n          )\n        }\n      },\n      [\n        src,\n        placeholder,\n        onLoadRef,\n        onLoadingCompleteRef,\n        setBlurComplete,\n        onError,\n        unoptimized,\n        sizesInput,\n      ]\n    )\n\n    const ref = useMergedRef(forwardedRef, ownRef)\n\n    return (\n      <img\n        {...rest}\n        {...getDynamicProps(fetchPriority)}\n        // It's intended to keep `loading` before `src` because React updates\n        // props in order which causes Safari/Firefox to not lazy load properly.\n        // See https://github.com/facebook/react/issues/25883\n        loading={loading}\n        width={width}\n        height={height}\n        decoding={decoding}\n        data-nimg={fill ? 'fill' : '1'}\n        className={className}\n        style={style}\n        // It's intended to keep `src` the last attribute because React updates\n        // attributes in order. If we keep `src` the first one, Safari will\n        // immediately start to fetch `src`, before `sizes` and `srcSet` are even\n        // updated by React. That causes multiple unnecessary requests if `srcSet`\n        // and `sizes` are defined.\n        // This bug cannot be reproduced in Chrome or Firefox.\n        sizes={sizes}\n        srcSet={srcSet}\n        src={src}\n        ref={ref}\n        onLoad={(event) => {\n          const img = event.currentTarget as ImgElementWithDataProp\n          handleLoading(\n            img,\n            placeholder,\n            onLoadRef,\n            onLoadingCompleteRef,\n            setBlurComplete,\n            unoptimized,\n            sizesInput\n          )\n        }}\n        onError={(event) => {\n          // if the real image fails to load, this will ensure \"alt\" is visible\n          setShowAltText(true)\n          if (placeholder !== 'empty') {\n            // If the real image fails to load, this will still remove the placeholder.\n            setBlurComplete(true)\n          }\n          if (onError) {\n            onError(event)\n          }\n        }}\n      />\n    )\n  }\n)\n\nfunction ImagePreload({\n  isAppRouter,\n  imgAttributes,\n}: {\n  isAppRouter: boolean\n  imgAttributes: ImgProps\n}) {\n  const opts = {\n    as: 'image',\n    imageSrcSet: imgAttributes.srcSet,\n    imageSizes: imgAttributes.sizes,\n    crossOrigin: imgAttributes.crossOrigin,\n    referrerPolicy: imgAttributes.referrerPolicy,\n    ...getDynamicProps(imgAttributes.fetchPriority),\n  }\n\n  if (isAppRouter && ReactDOM.preload) {\n    // See https://github.com/facebook/react/pull/26940\n    ReactDOM.preload(\n      imgAttributes.src,\n      // @ts-expect-error TODO: upgrade to `@types/react-dom@18.3.x`\n      opts\n    )\n    return null\n  }\n\n  return (\n    <Head>\n      <link\n        key={\n          '__nimg-' +\n          imgAttributes.src +\n          imgAttributes.srcSet +\n          imgAttributes.sizes\n        }\n        rel=\"preload\"\n        // Note how we omit the `href` attribute, as it would only be relevant\n        // for browsers that do not support `imagesrcset`, and in those cases\n        // it would cause the incorrect image to be preloaded.\n        //\n        // https://html.spec.whatwg.org/multipage/semantics.html#attr-link-imagesrcset\n        href={imgAttributes.srcSet ? undefined : imgAttributes.src}\n        {...opts}\n      />\n    </Head>\n  )\n}\n\n/**\n * The `Image` component is used to optimize images.\n *\n * Read more: [Next.js docs: `Image`](https://nextjs.org/docs/app/api-reference/components/image)\n */\nexport const Image = forwardRef<HTMLImageElement | null, ImageProps>(\n  (props, forwardedRef) => {\n    const pagesRouter = useContext(RouterContext)\n    // We're in the app directory if there is no pages router.\n    const isAppRouter = !pagesRouter\n\n    const configContext = useContext(ImageConfigContext)\n    const config = useMemo(() => {\n      const c = configEnv || configContext || imageConfigDefault\n      const allSizes = [...c.deviceSizes, ...c.imageSizes].sort((a, b) => a - b)\n      const deviceSizes = c.deviceSizes.sort((a, b) => a - b)\n      const qualities = c.qualities?.sort((a, b) => a - b)\n      return { ...c, allSizes, deviceSizes, qualities }\n    }, [configContext])\n\n    const { onLoad, onLoadingComplete } = props\n    const onLoadRef = useRef(onLoad)\n\n    useEffect(() => {\n      onLoadRef.current = onLoad\n    }, [onLoad])\n\n    const onLoadingCompleteRef = useRef(onLoadingComplete)\n\n    useEffect(() => {\n      onLoadingCompleteRef.current = onLoadingComplete\n    }, [onLoadingComplete])\n\n    const [blurComplete, setBlurComplete] = useState(false)\n    const [showAltText, setShowAltText] = useState(false)\n\n    const { props: imgAttributes, meta: imgMeta } = getImgProps(props, {\n      defaultLoader,\n      imgConf: config,\n      blurComplete,\n      showAltText,\n    })\n\n    return (\n      <>\n        {\n          <ImageElement\n            {...imgAttributes}\n            unoptimized={imgMeta.unoptimized}\n            placeholder={imgMeta.placeholder}\n            fill={imgMeta.fill}\n            onLoadRef={onLoadRef}\n            onLoadingCompleteRef={onLoadingCompleteRef}\n            setBlurComplete={setBlurComplete}\n            setShowAltText={setShowAltText}\n            sizesInput={props.sizes}\n            ref={forwardedRef}\n          />\n        }\n        {imgMeta.priority ? (\n          <ImagePreload\n            isAppRouter={isAppRouter}\n            imgAttributes={imgAttributes}\n          />\n        ) : null}\n      </>\n    )\n  }\n)\n"], "names": ["Image", "configEnv", "process", "env", "__NEXT_IMAGE_OPTS", "window", "globalThis", "__NEXT_IMAGE_IMPORTED", "handleLoading", "img", "placeholder", "onLoadRef", "onLoadingCompleteRef", "setBlurComplete", "unoptimized", "sizesInput", "src", "p", "decode", "Promise", "resolve", "catch", "then", "parentElement", "isConnected", "current", "event", "Event", "Object", "defineProperty", "writable", "value", "prevented", "stopped", "nativeEvent", "currentTarget", "target", "isDefaultPrevented", "isPropagationStopped", "persist", "preventDefault", "stopPropagation", "NODE_ENV", "origSrc", "URL", "searchParams", "get", "getAttribute", "widthViewportRatio", "getBoundingClientRect", "width", "innerWidth", "warnOnce", "position", "getComputedStyle", "valid", "includes", "map", "String", "join", "height", "heightModified", "toString", "widthModified", "getDynamicProps", "fetchPriority", "Boolean", "use", "fetchpriority", "ImageElement", "forwardRef", "forwardedRef", "srcSet", "sizes", "decoding", "className", "style", "loading", "fill", "setShowAltText", "onLoad", "onError", "rest", "ownRef", "useCallback", "console", "error", "complete", "ref", "useMergedRef", "data-nimg", "ImagePreload", "isAppRouter", "imgAttributes", "opts", "as", "imageSrcSet", "imageSizes", "crossOrigin", "referrerPolicy", "ReactDOM", "preload", "Head", "link", "rel", "href", "undefined", "props", "pagesRouter", "useContext", "RouterContext", "configContext", "ImageConfigContext", "config", "useMemo", "c", "imageConfigDefault", "allSizes", "deviceSizes", "sort", "a", "b", "qualities", "onLoadingComplete", "useRef", "useEffect", "blurComplete", "useState", "showAltText", "meta", "imgMeta", "getImgProps", "defaultLoader", "imgConf", "priority"], "mappings": "AAoCkBE,QAAQC,GAAG,CAACC,iBAAiB;AApC/C;;;;;+BA0WaJ,SAAAA;;;eAAAA;;;;;;iEA/VN;mEACc;+DACJ;6BACW;6BAYO;iDACA;0BACV;4CACK;sEAGJ;8BACG;AAE7B,4CAA4C;AAC5C,MAAMC;AAEN,IAAI,OAAOI,WAAW,aAAa;;IAC/BC,WAAmBC,qBAAqB,GAAG;AAC/C;AAmBA,0EAA0E;AAC1E,iDAAiD;AACjD,SAASC,cACPC,GAA2B,EAC3BC,WAA6B,EAC7BC,SAAqD,EACrDC,oBAA2E,EAC3EC,eAAqC,EACrCC,WAAoB,EACpBC,UAA8B;IAE9B,MAAMC,MAAMP,OAAAA,OAAAA,KAAAA,IAAAA,IAAKO,GAAG;IACpB,IAAI,CAACP,OAAOA,GAAG,CAAC,kBAAkB,KAAKO,KAAK;QAC1C;IACF;IACAP,GAAG,CAAC,kBAAkB,GAAGO;IACzB,MAAMC,IAAI,YAAYR,MAAMA,IAAIS,MAAM,KAAKC,QAAQC,OAAO;IAC1DH,EAAEI,KAAK,CAAC,KAAO,GAAGC,IAAI,CAAC;QACrB,IAAI,CAACb,IAAIc,aAAa,IAAI,CAACd,IAAIe,WAAW,EAAE;YAC1C,wCAAwC;YACxC,uBAAuB;YACvB,sCAAsC;YACtC,sBAAsB;YACtB,uBAAuB;YACvB;QACF;QACA,IAAId,gBAAgB,SAAS;YAC3BG,gBAAgB;QAClB;QACA,IAAIF,aAAAA,OAAAA,KAAAA,IAAAA,UAAWc,OAAO,EAAE;YACtB,+CAA+C;YAC/C,0CAA0C;YAC1C,2CAA2C;YAC3C,MAAMC,QAAQ,IAAIC,MAAM;YACxBC,OAAOC,cAAc,CAACH,OAAO,UAAU;gBAAEI,UAAU;gBAAOC,OAAOtB;YAAI;YACrE,IAAIuB,YAAY;YAChB,IAAIC,UAAU;YACdtB,UAAUc,OAAO,CAAC;gBAChB,GAAGC,KAAK;gBACRQ,aAAaR;gBACbS,eAAe1B;gBACf2B,QAAQ3B;gBACR4B,oBAAoB,IAAML;gBAC1BM,sBAAsB,IAAML;gBAC5BM,SAAS,KAAO;gBAChBC,gBAAgB;oBACdR,YAAY;oBACZN,MAAMc,cAAc;gBACtB;gBACAC,iBAAiB;oBACfR,UAAU;oBACVP,MAAMe,eAAe;gBACvB;YACF;QACF;QACA,IAAI7B,wBAAAA,OAAAA,KAAAA,IAAAA,qBAAsBa,OAAO,EAAE;YACjCb,qBAAqBa,OAAO,CAAChB;QAC/B;QACA,IAAIP,QAAQC,GAAG,CAACuC,QAAQ,KAAK,WAAc;YACzC,MAAMC,UAAU,IAAIC,IAAI5B,KAAK,YAAY6B,YAAY,CAACC,GAAG,CAAC,UAAU9B;YACpE,IAAIP,IAAIsC,YAAY,CAAC,iBAAiB,QAAQ;gBAC5C,IAAI,CAACjC,eAAgB,CAAA,CAACC,cAAcA,eAAe,OAAM,GAAI;oBAC3D,IAAIiC,qBACFvC,IAAIwC,qBAAqB,GAAGC,KAAK,GAAG7C,OAAO8C,UAAU;oBACvD,IAAIH,qBAAqB,KAAK;wBAC5B,IAAIjC,eAAe,SAAS;4BAC1BqC,CAAAA,GAAAA,UAAAA,QAAQ,EACL,qBAAkBT,UAAQ;wBAE/B,OAAO;4BACLS,CAAAA,GAAAA,UAAAA,QAAQ,EACL,qBAAkBT,UAAQ;wBAE/B;oBACF;gBACF;gBACA,IAAIlC,IAAIc,aAAa,EAAE;oBACrB,MAAM,EAAE8B,QAAQ,EAAE,GAAGhD,OAAOiD,gBAAgB,CAAC7C,IAAIc,aAAa;oBAC9D,MAAMgC,QAAQ;wBAAC;wBAAY;wBAAS;qBAAW;oBAC/C,IAAI,CAACA,MAAMC,QAAQ,CAACH,WAAW;wBAC7BD,CAAAA,GAAAA,UAAAA,QAAQ,EACL,qBAAkBT,UAAQ,wEAAqEU,WAAS,wBAAqBE,MAC3HE,GAAG,CAACC,QACJC,IAAI,CAAC,OAAK;oBAEjB;gBACF;gBACA,IAAIlD,IAAImD,MAAM,KAAK,GAAG;oBACpBR,CAAAA,GAAAA,UAAAA,QAAQ,EACL,qBAAkBT,UAAQ;gBAE/B;YACF;YAEA,MAAMkB,iBACJpD,IAAImD,MAAM,CAACE,QAAQ,OAAOrD,IAAIsC,YAAY,CAAC;YAC7C,MAAMgB,gBAAgBtD,IAAIyC,KAAK,CAACY,QAAQ,OAAOrD,IAAIsC,YAAY,CAAC;YAChE,IACGc,kBAAkB,CAACE,iBACnB,CAACF,kBAAkBE,eACpB;gBACAX,CAAAA,GAAAA,UAAAA,QAAQ,EACL,qBAAkBT,UAAQ;YAE/B;QACF;IACF;AACF;AAEA,SAASqB,gBACPC,aAAsB;IAEtB,IAAIC,QAAQC,OAAAA,GAAG,GAAG;QAChB,kDAAkD;QAClD,iDAAiD;QACjD,mDAAmD;QACnD,OAAO;YAAEF;QAAc;IACzB;IACA,uDAAuD;IACvD,4CAA4C;IAC5C,OAAO;QAAEG,eAAeH;IAAc;AACxC;AAEA,MAAMI,eAAAA,WAAAA,GAAeC,CAAAA,GAAAA,OAAAA,UAAU,EAC7B,CAAA,OAwBEC;QAvBA,EACEvD,GAAG,EACHwD,MAAM,EACNC,KAAK,EACLb,MAAM,EACNV,KAAK,EACLwB,QAAQ,EACRC,SAAS,EACTC,KAAK,EACLX,aAAa,EACbvD,WAAW,EACXmE,OAAO,EACP/D,WAAW,EACXgE,IAAI,EACJnE,SAAS,EACTC,oBAAoB,EACpBC,eAAe,EACfkE,cAAc,EACdhE,UAAU,EACViE,MAAM,EACNC,OAAO,EACP,GAAGC,MACJ,GAAA;IAGD,MAAMC,SAASC,CAAAA,GAAAA,OAAAA,WAAW,EACxB,CAAC3E;QACC,IAAI,CAACA,KAAK;YACR;QACF;QACA,IAAIwE,SAAS;YACX,2EAA2E;YAC3E,iFAAiF;YACjF,kFAAkF;YAClF,0CAA0C;YAC1CxE,IAAIO,GAAG,GAAGP,IAAIO,GAAG;QACnB;QACA,IAAId,QAAQC,GAAG,CAACuC,QAAQ,KAAK,WAAc;YACzC,IAAI,CAAC1B,KAAK;gBACRqE,QAAQC,KAAK,CAAE,6CAA4C7E;YAC7D;YACA,IAAIA,IAAIsC,YAAY,CAAC,WAAW,MAAM;gBACpCsC,QAAQC,KAAK,CACV;YAEL;QACF;QACA,IAAI7E,IAAI8E,QAAQ,EAAE;YAChB/E,cACEC,KACAC,aACAC,WACAC,sBACAC,iBACAC,aACAC;QAEJ;IACF,GACA;QACEC;QACAN;QACAC;QACAC;QACAC;QACAoE;QACAnE;QACAC;KACD;IAGH,MAAMyE,MAAMC,CAAAA,GAAAA,cAAAA,YAAY,EAAClB,cAAcY;IAEvC,OAAA,WAAA,GACE,CAAA,GAAA,YAAA,GAAA,EAAC1E,OAAAA;QACE,GAAGyE,IAAI;QACP,GAAGlB,gBAAgBC,cAAc;QAClC,qEAAqE;QACrE,wEAAwE;QACxE,qDAAqD;QACrDY,SAASA;QACT3B,OAAOA;QACPU,QAAQA;QACRc,UAAUA;QACVgB,aAAWZ,OAAO,SAAS;QAC3BH,WAAWA;QACXC,OAAOA;QACP,uEAAuE;QACvE,mEAAmE;QACnE,yEAAyE;QACzE,0EAA0E;QAC1E,2BAA2B;QAC3B,sDAAsD;QACtDH,OAAOA;QACPD,QAAQA;QACRxD,KAAKA;QACLwE,KAAKA;QACLR,QAAQ,CAACtD;YACP,MAAMjB,MAAMiB,MAAMS,aAAa;YAC/B3B,cACEC,KACAC,aACAC,WACAC,sBACAC,iBACAC,aACAC;QAEJ;QACAkE,SAAS,CAACvD;YACR,qEAAqE;YACrEqD,eAAe;YACf,IAAIrE,gBAAgB,SAAS;gBAC3B,2EAA2E;gBAC3EG,gBAAgB;YAClB;YACA,IAAIoE,SAAS;gBACXA,QAAQvD;YACV;QACF;;AAGN;AAGF,SAASiE,aAAa,KAMrB;IANqB,IAAA,EACpBC,WAAW,EACXC,aAAa,EAId,GANqB;IAOpB,MAAMC,OAAO;QACXC,IAAI;QACJC,aAAaH,cAAcrB,MAAM;QACjCyB,YAAYJ,cAAcpB,KAAK;QAC/ByB,aAAaL,cAAcK,WAAW;QACtCC,gBAAgBN,cAAcM,cAAc;QAC5C,GAAGnC,gBAAgB6B,cAAc5B,aAAa,CAAC;IACjD;IAEA,IAAI2B,eAAeQ,UAAAA,OAAQ,CAACC,OAAO,EAAE;QACnC,mDAAmD;QACnDD,UAAAA,OAAQ,CAACC,OAAO,CACdR,cAAc7E,GAAG,EACjB,AACA8E,8DAD8D;QAGhE,OAAO;IACT;IAEA,OAAA,WAAA,GACE,CAAA,GAAA,YAAA,GAAA,EAACQ,MAAAA,OAAI,EAAA;kBACH,WAAA,GAAA,CAAA,GAAA,YAAA,GAAA,EAACC,QAAAA;YAOCC,KAAI;YACJ,sEAAsE;YACtE,qEAAqE;YACrE,sDAAsD;YACtD,EAAE;YACF,8EAA8E;YAC9EC,MAAMZ,cAAcrB,MAAM,GAAGkC,YAAYb,cAAc7E,GAAG;YACzD,GAAG8E,IAAI;WAZN,YACAD,cAAc7E,GAAG,GACjB6E,cAAcrB,MAAM,GACpBqB,cAAcpB,KAAK;;AAa7B;AAOO,MAAMzE,QAAAA,WAAAA,GAAQsE,CAAAA,GAAAA,OAAAA,UAAU,EAC7B,CAACqC,OAAOpC;IACN,MAAMqC,cAAcC,CAAAA,GAAAA,OAAAA,UAAU,EAACC,4BAAAA,aAAa;IAC5C,0DAA0D;IAC1D,MAAMlB,cAAc,CAACgB;IAErB,MAAMG,gBAAgBF,CAAAA,GAAAA,OAAAA,UAAU,EAACG,iCAAAA,kBAAkB;IACnD,MAAMC,SAASC,CAAAA,GAAAA,OAAAA,OAAO,EAAC;YAIHC;QAHlB,MAAMA,IAAIlH,aAAa8G,iBAAiBK,aAAAA,kBAAkB;QAC1D,MAAMC,WAAW;eAAIF,EAAEG,WAAW;eAAKH,EAAElB,UAAU;SAAC,CAACsB,IAAI,CAAC,CAACC,GAAGC,IAAMD,IAAIC;QACxE,MAAMH,cAAcH,EAAEG,WAAW,CAACC,IAAI,CAAC,CAACC,GAAGC,IAAMD,IAAIC;QACrD,MAAMC,YAAAA,CAAYP,eAAAA,EAAEO,SAAS,KAAA,OAAA,KAAA,IAAXP,aAAaI,IAAI,CAAC,CAACC,GAAGC,IAAMD,IAAIC;QAClD,OAAO;YAAE,GAAGN,CAAC;YAAEE;YAAUC;YAAaI;QAAU;IAClD,GAAG;QAACX;KAAc;IAElB,MAAM,EAAE/B,MAAM,EAAE2C,iBAAiB,EAAE,GAAGhB;IACtC,MAAMhG,YAAYiH,CAAAA,GAAAA,OAAAA,MAAM,EAAC5C;IAEzB6C,CAAAA,GAAAA,OAAAA,SAAS,EAAC;QACRlH,UAAUc,OAAO,GAAGuD;IACtB,GAAG;QAACA;KAAO;IAEX,MAAMpE,uBAAuBgH,CAAAA,GAAAA,OAAAA,MAAM,EAACD;IAEpCE,CAAAA,GAAAA,OAAAA,SAAS,EAAC;QACRjH,qBAAqBa,OAAO,GAAGkG;IACjC,GAAG;QAACA;KAAkB;IAEtB,MAAM,CAACG,cAAcjH,gBAAgB,GAAGkH,CAAAA,GAAAA,OAAAA,QAAQ,EAAC;IACjD,MAAM,CAACC,aAAajD,eAAe,GAAGgD,CAAAA,GAAAA,OAAAA,QAAQ,EAAC;IAE/C,MAAM,EAAEpB,OAAOd,aAAa,EAAEoC,MAAMC,OAAO,EAAE,GAAGC,CAAAA,GAAAA,aAAAA,WAAW,EAACxB,OAAO;QACjEyB,eAAAA,aAAAA,OAAa;QACbC,SAASpB;QACTa;QACAE;IACF;IAEA,OAAA,WAAA,GACE,CAAA,GAAA,YAAA,IAAA,EAAA,YAAA,QAAA,EAAA;;0BAEI,CAAA,GAAA,YAAA,GAAA,EAAC3D,cAAAA;gBACE,GAAGwB,aAAa;gBACjB/E,aAAaoH,QAAQpH,WAAW;gBAChCJ,aAAawH,QAAQxH,WAAW;gBAChCoE,MAAMoD,QAAQpD,IAAI;gBAClBnE,WAAWA;gBACXC,sBAAsBA;gBACtBC,iBAAiBA;gBACjBkE,gBAAgBA;gBAChBhE,YAAY4F,MAAMlC,KAAK;gBACvBe,KAAKjB;;YAGR2D,QAAQI,QAAQ,GAAA,WAAA,GACf,CAAA,GAAA,YAAA,GAAA,EAAC3C,cAAAA;gBACCC,aAAaA;gBACbC,eAAeA;iBAEf;;;AAGV", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3164, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/AI_Projects/CRMcriadores/crm-interno/node_modules/next/src/shared/lib/image-external.tsx"], "sourcesContent": ["import type { ImageConfigComplete, ImageLoaderProps } from './image-config'\nimport type { ImageProps, ImageLoader, StaticImageData } from './get-img-props'\n\nimport { getImgProps } from './get-img-props'\nimport { Image } from '../../client/image-component'\n\n// @ts-ignore - This is replaced by webpack alias\nimport defaultLoader from 'next/dist/shared/lib/image-loader'\n\n/**\n * For more advanced use cases, you can call `getImageProps()`\n * to get the props that would be passed to the underlying `<img>` element,\n * and instead pass to them to another component, style, canvas, etc.\n *\n * Read more: [Next.js docs: `getImageProps`](https://nextjs.org/docs/app/api-reference/components/image#getimageprops)\n */\nexport function getImageProps(imgProps: ImageProps) {\n  const { props } = getImgProps(imgProps, {\n    defaultLoader,\n    // This is replaced by webpack define plugin\n    imgConf: process.env.__NEXT_IMAGE_OPTS as any as ImageConfigComplete,\n  })\n  // Normally we don't care about undefined props because we pass to JSX,\n  // but this exported function could be used by the end user for anything\n  // so we delete undefined props to clean it up a little.\n  for (const [key, value] of Object.entries(props)) {\n    if (value === undefined) {\n      delete props[key as keyof typeof props]\n    }\n  }\n  return { props }\n}\n\nexport default Image\n\nexport type { ImageProps, ImageLoaderProps, ImageLoader, StaticImageData }\n"], "names": ["getImageProps", "imgProps", "props", "getImgProps", "defaultLoader", "imgConf", "process", "env", "__NEXT_IMAGE_OPTS", "key", "value", "Object", "entries", "undefined", "Image"], "mappings": "AAoBaM,QAAQC,GAAG,CAACC,iBAAiB;;;;;;;;;;;;;;;;IAa1C,OAAoB,EAAA;eAApB;;IAjBgBR,aAAa,EAAA;eAAbA;;;;6BAbY;gCACN;sEAGI;AASnB,SAASA,cAAcC,QAAoB;IAChD,MAAM,EAAEC,KAAK,EAAE,GAAGC,CAAAA,GAAAA,aAAAA,WAAW,EAACF,UAAU;QACtCG,eAAAA,aAAAA,OAAa;QACb,4CAA4C;QAC5CC,OAAAA;IACF;IACA,uEAAuE;IACvE,wEAAwE;IACxE,wDAAwD;IACxD,KAAK,MAAM,CAACI,KAAKC,MAAM,IAAIC,OAAOC,OAAO,CAACV,OAAQ;QAChD,IAAIQ,UAAUG,WAAW;YACvB,OAAOX,KAAK,CAACO,IAA0B;QACzC;IACF;IACA,OAAO;QAAEP;IAAM;AACjB;MAEA,WAAeY,gBAAAA,KAAK", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3215, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/AI_Projects/CRMcriadores/crm-interno/node_modules/next/image.js"], "sourcesContent": ["module.exports = require('./dist/shared/lib/image-external')\n"], "names": [], "mappings": "AAAA,OAAO,OAAO", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3222, "column": 0}, "map": {"version": 3, "file": "utilities.esm.js", "sources": ["file:///Users/<USER>/Documents/AI_Projects/CRMcriadores/crm-interno/node_modules/%40dnd-kit/utilities/src/hooks/useCombinedRefs.ts", "file:///Users/<USER>/Documents/AI_Projects/CRMcriadores/crm-interno/node_modules/%40dnd-kit/utilities/src/execution-context/canUseDOM.ts", "file:///Users/<USER>/Documents/AI_Projects/CRMcriadores/crm-interno/node_modules/%40dnd-kit/utilities/src/type-guards/isWindow.ts", "file:///Users/<USER>/Documents/AI_Projects/CRMcriadores/crm-interno/node_modules/%40dnd-kit/utilities/src/type-guards/isNode.ts", "file:///Users/<USER>/Documents/AI_Projects/CRMcriadores/crm-interno/node_modules/%40dnd-kit/utilities/src/execution-context/getWindow.ts", "file:///Users/<USER>/Documents/AI_Projects/CRMcriadores/crm-interno/node_modules/%40dnd-kit/utilities/src/type-guards/isDocument.ts", "file:///Users/<USER>/Documents/AI_Projects/CRMcriadores/crm-interno/node_modules/%40dnd-kit/utilities/src/type-guards/isHTMLElement.ts", "file:///Users/<USER>/Documents/AI_Projects/CRMcriadores/crm-interno/node_modules/%40dnd-kit/utilities/src/type-guards/isSVGElement.ts", "file:///Users/<USER>/Documents/AI_Projects/CRMcriadores/crm-interno/node_modules/%40dnd-kit/utilities/src/execution-context/getOwnerDocument.ts", "file:///Users/<USER>/Documents/AI_Projects/CRMcriadores/crm-interno/node_modules/%40dnd-kit/utilities/src/hooks/useIsomorphicLayoutEffect.ts", "file:///Users/<USER>/Documents/AI_Projects/CRMcriadores/crm-interno/node_modules/%40dnd-kit/utilities/src/hooks/useEvent.ts", "file:///Users/<USER>/Documents/AI_Projects/CRMcriadores/crm-interno/node_modules/%40dnd-kit/utilities/src/hooks/useInterval.ts", "file:///Users/<USER>/Documents/AI_Projects/CRMcriadores/crm-interno/node_modules/%40dnd-kit/utilities/src/hooks/useLatestValue.ts", "file:///Users/<USER>/Documents/AI_Projects/CRMcriadores/crm-interno/node_modules/%40dnd-kit/utilities/src/hooks/useLazyMemo.ts", "file:///Users/<USER>/Documents/AI_Projects/CRMcriadores/crm-interno/node_modules/%40dnd-kit/utilities/src/hooks/useNodeRef.ts", "file:///Users/<USER>/Documents/AI_Projects/CRMcriadores/crm-interno/node_modules/%40dnd-kit/utilities/src/hooks/usePrevious.ts", "file:///Users/<USER>/Documents/AI_Projects/CRMcriadores/crm-interno/node_modules/%40dnd-kit/utilities/src/hooks/useUniqueId.ts", "file:///Users/<USER>/Documents/AI_Projects/CRMcriadores/crm-interno/node_modules/%40dnd-kit/utilities/src/adjustment.ts", "file:///Users/<USER>/Documents/AI_Projects/CRMcriadores/crm-interno/node_modules/%40dnd-kit/utilities/src/event/hasViewportRelativeCoordinates.ts", "file:///Users/<USER>/Documents/AI_Projects/CRMcriadores/crm-interno/node_modules/%40dnd-kit/utilities/src/event/isKeyboardEvent.ts", "file:///Users/<USER>/Documents/AI_Projects/CRMcriadores/crm-interno/node_modules/%40dnd-kit/utilities/src/event/isTouchEvent.ts", "file:///Users/<USER>/Documents/AI_Projects/CRMcriadores/crm-interno/node_modules/%40dnd-kit/utilities/src/coordinates/getEventCoordinates.ts", "file:///Users/<USER>/Documents/AI_Projects/CRMcriadores/crm-interno/node_modules/%40dnd-kit/utilities/src/css.ts", "file:///Users/<USER>/Documents/AI_Projects/CRMcriadores/crm-interno/node_modules/%40dnd-kit/utilities/src/focus/findFirstFocusableNode.ts"], "sourcesContent": ["import {useMemo} from 'react';\n\nexport function useCombinedRefs<T>(\n  ...refs: ((node: T) => void)[]\n): (node: T) => void {\n  return useMemo(\n    () => (node: T) => {\n      refs.forEach((ref) => ref(node));\n    },\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    refs\n  );\n}\n", "// https://github.com/facebook/react/blob/master/packages/shared/ExecutionEnvironment.js\nexport const canUseDOM =\n  typeof window !== 'undefined' &&\n  typeof window.document !== 'undefined' &&\n  typeof window.document.createElement !== 'undefined';\n", "export function isWindow(element: Object): element is typeof window {\n  const elementString = Object.prototype.toString.call(element);\n  return (\n    elementString === '[object Window]' ||\n    // In Electron context the Window object serializes to [object global]\n    elementString === '[object global]'\n  );\n}\n", "export function isNode(node: Object): node is Node {\n  return 'nodeType' in node;\n}\n", "import {isWindow} from '../type-guards/isWindow';\nimport {isNode} from '../type-guards/isNode';\n\nexport function getWindow(target: Event['target']): typeof window {\n  if (!target) {\n    return window;\n  }\n\n  if (isWindow(target)) {\n    return target;\n  }\n\n  if (!isNode(target)) {\n    return window;\n  }\n\n  return target.ownerDocument?.defaultView ?? window;\n}\n", "import {getWindow} from '../execution-context/getWindow';\n\nexport function isDocument(node: Node): node is Document {\n  const {Document} = getWindow(node);\n\n  return node instanceof Document;\n}\n", "import {getWindow} from '../execution-context/getWindow';\n\nimport {isWindow} from './isWindow';\n\nexport function isHTMLElement(node: Node | Window): node is HTMLElement {\n  if (isWindow(node)) {\n    return false;\n  }\n\n  return node instanceof getWindow(node).HTMLElement;\n}\n", "import {getWindow} from '../execution-context/getWindow';\n\nexport function isSVGElement(node: Node): node is SVGElement {\n  return node instanceof getWindow(node).SVGElement;\n}\n", "import {\n  isWindow,\n  isHTMLElement,\n  isDocument,\n  isNode,\n  isSVGElement,\n} from '../type-guards';\n\nexport function getOwnerDocument(target: Event['target']): Document {\n  if (!target) {\n    return document;\n  }\n\n  if (isWindow(target)) {\n    return target.document;\n  }\n\n  if (!isNode(target)) {\n    return document;\n  }\n\n  if (isDocument(target)) {\n    return target;\n  }\n\n  if (isHTMLElement(target) || isSVGElement(target)) {\n    return target.ownerDocument;\n  }\n\n  return document;\n}\n", "import {useEffect, useLayoutEffect} from 'react';\n\nimport {canUseDOM} from '../execution-context';\n\n/**\n * A hook that resolves to useEffect on the server and useLayoutEffect on the client\n * @param callback {function} Callback function that is invoked when the dependencies of the hook change\n */\nexport const useIsomorphicLayoutEffect = canUseDOM\n  ? useLayoutEffect\n  : useEffect;\n", "import {useCallback, useRef} from 'react';\n\nimport {useIsomorphicLayoutEffect} from './useIsomorphicLayoutEffect';\n\nexport function useEvent<T extends Function>(handler: T | undefined) {\n  const handlerRef = useRef<T | undefined>(handler);\n\n  useIsomorphicLayoutEffect(() => {\n    handlerRef.current = handler;\n  });\n\n  return useCallback(function (...args: any) {\n    return handlerRef.current?.(...args);\n  }, []);\n}\n", "import {useCallback, useRef} from 'react';\n\nexport function useInterval() {\n  const intervalRef = useRef<number | null>(null);\n\n  const set = useCallback((listener: Function, duration: number) => {\n    intervalRef.current = setInterval(listener, duration);\n  }, []);\n\n  const clear = useCallback(() => {\n    if (intervalRef.current !== null) {\n      clearInterval(intervalRef.current);\n      intervalRef.current = null;\n    }\n  }, []);\n\n  return [set, clear] as const;\n}\n", "import {useRef} from 'react';\nimport type {DependencyList} from 'react';\n\nimport {useIsomorphicLayoutEffect} from './useIsomorphicLayoutEffect';\n\nexport function useLatestValue<T extends any>(\n  value: T,\n  dependencies: DependencyList = [value]\n) {\n  const valueRef = useRef<T>(value);\n\n  useIsomorphicLayoutEffect(() => {\n    if (valueRef.current !== value) {\n      valueRef.current = value;\n    }\n  }, dependencies);\n\n  return valueRef;\n}\n", "import {useMemo, useRef} from 'react';\n\nexport function useLazyMemo<T>(\n  callback: (prevValue: T | undefined) => T,\n  dependencies: any[]\n) {\n  const valueRef = useRef<T>();\n\n  return useMemo(\n    () => {\n      const newValue = callback(valueRef.current);\n      valueRef.current = newValue;\n\n      return newValue;\n    },\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    [...dependencies]\n  );\n}\n", "import {useRef, useCallback} from 'react';\n\nimport {useEvent} from './useEvent';\n\nexport function useNodeRef(\n  onChange?: (\n    newElement: HTMLElement | null,\n    previousElement: HTMLElement | null\n  ) => void\n) {\n  const onChangeHandler = useEvent(onChange);\n  const node = useRef<HTMLElement | null>(null);\n  const setNodeRef = useCallback(\n    (element: HTMLElement | null) => {\n      if (element !== node.current) {\n        onChangeHandler?.(element, node.current);\n      }\n\n      node.current = element;\n    },\n    //eslint-disable-next-line\n    []\n  );\n\n  return [node, setNodeRef] as const;\n}\n", "import {useRef, useEffect} from 'react';\n\nexport function usePrevious<T>(value: T) {\n  const ref = useRef<T>();\n\n  useEffect(() => {\n    ref.current = value;\n  }, [value]);\n\n  return ref.current;\n}\n", "import {useMemo} from 'react';\n\nlet ids: Record<string, number> = {};\n\nexport function useUniqueId(prefix: string, value?: string) {\n  return useMemo(() => {\n    if (value) {\n      return value;\n    }\n\n    const id = ids[prefix] == null ? 0 : ids[prefix] + 1;\n    ids[prefix] = id;\n\n    return `${prefix}-${id}`;\n  }, [prefix, value]);\n}\n", "function createAdjustmentFn(modifier: number) {\n  return <T extends Record<U, number>, U extends string>(\n    object: T,\n    ...adjustments: Partial<T>[]\n  ): T => {\n    return adjustments.reduce<T>(\n      (accumulator, adjustment) => {\n        const entries = Object.entries(adjustment) as [U, number][];\n\n        for (const [key, valueAdjustment] of entries) {\n          const value = accumulator[key];\n\n          if (value != null) {\n            accumulator[key] = (value + modifier * valueAdjustment) as T[U];\n          }\n        }\n\n        return accumulator;\n      },\n      {\n        ...object,\n      }\n    );\n  };\n}\n\nexport const add = createAdjustmentFn(1);\nexport const subtract = createAdjustmentFn(-1);\n", "export function hasViewportRelativeCoordinates(\n  event: Event\n): event is Event & Pick<PointerEvent, 'clientX' | 'clientY'> {\n  return 'clientX' in event && 'clientY' in event;\n}\n", "import {getWindow} from '../execution-context';\n\nexport function isKeyboardEvent(\n  event: Event | undefined | null\n): event is KeyboardEvent {\n  if (!event) {\n    return false;\n  }\n\n  const {KeyboardEvent} = getWindow(event.target);\n\n  return KeyboardEvent && event instanceof KeyboardEvent;\n}\n", "import {getWindow} from '../execution-context';\n\nexport function isTouchEvent(\n  event: Event | undefined | null\n): event is TouchEvent {\n  if (!event) {\n    return false;\n  }\n\n  const {TouchEvent} = getWindow(event.target);\n\n  return TouchEvent && event instanceof TouchEvent;\n}\n", "import type {Coordinates} from './types';\nimport {isTouchEvent, hasViewportRelativeCoordinates} from '../event';\n\n/**\n * Returns the normalized x and y coordinates for mouse and touch events.\n */\nexport function getEventCoordinates(event: Event): Coordinates | null {\n  if (isTouchEvent(event)) {\n    if (event.touches && event.touches.length) {\n      const {clientX: x, clientY: y} = event.touches[0];\n\n      return {\n        x,\n        y,\n      };\n    } else if (event.changedTouches && event.changedTouches.length) {\n      const {clientX: x, clientY: y} = event.changedTouches[0];\n\n      return {\n        x,\n        y,\n      };\n    }\n  }\n\n  if (hasViewportRelativeCoordinates(event)) {\n    return {\n      x: event.clientX,\n      y: event.clientY,\n    };\n  }\n\n  return null;\n}\n", "export type Transform = {\n  x: number;\n  y: number;\n  scaleX: number;\n  scaleY: number;\n};\n\nexport interface Transition {\n  property: string;\n  easing: string;\n  duration: number;\n}\n\nexport const CSS = Object.freeze({\n  Translate: {\n    toString(transform: Transform | null) {\n      if (!transform) {\n        return;\n      }\n\n      const {x, y} = transform;\n\n      return `translate3d(${x ? Math.round(x) : 0}px, ${\n        y ? Math.round(y) : 0\n      }px, 0)`;\n    },\n  },\n  Scale: {\n    toString(transform: Transform | null) {\n      if (!transform) {\n        return;\n      }\n\n      const {scaleX, scaleY} = transform;\n\n      return `scaleX(${scaleX}) scaleY(${scaleY})`;\n    },\n  },\n  Transform: {\n    toString(transform: Transform | null) {\n      if (!transform) {\n        return;\n      }\n\n      return [\n        CSS.Translate.toString(transform),\n        CSS.Scale.toString(transform),\n      ].join(' ');\n    },\n  },\n  Transition: {\n    toString({property, duration, easing}: Transition) {\n      return `${property} ${duration}ms ${easing}`;\n    },\n  },\n});\n", "const SELECTOR =\n  'a,frame,iframe,input:not([type=hidden]):not(:disabled),select:not(:disabled),textarea:not(:disabled),button:not(:disabled),*[tabindex]';\n\nexport function findFirstFocusableNode(\n  element: HTMLElement\n): HTMLElement | null {\n  if (element.matches(SELECTOR)) {\n    return element;\n  }\n\n  return element.querySelector(SELECTOR);\n}\n"], "names": ["useCombinedRefs", "refs", "useMemo", "node", "for<PERSON>ach", "ref", "canUseDOM", "window", "document", "createElement", "isWindow", "element", "elementString", "Object", "prototype", "toString", "call", "isNode", "getWindow", "target", "ownerDocument", "defaultView", "isDocument", "Document", "isHTMLElement", "HTMLElement", "isSVGElement", "SVGElement", "getOwnerDocument", "useIsomorphicLayoutEffect", "useLayoutEffect", "useEffect", "useEvent", "handler", "handler<PERSON>ef", "useRef", "current", "useCallback", "args", "useInterval", "intervalRef", "set", "listener", "duration", "setInterval", "clear", "clearInterval", "useLatestValue", "value", "dependencies", "valueRef", "useLazyMemo", "callback", "newValue", "useNodeRef", "onChange", "onChangeHandler", "setNodeRef", "usePrevious", "ids", "useUniqueId", "prefix", "id", "createAdjustmentFn", "modifier", "object", "adjustments", "reduce", "accumulator", "adjustment", "entries", "key", "valueAdjustment", "add", "subtract", "hasViewportRelativeCoordinates", "event", "isKeyboardEvent", "KeyboardEvent", "isTouchEvent", "TouchEvent", "getEventCoordinates", "touches", "length", "clientX", "x", "clientY", "y", "changedTouches", "CSS", "freeze", "Translate", "transform", "Math", "round", "Scale", "scaleX", "scaleY", "Transform", "join", "Transition", "property", "easing", "SELECTOR", "findFirstFocusableNode", "matches", "querySelector"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;SAEgBA;qCACXC,OAAAA,IAAAA,MAAAA,OAAAA,OAAAA,GAAAA,OAAAA,MAAAA,OAAAA;QAAAA,IAAAA,CAAAA,KAAAA,GAAAA,SAAAA,CAAAA,KAAAA;;IAEH,OAAOC,4KAAAA,AAAO;mCACZ;4CAAOC,IAAD;oBACJF,IAAI,CAACG,OAAL;oDAAcC,GAAD,GAASA,GAAG,CAACF,IAAD,CAAzB;;iBAFU;;kCAKZF,IALY,CAAd;AAOD;ACZD,wFAAA;AACA,MAAaK,SAAS,GACpB,OAAOC,MAAP,KAAkB,WAAlB,IACA,OAAOA,MAAM,CAACC,QAAd,KAA2B,WAD3B,IAEA,OAAOD,MAAM,CAACC,QAAP,CAAgBC,aAAvB,KAAyC,WAHpC;SCDSC,SAASC,OAAAA;IACvB,MAAMC,aAAa,GAAGC,MAAM,CAACC,SAAP,CAAiBC,QAAjB,CAA0BC,IAA1B,CAA+BL,OAA/B,CAAtB;IACA,OACEC,aAAa,KAAK,iBAAlB,IAAA,sEAAA;IAEAA,aAAa,KAAK,iBAHpB;AAKD;SCPeK,OAAOd,IAAAA;IACrB,OAAO,cAAcA,IAArB;AACD;SCCee,UAAUC,MAAAA;;IACxB,IAAI,CAACA,MAAL,EAAa;QACX,OAAOZ,MAAP;;IAGF,IAAIG,QAAQ,CAACS,MAAD,CAAZ,EAAsB;QACpB,OAAOA,MAAP;;IAGF,IAAI,CAACF,MAAM,CAACE,MAAD,CAAX,EAAqB;QACnB,OAAOZ,MAAP;;IAGF,OAAA,CAAA,wBAAA,CAAA,yBAAOY,MAAM,CAACC,aAAd,KAAA,OAAA,KAAA,IAAO,uBAAsBC,WAA7B,KAAA,OAAA,wBAA4Cd,MAA5C;AACD;SCfee,WAAWnB,IAAAA;IACzB,MAAM,EAACoB,QAAAA,KAAYL,SAAS,CAACf,IAAD,CAA5B;IAEA,OAAOA,IAAI,YAAYoB,QAAvB;AACD;SCFeC,cAAcrB,IAAAA;IAC5B,IAAIO,QAAQ,CAACP,IAAD,CAAZ,EAAoB;QAClB,OAAO,KAAP;;IAGF,OAAOA,IAAI,YAAYe,SAAS,CAACf,IAAD,CAAT,CAAgBsB,WAAvC;AACD;SCReC,aAAavB,IAAAA;IAC3B,OAAOA,IAAI,YAAYe,SAAS,CAACf,IAAD,CAAT,CAAgBwB,UAAvC;AACD;SCIeC,iBAAiBT,MAAAA;IAC/B,IAAI,CAACA,MAAL,EAAa;QACX,OAAOX,QAAP;;IAGF,IAAIE,QAAQ,CAACS,MAAD,CAAZ,EAAsB;QACpB,OAAOA,MAAM,CAACX,QAAd;;IAGF,IAAI,CAACS,MAAM,CAACE,MAAD,CAAX,EAAqB;QACnB,OAAOX,QAAP;;IAGF,IAAIc,UAAU,CAACH,MAAD,CAAd,EAAwB;QACtB,OAAOA,MAAP;;IAGF,IAAIK,aAAa,CAACL,MAAD,CAAb,IAAyBO,YAAY,CAACP,MAAD,CAAzC,EAAmD;QACjD,OAAOA,MAAM,CAACC,aAAd;;IAGF,OAAOZ,QAAP;AACD;AC1BD;;;IAIA,MAAaqB,yBAAyB,GAAGvB,SAAS,iKAC9CwB,kBAD8C,iKAE9CC,YAFG;SCJSC,SAA6BC,OAAAA;IAC3C,MAAMC,UAAU,GAAGC,2KAAAA,AAAM,EAAgBF,OAAhB,CAAzB;IAEAJ,yBAAyB;8CAAC;YACxBK,UAAU,CAACE,OAAX,GAAqBH,OAArB;SADuB,CAAzB;;IAIA,yKAAOI,cAAAA,AAAW;gCAAC;6CAAaC,OAAAA,IAAAA,MAAAA,OAAAA,OAAAA,GAAAA,OAAAA,MAAAA,OAAAA;gBAAAA,IAAAA,CAAAA,KAAAA,GAAAA,SAAAA,CAAAA,KAAAA;;YAC9B,OAAOJ,UAAU,CAACE,OAAlB,IAAA,OAAA,KAAA,IAAOF,UAAU,CAACE,OAAX,CAAqB,GAAGE,IAAxB,CAAP;SADgB;+BAEf,EAFe,CAAlB;AAGD;SCZeC;IACd,MAAMC,WAAW,qKAAGL,SAAAA,AAAM,EAAgB,IAAhB,CAA1B;IAEA,MAAMM,GAAG,GAAGJ,gLAAAA,AAAW;wCAAC,CAACK,QAAD,EAAqBC,QAArB;YACtBH,WAAW,CAACJ,OAAZ,GAAsBQ,WAAW,CAACF,QAAD,EAAWC,QAAX,CAAjC;SADqB;uCAEpB,EAFoB,CAAvB;IAIA,MAAME,KAAK,qKAAGR,cAAAA,AAAW;0CAAC;YACxB,IAAIG,WAAW,CAACJ,OAAZ,KAAwB,IAA5B,EAAkC;gBAChCU,aAAa,CAACN,WAAW,CAACJ,OAAb,CAAb;gBACAI,WAAW,CAACJ,OAAZ,GAAsB,IAAtB;;SAHqB;yCAKtB,EALsB,CAAzB;IAOA,OAAO;QAACK,GAAD;QAAMI,KAAN;KAAP;AACD;SCZeE,eACdC,KAAAA,EACAC,YAAAA;QAAAA,iBAAAA,KAAAA,GAAAA;QAAAA,eAA+B;YAACD,KAAD;SAAA;;IAE/B,MAAME,QAAQ,qKAAGf,SAAAA,AAAM,EAAIa,KAAJ,CAAvB;IAEAnB,yBAAyB;oDAAC;YACxB,IAAIqB,QAAQ,CAACd,OAAT,KAAqBY,KAAzB,EAAgC;gBAC9BE,QAAQ,CAACd,OAAT,GAAmBY,KAAnB;;SAFqB;mDAItBC,YAJsB,CAAzB;IAMA,OAAOC,QAAP;AACD;SChBeC,YACdC,QAAAA,EACAH,YAAAA;IAEA,MAAMC,QAAQ,GAAGf,2KAAAA,AAAM,EAAvB;IAEA,yKAAOjC,UAAAA,AAAO;+BACZ;YACE,MAAMmD,QAAQ,GAAGD,QAAQ,CAACF,QAAQ,CAACd,OAAV,CAAzB;YACAc,QAAQ,CAACd,OAAT,GAAmBiB,QAAnB;YAEA,OAAOA,QAAP;SALU;8BAQZ,CAAC;WAAGJ,YAAJ;KARY,CAAd;AAUD;SCdeK,WACdC,QAAAA;IAKA,MAAMC,eAAe,GAAGxB,QAAQ,CAACuB,QAAD,CAAhC;IACA,MAAMpD,IAAI,qKAAGgC,SAAAA,AAAM,EAAqB,IAArB,CAAnB;IACA,MAAMsB,UAAU,qKAAGpB,cAAAA,AAAW;+CAC3B1B,OAAD;YACE,IAAIA,OAAO,KAAKR,IAAI,CAACiC,OAArB,EAA8B;gBAC5BoB,eAAe,IAAA,IAAf,GAAA,KAAA,IAAAA,eAAe,CAAG7C,OAAH,EAAYR,IAAI,CAACiC,OAAjB,CAAf;;YAGFjC,IAAI,CAACiC,OAAL,GAAezB,OAAf;SAN0B;6CAS5B,EAT4B,CAA9B;IAYA,OAAO;QAACR,IAAD;QAAOsD,UAAP;KAAP;AACD;SCvBeC,YAAeV,KAAAA;IAC7B,MAAM3C,GAAG,qKAAG8B,SAAAA,AAAM,EAAlB;sKAEAJ,YAAAA,AAAS;iCAAC;YACR1B,GAAG,CAAC+B,OAAJ,GAAcY,KAAd;SADO;gCAEN;QAACA,KAAD;KAFM,CAAT;IAIA,OAAO3C,GAAG,CAAC+B,OAAX;AACD;ACRD,IAAIuB,GAAG,GAA2B,CAAA,CAAlC;AAEA,SAAgBC,YAAYC,MAAAA,EAAgBb,KAAAA;IAC1C,yKAAO9C,UAAAA,AAAO;+BAAC;YACb,IAAI8C,KAAJ,EAAW;gBACT,OAAOA,KAAP;;YAGF,MAAMc,EAAE,GAAGH,GAAG,CAACE,MAAD,CAAH,IAAe,IAAf,GAAsB,CAAtB,GAA0BF,GAAG,CAACE,MAAD,CAAH,GAAc,CAAnD;YACAF,GAAG,CAACE,MAAD,CAAH,GAAcC,EAAd;YAEA,OAAUD,MAAV,GAAA,MAAoBC,EAApB;SARY;8BASX;QAACD,MAAD;QAASb,KAAT;KATW,CAAd;AAUD;ACfD,SAASe,kBAAT,CAA4BC,QAA5B;IACE,OAAO,SACLC,MADK;yCAEFC,cAAAA,IAAAA,MAAAA,OAAAA,IAAAA,OAAAA,IAAAA,IAAAA,OAAAA,GAAAA,OAAAA,MAAAA,OAAAA;YAAAA,WAAAA,CAAAA,OAAAA,EAAAA,GAAAA,SAAAA,CAAAA,KAAAA;;QAEH,OAAOA,WAAW,CAACC,MAAZ,CACL,CAACC,WAAD,EAAcC,UAAd;YACE,MAAMC,OAAO,GAAGzD,MAAM,CAACyD,OAAP,CAAeD,UAAf,CAAhB;YAEA,KAAK,MAAM,CAACE,GAAD,EAAMC,eAAN,CAAX,IAAqCF,OAArC,CAA8C;gBAC5C,MAAMtB,KAAK,GAAGoB,WAAW,CAACG,GAAD,CAAzB;gBAEA,IAAIvB,KAAK,IAAI,IAAb,EAAmB;oBACjBoB,WAAW,CAACG,GAAD,CAAX,GAAoBvB,KAAK,GAAGgB,QAAQ,GAAGQ,eAAvC;;;YAIJ,OAAOJ,WAAP;SAZG,EAcL;YACE,GAAGH,MAAAA;SAfA,CAAP;KAJF;AAuBD;AAED,MAAaQ,GAAG,GAAA,WAAA,GAAGV,kBAAkB,CAAC,CAAD,CAA9B;AACP,MAAaW,QAAQ,GAAA,WAAA,GAAGX,kBAAkB,CAAC,CAAC,CAAF,CAAnC;SC3BSY,+BACdC,KAAAA;IAEA,OAAO,aAAaA,KAAb,IAAsB,aAAaA,KAA1C;AACD;SCFeC,gBACdD,KAAAA;IAEA,IAAI,CAACA,KAAL,EAAY;QACV,OAAO,KAAP;;IAGF,MAAM,EAACE,aAAAA,KAAiB5D,SAAS,CAAC0D,KAAK,CAACzD,MAAP,CAAjC;IAEA,OAAO2D,aAAa,IAAIF,KAAK,YAAYE,aAAzC;AACD;SCVeC,aACdH,KAAAA;IAEA,IAAI,CAACA,KAAL,EAAY;QACV,OAAO,KAAP;;IAGF,MAAM,EAACI,UAAAA,KAAc9D,SAAS,CAAC0D,KAAK,CAACzD,MAAP,CAA9B;IAEA,OAAO6D,UAAU,IAAIJ,KAAK,YAAYI,UAAtC;AACD;ACTD;;IAGA,SAAgBC,oBAAoBL,KAAAA;IAClC,IAAIG,YAAY,CAACH,KAAD,CAAhB,EAAyB;QACvB,IAAIA,KAAK,CAACM,OAAN,IAAiBN,KAAK,CAACM,OAAN,CAAcC,MAAnC,EAA2C;YACzC,MAAM,EAACC,OAAO,EAAEC,CAAV,EAAaC,OAAO,EAAEC,CAAAA,KAAKX,KAAK,CAACM,OAAN,CAAc,CAAd,CAAjC;YAEA,OAAO;gBACLG,CADK;gBAELE;aAFF;SAHF,MAOO,IAAIX,KAAK,CAACY,cAAN,IAAwBZ,KAAK,CAACY,cAAN,CAAqBL,MAAjD,EAAyD;YAC9D,MAAM,EAACC,OAAO,EAAEC,CAAV,EAAaC,OAAO,EAAEC,CAAAA,KAAKX,KAAK,CAACY,cAAN,CAAqB,CAArB,CAAjC;YAEA,OAAO;gBACLH,CADK;gBAELE;aAFF;;;IAOJ,IAAIZ,8BAA8B,CAACC,KAAD,CAAlC,EAA2C;QACzC,OAAO;YACLS,CAAC,EAAET,KAAK,CAACQ,OADJ;YAELG,CAAC,EAAEX,KAAK,CAACU,OAAAA;SAFX;;IAMF,OAAO,IAAP;AACD;MCpBYG,GAAG,GAAA,WAAA,GAAG5E,MAAM,CAAC6E,MAAP,CAAc;IAC/BC,SAAS,EAAE;QACT5E,QAAQ,EAAC6E,SAAD;YACN,IAAI,CAACA,SAAL,EAAgB;gBACd;;YAGF,MAAM,EAACP,CAAD,EAAIE,CAAAA,KAAKK,SAAf;YAEA,OAAA,iBAAA,CAAsBP,CAAC,GAAGQ,IAAI,CAACC,KAAL,CAAWT,CAAX,CAAH,GAAmB,CAA1C,IAAA,SAAA,CACEE,CAAC,GAAGM,IAAI,CAACC,KAAL,CAAWP,CAAX,CAAH,GAAmB,CADtB,IAAA;;KAT2B;IAc/BQ,KAAK,EAAE;QACLhF,QAAQ,EAAC6E,SAAD;YACN,IAAI,CAACA,SAAL,EAAgB;gBACd;;YAGF,MAAM,EAACI,MAAD,EAASC,MAAAA,KAAUL,SAAzB;YAEA,OAAA,YAAiBI,MAAjB,GAAA,cAAmCC,MAAnC,GAAA;;KAtB2B;IAyB/BC,SAAS,EAAE;QACTnF,QAAQ,EAAC6E,SAAD;YACN,IAAI,CAACA,SAAL,EAAgB;gBACd;;YAGF,OAAO;gBACLH,GAAG,CAACE,SAAJ,CAAc5E,QAAd,CAAuB6E,SAAvB,CADK;gBAELH,GAAG,CAACM,KAAJ,CAAUhF,QAAV,CAAmB6E,SAAnB,CAFK;aAAA,CAGLO,IAHK,CAGA,GAHA,CAAP;;KA/B2B;IAqC/BC,UAAU,EAAE;QACVrF,QAAQ,EAAA,IAAA;gBAAC,EAACsF,QAAD,EAAW1D,QAAX,EAAqB2D,MAAAA;YAC5B,OAAUD,QAAV,GAAA,MAAsB1D,QAAtB,GAAA,QAAoC2D,MAApC;;;AAvC2B,CAAd,CAAZ;ACbP,MAAMC,QAAQ,GACZ,wIADF;AAGA,SAAgBC,uBACd7F,OAAAA;IAEA,IAAIA,OAAO,CAAC8F,OAAR,CAAgBF,QAAhB,CAAJ,EAA+B;QAC7B,OAAO5F,OAAP;;IAGF,OAAOA,OAAO,CAAC+F,aAAR,CAAsBH,QAAtB,CAAP;AACD", "ignoreList": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23], "debugId": null}}, {"offset": {"line": 3546, "column": 0}, "map": {"version": 3, "file": "accessibility.esm.js", "sources": ["file:///Users/<USER>/Documents/AI_Projects/CRMcriadores/crm-interno/node_modules/%40dnd-kit/accessibility/src/components/HiddenText/HiddenText.tsx", "file:///Users/<USER>/Documents/AI_Projects/CRMcriadores/crm-interno/node_modules/%40dnd-kit/accessibility/src/components/LiveRegion/LiveRegion.tsx", "file:///Users/<USER>/Documents/AI_Projects/CRMcriadores/crm-interno/node_modules/%40dnd-kit/accessibility/src/hooks/useAnnouncement.ts"], "sourcesContent": ["import React from 'react';\n\ninterface Props {\n  id: string;\n  value: string;\n}\n\nconst hiddenStyles: React.CSSProperties = {\n  display: 'none',\n};\n\nexport function HiddenText({id, value}: Props) {\n  return (\n    <div id={id} style={hiddenStyles}>\n      {value}\n    </div>\n  );\n}\n", "import React from 'react';\n\nexport interface Props {\n  id: string;\n  announcement: string;\n  ariaLiveType?: \"polite\" | \"assertive\" | \"off\";\n}\n\nexport function LiveRegion({id, announcement, ariaLiveType = \"assertive\"}: Props) {\n  // Hide element visually but keep it readable by screen readers\n  const visuallyHidden: React.CSSProperties = {\n    position: 'fixed',\n    top: 0,\n    left: 0,\n    width: 1,\n    height: 1,\n    margin: -1,\n    border: 0,\n    padding: 0,\n    overflow: 'hidden',\n    clip: 'rect(0 0 0 0)',\n    clipPath: 'inset(100%)',\n    whiteSpace: 'nowrap',\n  };\n  \n  return (\n    <div\n      id={id}\n      style={visuallyHidden}\n      role=\"status\"\n      aria-live={ariaLiveType}\n      aria-atomic\n    >\n      {announcement}\n    </div>\n  );\n}\n", "import {useCallback, useState} from 'react';\n\nexport function useAnnouncement() {\n  const [announcement, setAnnouncement] = useState('');\n  const announce = useCallback((value: string | undefined) => {\n    if (value != null) {\n      setAnnouncement(value);\n    }\n  }, []);\n\n  return {announce, announcement} as const;\n}\n"], "names": ["hiddenStyles", "display", "HiddenText", "id", "value", "React", "style", "LiveRegion", "announcement", "ariaLiveType", "visuallyHidden", "position", "top", "left", "width", "height", "margin", "border", "padding", "overflow", "clip", "clipPath", "whiteSpace", "role", "useAnnouncement", "setAnnouncement", "useState", "announce", "useCallback"], "mappings": ";;;;;;;AAOA,MAAMA,YAAY,GAAwB;IACxCC,OAAO,EAAE;AAD+B,CAA1C;SAIgBC,WAAAA,IAAAA;QAAW,EAACC,EAAD,EAAKC,KAAAA;IAC9B,OACEC,wKAAAA,CAAAA,aAAA,CAAA,KAAA,EAAA;QAAKF,EAAE,EAAEA;QAAIG,KAAK,EAAEN;KAApB,EACGI,KADH,CADF;AAKD;SCTeG,WAAAA,IAAAA;QAAW,EAACJ,EAAD,EAAKK,YAAL,EAAmBC,YAAY,GAAG,WAAA;;IAE3D,MAAMC,cAAc,GAAwB;QAC1CC,QAAQ,EAAE,OADgC;QAE1CC,GAAG,EAAE,CAFqC;QAG1CC,IAAI,EAAE,CAHoC;QAI1CC,KAAK,EAAE,CAJmC;QAK1CC,MAAM,EAAE,CALkC;QAM1CC,MAAM,EAAE,CAAC,CANiC;QAO1CC,MAAM,EAAE,CAPkC;QAQ1CC,OAAO,EAAE,CARiC;QAS1CC,QAAQ,EAAE,QATgC;QAU1CC,IAAI,EAAE,eAVoC;QAW1CC,QAAQ,EAAE,aAXgC;QAY1CC,UAAU,EAAE;KAZd;IAeA,OACEjB,wKAAAA,CAAAA,aAAA,CAAA,KAAA,EAAA;QACEF,EAAE,EAAEA;QACJG,KAAK,EAAEI;QACPa,IAAI,EAAC;qBACMd;;KAJb,EAOGD,YAPH,CADF;AAWD;SClCegB;IACd,MAAM,CAAChB,YAAD,EAAeiB,eAAf,CAAA,qKAAkCC,WAAAA,AAAQ,EAAC,EAAD,CAAhD;IACA,MAAMC,QAAQ,qKAAGC,cAAAA,AAAW;kDAAExB,KAAD;YAC3B,IAAIA,KAAK,IAAI,IAAb,EAAmB;gBACjBqB,eAAe,CAACrB,KAAD,CAAf;;SAFwB;gDAIzB,EAJyB,CAA5B;IAMA,OAAO;QAACuB,QAAD;QAAWnB;KAAlB;AACD", "ignoreList": [0, 1, 2], "debugId": null}}, {"offset": {"line": 3610, "column": 0}, "map": {"version": 3, "file": "core.esm.js", "sources": ["file:///Users/<USER>/Documents/AI_Projects/CRMcriadores/crm-interno/node_modules/%40dnd-kit/core/src/components/DndMonitor/context.ts", "file:///Users/<USER>/Documents/AI_Projects/CRMcriadores/crm-interno/node_modules/%40dnd-kit/core/src/components/DndMonitor/useDndMonitor.ts", "file:///Users/<USER>/Documents/AI_Projects/CRMcriadores/crm-interno/node_modules/%40dnd-kit/core/src/components/DndMonitor/useDndMonitorProvider.tsx", "file:///Users/<USER>/Documents/AI_Projects/CRMcriadores/crm-interno/node_modules/%40dnd-kit/core/src/components/Accessibility/defaults.ts", "file:///Users/<USER>/Documents/AI_Projects/CRMcriadores/crm-interno/node_modules/%40dnd-kit/core/src/components/Accessibility/Accessibility.tsx", "file:///Users/<USER>/Documents/AI_Projects/CRMcriadores/crm-interno/node_modules/%40dnd-kit/core/src/store/actions.ts", "file:///Users/<USER>/Documents/AI_Projects/CRMcriadores/crm-interno/node_modules/%40dnd-kit/core/src/utilities/other/noop.ts", "file:///Users/<USER>/Documents/AI_Projects/CRMcriadores/crm-interno/node_modules/%40dnd-kit/core/src/sensors/useSensor.ts", "file:///Users/<USER>/Documents/AI_Projects/CRMcriadores/crm-interno/node_modules/%40dnd-kit/core/src/sensors/useSensors.ts", "file:///Users/<USER>/Documents/AI_Projects/CRMcriadores/crm-interno/node_modules/%40dnd-kit/core/src/utilities/coordinates/constants.ts", "file:///Users/<USER>/Documents/AI_Projects/CRMcriadores/crm-interno/node_modules/%40dnd-kit/core/src/utilities/coordinates/distanceBetweenPoints.ts", "file:///Users/<USER>/Documents/AI_Projects/CRMcriadores/crm-interno/node_modules/%40dnd-kit/core/src/utilities/coordinates/getRelativeTransformOrigin.ts", "file:///Users/<USER>/Documents/AI_Projects/CRMcriadores/crm-interno/node_modules/%40dnd-kit/core/src/utilities/algorithms/helpers.ts", "file:///Users/<USER>/Documents/AI_Projects/CRMcriadores/crm-interno/node_modules/%40dnd-kit/core/src/utilities/algorithms/closestCenter.ts", "file:///Users/<USER>/Documents/AI_Projects/CRMcriadores/crm-interno/node_modules/%40dnd-kit/core/src/utilities/algorithms/closestCorners.ts", "file:///Users/<USER>/Documents/AI_Projects/CRMcriadores/crm-interno/node_modules/%40dnd-kit/core/src/utilities/algorithms/rectIntersection.ts", "file:///Users/<USER>/Documents/AI_Projects/CRMcriadores/crm-interno/node_modules/%40dnd-kit/core/src/utilities/algorithms/pointerWithin.ts", "file:///Users/<USER>/Documents/AI_Projects/CRMcriadores/crm-interno/node_modules/%40dnd-kit/core/src/utilities/rect/adjustScale.ts", "file:///Users/<USER>/Documents/AI_Projects/CRMcriadores/crm-interno/node_modules/%40dnd-kit/core/src/utilities/rect/getRectDelta.ts", "file:///Users/<USER>/Documents/AI_Projects/CRMcriadores/crm-interno/node_modules/%40dnd-kit/core/src/utilities/rect/rectAdjustment.ts", "file:///Users/<USER>/Documents/AI_Projects/CRMcriadores/crm-interno/node_modules/%40dnd-kit/core/src/utilities/transform/parseTransform.ts", "file:///Users/<USER>/Documents/AI_Projects/CRMcriadores/crm-interno/node_modules/%40dnd-kit/core/src/utilities/transform/inverseTransform.ts", "file:///Users/<USER>/Documents/AI_Projects/CRMcriadores/crm-interno/node_modules/%40dnd-kit/core/src/utilities/rect/getRect.ts", "file:///Users/<USER>/Documents/AI_Projects/CRMcriadores/crm-interno/node_modules/%40dnd-kit/core/src/utilities/rect/getWindowClientRect.ts", "file:///Users/<USER>/Documents/AI_Projects/CRMcriadores/crm-interno/node_modules/%40dnd-kit/core/src/utilities/scroll/isFixed.ts", "file:///Users/<USER>/Documents/AI_Projects/CRMcriadores/crm-interno/node_modules/%40dnd-kit/core/src/utilities/scroll/isScrollable.ts", "file:///Users/<USER>/Documents/AI_Projects/CRMcriadores/crm-interno/node_modules/%40dnd-kit/core/src/utilities/scroll/getScrollableAncestors.ts", "file:///Users/<USER>/Documents/AI_Projects/CRMcriadores/crm-interno/node_modules/%40dnd-kit/core/src/utilities/scroll/getScrollableElement.ts", "file:///Users/<USER>/Documents/AI_Projects/CRMcriadores/crm-interno/node_modules/%40dnd-kit/core/src/utilities/scroll/getScrollCoordinates.ts", "file:///Users/<USER>/Documents/AI_Projects/CRMcriadores/crm-interno/node_modules/%40dnd-kit/core/src/types/direction.ts", "file:///Users/<USER>/Documents/AI_Projects/CRMcriadores/crm-interno/node_modules/%40dnd-kit/core/src/utilities/scroll/documentScrollingElement.ts", "file:///Users/<USER>/Documents/AI_Projects/CRMcriadores/crm-interno/node_modules/%40dnd-kit/core/src/utilities/scroll/getScrollPosition.ts", "file:///Users/<USER>/Documents/AI_Projects/CRMcriadores/crm-interno/node_modules/%40dnd-kit/core/src/utilities/scroll/getScrollDirectionAndSpeed.ts", "file:///Users/<USER>/Documents/AI_Projects/CRMcriadores/crm-interno/node_modules/%40dnd-kit/core/src/utilities/scroll/getScrollElementRect.ts", "file:///Users/<USER>/Documents/AI_Projects/CRMcriadores/crm-interno/node_modules/%40dnd-kit/core/src/utilities/scroll/getScrollOffsets.ts", "file:///Users/<USER>/Documents/AI_Projects/CRMcriadores/crm-interno/node_modules/%40dnd-kit/core/src/utilities/scroll/scrollIntoViewIfNeeded.ts", "file:///Users/<USER>/Documents/AI_Projects/CRMcriadores/crm-interno/node_modules/%40dnd-kit/core/src/utilities/rect/Rect.ts", "file:///Users/<USER>/Documents/AI_Projects/CRMcriadores/crm-interno/node_modules/%40dnd-kit/core/src/sensors/utilities/Listeners.ts", "file:///Users/<USER>/Documents/AI_Projects/CRMcriadores/crm-interno/node_modules/%40dnd-kit/core/src/sensors/utilities/getEventListenerTarget.ts", "file:///Users/<USER>/Documents/AI_Projects/CRMcriadores/crm-interno/node_modules/%40dnd-kit/core/src/sensors/utilities/hasExceededDistance.ts", "file:///Users/<USER>/Documents/AI_Projects/CRMcriadores/crm-interno/node_modules/%40dnd-kit/core/src/sensors/events.ts", "file:///Users/<USER>/Documents/AI_Projects/CRMcriadores/crm-interno/node_modules/%40dnd-kit/core/src/sensors/keyboard/types.ts", "file:///Users/<USER>/Documents/AI_Projects/CRMcriadores/crm-interno/node_modules/%40dnd-kit/core/src/sensors/keyboard/defaults.ts", "file:///Users/<USER>/Documents/AI_Projects/CRMcriadores/crm-interno/node_modules/%40dnd-kit/core/src/sensors/keyboard/KeyboardSensor.ts", "file:///Users/<USER>/Documents/AI_Projects/CRMcriadores/crm-interno/node_modules/%40dnd-kit/core/src/sensors/pointer/AbstractPointerSensor.ts", "file:///Users/<USER>/Documents/AI_Projects/CRMcriadores/crm-interno/node_modules/%40dnd-kit/core/src/sensors/pointer/PointerSensor.ts", "file:///Users/<USER>/Documents/AI_Projects/CRMcriadores/crm-interno/node_modules/%40dnd-kit/core/src/sensors/mouse/MouseSensor.ts", "file:///Users/<USER>/Documents/AI_Projects/CRMcriadores/crm-interno/node_modules/%40dnd-kit/core/src/sensors/touch/TouchSensor.ts", "file:///Users/<USER>/Documents/AI_Projects/CRMcriadores/crm-interno/node_modules/%40dnd-kit/core/src/hooks/utilities/useAutoScroller.ts", "file:///Users/<USER>/Documents/AI_Projects/CRMcriadores/crm-interno/node_modules/%40dnd-kit/core/src/hooks/utilities/useCachedNode.ts", "file:///Users/<USER>/Documents/AI_Projects/CRMcriadores/crm-interno/node_modules/%40dnd-kit/core/src/hooks/utilities/useCombineActivators.ts", "file:///Users/<USER>/Documents/AI_Projects/CRMcriadores/crm-interno/node_modules/%40dnd-kit/core/src/hooks/utilities/useDroppableMeasuring.ts", "file:///Users/<USER>/Documents/AI_Projects/CRMcriadores/crm-interno/node_modules/%40dnd-kit/core/src/hooks/utilities/useInitialValue.ts", "file:///Users/<USER>/Documents/AI_Projects/CRMcriadores/crm-interno/node_modules/%40dnd-kit/core/src/hooks/utilities/useInitialRect.ts", "file:///Users/<USER>/Documents/AI_Projects/CRMcriadores/crm-interno/node_modules/%40dnd-kit/core/src/hooks/utilities/useMutationObserver.ts", "file:///Users/<USER>/Documents/AI_Projects/CRMcriadores/crm-interno/node_modules/%40dnd-kit/core/src/hooks/utilities/useResizeObserver.ts", "file:///Users/<USER>/Documents/AI_Projects/CRMcriadores/crm-interno/node_modules/%40dnd-kit/core/src/hooks/utilities/useRect.ts", "file:///Users/<USER>/Documents/AI_Projects/CRMcriadores/crm-interno/node_modules/%40dnd-kit/core/src/hooks/utilities/useRectDelta.ts", "file:///Users/<USER>/Documents/AI_Projects/CRMcriadores/crm-interno/node_modules/%40dnd-kit/core/src/hooks/utilities/useScrollableAncestors.ts", "file:///Users/<USER>/Documents/AI_Projects/CRMcriadores/crm-interno/node_modules/%40dnd-kit/core/src/hooks/utilities/useScrollOffsets.ts", "file:///Users/<USER>/Documents/AI_Projects/CRMcriadores/crm-interno/node_modules/%40dnd-kit/core/src/hooks/utilities/useScrollOffsetsDelta.ts", "file:///Users/<USER>/Documents/AI_Projects/CRMcriadores/crm-interno/node_modules/%40dnd-kit/core/src/hooks/utilities/useSensorSetup.ts", "file:///Users/<USER>/Documents/AI_Projects/CRMcriadores/crm-interno/node_modules/%40dnd-kit/core/src/hooks/utilities/useSyntheticListeners.ts", "file:///Users/<USER>/Documents/AI_Projects/CRMcriadores/crm-interno/node_modules/%40dnd-kit/core/src/hooks/utilities/useWindowRect.ts", "file:///Users/<USER>/Documents/AI_Projects/CRMcriadores/crm-interno/node_modules/%40dnd-kit/core/src/hooks/utilities/useRects.ts", "file:///Users/<USER>/Documents/AI_Projects/CRMcriadores/crm-interno/node_modules/%40dnd-kit/core/src/utilities/nodes/getMeasurableNode.ts", "file:///Users/<USER>/Documents/AI_Projects/CRMcriadores/crm-interno/node_modules/%40dnd-kit/core/src/hooks/utilities/useDragOverlayMeasuring.ts", "file:///Users/<USER>/Documents/AI_Projects/CRMcriadores/crm-interno/node_modules/%40dnd-kit/core/src/components/DndContext/defaults.ts", "file:///Users/<USER>/Documents/AI_Projects/CRMcriadores/crm-interno/node_modules/%40dnd-kit/core/src/store/constructors.ts", "file:///Users/<USER>/Documents/AI_Projects/CRMcriadores/crm-interno/node_modules/%40dnd-kit/core/src/store/context.ts", "file:///Users/<USER>/Documents/AI_Projects/CRMcriadores/crm-interno/node_modules/%40dnd-kit/core/src/store/reducer.ts", "file:///Users/<USER>/Documents/AI_Projects/CRMcriadores/crm-interno/node_modules/%40dnd-kit/core/src/components/Accessibility/components/RestoreFocus.tsx", "file:///Users/<USER>/Documents/AI_Projects/CRMcriadores/crm-interno/node_modules/%40dnd-kit/core/src/modifiers/applyModifiers.ts", "file:///Users/<USER>/Documents/AI_Projects/CRMcriadores/crm-interno/node_modules/%40dnd-kit/core/src/components/DndContext/hooks/useMeasuringConfiguration.ts", "file:///Users/<USER>/Documents/AI_Projects/CRMcriadores/crm-interno/node_modules/%40dnd-kit/core/src/components/DndContext/hooks/useLayoutShiftScrollCompensation.ts", "file:///Users/<USER>/Documents/AI_Projects/CRMcriadores/crm-interno/node_modules/%40dnd-kit/core/src/components/DndContext/DndContext.tsx", "file:///Users/<USER>/Documents/AI_Projects/CRMcriadores/crm-interno/node_modules/%40dnd-kit/core/src/hooks/useDraggable.ts", "file:///Users/<USER>/Documents/AI_Projects/CRMcriadores/crm-interno/node_modules/%40dnd-kit/core/src/hooks/useDndContext.ts", "file:///Users/<USER>/Documents/AI_Projects/CRMcriadores/crm-interno/node_modules/%40dnd-kit/core/src/hooks/useDroppable.ts", "file:///Users/<USER>/Documents/AI_Projects/CRMcriadores/crm-interno/node_modules/%40dnd-kit/core/src/components/DragOverlay/components/AnimationManager/AnimationManager.tsx", "file:///Users/<USER>/Documents/AI_Projects/CRMcriadores/crm-interno/node_modules/%40dnd-kit/core/src/components/DragOverlay/components/NullifiedContextProvider/NullifiedContextProvider.tsx", "file:///Users/<USER>/Documents/AI_Projects/CRMcriadores/crm-interno/node_modules/%40dnd-kit/core/src/components/DragOverlay/components/PositionedOverlay/PositionedOverlay.tsx", "file:///Users/<USER>/Documents/AI_Projects/CRMcriadores/crm-interno/node_modules/%40dnd-kit/core/src/components/DragOverlay/hooks/useDropAnimation.ts", "file:///Users/<USER>/Documents/AI_Projects/CRMcriadores/crm-interno/node_modules/%40dnd-kit/core/src/components/DragOverlay/hooks/useKey.ts", "file:///Users/<USER>/Documents/AI_Projects/CRMcriadores/crm-interno/node_modules/%40dnd-kit/core/src/components/DragOverlay/DragOverlay.tsx"], "sourcesContent": ["import {createContext} from 'react';\n\nimport type {RegisterListener} from './types';\n\nexport const DndMonitorContext = createContext<RegisterListener | null>(null);\n", "import {useContext, useEffect} from 'react';\n\nimport {DndMonitorContext} from './context';\nimport type {DndMonitorListener} from './types';\n\nexport function useDndMonitor(listener: DndMonitorListener) {\n  const registerListener = useContext(DndMonitorContext);\n\n  useEffect(() => {\n    if (!registerListener) {\n      throw new Error(\n        'useDndMonitor must be used within a children of <DndContext>'\n      );\n    }\n\n    const unsubscribe = registerListener(listener);\n\n    return unsubscribe;\n  }, [listener, registerListener]);\n}\n", "import {useCallback, useState} from 'react';\n\nimport type {DndMonitorListener, DndMonitorEvent} from './types';\n\nexport function useDndMonitorProvider() {\n  const [listeners] = useState(() => new Set<DndMonitorListener>());\n\n  const registerListener = useCallback(\n    (listener) => {\n      listeners.add(listener);\n      return () => listeners.delete(listener);\n    },\n    [listeners]\n  );\n\n  const dispatch = useCallback(\n    ({type, event}: DndMonitorEvent) => {\n      listeners.forEach((listener) => listener[type]?.(event as any));\n    },\n    [listeners]\n  );\n\n  return [dispatch, registerListener] as const;\n}\n", "import type {Announcements, ScreenReaderInstructions} from './types';\n\nexport const defaultScreenReaderInstructions: ScreenReaderInstructions = {\n  draggable: `\n    To pick up a draggable item, press the space bar.\n    While dragging, use the arrow keys to move the item.\n    Press space again to drop the item in its new position, or press escape to cancel.\n  `,\n};\n\nexport const defaultAnnouncements: Announcements = {\n  onDragStart({active}) {\n    return `Picked up draggable item ${active.id}.`;\n  },\n  onDragOver({active, over}) {\n    if (over) {\n      return `Draggable item ${active.id} was moved over droppable area ${over.id}.`;\n    }\n\n    return `Draggable item ${active.id} is no longer over a droppable area.`;\n  },\n  onDragEnd({active, over}) {\n    if (over) {\n      return `Draggable item ${active.id} was dropped over droppable area ${over.id}`;\n    }\n\n    return `Draggable item ${active.id} was dropped.`;\n  },\n  onDragCancel({active}) {\n    return `Dragging was cancelled. Draggable item ${active.id} was dropped.`;\n  },\n};\n", "import React, {useEffect, useMemo, useState} from 'react';\nimport {createPortal} from 'react-dom';\nimport {useUniqueId} from '@dnd-kit/utilities';\nimport {HiddenText, LiveRegion, useAnnouncement} from '@dnd-kit/accessibility';\n\nimport {DndMonitorListener, useDndMonitor} from '../DndMonitor';\n\nimport type {Announcements, ScreenReaderInstructions} from './types';\nimport {\n  defaultAnnouncements,\n  defaultScreenReaderInstructions,\n} from './defaults';\n\ninterface Props {\n  announcements?: Announcements;\n  container?: Element;\n  screenReaderInstructions?: ScreenReaderInstructions;\n  hiddenTextDescribedById: string;\n}\n\nexport function Accessibility({\n  announcements = defaultAnnouncements,\n  container,\n  hiddenTextDescribedById,\n  screenReaderInstructions = defaultScreenReaderInstructions,\n}: Props) {\n  const {announce, announcement} = useAnnouncement();\n  const liveRegionId = useUniqueId(`DndLiveRegion`);\n  const [mounted, setMounted] = useState(false);\n\n  useEffect(() => {\n    setMounted(true);\n  }, []);\n\n  useDndMonitor(\n    useMemo<DndMonitorListener>(\n      () => ({\n        onDragStart({active}) {\n          announce(announcements.onDragStart({active}));\n        },\n        onDragMove({active, over}) {\n          if (announcements.onDragMove) {\n            announce(announcements.onDragMove({active, over}));\n          }\n        },\n        onDragOver({active, over}) {\n          announce(announcements.onDragOver({active, over}));\n        },\n        onDragEnd({active, over}) {\n          announce(announcements.onDragEnd({active, over}));\n        },\n        onDragCancel({active, over}) {\n          announce(announcements.onDragCancel({active, over}));\n        },\n      }),\n      [announce, announcements]\n    )\n  );\n\n  if (!mounted) {\n    return null;\n  }\n\n  const markup = (\n    <>\n      <HiddenText\n        id={hiddenTextDescribedById}\n        value={screenReaderInstructions.draggable}\n      />\n      <LiveRegion id={liveRegionId} announcement={announcement} />\n    </>\n  );\n\n  return container ? createPortal(markup, container) : markup;\n}\n", "import type {Coordinates, UniqueIdentifier} from '../types';\nimport type {DroppableContainer} from './types';\n\nexport enum Action {\n  DragStart = 'dragStart',\n  DragMove = 'dragMove',\n  DragEnd = 'dragEnd',\n  DragCancel = 'dragCancel',\n  DragOver = 'dragOver',\n  RegisterDroppable = 'registerDroppable',\n  SetDroppableDisabled = 'setDroppableDisabled',\n  UnregisterDroppable = 'unregisterDroppable',\n}\n\nexport type Actions =\n  | {\n      type: Action.DragStart;\n      active: UniqueIdentifier;\n      initialCoordinates: Coordinates;\n    }\n  | {type: Action.DragMove; coordinates: Coordinates}\n  | {type: Action.DragEnd}\n  | {type: Action.DragCancel}\n  | {\n      type: Action.RegisterDroppable;\n      element: DroppableContainer;\n    }\n  | {\n      type: Action.SetDroppableDisabled;\n      id: UniqueIdentifier;\n      key: UniqueIdentifier;\n      disabled: boolean;\n    }\n  | {\n      type: Action.UnregisterDroppable;\n      id: UniqueIdentifier;\n      key: UniqueIdentifier;\n    };\n", "export function noop(..._args: any) {}\n", "import {useMemo} from 'react';\n\nimport type {Sensor, SensorDescriptor, SensorOptions} from './types';\n\nexport function useSensor<T extends SensorOptions>(\n  sensor: Sensor<T>,\n  options?: T\n): SensorDescriptor<T> {\n  return useMemo(\n    () => ({\n      sensor,\n      options: options ?? ({} as T),\n    }),\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    [sensor, options]\n  );\n}\n", "import {useMemo} from 'react';\n\nimport type {SensorDescriptor, SensorOptions} from './types';\n\nexport function useSensors(\n  ...sensors: (SensorDescriptor<any> | undefined | null)[]\n): SensorDescriptor<SensorOptions>[] {\n  return useMemo(\n    () =>\n      [...sensors].filter(\n        (sensor): sensor is SensorDescriptor<any> => sensor != null\n      ),\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    [...sensors]\n  );\n}\n", "import type {Coordinates} from '../../types';\n\nexport const defaultCoordinates: Coordinates = Object.freeze({\n  x: 0,\n  y: 0,\n});\n", "import type {Coordinates} from '../../types';\n\n/**\n * Returns the distance between two points\n */\nexport function distanceBetween(p1: Coordinates, p2: Coordinates) {\n  return Math.sqrt(Math.pow(p1.x - p2.x, 2) + Math.pow(p1.y - p2.y, 2));\n}\n", "import {getEventCoordinates} from '@dnd-kit/utilities';\nimport type {ClientRect} from '../../types';\n\nexport function getRelativeTransformOrigin(\n  event: MouseEvent | TouchEvent | KeyboardEvent,\n  rect: ClientRect\n) {\n  const eventCoordinates = getEventCoordinates(event);\n\n  if (!eventCoordinates) {\n    return '0 0';\n  }\n\n  const transformOrigin = {\n    x: ((eventCoordinates.x - rect.left) / rect.width) * 100,\n    y: ((eventCoordinates.y - rect.top) / rect.height) * 100,\n  };\n\n  return `${transformOrigin.x}% ${transformOrigin.y}%`;\n}\n", "/* eslint-disable no-redeclare */\nimport type {ClientRect} from '../../types';\n\nimport type {Collision, CollisionDescriptor} from './types';\n\n/**\n * Sort collisions from smallest to greatest value\n */\nexport function sortCollisionsAsc(\n  {data: {value: a}}: CollisionDescriptor,\n  {data: {value: b}}: CollisionDescriptor\n) {\n  return a - b;\n}\n\n/**\n * Sort collisions from greatest to smallest value\n */\nexport function sortCollisionsDesc(\n  {data: {value: a}}: CollisionDescriptor,\n  {data: {value: b}}: CollisionDescriptor\n) {\n  return b - a;\n}\n\n/**\n * Returns the coordinates of the corners of a given rectangle:\n * [TopLeft {x, y}, TopRight {x, y}, BottomLeft {x, y}, BottomRight {x, y}]\n */\nexport function cornersOfRectangle({left, top, height, width}: ClientRect) {\n  return [\n    {\n      x: left,\n      y: top,\n    },\n    {\n      x: left + width,\n      y: top,\n    },\n    {\n      x: left,\n      y: top + height,\n    },\n    {\n      x: left + width,\n      y: top + height,\n    },\n  ];\n}\n\n/**\n * Returns the first collision, or null if there isn't one.\n * If a property is specified, returns the specified property of the first collision.\n */\nexport function getFirstCollision(\n  collisions: Collision[] | null | undefined\n): Collision | null;\nexport function getFirstCollision<T extends keyof Collision>(\n  collisions: Collision[] | null | undefined,\n  property: T\n): Collision[T] | null;\nexport function getFirstCollision(\n  collisions: Collision[] | null | undefined,\n  property?: keyof Collision\n) {\n  if (!collisions || collisions.length === 0) {\n    return null;\n  }\n\n  const [firstCollision] = collisions;\n\n  return property ? firstCollision[property] : firstCollision;\n}\n", "import {distanceBetween} from '../coordinates';\nimport type {Coordinates, ClientRect} from '../../types';\n\nimport type {CollisionDescriptor, CollisionDetection} from './types';\nimport {sortCollisionsAsc} from './helpers';\n\n/**\n * Returns the coordinates of the center of a given ClientRect\n */\nfunction centerOfRectangle(\n  rect: ClientRect,\n  left = rect.left,\n  top = rect.top\n): Coordinates {\n  return {\n    x: left + rect.width * 0.5,\n    y: top + rect.height * 0.5,\n  };\n}\n\n/**\n * Returns the closest rectangles from an array of rectangles to the center of a given\n * rectangle.\n */\nexport const closestCenter: CollisionDetection = ({\n  collisionRect,\n  droppableRects,\n  droppableContainers,\n}) => {\n  const centerRect = centerOfRectangle(\n    collisionRect,\n    collisionRect.left,\n    collisionRect.top\n  );\n  const collisions: CollisionDescriptor[] = [];\n\n  for (const droppableContainer of droppableContainers) {\n    const {id} = droppableContainer;\n    const rect = droppableRects.get(id);\n\n    if (rect) {\n      const distBetween = distanceBetween(centerOfRectangle(rect), centerRect);\n\n      collisions.push({id, data: {droppableContainer, value: distBetween}});\n    }\n  }\n\n  return collisions.sort(sortCollisionsAsc);\n};\n", "import {distanceBetween} from '../coordinates';\n\nimport type {CollisionDescriptor, CollisionDetection} from './types';\nimport {cornersOfRectangle, sortCollisionsAsc} from './helpers';\n\n/**\n * Returns the closest rectangles from an array of rectangles to the corners of\n * another rectangle.\n */\nexport const closestCorners: CollisionDetection = ({\n  collisionRect,\n  droppableRects,\n  droppableContainers,\n}) => {\n  const corners = cornersOfRectangle(collisionRect);\n  const collisions: CollisionDescriptor[] = [];\n\n  for (const droppableContainer of droppableContainers) {\n    const {id} = droppableContainer;\n    const rect = droppableRects.get(id);\n\n    if (rect) {\n      const rectCorners = cornersOfRectangle(rect);\n      const distances = corners.reduce((accumulator, corner, index) => {\n        return accumulator + distanceBetween(rectCorners[index], corner);\n      }, 0);\n      const effectiveDistance = Number((distances / 4).toFixed(4));\n\n      collisions.push({\n        id,\n        data: {droppableContainer, value: effectiveDistance},\n      });\n    }\n  }\n\n  return collisions.sort(sortCollisionsAsc);\n};\n", "import type {ClientRect} from '../../types';\n\nimport type {CollisionDescriptor, CollisionDetection} from './types';\nimport {sortCollisionsDesc} from './helpers';\n\n/**\n * Returns the intersecting rectangle area between two rectangles\n */\nexport function getIntersectionRatio(\n  entry: ClientRect,\n  target: ClientRect\n): number {\n  const top = Math.max(target.top, entry.top);\n  const left = Math.max(target.left, entry.left);\n  const right = Math.min(target.left + target.width, entry.left + entry.width);\n  const bottom = Math.min(target.top + target.height, entry.top + entry.height);\n  const width = right - left;\n  const height = bottom - top;\n\n  if (left < right && top < bottom) {\n    const targetArea = target.width * target.height;\n    const entryArea = entry.width * entry.height;\n    const intersectionArea = width * height;\n    const intersectionRatio =\n      intersectionArea / (targetArea + entryArea - intersectionArea);\n\n    return Number(intersectionRatio.toFixed(4));\n  }\n\n  // Rectangles do not overlap, or overlap has an area of zero (edge/corner overlap)\n  return 0;\n}\n\n/**\n * Returns the rectangles that has the greatest intersection area with a given\n * rectangle in an array of rectangles.\n */\nexport const rectIntersection: CollisionDetection = ({\n  collisionRect,\n  droppableRects,\n  droppableContainers,\n}) => {\n  const collisions: CollisionDescriptor[] = [];\n\n  for (const droppableContainer of droppableContainers) {\n    const {id} = droppableContainer;\n    const rect = droppableRects.get(id);\n\n    if (rect) {\n      const intersectionRatio = getIntersectionRatio(rect, collisionRect);\n\n      if (intersectionRatio > 0) {\n        collisions.push({\n          id,\n          data: {droppableContainer, value: intersectionRatio},\n        });\n      }\n    }\n  }\n\n  return collisions.sort(sortCollisionsDesc);\n};\n", "import type {Coordinates, ClientRect} from '../../types';\nimport {distanceBetween} from '../coordinates';\n\nimport type {CollisionDescriptor, CollisionDetection} from './types';\nimport {cornersOfRectangle, sortCollisionsAsc} from './helpers';\n\n/**\n * Check if a given point is contained within a bounding rectangle\n */\nfunction isPointWithinRect(point: Coordinates, rect: ClientRect): boolean {\n  const {top, left, bottom, right} = rect;\n\n  return (\n    top <= point.y && point.y <= bottom && left <= point.x && point.x <= right\n  );\n}\n\n/**\n * Returns the rectangles that the pointer is hovering over\n */\nexport const pointerWithin: CollisionDetection = ({\n  droppableContainers,\n  droppableRects,\n  pointerCoordinates,\n}) => {\n  if (!pointerCoordinates) {\n    return [];\n  }\n\n  const collisions: CollisionDescriptor[] = [];\n\n  for (const droppableContainer of droppableContainers) {\n    const {id} = droppableContainer;\n    const rect = droppableRects.get(id);\n\n    if (rect && isPointWithinRect(pointerCoordinates, rect)) {\n      /* There may be more than a single rectangle intersecting\n       * with the pointer coordinates. In order to sort the\n       * colliding rectangles, we measure the distance between\n       * the pointer and the corners of the intersecting rectangle\n       */\n      const corners = cornersOfRectangle(rect);\n      const distances = corners.reduce((accumulator, corner) => {\n        return accumulator + distanceBetween(pointerCoordinates, corner);\n      }, 0);\n      const effectiveDistance = Number((distances / 4).toFixed(4));\n\n      collisions.push({\n        id,\n        data: {droppableContainer, value: effectiveDistance},\n      });\n    }\n  }\n\n  return collisions.sort(sortCollisionsAsc);\n};\n", "import type {Transform} from '@dnd-kit/utilities';\nimport type {ClientRect} from '../../types';\n\nexport function adjustScale(\n  transform: Transform,\n  rect1: ClientRect | null,\n  rect2: ClientRect | null\n): Transform {\n  return {\n    ...transform,\n    scaleX: rect1 && rect2 ? rect1.width / rect2.width : 1,\n    scaleY: rect1 && rect2 ? rect1.height / rect2.height : 1,\n  };\n}\n", "import type {Coordinates, ClientRect} from '../../types';\nimport {defaultCoordinates} from '../coordinates';\n\nexport function getRectDelta(\n  rect1: ClientRect | null,\n  rect2: ClientRect | null\n): Coordinates {\n  return rect1 && rect2\n    ? {\n        x: rect1.left - rect2.left,\n        y: rect1.top - rect2.top,\n      }\n    : defaultCoordinates;\n}\n", "import type {Coordinates, ClientRect} from '../../types';\n\nexport function createRectAdjustmentFn(modifier: number) {\n  return function adjustClientRect(\n    rect: ClientRect,\n    ...adjustments: Coordinates[]\n  ): ClientRect {\n    return adjustments.reduce<ClientRect>(\n      (acc, adjustment) => ({\n        ...acc,\n        top: acc.top + modifier * adjustment.y,\n        bottom: acc.bottom + modifier * adjustment.y,\n        left: acc.left + modifier * adjustment.x,\n        right: acc.right + modifier * adjustment.x,\n      }),\n      {...rect}\n    );\n  };\n}\n\nexport const getAdjustedRect = createRectAdjustmentFn(1);\n", "import type {Transform} from '@dnd-kit/utilities';\n\nexport function parseTransform(transform: string): Transform | null {\n  if (transform.startsWith('matrix3d(')) {\n    const transformArray = transform.slice(9, -1).split(/, /);\n\n    return {\n      x: +transformArray[12],\n      y: +transformArray[13],\n      scaleX: +transformArray[0],\n      scaleY: +transformArray[5],\n    };\n  } else if (transform.startsWith('matrix(')) {\n    const transformArray = transform.slice(7, -1).split(/, /);\n\n    return {\n      x: +transformArray[4],\n      y: +transformArray[5],\n      scaleX: +transformArray[0],\n      scaleY: +transformArray[3],\n    };\n  }\n\n  return null;\n}\n", "import type {ClientRect} from '../../types';\n\nimport {parseTransform} from './parseTransform';\n\nexport function inverseTransform(\n  rect: ClientRect,\n  transform: string,\n  transformOrigin: string\n): ClientRect {\n  const parsedTransform = parseTransform(transform);\n\n  if (!parsedTransform) {\n    return rect;\n  }\n\n  const {scaleX, scaleY, x: translateX, y: translateY} = parsedTransform;\n\n  const x = rect.left - translateX - (1 - scaleX) * parseFloat(transformOrigin);\n  const y =\n    rect.top -\n    translateY -\n    (1 - scaleY) *\n      parseFloat(transformOrigin.slice(transformOrigin.indexOf(' ') + 1));\n  const w = scaleX ? rect.width / scaleX : rect.width;\n  const h = scaleY ? rect.height / scaleY : rect.height;\n\n  return {\n    width: w,\n    height: h,\n    top: y,\n    right: x + w,\n    bottom: y + h,\n    left: x,\n  };\n}\n", "import {getWindow} from '@dnd-kit/utilities';\n\nimport type {ClientRect} from '../../types';\nimport {inverseTransform} from '../transform';\n\ninterface Options {\n  ignoreTransform?: boolean;\n}\n\nconst defaultOptions: Options = {ignoreTransform: false};\n\n/**\n * Returns the bounding client rect of an element relative to the viewport.\n */\nexport function getClientRect(\n  element: Element,\n  options: Options = defaultOptions\n) {\n  let rect: ClientRect = element.getBoundingClientRect();\n\n  if (options.ignoreTransform) {\n    const {transform, transformOrigin} =\n      getWindow(element).getComputedStyle(element);\n\n    if (transform) {\n      rect = inverseTransform(rect, transform, transformOrigin);\n    }\n  }\n\n  const {top, left, width, height, bottom, right} = rect;\n\n  return {\n    top,\n    left,\n    width,\n    height,\n    bottom,\n    right,\n  };\n}\n\n/**\n * Returns the bounding client rect of an element relative to the viewport.\n *\n * @remarks\n * The ClientRect returned by this method does not take into account transforms\n * applied to the element it measures.\n *\n */\nexport function getTransformAgnosticClientRect(element: Element): ClientRect {\n  return getClientRect(element, {ignoreTransform: true});\n}\n", "import type {ClientRect} from '../../types';\n\nexport function getWindowClientRect(element: typeof window): ClientRect {\n  const width = element.innerWidth;\n  const height = element.innerHeight;\n\n  return {\n    top: 0,\n    left: 0,\n    right: width,\n    bottom: height,\n    width,\n    height,\n  };\n}\n", "import {getWindow} from '@dnd-kit/utilities';\n\nexport function isFixed(\n  node: HTMLElement,\n  computedStyle: CSSStyleDeclaration = getWindow(node).getComputedStyle(node)\n): boolean {\n  return computedStyle.position === 'fixed';\n}\n", "import {getWindow} from '@dnd-kit/utilities';\n\nexport function isScrollable(\n  element: HTMLElement,\n  computedStyle: CSSStyleDeclaration = getWindow(element).getComputedStyle(\n    element\n  )\n): boolean {\n  const overflowRegex = /(auto|scroll|overlay)/;\n  const properties = ['overflow', 'overflowX', 'overflowY'];\n\n  return properties.some((property) => {\n    const value = computedStyle[property as keyof CSSStyleDeclaration];\n\n    return typeof value === 'string' ? overflowRegex.test(value) : false;\n  });\n}\n", "import {\n  getWindow,\n  isDocument,\n  isHTMLElement,\n  isSVGElement,\n} from '@dnd-kit/utilities';\n\nimport {isFixed} from './isFixed';\nimport {isScrollable} from './isScrollable';\n\nexport function getScrollableAncestors(\n  element: Node | null,\n  limit?: number\n): Element[] {\n  const scrollParents: Element[] = [];\n\n  function findScrollableAncestors(node: Node | null): Element[] {\n    if (limit != null && scrollParents.length >= limit) {\n      return scrollParents;\n    }\n\n    if (!node) {\n      return scrollParents;\n    }\n\n    if (\n      isDocument(node) &&\n      node.scrollingElement != null &&\n      !scrollParents.includes(node.scrollingElement)\n    ) {\n      scrollParents.push(node.scrollingElement);\n\n      return scrollParents;\n    }\n\n    if (!isHTMLElement(node) || isSVGElement(node)) {\n      return scrollParents;\n    }\n\n    if (scrollParents.includes(node)) {\n      return scrollParents;\n    }\n\n    const computedStyle = getWindow(element).getComputedStyle(node);\n\n    if (node !== element) {\n      if (isScrollable(node, computedStyle)) {\n        scrollParents.push(node);\n      }\n    }\n\n    if (isFixed(node, computedStyle)) {\n      return scrollParents;\n    }\n\n    return findScrollableAncestors(node.parentNode);\n  }\n\n  if (!element) {\n    return scrollParents;\n  }\n\n  return findScrollableAncestors(element);\n}\n\nexport function getFirstScrollableAncestor(node: Node | null): Element | null {\n  const [firstScrollableAncestor] = getScrollableAncestors(node, 1);\n\n  return firstScrollableAncestor ?? null;\n}\n", "import {\n  canUseDOM,\n  isHTMLElement,\n  isDocument,\n  getOwnerDocument,\n  isNode,\n  isWindow,\n} from '@dnd-kit/utilities';\n\nexport function getScrollableElement(element: EventTarget | null) {\n  if (!canUseDOM || !element) {\n    return null;\n  }\n\n  if (isWindow(element)) {\n    return element;\n  }\n\n  if (!isNode(element)) {\n    return null;\n  }\n\n  if (\n    isDocument(element) ||\n    element === getOwnerDocument(element).scrollingElement\n  ) {\n    return window;\n  }\n\n  if (isHTMLElement(element)) {\n    return element;\n  }\n\n  return null;\n}\n", "import {isWindow} from '@dnd-kit/utilities';\n\nimport type {Coordinates} from '../../types';\n\nexport function getScrollXCoordinate(element: Element | typeof window): number {\n  if (isWindow(element)) {\n    return element.scrollX;\n  }\n\n  return element.scrollLeft;\n}\n\nexport function getScrollYCoordinate(element: Element | typeof window): number {\n  if (isWindow(element)) {\n    return element.scrollY;\n  }\n\n  return element.scrollTop;\n}\n\nexport function getScrollCoordinates(\n  element: Element | typeof window\n): Coordinates {\n  return {\n    x: getScrollXCoordinate(element),\n    y: getScrollYCoordinate(element),\n  };\n}\n", "export enum Direction {\n  Forward = 1,\n  Backward = -1,\n}\n", "import {canUseDOM} from '@dnd-kit/utilities';\n\nexport function isDocumentScrollingElement(element: Element | null) {\n  if (!canUseDOM || !element) {\n    return false;\n  }\n\n  return element === document.scrollingElement;\n}\n", "import {isDocumentScrollingElement} from './documentScrollingElement';\n\nexport function getScrollPosition(scrollingContainer: Element) {\n  const minScroll = {\n    x: 0,\n    y: 0,\n  };\n  const dimensions = isDocumentScrollingElement(scrollingContainer)\n    ? {\n        height: window.innerHeight,\n        width: window.innerWidth,\n      }\n    : {\n        height: scrollingContainer.clientHeight,\n        width: scrollingContainer.clientWidth,\n      };\n  const maxScroll = {\n    x: scrollingContainer.scrollWidth - dimensions.width,\n    y: scrollingContainer.scrollHeight - dimensions.height,\n  };\n\n  const isTop = scrollingContainer.scrollTop <= minScroll.y;\n  const isLeft = scrollingContainer.scrollLeft <= minScroll.x;\n  const isBottom = scrollingContainer.scrollTop >= maxScroll.y;\n  const isRight = scrollingContainer.scrollLeft >= maxScroll.x;\n\n  return {\n    isTop,\n    isLeft,\n    isBottom,\n    isRight,\n    maxScroll,\n    minScroll,\n  };\n}\n", "import {Direction, ClientRect} from '../../types';\nimport {getScrollPosition} from './getScrollPosition';\n\ninterface PositionalCoordinates\n  extends Pick<ClientRect, 'top' | 'left' | 'right' | 'bottom'> {}\n\nconst defaultThreshold = {\n  x: 0.2,\n  y: 0.2,\n};\n\nexport function getScrollDirectionAndSpeed(\n  scrollContainer: Element,\n  scrollContainerRect: ClientRect,\n  {top, left, right, bottom}: PositionalCoordinates,\n  acceleration = 10,\n  thresholdPercentage = defaultThreshold\n) {\n  const {isTop, isBottom, isLeft, isRight} = getScrollPosition(scrollContainer);\n\n  const direction = {\n    x: 0,\n    y: 0,\n  };\n  const speed = {\n    x: 0,\n    y: 0,\n  };\n  const threshold = {\n    height: scrollContainerRect.height * thresholdPercentage.y,\n    width: scrollContainerRect.width * thresholdPercentage.x,\n  };\n\n  if (!isTop && top <= scrollContainerRect.top + threshold.height) {\n    // Scroll Up\n    direction.y = Direction.Backward;\n    speed.y =\n      acceleration *\n      Math.abs(\n        (scrollContainerRect.top + threshold.height - top) / threshold.height\n      );\n  } else if (\n    !isBottom &&\n    bottom >= scrollContainerRect.bottom - threshold.height\n  ) {\n    // Scroll Down\n    direction.y = Direction.Forward;\n    speed.y =\n      acceleration *\n      Math.abs(\n        (scrollContainerRect.bottom - threshold.height - bottom) /\n          threshold.height\n      );\n  }\n\n  if (!isRight && right >= scrollContainerRect.right - threshold.width) {\n    // Scroll Right\n    direction.x = Direction.Forward;\n    speed.x =\n      acceleration *\n      Math.abs(\n        (scrollContainerRect.right - threshold.width - right) / threshold.width\n      );\n  } else if (!isLeft && left <= scrollContainerRect.left + threshold.width) {\n    // Scroll Left\n    direction.x = Direction.Backward;\n    speed.x =\n      acceleration *\n      Math.abs(\n        (scrollContainerRect.left + threshold.width - left) / threshold.width\n      );\n  }\n\n  return {\n    direction,\n    speed,\n  };\n}\n", "export function getScrollElementRect(element: Element) {\n  if (element === document.scrollingElement) {\n    const {innerWidth, innerHeight} = window;\n\n    return {\n      top: 0,\n      left: 0,\n      right: innerWidth,\n      bottom: innerHeight,\n      width: innerWidth,\n      height: innerHeight,\n    };\n  }\n\n  const {top, left, right, bottom} = element.getBoundingClientRect();\n\n  return {\n    top,\n    left,\n    right,\n    bottom,\n    width: element.clientWidth,\n    height: element.clientHeight,\n  };\n}\n", "import {add} from '@dnd-kit/utilities';\n\nimport type {Coordinates} from '../../types';\nimport {\n  getScrollCoordinates,\n  getScrollXCoordinate,\n  getScrollYCoordinate,\n} from './getScrollCoordinates';\nimport {defaultCoordinates} from '../coordinates';\n\nexport function getScrollOffsets(scrollableAncestors: Element[]): Coordinates {\n  return scrollableAncestors.reduce<Coordinates>((acc, node) => {\n    return add(acc, getScrollCoordinates(node));\n  }, defaultCoordinates);\n}\n\nexport function getScrollXOffset(scrollableAncestors: Element[]): number {\n  return scrollableAncestors.reduce<number>((acc, node) => {\n    return acc + getScrollXCoordinate(node);\n  }, 0);\n}\n\nexport function getScrollYOffset(scrollableAncestors: Element[]): number {\n  return scrollableAncestors.reduce<number>((acc, node) => {\n    return acc + getScrollYCoordinate(node);\n  }, 0);\n}\n", "import type {ClientRect} from '../../types';\nimport {getClientRect} from '../rect/getRect';\nimport {getFirstScrollableAncestor} from './getScrollableAncestors';\n\nexport function scrollIntoViewIfNeeded(\n  element: HTMLElement | null | undefined,\n  measure: (node: HTMLElement) => ClientRect = getClientRect\n) {\n  if (!element) {\n    return;\n  }\n\n  const {top, left, bottom, right} = measure(element);\n  const firstScrollableAncestor = getFirstScrollableAncestor(element);\n\n  if (!firstScrollableAncestor) {\n    return;\n  }\n\n  if (\n    bottom <= 0 ||\n    right <= 0 ||\n    top >= window.innerHeight ||\n    left >= window.innerWidth\n  ) {\n    element.scrollIntoView({\n      block: 'center',\n      inline: 'center',\n    });\n  }\n}\n", "import type {ClientRect} from '../../types/rect';\nimport {\n  getScrollableAncestors,\n  getScrollOffsets,\n  getScrollXOffset,\n  getScrollYOffset,\n} from '../scroll';\n\nconst properties = [\n  ['x', ['left', 'right'], getScrollXOffset],\n  ['y', ['top', 'bottom'], getScrollYOffset],\n] as const;\n\nexport class Rect {\n  constructor(rect: ClientRect, element: Element) {\n    const scrollableAncestors = getScrollableAncestors(element);\n    const scrollOffsets = getScrollOffsets(scrollableAncestors);\n\n    this.rect = {...rect};\n    this.width = rect.width;\n    this.height = rect.height;\n\n    for (const [axis, keys, getScrollOffset] of properties) {\n      for (const key of keys) {\n        Object.defineProperty(this, key, {\n          get: () => {\n            const currentOffsets = getScrollOffset(scrollableAncestors);\n            const scrollOffsetsDeltla = scrollOffsets[axis] - currentOffsets;\n\n            return this.rect[key] + scrollOffsetsDeltla;\n          },\n          enumerable: true,\n        });\n      }\n    }\n\n    Object.defineProperty(this, 'rect', {enumerable: false});\n  }\n\n  private rect: ClientRect;\n\n  public width: number;\n\n  public height: number;\n\n  // The below properties are set by the `Object.defineProperty` calls in the constructor\n  // @ts-ignore\n  public top: number;\n  // @ts-ignore\n  public bottom: number;\n  // @ts-ignore\n  public right: number;\n  // @ts-ignore\n  public left: number;\n}\n", "export class Listeners {\n  private listeners: [\n    string,\n    EventListenerOrEventListenerObject,\n    AddEventListenerOptions | boolean | undefined\n  ][] = [];\n\n  constructor(private target: EventTarget | null) {}\n\n  public add<T extends Event>(\n    eventName: string,\n    handler: (event: T) => void,\n    options?: AddEventListenerOptions | boolean\n  ) {\n    this.target?.addEventListener(eventName, handler as EventListener, options);\n    this.listeners.push([eventName, handler as EventListener, options]);\n  }\n\n  public removeAll = () => {\n    this.listeners.forEach((listener) =>\n      this.target?.removeEventListener(...listener)\n    );\n  };\n}\n", "import {getOwnerDocument, getWindow} from '@dnd-kit/utilities';\n\nexport function getEventListenerTarget(\n  target: EventTarget | null\n): EventTarget | Document {\n  // If the `event.target` element is removed from the document events will still be targeted\n  // at it, and hence won't always bubble up to the window or document anymore.\n  // If there is any risk of an element being removed while it is being dragged,\n  // the best practice is to attach the event listeners directly to the target.\n  // https://developer.mozilla.org/en-US/docs/Web/API/EventTarget\n\n  const {EventTarget} = getWindow(target);\n\n  return target instanceof EventTarget ? target : getOwnerDocument(target);\n}\n", "import type {Coordinates, DistanceMeasurement} from '../../types';\n\nexport function hasExceededDistance(\n  delta: Coordinates,\n  measurement: DistanceMeasurement\n): boolean {\n  const dx = Math.abs(delta.x);\n  const dy = Math.abs(delta.y);\n\n  if (typeof measurement === 'number') {\n    return Math.sqrt(dx ** 2 + dy ** 2) > measurement;\n  }\n\n  if ('x' in measurement && 'y' in measurement) {\n    return dx > measurement.x && dy > measurement.y;\n  }\n\n  if ('x' in measurement) {\n    return dx > measurement.x;\n  }\n\n  if ('y' in measurement) {\n    return dy > measurement.y;\n  }\n\n  return false;\n}\n", "export enum EventName {\n  Click = 'click',\n  DragStart = 'dragstart',\n  Keydown = 'keydown',\n  ContextMenu = 'contextmenu',\n  Resize = 'resize',\n  SelectionChange = 'selectionchange',\n  VisibilityChange = 'visibilitychange',\n}\n\nexport function preventDefault(event: Event) {\n  event.preventDefault();\n}\n\nexport function stopPropagation(event: Event) {\n  event.stopPropagation();\n}\n", "import type {Coordinates, UniqueIdentifier} from '../../types';\nimport type {SensorContext} from '../types';\n\nexport enum KeyboardCode {\n  Space = 'Space',\n  Down = 'ArrowDown',\n  Right = 'ArrowRight',\n  Left = 'ArrowLeft',\n  Up = 'ArrowUp',\n  Esc = 'Escape',\n  Enter = 'Enter',\n  Tab = 'Tab',\n}\n\nexport type KeyboardCodes = {\n  start: KeyboardEvent['code'][];\n  cancel: KeyboardEvent['code'][];\n  end: KeyboardEvent['code'][];\n};\n\nexport type KeyboardCoordinateGetter = (\n  event: KeyboardEvent,\n  args: {\n    active: UniqueIdentifier;\n    currentCoordinates: Coordinates;\n    context: SensorContext;\n  }\n) => Coordinates | void;\n", "import {KeyboardCoordinateGetter, KeyboardCode, KeyboardCodes} from './types';\n\nexport const defaultKeyboardCodes: KeyboardCodes = {\n  start: [KeyboardCode.Space, KeyboardCode.Enter],\n  cancel: [KeyboardCode.Esc],\n  end: [KeyboardCode.Space, KeyboardCode.Enter, KeyboardCode.Tab],\n};\n\nexport const defaultKeyboardCoordinateGetter: KeyboardCoordinateGetter = (\n  event,\n  {currentCoordinates}\n) => {\n  switch (event.code) {\n    case KeyboardCode.Right:\n      return {\n        ...currentCoordinates,\n        x: currentCoordinates.x + 25,\n      };\n    case KeyboardCode.Left:\n      return {\n        ...currentCoordinates,\n        x: currentCoordinates.x - 25,\n      };\n    case KeyboardCode.Down:\n      return {\n        ...currentCoordinates,\n        y: currentCoordinates.y + 25,\n      };\n    case KeyboardCode.Up:\n      return {\n        ...currentCoordinates,\n        y: currentCoordinates.y - 25,\n      };\n  }\n\n  return undefined;\n};\n", "import {\n  add as getAdjustedCoordinates,\n  subtract as getCoordinates<PERSON><PERSON><PERSON>,\n  getOwnerDocument,\n  getWindow,\n  isKeyboardEvent,\n} from '@dnd-kit/utilities';\n\nimport type {Coordinates} from '../../types';\nimport {\n  defaultCoordinates,\n  getScrollPosition,\n  getScrollElementRect,\n} from '../../utilities';\nimport {scrollIntoViewIfNeeded} from '../../utilities/scroll';\nimport {EventName} from '../events';\nimport {Listeners} from '../utilities';\nimport type {\n  Activators,\n  SensorInstance,\n  SensorProps,\n  SensorOptions,\n} from '../types';\n\nimport {KeyboardCoordinateGetter, KeyboardCode, KeyboardCodes} from './types';\nimport {\n  defaultKeyboardCodes,\n  defaultKeyboardCoordinateGetter,\n} from './defaults';\n\nexport interface KeyboardSensorOptions extends SensorOptions {\n  keyboardCodes?: KeyboardCodes;\n  coordinateGetter?: KeyboardCoordinateGetter;\n  scrollBehavior?: ScrollBehavior;\n  onActivation?({event}: {event: KeyboardEvent}): void;\n}\n\nexport type KeyboardSensorProps = SensorProps<KeyboardSensorOptions>;\n\nexport class KeyboardSensor implements SensorInstance {\n  public autoScrollEnabled = false;\n  private referenceCoordinates: Coordinates | undefined;\n  private listeners: Listeners;\n  private windowListeners: Listeners;\n\n  constructor(private props: KeyboardSensorProps) {\n    const {\n      event: {target},\n    } = props;\n\n    this.props = props;\n    this.listeners = new Listeners(getOwnerDocument(target));\n    this.windowListeners = new Listeners(getWindow(target));\n    this.handleKeyDown = this.handleKeyDown.bind(this);\n    this.handleCancel = this.handleCancel.bind(this);\n\n    this.attach();\n  }\n\n  private attach() {\n    this.handleStart();\n\n    this.windowListeners.add(EventName.Resize, this.handleCancel);\n    this.windowListeners.add(EventName.VisibilityChange, this.handleCancel);\n\n    setTimeout(() => this.listeners.add(EventName.Keydown, this.handleKeyDown));\n  }\n\n  private handleStart() {\n    const {activeNode, onStart} = this.props;\n    const node = activeNode.node.current;\n\n    if (node) {\n      scrollIntoViewIfNeeded(node);\n    }\n\n    onStart(defaultCoordinates);\n  }\n\n  private handleKeyDown(event: Event) {\n    if (isKeyboardEvent(event)) {\n      const {active, context, options} = this.props;\n      const {\n        keyboardCodes = defaultKeyboardCodes,\n        coordinateGetter = defaultKeyboardCoordinateGetter,\n        scrollBehavior = 'smooth',\n      } = options;\n      const {code} = event;\n\n      if (keyboardCodes.end.includes(code)) {\n        this.handleEnd(event);\n        return;\n      }\n\n      if (keyboardCodes.cancel.includes(code)) {\n        this.handleCancel(event);\n        return;\n      }\n\n      const {collisionRect} = context.current;\n      const currentCoordinates = collisionRect\n        ? {x: collisionRect.left, y: collisionRect.top}\n        : defaultCoordinates;\n\n      if (!this.referenceCoordinates) {\n        this.referenceCoordinates = currentCoordinates;\n      }\n\n      const newCoordinates = coordinateGetter(event, {\n        active,\n        context: context.current,\n        currentCoordinates,\n      });\n\n      if (newCoordinates) {\n        const coordinatesDelta = getCoordinatesDelta(\n          newCoordinates,\n          currentCoordinates\n        );\n        const scrollDelta = {\n          x: 0,\n          y: 0,\n        };\n        const {scrollableAncestors} = context.current;\n\n        for (const scrollContainer of scrollableAncestors) {\n          const direction = event.code;\n          const {isTop, isRight, isLeft, isBottom, maxScroll, minScroll} =\n            getScrollPosition(scrollContainer);\n          const scrollElementRect = getScrollElementRect(scrollContainer);\n\n          const clampedCoordinates = {\n            x: Math.min(\n              direction === KeyboardCode.Right\n                ? scrollElementRect.right - scrollElementRect.width / 2\n                : scrollElementRect.right,\n              Math.max(\n                direction === KeyboardCode.Right\n                  ? scrollElementRect.left\n                  : scrollElementRect.left + scrollElementRect.width / 2,\n                newCoordinates.x\n              )\n            ),\n            y: Math.min(\n              direction === KeyboardCode.Down\n                ? scrollElementRect.bottom - scrollElementRect.height / 2\n                : scrollElementRect.bottom,\n              Math.max(\n                direction === KeyboardCode.Down\n                  ? scrollElementRect.top\n                  : scrollElementRect.top + scrollElementRect.height / 2,\n                newCoordinates.y\n              )\n            ),\n          };\n\n          const canScrollX =\n            (direction === KeyboardCode.Right && !isRight) ||\n            (direction === KeyboardCode.Left && !isLeft);\n          const canScrollY =\n            (direction === KeyboardCode.Down && !isBottom) ||\n            (direction === KeyboardCode.Up && !isTop);\n\n          if (canScrollX && clampedCoordinates.x !== newCoordinates.x) {\n            const newScrollCoordinates =\n              scrollContainer.scrollLeft + coordinatesDelta.x;\n            const canScrollToNewCoordinates =\n              (direction === KeyboardCode.Right &&\n                newScrollCoordinates <= maxScroll.x) ||\n              (direction === KeyboardCode.Left &&\n                newScrollCoordinates >= minScroll.x);\n\n            if (canScrollToNewCoordinates && !coordinatesDelta.y) {\n              // We don't need to update coordinates, the scroll adjustment alone will trigger\n              // logic to auto-detect the new container we are over\n              scrollContainer.scrollTo({\n                left: newScrollCoordinates,\n                behavior: scrollBehavior,\n              });\n              return;\n            }\n\n            if (canScrollToNewCoordinates) {\n              scrollDelta.x = scrollContainer.scrollLeft - newScrollCoordinates;\n            } else {\n              scrollDelta.x =\n                direction === KeyboardCode.Right\n                  ? scrollContainer.scrollLeft - maxScroll.x\n                  : scrollContainer.scrollLeft - minScroll.x;\n            }\n\n            if (scrollDelta.x) {\n              scrollContainer.scrollBy({\n                left: -scrollDelta.x,\n                behavior: scrollBehavior,\n              });\n            }\n            break;\n          } else if (canScrollY && clampedCoordinates.y !== newCoordinates.y) {\n            const newScrollCoordinates =\n              scrollContainer.scrollTop + coordinatesDelta.y;\n            const canScrollToNewCoordinates =\n              (direction === KeyboardCode.Down &&\n                newScrollCoordinates <= maxScroll.y) ||\n              (direction === KeyboardCode.Up &&\n                newScrollCoordinates >= minScroll.y);\n\n            if (canScrollToNewCoordinates && !coordinatesDelta.x) {\n              // We don't need to update coordinates, the scroll adjustment alone will trigger\n              // logic to auto-detect the new container we are over\n              scrollContainer.scrollTo({\n                top: newScrollCoordinates,\n                behavior: scrollBehavior,\n              });\n              return;\n            }\n\n            if (canScrollToNewCoordinates) {\n              scrollDelta.y = scrollContainer.scrollTop - newScrollCoordinates;\n            } else {\n              scrollDelta.y =\n                direction === KeyboardCode.Down\n                  ? scrollContainer.scrollTop - maxScroll.y\n                  : scrollContainer.scrollTop - minScroll.y;\n            }\n\n            if (scrollDelta.y) {\n              scrollContainer.scrollBy({\n                top: -scrollDelta.y,\n                behavior: scrollBehavior,\n              });\n            }\n\n            break;\n          }\n        }\n\n        this.handleMove(\n          event,\n          getAdjustedCoordinates(\n            getCoordinatesDelta(newCoordinates, this.referenceCoordinates),\n            scrollDelta\n          )\n        );\n      }\n    }\n  }\n\n  private handleMove(event: Event, coordinates: Coordinates) {\n    const {onMove} = this.props;\n\n    event.preventDefault();\n    onMove(coordinates);\n  }\n\n  private handleEnd(event: Event) {\n    const {onEnd} = this.props;\n\n    event.preventDefault();\n    this.detach();\n    onEnd();\n  }\n\n  private handleCancel(event: Event) {\n    const {onCancel} = this.props;\n\n    event.preventDefault();\n    this.detach();\n    onCancel();\n  }\n\n  private detach() {\n    this.listeners.removeAll();\n    this.windowListeners.removeAll();\n  }\n\n  static activators: Activators<KeyboardSensorOptions> = [\n    {\n      eventName: 'onKeyDown' as const,\n      handler: (\n        event: React.KeyboardEvent,\n        {keyboardCodes = defaultKeyboardCodes, onActivation},\n        {active}\n      ) => {\n        const {code} = event.nativeEvent;\n\n        if (keyboardCodes.start.includes(code)) {\n          const activator = active.activatorNode.current;\n\n          if (activator && event.target !== activator) {\n            return false;\n          }\n\n          event.preventDefault();\n\n          onActivation?.({event: event.nativeEvent});\n\n          return true;\n        }\n\n        return false;\n      },\n    },\n  ];\n}\n", "import {\n  subtract as getCoordina<PERSON><PERSON><PERSON><PERSON>,\n  getEventCoordinates,\n  getOwnerDocument,\n  getWindow,\n} from '@dnd-kit/utilities';\n\nimport {defaultCoordinates} from '../../utilities';\nimport {\n  getEventListenerTarget,\n  hasExceededDistance,\n  Listeners,\n} from '../utilities';\nimport {EventName, preventDefault, stopPropagation} from '../events';\nimport {KeyboardCode} from '../keyboard';\nimport type {SensorInstance, SensorProps, SensorOptions} from '../types';\nimport type {Coordinates, DistanceMeasurement} from '../../types';\n\ninterface DistanceConstraint {\n  distance: DistanceMeasurement;\n  tolerance?: DistanceMeasurement;\n}\n\ninterface DelayConstraint {\n  delay: number;\n  tolerance: DistanceMeasurement;\n}\n\ninterface EventDescriptor {\n  name: keyof DocumentEventMap;\n  passive?: boolean;\n}\n\nexport interface PointerEventHandlers {\n  cancel?: EventDescriptor;\n  move: EventDescriptor;\n  end: EventDescriptor;\n}\n\nexport type PointerActivationConstraint =\n  | DelayConstraint\n  | DistanceConstraint\n  | (DelayConstraint & DistanceConstraint);\n\nfunction isDistanceConstraint(\n  constraint: PointerActivationConstraint\n): constraint is PointerActivationConstraint & DistanceConstraint {\n  return Boolean(constraint && 'distance' in constraint);\n}\n\nfunction isDelayConstraint(\n  constraint: PointerActivationConstraint\n): constraint is DelayConstraint {\n  return Boolean(constraint && 'delay' in constraint);\n}\n\nexport interface AbstractPointerSensorOptions extends SensorOptions {\n  activationConstraint?: PointerActivationConstraint;\n  bypassActivationConstraint?(\n    props: Pick<AbstractPointerSensorProps, 'activeNode' | 'event' | 'options'>\n  ): boolean;\n  onActivation?({event}: {event: Event}): void;\n}\n\nexport type AbstractPointerSensorProps =\n  SensorProps<AbstractPointerSensorOptions>;\n\nexport class AbstractPointerSensor implements SensorInstance {\n  public autoScrollEnabled = true;\n  private document: Document;\n  private activated: boolean = false;\n  private initialCoordinates: Coordinates;\n  private timeoutId: NodeJS.Timeout | null = null;\n  private listeners: Listeners;\n  private documentListeners: Listeners;\n  private windowListeners: Listeners;\n\n  constructor(\n    private props: AbstractPointerSensorProps,\n    private events: PointerEventHandlers,\n    listenerTarget = getEventListenerTarget(props.event.target)\n  ) {\n    const {event} = props;\n    const {target} = event;\n\n    this.props = props;\n    this.events = events;\n    this.document = getOwnerDocument(target);\n    this.documentListeners = new Listeners(this.document);\n    this.listeners = new Listeners(listenerTarget);\n    this.windowListeners = new Listeners(getWindow(target));\n    this.initialCoordinates = getEventCoordinates(event) ?? defaultCoordinates;\n    this.handleStart = this.handleStart.bind(this);\n    this.handleMove = this.handleMove.bind(this);\n    this.handleEnd = this.handleEnd.bind(this);\n    this.handleCancel = this.handleCancel.bind(this);\n    this.handleKeydown = this.handleKeydown.bind(this);\n    this.removeTextSelection = this.removeTextSelection.bind(this);\n\n    this.attach();\n  }\n\n  private attach() {\n    const {\n      events,\n      props: {\n        options: {activationConstraint, bypassActivationConstraint},\n      },\n    } = this;\n\n    this.listeners.add(events.move.name, this.handleMove, {passive: false});\n    this.listeners.add(events.end.name, this.handleEnd);\n\n    if (events.cancel) {\n      this.listeners.add(events.cancel.name, this.handleCancel);\n    }\n\n    this.windowListeners.add(EventName.Resize, this.handleCancel);\n    this.windowListeners.add(EventName.DragStart, preventDefault);\n    this.windowListeners.add(EventName.VisibilityChange, this.handleCancel);\n    this.windowListeners.add(EventName.ContextMenu, preventDefault);\n    this.documentListeners.add(EventName.Keydown, this.handleKeydown);\n\n    if (activationConstraint) {\n      if (\n        bypassActivationConstraint?.({\n          event: this.props.event,\n          activeNode: this.props.activeNode,\n          options: this.props.options,\n        })\n      ) {\n        return this.handleStart();\n      }\n\n      if (isDelayConstraint(activationConstraint)) {\n        this.timeoutId = setTimeout(\n          this.handleStart,\n          activationConstraint.delay\n        );\n        this.handlePending(activationConstraint);\n        return;\n      }\n\n      if (isDistanceConstraint(activationConstraint)) {\n        this.handlePending(activationConstraint);\n        return;\n      }\n    }\n\n    this.handleStart();\n  }\n\n  private detach() {\n    this.listeners.removeAll();\n    this.windowListeners.removeAll();\n\n    // Wait until the next event loop before removing document listeners\n    // This is necessary because we listen for `click` and `selection` events on the document\n    setTimeout(this.documentListeners.removeAll, 50);\n\n    if (this.timeoutId !== null) {\n      clearTimeout(this.timeoutId);\n      this.timeoutId = null;\n    }\n  }\n\n  private handlePending(\n    constraint: PointerActivationConstraint,\n    offset?: Coordinates | undefined\n  ): void {\n    const {active, onPending} = this.props;\n    onPending(active, constraint, this.initialCoordinates, offset);\n  }\n\n  private handleStart() {\n    const {initialCoordinates} = this;\n    const {onStart} = this.props;\n\n    if (initialCoordinates) {\n      this.activated = true;\n\n      // Stop propagation of click events once activation constraints are met\n      this.documentListeners.add(EventName.Click, stopPropagation, {\n        capture: true,\n      });\n\n      // Remove any text selection from the document\n      this.removeTextSelection();\n\n      // Prevent further text selection while dragging\n      this.documentListeners.add(\n        EventName.SelectionChange,\n        this.removeTextSelection\n      );\n\n      onStart(initialCoordinates);\n    }\n  }\n\n  private handleMove(event: Event) {\n    const {activated, initialCoordinates, props} = this;\n    const {\n      onMove,\n      options: {activationConstraint},\n    } = props;\n\n    if (!initialCoordinates) {\n      return;\n    }\n\n    const coordinates = getEventCoordinates(event) ?? defaultCoordinates;\n    const delta = getCoordinatesDelta(initialCoordinates, coordinates);\n\n    // Constraint validation\n    if (!activated && activationConstraint) {\n      if (isDistanceConstraint(activationConstraint)) {\n        if (\n          activationConstraint.tolerance != null &&\n          hasExceededDistance(delta, activationConstraint.tolerance)\n        ) {\n          return this.handleCancel();\n        }\n\n        if (hasExceededDistance(delta, activationConstraint.distance)) {\n          return this.handleStart();\n        }\n      }\n\n      if (isDelayConstraint(activationConstraint)) {\n        if (hasExceededDistance(delta, activationConstraint.tolerance)) {\n          return this.handleCancel();\n        }\n      }\n\n      this.handlePending(activationConstraint, delta);\n      return;\n    }\n\n    if (event.cancelable) {\n      event.preventDefault();\n    }\n\n    onMove(coordinates);\n  }\n\n  private handleEnd() {\n    const {onAbort, onEnd} = this.props;\n\n    this.detach();\n    if (!this.activated) {\n      onAbort(this.props.active);\n    }\n    onEnd();\n  }\n\n  private handleCancel() {\n    const {onAbort, onCancel} = this.props;\n\n    this.detach();\n    if (!this.activated) {\n      onAbort(this.props.active);\n    }\n    onCancel();\n  }\n\n  private handleKeydown(event: KeyboardEvent) {\n    if (event.code === KeyboardCode.Esc) {\n      this.handleCancel();\n    }\n  }\n\n  private removeTextSelection() {\n    this.document.getSelection()?.removeAllRanges();\n  }\n}\n", "import type {PointerEvent} from 'react';\nimport {getOwnerDocument} from '@dnd-kit/utilities';\n\nimport type {SensorProps} from '../types';\nimport {\n  AbstractPointerSensor,\n  AbstractPointerSensorOptions,\n  PointerEventHandlers,\n} from './AbstractPointerSensor';\n\nconst events: PointerEventHandlers = {\n  cancel: {name: 'pointercancel'},\n  move: {name: 'pointermove'},\n  end: {name: 'pointerup'},\n};\n\nexport interface PointerSensorOptions extends AbstractPointerSensorOptions {}\n\nexport type PointerSensorProps = SensorProps<PointerSensorOptions>;\n\nexport class PointerSensor extends AbstractPointerSensor {\n  constructor(props: PointerSensorProps) {\n    const {event} = props;\n    // Pointer events stop firing if the target is unmounted while dragging\n    // Therefore we attach listeners to the owner document instead\n    const listenerTarget = getOwnerDocument(event.target);\n\n    super(props, events, listenerTarget);\n  }\n\n  static activators = [\n    {\n      eventName: 'onPointerDown' as const,\n      handler: (\n        {nativeEvent: event}: PointerEvent,\n        {onActivation}: PointerSensorOptions\n      ) => {\n        if (!event.isPrimary || event.button !== 0) {\n          return false;\n        }\n\n        onActivation?.({event});\n\n        return true;\n      },\n    },\n  ];\n}\n", "import type {MouseEvent} from 'react';\nimport {getOwnerDocument} from '@dnd-kit/utilities';\n\nimport type {SensorProps} from '../types';\nimport {\n  AbstractPointerSensor,\n  PointerEventHandlers,\n  AbstractPointerSensorOptions,\n} from '../pointer';\n\nconst events: PointerEventHandlers = {\n  move: {name: 'mousemove'},\n  end: {name: 'mouseup'},\n};\n\nenum MouseButton {\n  RightClick = 2,\n}\n\nexport interface MouseSensorOptions extends AbstractPointerSensorOptions {}\n\nexport type MouseSensorProps = SensorProps<MouseSensorOptions>;\n\nexport class MouseSensor extends AbstractPointerSensor {\n  constructor(props: MouseSensorProps) {\n    super(props, events, getOwnerDocument(props.event.target));\n  }\n\n  static activators = [\n    {\n      eventName: 'onMouseDown' as const,\n      handler: (\n        {nativeEvent: event}: MouseEvent,\n        {onActivation}: MouseSensorOptions\n      ) => {\n        if (event.button === MouseButton.RightClick) {\n          return false;\n        }\n\n        onActivation?.({event});\n\n        return true;\n      },\n    },\n  ];\n}\n", "import type {TouchEvent} from 'react';\n\nimport {\n  AbstractPointerSensor,\n  PointerSensorProps,\n  PointerEventHandlers,\n  PointerSensorOptions,\n} from '../pointer';\nimport type {SensorProps} from '../types';\n\nconst events: PointerEventHandlers = {\n  cancel: {name: 'touchcancel'},\n  move: {name: 'touchmove'},\n  end: {name: 'touchend'},\n};\n\nexport interface TouchSensorOptions extends PointerSensorOptions {}\n\nexport type TouchSensorProps = SensorProps<TouchSensorOptions>;\n\nexport class TouchSensor extends AbstractPointerSensor {\n  constructor(props: PointerSensorProps) {\n    super(props, events);\n  }\n\n  static activators = [\n    {\n      eventName: 'onTouchStart' as const,\n      handler: (\n        {nativeEvent: event}: TouchEvent,\n        {onActivation}: TouchSensorOptions\n      ) => {\n        const {touches} = event;\n\n        if (touches.length > 1) {\n          return false;\n        }\n\n        onActivation?.({event});\n\n        return true;\n      },\n    },\n  ];\n\n  static setup() {\n    // Adding a non-capture and non-passive `touchmove` listener in order\n    // to force `event.preventDefault()` calls to work in dynamically added\n    // touchmove event handlers. This is required for iOS Safari.\n    window.addEventListener(events.move.name, noop, {\n      capture: false,\n      passive: false,\n    });\n\n    return function teardown() {\n      window.removeEventListener(events.move.name, noop);\n    };\n\n    // We create a new handler because the teardown function of another sensor\n    // could remove our event listener if we use a referentially equal listener.\n    function noop() {}\n  }\n}\n", "import {useCallback, useEffect, useMemo, useRef} from 'react';\nimport {useInterval, useLazyMemo, usePrevious} from '@dnd-kit/utilities';\n\nimport {getScrollDirectionAndSpeed} from '../../utilities';\nimport {Direction} from '../../types';\nimport type {Coordinates, ClientRect} from '../../types';\n\nexport type ScrollAncestorSortingFn = (ancestors: Element[]) => Element[];\n\nexport enum AutoScrollActivator {\n  Pointer,\n  DraggableRect,\n}\n\nexport interface Options {\n  acceleration?: number;\n  activator?: AutoScrollActivator;\n  canScroll?: CanScroll;\n  enabled?: boolean;\n  interval?: number;\n  layoutShiftCompensation?:\n    | boolean\n    | {\n        x: boolean;\n        y: boolean;\n      };\n  order?: TraversalOrder;\n  threshold?: {\n    x: number;\n    y: number;\n  };\n}\n\ninterface Arguments extends Options {\n  draggingRect: ClientRect | null;\n  enabled: boolean;\n  pointerCoordinates: Coordinates | null;\n  scrollableAncestors: Element[];\n  scrollableAncestorRects: ClientRect[];\n  delta: Coordinates;\n}\n\nexport type CanScroll = (element: Element) => boolean;\n\nexport enum TraversalOrder {\n  TreeOrder,\n  ReversedTreeOrder,\n}\n\ninterface ScrollDirection {\n  x: 0 | Direction;\n  y: 0 | Direction;\n}\n\nexport function useAutoScroller({\n  acceleration,\n  activator = AutoScrollActivator.Pointer,\n  canScroll,\n  draggingRect,\n  enabled,\n  interval = 5,\n  order = TraversalOrder.TreeOrder,\n  pointerCoordinates,\n  scrollableAncestors,\n  scrollableAncestorRects,\n  delta,\n  threshold,\n}: Arguments) {\n  const scrollIntent = useScrollIntent({delta, disabled: !enabled});\n  const [setAutoScrollInterval, clearAutoScrollInterval] = useInterval();\n  const scrollSpeed = useRef<Coordinates>({x: 0, y: 0});\n  const scrollDirection = useRef<ScrollDirection>({x: 0, y: 0});\n  const rect = useMemo(() => {\n    switch (activator) {\n      case AutoScrollActivator.Pointer:\n        return pointerCoordinates\n          ? {\n              top: pointerCoordinates.y,\n              bottom: pointerCoordinates.y,\n              left: pointerCoordinates.x,\n              right: pointerCoordinates.x,\n            }\n          : null;\n      case AutoScrollActivator.DraggableRect:\n        return draggingRect;\n    }\n  }, [activator, draggingRect, pointerCoordinates]);\n  const scrollContainerRef = useRef<Element | null>(null);\n  const autoScroll = useCallback(() => {\n    const scrollContainer = scrollContainerRef.current;\n\n    if (!scrollContainer) {\n      return;\n    }\n\n    const scrollLeft = scrollSpeed.current.x * scrollDirection.current.x;\n    const scrollTop = scrollSpeed.current.y * scrollDirection.current.y;\n\n    scrollContainer.scrollBy(scrollLeft, scrollTop);\n  }, []);\n  const sortedScrollableAncestors = useMemo(\n    () =>\n      order === TraversalOrder.TreeOrder\n        ? [...scrollableAncestors].reverse()\n        : scrollableAncestors,\n    [order, scrollableAncestors]\n  );\n\n  useEffect(\n    () => {\n      if (!enabled || !scrollableAncestors.length || !rect) {\n        clearAutoScrollInterval();\n        return;\n      }\n\n      for (const scrollContainer of sortedScrollableAncestors) {\n        if (canScroll?.(scrollContainer) === false) {\n          continue;\n        }\n\n        const index = scrollableAncestors.indexOf(scrollContainer);\n        const scrollContainerRect = scrollableAncestorRects[index];\n\n        if (!scrollContainerRect) {\n          continue;\n        }\n\n        const {direction, speed} = getScrollDirectionAndSpeed(\n          scrollContainer,\n          scrollContainerRect,\n          rect,\n          acceleration,\n          threshold\n        );\n\n        for (const axis of ['x', 'y'] as const) {\n          if (!scrollIntent[axis][direction[axis] as Direction]) {\n            speed[axis] = 0;\n            direction[axis] = 0;\n          }\n        }\n\n        if (speed.x > 0 || speed.y > 0) {\n          clearAutoScrollInterval();\n\n          scrollContainerRef.current = scrollContainer;\n          setAutoScrollInterval(autoScroll, interval);\n\n          scrollSpeed.current = speed;\n          scrollDirection.current = direction;\n\n          return;\n        }\n      }\n\n      scrollSpeed.current = {x: 0, y: 0};\n      scrollDirection.current = {x: 0, y: 0};\n      clearAutoScrollInterval();\n    },\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    [\n      acceleration,\n      autoScroll,\n      canScroll,\n      clearAutoScrollInterval,\n      enabled,\n      interval,\n      // eslint-disable-next-line react-hooks/exhaustive-deps\n      JSON.stringify(rect),\n      // eslint-disable-next-line react-hooks/exhaustive-deps\n      JSON.stringify(scrollIntent),\n      setAutoScrollInterval,\n      scrollableAncestors,\n      sortedScrollableAncestors,\n      scrollableAncestorRects,\n      // eslint-disable-next-line react-hooks/exhaustive-deps\n      JSON.stringify(threshold),\n    ]\n  );\n}\n\ninterface ScrollIntent {\n  x: Record<Direction, boolean>;\n  y: Record<Direction, boolean>;\n}\n\nconst defaultScrollIntent: ScrollIntent = {\n  x: {[Direction.Backward]: false, [Direction.Forward]: false},\n  y: {[Direction.Backward]: false, [Direction.Forward]: false},\n};\n\nfunction useScrollIntent({\n  delta,\n  disabled,\n}: {\n  delta: Coordinates;\n  disabled: boolean;\n}): ScrollIntent {\n  const previousDelta = usePrevious(delta);\n\n  return useLazyMemo<ScrollIntent>(\n    (previousIntent) => {\n      if (disabled || !previousDelta || !previousIntent) {\n        // Reset scroll intent tracking when auto-scrolling is disabled\n        return defaultScrollIntent;\n      }\n\n      const direction = {\n        x: Math.sign(delta.x - previousDelta.x),\n        y: Math.sign(delta.y - previousDelta.y),\n      };\n\n      // Keep track of the user intent to scroll in each direction for both axis\n      return {\n        x: {\n          [Direction.Backward]:\n            previousIntent.x[Direction.Backward] || direction.x === -1,\n          [Direction.Forward]:\n            previousIntent.x[Direction.Forward] || direction.x === 1,\n        },\n        y: {\n          [Direction.Backward]:\n            previousIntent.y[Direction.Backward] || direction.y === -1,\n          [Direction.Forward]:\n            previousIntent.y[Direction.Forward] || direction.y === 1,\n        },\n      };\n    },\n    [disabled, delta, previousDelta]\n  );\n}\n", "import {useLazyMemo} from '@dnd-kit/utilities';\n\nimport type {DraggableNode, DraggableNodes} from '../../store';\nimport type {UniqueIdentifier} from '../../types';\n\nexport function useCachedNode(\n  draggableNodes: DraggableNodes,\n  id: UniqueIdentifier | null\n): DraggableNode['node']['current'] {\n  const draggableNode = id != null ? draggableNodes.get(id) : undefined;\n  const node = draggableNode ? draggableNode.node.current : null;\n\n  return useLazyMemo(\n    (cachedNode) => {\n      if (id == null) {\n        return null;\n      }\n\n      // In some cases, the draggable node can unmount while dragging\n      // This is the case for virtualized lists. In those situations,\n      // we fall back to the last known value for that node.\n      return node ?? cachedNode ?? null;\n    },\n    [node, id]\n  );\n}\n", "import {useMemo} from 'react';\n\nimport type {SensorActivatorFunction, SensorDescriptor} from '../../sensors';\nimport type {\n  SyntheticListener,\n  SyntheticListeners,\n} from './useSyntheticListeners';\n\nexport function useCombineActivators(\n  sensors: SensorDescriptor<any>[],\n  getSyntheticHandler: (\n    handler: SensorActivatorFunction<any>,\n    sensor: SensorDescriptor<any>\n  ) => SyntheticListener['handler']\n): SyntheticListeners {\n  return useMemo(\n    () =>\n      sensors.reduce<SyntheticListeners>((accumulator, sensor) => {\n        const {sensor: Sensor} = sensor;\n\n        const sensorActivators = Sensor.activators.map((activator) => ({\n          eventName: activator.eventName,\n          handler: getSyntheticHandler(activator.handler, sensor),\n        }));\n\n        return [...accumulator, ...sensorActivators];\n      }, []),\n    [sensors, getSyntheticHandler]\n  );\n}\n", "import {useCallback, useEffect, useRef, useState} from 'react';\nimport {useLatestValue, useLazyMemo} from '@dnd-kit/utilities';\n\nimport {Rect} from '../../utilities/rect';\nimport type {DroppableContainer, RectMap} from '../../store/types';\nimport type {ClientRect, UniqueIdentifier} from '../../types';\n\ninterface Arguments {\n  dragging: boolean;\n  dependencies: any[];\n  config: DroppableMeasuring;\n}\n\nexport enum MeasuringStrategy {\n  Always,\n  BeforeDragging,\n  WhileDragging,\n}\n\nexport enum MeasuringFrequency {\n  Optimized = 'optimized',\n}\n\ntype MeasuringFunction = (element: HTMLElement) => ClientRect;\n\nexport interface DroppableMeasuring {\n  measure: MeasuringFunction;\n  strategy: MeasuringStrategy;\n  frequency: MeasuringFrequency | number;\n}\n\nconst defaultValue: RectMap = new Map();\n\nexport function useDroppableMeasuring(\n  containers: DroppableContainer[],\n  {dragging, dependencies, config}: Arguments\n) {\n  const [queue, setQueue] = useState<UniqueIdentifier[] | null>(null);\n  const {frequency, measure, strategy} = config;\n  const containersRef = useRef(containers);\n  const disabled = isDisabled();\n  const disabledRef = useLatestValue(disabled);\n  const measureDroppableContainers = useCallback(\n    (ids: UniqueIdentifier[] = []) => {\n      if (disabledRef.current) {\n        return;\n      }\n\n      setQueue((value) => {\n        if (value === null) {\n          return ids;\n        }\n\n        return value.concat(ids.filter((id) => !value.includes(id)));\n      });\n    },\n    [disabledRef]\n  );\n  const timeoutId = useRef<NodeJS.Timeout | null>(null);\n  const droppableRects = useLazyMemo<RectMap>(\n    (previousValue) => {\n      if (disabled && !dragging) {\n        return defaultValue;\n      }\n\n      if (\n        !previousValue ||\n        previousValue === defaultValue ||\n        containersRef.current !== containers ||\n        queue != null\n      ) {\n        const map: RectMap = new Map();\n\n        for (let container of containers) {\n          if (!container) {\n            continue;\n          }\n\n          if (\n            queue &&\n            queue.length > 0 &&\n            !queue.includes(container.id) &&\n            container.rect.current\n          ) {\n            // This container does not need to be re-measured\n            map.set(container.id, container.rect.current);\n            continue;\n          }\n\n          const node = container.node.current;\n          const rect = node ? new Rect(measure(node), node) : null;\n\n          container.rect.current = rect;\n\n          if (rect) {\n            map.set(container.id, rect);\n          }\n        }\n\n        return map;\n      }\n\n      return previousValue;\n    },\n    [containers, queue, dragging, disabled, measure]\n  );\n\n  useEffect(() => {\n    containersRef.current = containers;\n  }, [containers]);\n\n  useEffect(\n    () => {\n      if (disabled) {\n        return;\n      }\n\n      measureDroppableContainers();\n    },\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    [dragging, disabled]\n  );\n\n  useEffect(\n    () => {\n      if (queue && queue.length > 0) {\n        setQueue(null);\n      }\n    },\n    //eslint-disable-next-line react-hooks/exhaustive-deps\n    [JSON.stringify(queue)]\n  );\n\n  useEffect(\n    () => {\n      if (\n        disabled ||\n        typeof frequency !== 'number' ||\n        timeoutId.current !== null\n      ) {\n        return;\n      }\n\n      timeoutId.current = setTimeout(() => {\n        measureDroppableContainers();\n        timeoutId.current = null;\n      }, frequency);\n    },\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    [frequency, disabled, measureDroppableContainers, ...dependencies]\n  );\n\n  return {\n    droppableRects,\n    measureDroppableContainers,\n    measuringScheduled: queue != null,\n  };\n\n  function isDisabled() {\n    switch (strategy) {\n      case MeasuringStrategy.Always:\n        return false;\n      case MeasuringStrategy.BeforeDragging:\n        return dragging;\n      default:\n        return !dragging;\n    }\n  }\n}\n", "import {useLazyMemo} from '@dnd-kit/utilities';\n\ntype AnyFunction = (...args: any) => any;\n\nexport function useInitialValue<\n  T,\n  U extends AnyFunction | undefined = undefined\n>(\n  value: T | null,\n  computeFn?: U\n): U extends AnyFunction ? ReturnType<U> | null : T | null {\n  return useLazyMemo(\n    (previousValue) => {\n      if (!value) {\n        return null;\n      }\n\n      if (previousValue) {\n        return previousValue;\n      }\n\n      return typeof computeFn === 'function' ? computeFn(value) : value;\n    },\n    [computeFn, value]\n  );\n}\n", "import type {ClientRect} from '../../types';\nimport {useInitialValue} from './useInitialValue';\n\nexport function useInitialRect(\n  node: HTMLElement | null,\n  measure: (node: HTMLElement) => ClientRect\n) {\n  return useInitialValue(node, measure);\n}\n", "import {useEffect, useMemo} from 'react';\nimport {useEvent} from '@dnd-kit/utilities';\n\ninterface Arguments {\n  callback: MutationCallback;\n  disabled?: boolean;\n}\n\n/**\n * Returns a new MutationObserver instance.\n * If `MutationObserver` is undefined in the execution environment, returns `undefined`.\n */\nexport function useMutationObserver({callback, disabled}: Arguments) {\n  const handleMutations = useEvent(callback);\n  const mutationObserver = useMemo(() => {\n    if (\n      disabled ||\n      typeof window === 'undefined' ||\n      typeof window.MutationObserver === 'undefined'\n    ) {\n      return undefined;\n    }\n\n    const {MutationObserver} = window;\n\n    return new MutationObserver(handleMutations);\n  }, [handleMutations, disabled]);\n\n  useEffect(() => {\n    return () => mutationObserver?.disconnect();\n  }, [mutationObserver]);\n\n  return mutationObserver;\n}\n", "import {useEffect, useMemo} from 'react';\nimport {useEvent} from '@dnd-kit/utilities';\n\ninterface Arguments {\n  callback: ResizeObserverCallback;\n  disabled?: boolean;\n}\n\n/**\n * Returns a new ResizeObserver instance bound to the `onResize` callback.\n * If `ResizeObserver` is undefined in the execution environment, returns `undefined`.\n */\nexport function useResizeObserver({callback, disabled}: Arguments) {\n  const handleResize = useEvent(callback);\n  const resizeObserver = useMemo(\n    () => {\n      if (\n        disabled ||\n        typeof window === 'undefined' ||\n        typeof window.ResizeObserver === 'undefined'\n      ) {\n        return undefined;\n      }\n\n      const {ResizeObserver} = window;\n\n      return new ResizeObserver(handleResize);\n    },\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    [disabled]\n  );\n\n  useEffect(() => {\n    return () => resizeObserver?.disconnect();\n  }, [resizeObserver]);\n\n  return resizeObserver;\n}\n", "import {useState} from 'react';\nimport {useIsomorphicLayoutEffect} from '@dnd-kit/utilities';\n\nimport type {ClientRect} from '../../types';\nimport {getClientRect, Rect} from '../../utilities';\n\nimport {useMutationObserver} from './useMutationObserver';\nimport {useResizeObserver} from './useResizeObserver';\n\nfunction defaultMeasure(element: HTMLElement) {\n  return new Rect(getClientRect(element), element);\n}\n\nexport function useRect(\n  element: HTMLElement | null,\n  measure: (element: HTMLElement) => ClientRect = defaultMeasure,\n  fallbackRect?: ClientRect | null\n) {\n  const [rect, setRect] = useState<ClientRect | null>(null);\n\n  function measureRect() {\n    setRect((currentRect): ClientRect | null => {\n      if (!element) {\n        return null;\n      }\n  \n      if (element.isConnected === false) {\n        // Fall back to last rect we measured if the element is\n        // no longer connected to the DOM.\n        return currentRect ?? fallbackRect ?? null;\n      }\n  \n      const newRect = measure(element);\n  \n      if (JSON.stringify(currentRect) === JSON.stringify(newRect)) {\n        return currentRect;\n      }\n  \n      return newRect;\n    });\n  }\n  \n  const mutationObserver = useMutationObserver({\n    callback(records) {\n      if (!element) {\n        return;\n      }\n\n      for (const record of records) {\n        const {type, target} = record;\n\n        if (\n          type === 'childList' &&\n          target instanceof HTMLElement &&\n          target.contains(element)\n        ) {\n          measureRect();\n          break;\n        }\n      }\n    },\n  });\n  const resizeObserver = useResizeObserver({callback: measureRect});\n\n  useIsomorphicLayoutEffect(() => {\n    measureRect();\n\n    if (element) {\n      resizeObserver?.observe(element);\n      mutationObserver?.observe(document.body, {\n        childList: true,\n        subtree: true,\n      });\n    } else {\n      resizeObserver?.disconnect();\n      mutationObserver?.disconnect();\n    }\n  }, [element]);\n\n  return rect;\n}\n", "import type {ClientRect} from '../../types';\nimport {getRectDelta} from '../../utilities';\n\nimport {useInitialValue} from './useInitialValue';\n\nexport function useRectDelta(rect: ClientRect | null) {\n  const initialRect = useInitialValue(rect);\n\n  return getRectDelta(rect, initialRect);\n}\n", "import {useEffect, useRef} from 'react';\nimport {useLazyMemo} from '@dnd-kit/utilities';\n\nimport {getScrollableAncestors} from '../../utilities';\n\nconst defaultValue: Element[] = [];\n\nexport function useScrollableAncestors(node: HTMLElement | null) {\n  const previousNode = useRef(node);\n\n  const ancestors = useLazyMemo<Element[]>(\n    (previousValue) => {\n      if (!node) {\n        return defaultValue;\n      }\n\n      if (\n        previousValue &&\n        previousValue !== defaultValue &&\n        node &&\n        previousNode.current &&\n        node.parentNode === previousNode.current.parentNode\n      ) {\n        return previousValue;\n      }\n\n      return getScrollableAncestors(node);\n    },\n    [node]\n  );\n\n  useEffect(() => {\n    previousNode.current = node;\n  }, [node]);\n\n  return ancestors;\n}\n", "import {useState, useCallback, useMemo, useRef, useEffect} from 'react';\nimport {add} from '@dnd-kit/utilities';\n\nimport {\n  defaultCoordinates,\n  getScrollableElement,\n  getScrollCoordinates,\n  getScrollOffsets,\n} from '../../utilities';\nimport type {Coordinates} from '../../types';\n\ntype ScrollCoordinates = Map<HTMLElement | Window, Coordinates>;\n\nexport function useScrollOffsets(elements: Element[]): Coordinates {\n  const [\n    scrollCoordinates,\n    setScrollCoordinates,\n  ] = useState<ScrollCoordinates | null>(null);\n  const prevElements = useRef(elements);\n\n  // To-do: Throttle the handleScroll callback\n  const handleScroll = useCallback((event: Event) => {\n    const scrollingElement = getScrollableElement(event.target);\n\n    if (!scrollingElement) {\n      return;\n    }\n\n    setScrollCoordinates((scrollCoordinates) => {\n      if (!scrollCoordinates) {\n        return null;\n      }\n\n      scrollCoordinates.set(\n        scrollingElement,\n        getScrollCoordinates(scrollingElement)\n      );\n\n      return new Map(scrollCoordinates);\n    });\n  }, []);\n\n  useEffect(() => {\n    const previousElements = prevElements.current;\n\n    if (elements !== previousElements) {\n      cleanup(previousElements);\n\n      const entries = elements\n        .map((element) => {\n          const scrollableElement = getScrollableElement(element);\n\n          if (scrollableElement) {\n            scrollableElement.addEventListener('scroll', handleScroll, {\n              passive: true,\n            });\n\n            return [\n              scrollableElement,\n              getScrollCoordinates(scrollableElement),\n            ] as const;\n          }\n\n          return null;\n        })\n        .filter(\n          (\n            entry\n          ): entry is [\n            HTMLElement | (Window & typeof globalThis),\n            Coordinates\n          ] => entry != null\n        );\n\n      setScrollCoordinates(entries.length ? new Map(entries) : null);\n\n      prevElements.current = elements;\n    }\n\n    return () => {\n      cleanup(elements);\n      cleanup(previousElements);\n    };\n\n    function cleanup(elements: Element[]) {\n      elements.forEach((element) => {\n        const scrollableElement = getScrollableElement(element);\n\n        scrollableElement?.removeEventListener('scroll', handleScroll);\n      });\n    }\n  }, [handleScroll, elements]);\n\n  return useMemo(() => {\n    if (elements.length) {\n      return scrollCoordinates\n        ? Array.from(scrollCoordinates.values()).reduce(\n            (acc, coordinates) => add(acc, coordinates),\n            defaultCoordinates\n          )\n        : getScrollOffsets(elements);\n    }\n\n    return defaultCoordinates;\n  }, [elements, scrollCoordinates]);\n}\n", "import {useEffect, useRef} from 'react';\nimport {Coordinates, subtract} from '@dnd-kit/utilities';\n\nimport {defaultCoordinates} from '../../utilities';\n\nexport function useScrollOffsetsDelta(\n  scrollOffsets: Coordinates,\n  dependencies: any[] = []\n) {\n  const initialScrollOffsets = useRef<Coordinates | null>(null);\n\n  useEffect(\n    () => {\n      initialScrollOffsets.current = null;\n    },\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    dependencies\n  );\n\n  useEffect(() => {\n    const hasScrollOffsets = scrollOffsets !== defaultCoordinates;\n\n    if (hasScrollOffsets && !initialScrollOffsets.current) {\n      initialScrollOffsets.current = scrollOffsets;\n    }\n\n    if (!hasScrollOffsets && initialScrollOffsets.current) {\n      initialScrollOffsets.current = null;\n    }\n  }, [scrollOffsets]);\n\n  return initialScrollOffsets.current\n    ? subtract(scrollOffsets, initialScrollOffsets.current)\n    : defaultCoordinates;\n}\n", "import {useEffect} from 'react';\nimport {canUseDOM} from '@dnd-kit/utilities';\n\nimport type {SensorDescriptor} from '../../sensors';\n\nexport function useSensorSetup(sensors: SensorDescriptor<any>[]) {\n  useEffect(\n    () => {\n      if (!canUseDOM) {\n        return;\n      }\n\n      const teardownFns = sensors.map(({sensor}) => sensor.setup?.());\n\n      return () => {\n        for (const teardown of teardownFns) {\n          teardown?.();\n        }\n      };\n    },\n    // TO-DO: Sensors length could theoretically change which would not be a valid dependency\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    sensors.map(({sensor}) => sensor)\n  );\n}\n", "import {useMemo} from 'react';\n\nimport type {SyntheticEventName, UniqueIdentifier} from '../../types';\n\nexport type SyntheticListener = {\n  eventName: SyntheticEventName;\n  handler: (event: React.SyntheticEvent, id: UniqueIdentifier) => void;\n};\n\nexport type SyntheticListeners = SyntheticListener[];\n\nexport type SyntheticListenerMap = Record<string, Function>;\n\nexport function useSyntheticListeners(\n  listeners: SyntheticListeners,\n  id: UniqueIdentifier\n): SyntheticListenerMap {\n  return useMemo(() => {\n    return listeners.reduce<SyntheticListenerMap>(\n      (acc, {eventName, handler}) => {\n        acc[eventName] = (event: React.SyntheticEvent) => {\n          handler(event, id);\n        };\n\n        return acc;\n      },\n      {} as SyntheticListenerMap\n    );\n  }, [listeners, id]);\n}\n", "import {useMemo} from 'react';\n\nimport {getWindowClientRect} from '../../utilities/rect';\n\nexport function useWindowRect(element: typeof window | null) {\n  return useMemo(() => (element ? getWindowClientRect(element) : null), [\n    element,\n  ]);\n}\n", "import {useState} from 'react';\nimport {getWindow, useIsomorphicLayoutEffect} from '@dnd-kit/utilities';\n\nimport type {ClientRect} from '../../types';\nimport {Rect, getClientRect} from '../../utilities/rect';\nimport {isDocumentScrollingElement} from '../../utilities';\n\nimport {useResizeObserver} from './useResizeObserver';\nimport {useWindowRect} from './useWindowRect';\n\nconst defaultValue: Rect[] = [];\n\nexport function useRects(\n  elements: Element[],\n  measure: (element: Element) => ClientRect = getClientRect\n): ClientRect[] {\n  const [firstElement] = elements;\n  const windowRect = useWindowRect(\n    firstElement ? getWindow(firstElement) : null\n  );\n  const [rects, setRects] = useState<ClientRect[]>(defaultValue);\n\n  function measureRects() {\n    setRects(() => {\n      if (!elements.length) {\n        return defaultValue;\n      }\n\n      return elements.map((element) =>\n        isDocumentScrollingElement(element)\n          ? (windowRect as ClientRect)\n          : new Rect(measure(element), element)\n      );\n    });\n  }\n\n  const resizeObserver = useResizeObserver({callback: measureRects});\n\n  useIsomorphicLayoutEffect(() => {\n    resizeObserver?.disconnect();\n    measureRects();\n    elements.forEach((element) => resizeObserver?.observe(element));\n  }, [elements]);\n\n  return rects;\n}\n", "import {isHTMLElement} from '@dnd-kit/utilities';\n\nexport function getMeasurableNode(\n  node: HTMLElement | undefined | null\n): HTMLElement | null {\n  if (!node) {\n    return null;\n  }\n\n  if (node.children.length > 1) {\n    return node;\n  }\n  const firstChild = node.children[0];\n\n  return isHTMLElement(firstChild) ? firstChild : node;\n}\n", "import {useMemo, useCallback, useState} from 'react';\nimport {isHTMLElement, useNodeRef} from '@dnd-kit/utilities';\n\nimport {useResizeObserver} from './useResizeObserver';\nimport {getMeasurableNode} from '../../utilities/nodes';\nimport type {PublicContextDescriptor} from '../../store';\nimport type {ClientRect} from '../../types';\n\ninterface Arguments {\n  measure(element: HTMLElement): ClientRect;\n}\n\nexport function useDragOverlayMeasuring({\n  measure,\n}: Arguments): PublicContextDescriptor['dragOverlay'] {\n  const [rect, setRect] = useState<ClientRect | null>(null);\n  const handleResize = useCallback(\n    (entries: ResizeObserverEntry[]) => {\n      for (const {target} of entries) {\n        if (isHTMLElement(target)) {\n          setRect((rect) => {\n            const newRect = measure(target);\n\n            return rect\n              ? {...rect, width: newRect.width, height: newRect.height}\n              : newRect;\n          });\n          break;\n        }\n      }\n    },\n    [measure]\n  );\n  const resizeObserver = useResizeObserver({callback: handleResize});\n  const handleNodeChange = useCallback(\n    (element) => {\n      const node = getMeasurableNode(element);\n\n      resizeObserver?.disconnect();\n\n      if (node) {\n        resizeObserver?.observe(node);\n      }\n\n      setRect(node ? measure(node) : null);\n    },\n    [measure, resizeObserver]\n  );\n  const [nodeRef, setRef] = useNodeRef(handleNodeChange);\n\n  return useMemo(\n    () => ({\n      nodeRef,\n      rect,\n      setRef,\n    }),\n    [rect, nodeRef, setRef]\n  );\n}\n", "import type {DeepRequired} from '@dnd-kit/utilities';\n\nimport type {DataRef} from '../../store/types';\nimport {KeyboardSensor, PointerSensor} from '../../sensors';\nimport {MeasuringStrategy, MeasuringFrequency} from '../../hooks/utilities';\nimport {\n  getClientRect,\n  getTransformAgnosticClientRect,\n} from '../../utilities/rect';\n\nimport type {MeasuringConfiguration} from './types';\n\nexport const defaultSensors = [\n  {sensor: PointerSensor, options: {}},\n  {sensor: KeyboardSensor, options: {}},\n];\n\nexport const defaultData: DataRef = {current: {}};\n\nexport const defaultMeasuringConfiguration: DeepRequired<MeasuringConfiguration> = {\n  draggable: {\n    measure: getTransformAgnosticClientRect,\n  },\n  droppable: {\n    measure: getTransformAgnosticClientRect,\n    strategy: MeasuringStrategy.WhileDragging,\n    frequency: MeasuringFrequency.Optimized,\n  },\n  dragOverlay: {\n    measure: getClientRect,\n  },\n};\n", "import type {UniqueIdentifier} from '../types';\nimport type {DroppableContainer} from './types';\n\ntype Identifier = UniqueIdentifier | null | undefined;\n\nexport class DroppableContainersMap extends Map<\n  UniqueIdentifier,\n  DroppableContainer\n> {\n  get(id: Identifier) {\n    return id != null ? super.get(id) ?? undefined : undefined;\n  }\n\n  toArray(): DroppableContainer[] {\n    return Array.from(this.values());\n  }\n\n  getEnabled(): DroppableContainer[] {\n    return this.toArray().filter(({disabled}) => !disabled);\n  }\n\n  getNodeFor(id: Identifier) {\n    return this.get(id)?.node.current ?? undefined;\n  }\n}\n", "import {createContext} from 'react';\n\nimport {noop} from '../utilities/other';\nimport {defaultMeasuringConfiguration} from '../components/DndContext/defaults';\nimport {DroppableContainersMap} from './constructors';\nimport type {InternalContextDescriptor, PublicContextDescriptor} from './types';\n\nexport const defaultPublicContext: PublicContextDescriptor = {\n  activatorEvent: null,\n  active: null,\n  activeNode: null,\n  activeNodeRect: null,\n  collisions: null,\n  containerNodeRect: null,\n  draggableNodes: new Map(),\n  droppableRects: new Map(),\n  droppableContainers: new DroppableContainersMap(),\n  over: null,\n  dragOverlay: {\n    nodeRef: {\n      current: null,\n    },\n    rect: null,\n    setRef: noop,\n  },\n  scrollableAncestors: [],\n  scrollableAncestorRects: [],\n  measuringConfiguration: defaultMeasuringConfiguration,\n  measureDroppableContainers: noop,\n  windowRect: null,\n  measuringScheduled: false,\n};\n\nexport const defaultInternalContext: InternalContextDescriptor = {\n  activatorEvent: null,\n  activators: [],\n  active: null,\n  activeNodeRect: null,\n  ariaDescribedById: {\n    draggable: '',\n  },\n  dispatch: noop,\n  draggableNodes: new Map(),\n  over: null,\n  measureDroppableContainers: noop,\n};\n\nexport const InternalContext = createContext<InternalContextDescriptor>(\n  defaultInternalContext\n);\n\nexport const PublicContext = createContext<PublicContextDescriptor>(\n  defaultPublicContext\n);\n", "import {Action, Actions} from './actions';\nimport {DroppableContainersMap} from './constructors';\nimport type {State} from './types';\n\nexport function getInitialState(): State {\n  return {\n    draggable: {\n      active: null,\n      initialCoordinates: {x: 0, y: 0},\n      nodes: new Map(),\n      translate: {x: 0, y: 0},\n    },\n    droppable: {\n      containers: new DroppableContainersMap(),\n    },\n  };\n}\n\nexport function reducer(state: State, action: Actions): State {\n  switch (action.type) {\n    case Action.DragStart:\n      return {\n        ...state,\n        draggable: {\n          ...state.draggable,\n          initialCoordinates: action.initialCoordinates,\n          active: action.active,\n        },\n      };\n    case Action.DragMove:\n      if (state.draggable.active == null) {\n        return state;\n      }\n\n      return {\n        ...state,\n        draggable: {\n          ...state.draggable,\n          translate: {\n            x: action.coordinates.x - state.draggable.initialCoordinates.x,\n            y: action.coordinates.y - state.draggable.initialCoordinates.y,\n          },\n        },\n      };\n    case Action.DragEnd:\n    case Action.DragCancel:\n      return {\n        ...state,\n        draggable: {\n          ...state.draggable,\n          active: null,\n          initialCoordinates: {x: 0, y: 0},\n          translate: {x: 0, y: 0},\n        },\n      };\n\n    case Action.RegisterDroppable: {\n      const {element} = action;\n      const {id} = element;\n      const containers = new DroppableContainersMap(state.droppable.containers);\n      containers.set(id, element);\n\n      return {\n        ...state,\n        droppable: {\n          ...state.droppable,\n          containers,\n        },\n      };\n    }\n\n    case Action.SetDroppableDisabled: {\n      const {id, key, disabled} = action;\n      const element = state.droppable.containers.get(id);\n\n      if (!element || key !== element.key) {\n        return state;\n      }\n\n      const containers = new DroppableContainersMap(state.droppable.containers);\n      containers.set(id, {\n        ...element,\n        disabled,\n      });\n\n      return {\n        ...state,\n        droppable: {\n          ...state.droppable,\n          containers,\n        },\n      };\n    }\n\n    case Action.UnregisterDroppable: {\n      const {id, key} = action;\n      const element = state.droppable.containers.get(id);\n\n      if (!element || key !== element.key) {\n        return state;\n      }\n\n      const containers = new DroppableContainersMap(state.droppable.containers);\n      containers.delete(id);\n\n      return {\n        ...state,\n        droppable: {\n          ...state.droppable,\n          containers,\n        },\n      };\n    }\n\n    default: {\n      return state;\n    }\n  }\n}\n", "import {useContext, useEffect} from 'react';\nimport {\n  findFirstFocusableNode,\n  isKeyboardEvent,\n  usePrevious,\n} from '@dnd-kit/utilities';\n\nimport {InternalContext} from '../../../store';\n\ninterface Props {\n  disabled: boolean;\n}\n\nexport function RestoreFocus({disabled}: Props) {\n  const {active, activatorEvent, draggableNodes} = useContext(InternalContext);\n  const previousActivatorEvent = usePrevious(activatorEvent);\n  const previousActiveId = usePrevious(active?.id);\n\n  // Restore keyboard focus on the activator node\n  useEffect(() => {\n    if (disabled) {\n      return;\n    }\n\n    if (!activatorEvent && previousActivatorEvent && previousActiveId != null) {\n      if (!isKeyboardEvent(previousActivatorEvent)) {\n        return;\n      }\n\n      if (document.activeElement === previousActivatorEvent.target) {\n        // No need to restore focus\n        return;\n      }\n\n      const draggableNode = draggableNodes.get(previousActiveId);\n\n      if (!draggableNode) {\n        return;\n      }\n\n      const {activatorNode, node} = draggableNode;\n\n      if (!activatorNode.current && !node.current) {\n        return;\n      }\n\n      requestAnimationFrame(() => {\n        for (const element of [activatorNode.current, node.current]) {\n          if (!element) {\n            continue;\n          }\n\n          const focusableNode = findFirstFocusableNode(element);\n\n          if (focusableNode) {\n            focusableNode.focus();\n            break;\n          }\n        }\n      });\n    }\n  }, [\n    activatorEvent,\n    disabled,\n    draggableNodes,\n    previousActiveId,\n    previousActivatorEvent,\n  ]);\n\n  return null;\n}\n", "import type {FirstArgument, Transform} from '@dnd-kit/utilities';\n\nimport type {Modifiers, Modifier} from './types';\n\nexport function applyModifiers(\n  modifiers: Modifiers | undefined,\n  {transform, ...args}: FirstArgument<Modifier>\n): Transform {\n  return modifiers?.length\n    ? modifiers.reduce<Transform>((accumulator, modifier) => {\n        return modifier({\n          transform: accumulator,\n          ...args,\n        });\n      }, transform)\n    : transform;\n}\n", "import {useMemo} from 'react';\nimport type {DeepRequired} from '@dnd-kit/utilities';\n\nimport {defaultMeasuringConfiguration} from '../defaults';\nimport type {MeasuringConfiguration} from '../types';\n\nexport function useMeasuringConfiguration(\n  config: MeasuringConfiguration | undefined\n): DeepRequired<MeasuringConfiguration> {\n  return useMemo(\n    () => ({\n      draggable: {\n        ...defaultMeasuringConfiguration.draggable,\n        ...config?.draggable,\n      },\n      droppable: {\n        ...defaultMeasuringConfiguration.droppable,\n        ...config?.droppable,\n      },\n      dragOverlay: {\n        ...defaultMeasuringConfiguration.dragOverlay,\n        ...config?.dragOverlay,\n      },\n    }),\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    [config?.draggable, config?.droppable, config?.dragOverlay]\n  );\n}\n", "import {useRef} from 'react';\nimport {useIsomorphicLayoutEffect} from '@dnd-kit/utilities';\n\nimport {getRectDelta} from '../../../utilities/rect';\nimport {getFirstScrollableAncestor} from '../../../utilities/scroll';\nimport type {ClientRect} from '../../../types';\nimport type {DraggableNode} from '../../../store';\nimport type {MeasuringFunction} from '../types';\n\ninterface Options {\n  activeNode: DraggableNode | null | undefined;\n  config: boolean | {x: boolean; y: boolean} | undefined;\n  initialRect: ClientRect | null;\n  measure: MeasuringFunction;\n}\n\nexport function useLayoutShiftScrollCompensation({\n  activeNode,\n  measure,\n  initialRect,\n  config = true,\n}: Options) {\n  const initialized = useRef(false);\n  const {x, y} = typeof config === 'boolean' ? {x: config, y: config} : config;\n\n  useIsomorphicLayoutEffect(() => {\n    const disabled = !x && !y;\n\n    if (disabled || !activeNode) {\n      initialized.current = false;\n      return;\n    }\n\n    if (initialized.current || !initialRect) {\n      // Return early if layout shift scroll compensation was already attempted\n      // or if there is no initialRect to compare to.\n      return;\n    }\n\n    // Get the most up to date node ref for the active draggable\n    const node = activeNode?.node.current;\n\n    if (!node || node.isConnected === false) {\n      // Return early if there is no attached node ref or if the node is\n      // disconnected from the document.\n      return;\n    }\n\n    const rect = measure(node);\n    const rectDelta = getRectDelta(rect, initialRect);\n\n    if (!x) {\n      rectDelta.x = 0;\n    }\n\n    if (!y) {\n      rectDelta.y = 0;\n    }\n\n    // Only perform layout shift scroll compensation once\n    initialized.current = true;\n\n    if (Math.abs(rectDelta.x) > 0 || Math.abs(rectDelta.y) > 0) {\n      const firstScrollableAncestor = getFirstScrollableAncestor(node);\n\n      if (firstScrollableAncestor) {\n        firstScrollableAncestor.scrollBy({\n          top: rectDelta.y,\n          left: rectDelta.x,\n        });\n      }\n    }\n  }, [activeNode, x, y, initialRect, measure]);\n}\n", "import React, {\n  memo,\n  createContext,\n  useCallback,\n  useEffect,\n  useMemo,\n  useReducer,\n  useRef,\n  useState,\n} from 'react';\nimport {unstable_batchedUpdates} from 'react-dom';\nimport {\n  add,\n  getEventCoordinates,\n  getWindow,\n  useLatestValue,\n  useIsomorphicLayoutEffect,\n  useUniqueId,\n} from '@dnd-kit/utilities';\nimport type {Transform} from '@dnd-kit/utilities';\n\nimport {\n  Action,\n  PublicContext,\n  InternalContext,\n  PublicContextDescriptor,\n  InternalContextDescriptor,\n  getInitialState,\n  reducer,\n} from '../../store';\nimport {DndMonitorContext, useDndMonitorProvider} from '../DndMonitor';\nimport {\n  useAutoScroller,\n  useCachedNode,\n  useCombineActivators,\n  useDragOverlayMeasuring,\n  useDroppableMeasuring,\n  useInitialRect,\n  useRect,\n  useRectDelta,\n  useRects,\n  useScrollableAncestors,\n  useScrollOffsets,\n  useScrollOffsetsDelta,\n  useSensorSetup,\n  useWindowRect,\n} from '../../hooks/utilities';\nimport type {AutoScrollOptions, SyntheticListener} from '../../hooks/utilities';\nimport type {\n  Sensor,\n  SensorContext,\n  SensorDescriptor,\n  SensorActivatorFunction,\n  SensorInstance,\n} from '../../sensors';\nimport {\n  adjustScale,\n  CollisionDetection,\n  defaultCoordinates,\n  getAdjustedRect,\n  getFirstCollision,\n  rectIntersection,\n} from '../../utilities';\nimport {applyModifiers, Modifiers} from '../../modifiers';\nimport type {Active, Over} from '../../store/types';\nimport type {\n  DragStartEvent,\n  DragCancelEvent,\n  DragEndEvent,\n  DragMoveEvent,\n  DragOverEvent,\n  UniqueIdentifier,\n  DragPendingEvent,\n  DragAbortEvent,\n} from '../../types';\nimport {\n  Accessibility,\n  Announcements,\n  RestoreFocus,\n  ScreenReaderInstructions,\n} from '../Accessibility';\n\nimport {defaultData, defaultSensors} from './defaults';\nimport {\n  useLayoutShiftScrollCompensation,\n  useMeasuringConfiguration,\n} from './hooks';\nimport type {MeasuringConfiguration} from './types';\n\nexport interface Props {\n  id?: string;\n  accessibility?: {\n    announcements?: Announcements;\n    container?: Element;\n    restoreFocus?: boolean;\n    screenReaderInstructions?: ScreenReaderInstructions;\n  };\n  autoScroll?: boolean | AutoScrollOptions;\n  cancelDrop?: CancelDrop;\n  children?: React.ReactNode;\n  collisionDetection?: CollisionDetection;\n  measuring?: MeasuringConfiguration;\n  modifiers?: Modifiers;\n  sensors?: SensorDescriptor<any>[];\n  onDragAbort?(event: DragAbortEvent): void;\n  onDragPending?(event: DragPendingEvent): void;\n  onDragStart?(event: DragStartEvent): void;\n  onDragMove?(event: DragMoveEvent): void;\n  onDragOver?(event: DragOverEvent): void;\n  onDragEnd?(event: DragEndEvent): void;\n  onDragCancel?(event: DragCancelEvent): void;\n}\n\nexport interface CancelDropArguments extends DragEndEvent {}\n\nexport type CancelDrop = (\n  args: CancelDropArguments\n) => boolean | Promise<boolean>;\n\ninterface DndEvent extends Event {\n  dndKit?: {\n    capturedBy: Sensor<any>;\n  };\n}\n\nexport const ActiveDraggableContext = createContext<Transform>({\n  ...defaultCoordinates,\n  scaleX: 1,\n  scaleY: 1,\n});\n\nenum Status {\n  Uninitialized,\n  Initializing,\n  Initialized,\n}\n\nexport const DndContext = memo(function DndContext({\n  id,\n  accessibility,\n  autoScroll = true,\n  children,\n  sensors = defaultSensors,\n  collisionDetection = rectIntersection,\n  measuring,\n  modifiers,\n  ...props\n}: Props) {\n  const store = useReducer(reducer, undefined, getInitialState);\n  const [state, dispatch] = store;\n  const [dispatchMonitorEvent, registerMonitorListener] =\n    useDndMonitorProvider();\n  const [status, setStatus] = useState<Status>(Status.Uninitialized);\n  const isInitialized = status === Status.Initialized;\n  const {\n    draggable: {active: activeId, nodes: draggableNodes, translate},\n    droppable: {containers: droppableContainers},\n  } = state;\n  const node = activeId != null ? draggableNodes.get(activeId) : null;\n  const activeRects = useRef<Active['rect']['current']>({\n    initial: null,\n    translated: null,\n  });\n  const active = useMemo<Active | null>(\n    () =>\n      activeId != null\n        ? {\n            id: activeId,\n            // It's possible for the active node to unmount while dragging\n            data: node?.data ?? defaultData,\n            rect: activeRects,\n          }\n        : null,\n    [activeId, node]\n  );\n  const activeRef = useRef<UniqueIdentifier | null>(null);\n  const [activeSensor, setActiveSensor] = useState<SensorInstance | null>(null);\n  const [activatorEvent, setActivatorEvent] = useState<Event | null>(null);\n  const latestProps = useLatestValue(props, Object.values(props));\n  const draggableDescribedById = useUniqueId(`DndDescribedBy`, id);\n  const enabledDroppableContainers = useMemo(\n    () => droppableContainers.getEnabled(),\n    [droppableContainers]\n  );\n  const measuringConfiguration = useMeasuringConfiguration(measuring);\n  const {droppableRects, measureDroppableContainers, measuringScheduled} =\n    useDroppableMeasuring(enabledDroppableContainers, {\n      dragging: isInitialized,\n      dependencies: [translate.x, translate.y],\n      config: measuringConfiguration.droppable,\n    });\n  const activeNode = useCachedNode(draggableNodes, activeId);\n  const activationCoordinates = useMemo(\n    () => (activatorEvent ? getEventCoordinates(activatorEvent) : null),\n    [activatorEvent]\n  );\n  const autoScrollOptions = getAutoScrollerOptions();\n  const initialActiveNodeRect = useInitialRect(\n    activeNode,\n    measuringConfiguration.draggable.measure\n  );\n\n  useLayoutShiftScrollCompensation({\n    activeNode: activeId != null ? draggableNodes.get(activeId) : null,\n    config: autoScrollOptions.layoutShiftCompensation,\n    initialRect: initialActiveNodeRect,\n    measure: measuringConfiguration.draggable.measure,\n  });\n\n  const activeNodeRect = useRect(\n    activeNode,\n    measuringConfiguration.draggable.measure,\n    initialActiveNodeRect\n  );\n  const containerNodeRect = useRect(\n    activeNode ? activeNode.parentElement : null\n  );\n  const sensorContext = useRef<SensorContext>({\n    activatorEvent: null,\n    active: null,\n    activeNode,\n    collisionRect: null,\n    collisions: null,\n    droppableRects,\n    draggableNodes,\n    draggingNode: null,\n    draggingNodeRect: null,\n    droppableContainers,\n    over: null,\n    scrollableAncestors: [],\n    scrollAdjustedTranslate: null,\n  });\n  const overNode = droppableContainers.getNodeFor(\n    sensorContext.current.over?.id\n  );\n  const dragOverlay = useDragOverlayMeasuring({\n    measure: measuringConfiguration.dragOverlay.measure,\n  });\n\n  // Use the rect of the drag overlay if it is mounted\n  const draggingNode = dragOverlay.nodeRef.current ?? activeNode;\n  const draggingNodeRect = isInitialized\n    ? dragOverlay.rect ?? activeNodeRect\n    : null;\n  const usesDragOverlay = Boolean(\n    dragOverlay.nodeRef.current && dragOverlay.rect\n  );\n  // The delta between the previous and new position of the draggable node\n  // is only relevant when there is no drag overlay\n  const nodeRectDelta = useRectDelta(usesDragOverlay ? null : activeNodeRect);\n\n  // Get the window rect of the dragging node\n  const windowRect = useWindowRect(\n    draggingNode ? getWindow(draggingNode) : null\n  );\n\n  // Get scrollable ancestors of the dragging node\n  const scrollableAncestors = useScrollableAncestors(\n    isInitialized ? overNode ?? activeNode : null\n  );\n  const scrollableAncestorRects = useRects(scrollableAncestors);\n\n  // Apply modifiers\n  const modifiedTranslate = applyModifiers(modifiers, {\n    transform: {\n      x: translate.x - nodeRectDelta.x,\n      y: translate.y - nodeRectDelta.y,\n      scaleX: 1,\n      scaleY: 1,\n    },\n    activatorEvent,\n    active,\n    activeNodeRect,\n    containerNodeRect,\n    draggingNodeRect,\n    over: sensorContext.current.over,\n    overlayNodeRect: dragOverlay.rect,\n    scrollableAncestors,\n    scrollableAncestorRects,\n    windowRect,\n  });\n\n  const pointerCoordinates = activationCoordinates\n    ? add(activationCoordinates, translate)\n    : null;\n\n  const scrollOffsets = useScrollOffsets(scrollableAncestors);\n  // Represents the scroll delta since dragging was initiated\n  const scrollAdjustment = useScrollOffsetsDelta(scrollOffsets);\n  // Represents the scroll delta since the last time the active node rect was measured\n  const activeNodeScrollDelta = useScrollOffsetsDelta(scrollOffsets, [\n    activeNodeRect,\n  ]);\n\n  const scrollAdjustedTranslate = add(modifiedTranslate, scrollAdjustment);\n\n  const collisionRect = draggingNodeRect\n    ? getAdjustedRect(draggingNodeRect, modifiedTranslate)\n    : null;\n\n  const collisions =\n    active && collisionRect\n      ? collisionDetection({\n          active,\n          collisionRect,\n          droppableRects,\n          droppableContainers: enabledDroppableContainers,\n          pointerCoordinates,\n        })\n      : null;\n  const overId = getFirstCollision(collisions, 'id');\n  const [over, setOver] = useState<Over | null>(null);\n\n  // When there is no drag overlay used, we need to account for the\n  // window scroll delta\n  const appliedTranslate = usesDragOverlay\n    ? modifiedTranslate\n    : add(modifiedTranslate, activeNodeScrollDelta);\n\n  const transform = adjustScale(\n    appliedTranslate,\n    over?.rect ?? null,\n    activeNodeRect\n  );\n\n  const activeSensorRef = useRef<SensorInstance | null>(null);\n  const instantiateSensor = useCallback(\n    (\n      event: React.SyntheticEvent,\n      {sensor: Sensor, options}: SensorDescriptor<any>\n    ) => {\n      if (activeRef.current == null) {\n        return;\n      }\n\n      const activeNode = draggableNodes.get(activeRef.current);\n\n      if (!activeNode) {\n        return;\n      }\n\n      const activatorEvent = event.nativeEvent;\n\n      const sensorInstance = new Sensor({\n        active: activeRef.current,\n        activeNode,\n        event: activatorEvent,\n        options,\n        // Sensors need to be instantiated with refs for arguments that change over time\n        // otherwise they are frozen in time with the stale arguments\n        context: sensorContext,\n        onAbort(id) {\n          const draggableNode = draggableNodes.get(id);\n\n          if (!draggableNode) {\n            return;\n          }\n\n          const {onDragAbort} = latestProps.current;\n          const event: DragAbortEvent = {id};\n          onDragAbort?.(event);\n          dispatchMonitorEvent({type: 'onDragAbort', event});\n        },\n        onPending(id, constraint, initialCoordinates, offset) {\n          const draggableNode = draggableNodes.get(id);\n\n          if (!draggableNode) {\n            return;\n          }\n\n          const {onDragPending} = latestProps.current;\n          const event: DragPendingEvent = {\n            id,\n            constraint,\n            initialCoordinates,\n            offset,\n          };\n\n          onDragPending?.(event);\n          dispatchMonitorEvent({type: 'onDragPending', event});\n        },\n        onStart(initialCoordinates) {\n          const id = activeRef.current;\n\n          if (id == null) {\n            return;\n          }\n\n          const draggableNode = draggableNodes.get(id);\n\n          if (!draggableNode) {\n            return;\n          }\n\n          const {onDragStart} = latestProps.current;\n          const event: DragStartEvent = {\n            activatorEvent,\n            active: {id, data: draggableNode.data, rect: activeRects},\n          };\n\n          unstable_batchedUpdates(() => {\n            onDragStart?.(event);\n            setStatus(Status.Initializing);\n            dispatch({\n              type: Action.DragStart,\n              initialCoordinates,\n              active: id,\n            });\n            dispatchMonitorEvent({type: 'onDragStart', event});\n            setActiveSensor(activeSensorRef.current);\n            setActivatorEvent(activatorEvent);\n          });\n        },\n        onMove(coordinates) {\n          dispatch({\n            type: Action.DragMove,\n            coordinates,\n          });\n        },\n        onEnd: createHandler(Action.DragEnd),\n        onCancel: createHandler(Action.DragCancel),\n      });\n\n      activeSensorRef.current = sensorInstance;\n\n      function createHandler(type: Action.DragEnd | Action.DragCancel) {\n        return async function handler() {\n          const {active, collisions, over, scrollAdjustedTranslate} =\n            sensorContext.current;\n          let event: DragEndEvent | null = null;\n\n          if (active && scrollAdjustedTranslate) {\n            const {cancelDrop} = latestProps.current;\n\n            event = {\n              activatorEvent,\n              active: active,\n              collisions,\n              delta: scrollAdjustedTranslate,\n              over,\n            };\n\n            if (type === Action.DragEnd && typeof cancelDrop === 'function') {\n              const shouldCancel = await Promise.resolve(cancelDrop(event));\n\n              if (shouldCancel) {\n                type = Action.DragCancel;\n              }\n            }\n          }\n\n          activeRef.current = null;\n\n          unstable_batchedUpdates(() => {\n            dispatch({type});\n            setStatus(Status.Uninitialized);\n            setOver(null);\n            setActiveSensor(null);\n            setActivatorEvent(null);\n            activeSensorRef.current = null;\n\n            const eventName =\n              type === Action.DragEnd ? 'onDragEnd' : 'onDragCancel';\n\n            if (event) {\n              const handler = latestProps.current[eventName];\n\n              handler?.(event);\n              dispatchMonitorEvent({type: eventName, event});\n            }\n          });\n        };\n      }\n    },\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    [draggableNodes]\n  );\n\n  const bindActivatorToSensorInstantiator = useCallback(\n    (\n      handler: SensorActivatorFunction<any>,\n      sensor: SensorDescriptor<any>\n    ): SyntheticListener['handler'] => {\n      return (event, active) => {\n        const nativeEvent = event.nativeEvent as DndEvent;\n        const activeDraggableNode = draggableNodes.get(active);\n\n        if (\n          // Another sensor is already instantiating\n          activeRef.current !== null ||\n          // No active draggable\n          !activeDraggableNode ||\n          // Event has already been captured\n          nativeEvent.dndKit ||\n          nativeEvent.defaultPrevented\n        ) {\n          return;\n        }\n\n        const activationContext = {\n          active: activeDraggableNode,\n        };\n        const shouldActivate = handler(\n          event,\n          sensor.options,\n          activationContext\n        );\n\n        if (shouldActivate === true) {\n          nativeEvent.dndKit = {\n            capturedBy: sensor.sensor,\n          };\n\n          activeRef.current = active;\n          instantiateSensor(event, sensor);\n        }\n      };\n    },\n    [draggableNodes, instantiateSensor]\n  );\n\n  const activators = useCombineActivators(\n    sensors,\n    bindActivatorToSensorInstantiator\n  );\n\n  useSensorSetup(sensors);\n\n  useIsomorphicLayoutEffect(() => {\n    if (activeNodeRect && status === Status.Initializing) {\n      setStatus(Status.Initialized);\n    }\n  }, [activeNodeRect, status]);\n\n  useEffect(\n    () => {\n      const {onDragMove} = latestProps.current;\n      const {active, activatorEvent, collisions, over} = sensorContext.current;\n\n      if (!active || !activatorEvent) {\n        return;\n      }\n\n      const event: DragMoveEvent = {\n        active,\n        activatorEvent,\n        collisions,\n        delta: {\n          x: scrollAdjustedTranslate.x,\n          y: scrollAdjustedTranslate.y,\n        },\n        over,\n      };\n\n      unstable_batchedUpdates(() => {\n        onDragMove?.(event);\n        dispatchMonitorEvent({type: 'onDragMove', event});\n      });\n    },\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    [scrollAdjustedTranslate.x, scrollAdjustedTranslate.y]\n  );\n\n  useEffect(\n    () => {\n      const {\n        active,\n        activatorEvent,\n        collisions,\n        droppableContainers,\n        scrollAdjustedTranslate,\n      } = sensorContext.current;\n\n      if (\n        !active ||\n        activeRef.current == null ||\n        !activatorEvent ||\n        !scrollAdjustedTranslate\n      ) {\n        return;\n      }\n\n      const {onDragOver} = latestProps.current;\n      const overContainer = droppableContainers.get(overId);\n      const over =\n        overContainer && overContainer.rect.current\n          ? {\n              id: overContainer.id,\n              rect: overContainer.rect.current,\n              data: overContainer.data,\n              disabled: overContainer.disabled,\n            }\n          : null;\n      const event: DragOverEvent = {\n        active,\n        activatorEvent,\n        collisions,\n        delta: {\n          x: scrollAdjustedTranslate.x,\n          y: scrollAdjustedTranslate.y,\n        },\n        over,\n      };\n\n      unstable_batchedUpdates(() => {\n        setOver(over);\n        onDragOver?.(event);\n        dispatchMonitorEvent({type: 'onDragOver', event});\n      });\n    },\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    [overId]\n  );\n\n  useIsomorphicLayoutEffect(() => {\n    sensorContext.current = {\n      activatorEvent,\n      active,\n      activeNode,\n      collisionRect,\n      collisions,\n      droppableRects,\n      draggableNodes,\n      draggingNode,\n      draggingNodeRect,\n      droppableContainers,\n      over,\n      scrollableAncestors,\n      scrollAdjustedTranslate,\n    };\n\n    activeRects.current = {\n      initial: draggingNodeRect,\n      translated: collisionRect,\n    };\n  }, [\n    active,\n    activeNode,\n    collisions,\n    collisionRect,\n    draggableNodes,\n    draggingNode,\n    draggingNodeRect,\n    droppableRects,\n    droppableContainers,\n    over,\n    scrollableAncestors,\n    scrollAdjustedTranslate,\n  ]);\n\n  useAutoScroller({\n    ...autoScrollOptions,\n    delta: translate,\n    draggingRect: collisionRect,\n    pointerCoordinates,\n    scrollableAncestors,\n    scrollableAncestorRects,\n  });\n\n  const publicContext = useMemo(() => {\n    const context: PublicContextDescriptor = {\n      active,\n      activeNode,\n      activeNodeRect,\n      activatorEvent,\n      collisions,\n      containerNodeRect,\n      dragOverlay,\n      draggableNodes,\n      droppableContainers,\n      droppableRects,\n      over,\n      measureDroppableContainers,\n      scrollableAncestors,\n      scrollableAncestorRects,\n      measuringConfiguration,\n      measuringScheduled,\n      windowRect,\n    };\n\n    return context;\n  }, [\n    active,\n    activeNode,\n    activeNodeRect,\n    activatorEvent,\n    collisions,\n    containerNodeRect,\n    dragOverlay,\n    draggableNodes,\n    droppableContainers,\n    droppableRects,\n    over,\n    measureDroppableContainers,\n    scrollableAncestors,\n    scrollableAncestorRects,\n    measuringConfiguration,\n    measuringScheduled,\n    windowRect,\n  ]);\n\n  const internalContext = useMemo(() => {\n    const context: InternalContextDescriptor = {\n      activatorEvent,\n      activators,\n      active,\n      activeNodeRect,\n      ariaDescribedById: {\n        draggable: draggableDescribedById,\n      },\n      dispatch,\n      draggableNodes,\n      over,\n      measureDroppableContainers,\n    };\n\n    return context;\n  }, [\n    activatorEvent,\n    activators,\n    active,\n    activeNodeRect,\n    dispatch,\n    draggableDescribedById,\n    draggableNodes,\n    over,\n    measureDroppableContainers,\n  ]);\n\n  return (\n    <DndMonitorContext.Provider value={registerMonitorListener}>\n      <InternalContext.Provider value={internalContext}>\n        <PublicContext.Provider value={publicContext}>\n          <ActiveDraggableContext.Provider value={transform}>\n            {children}\n          </ActiveDraggableContext.Provider>\n        </PublicContext.Provider>\n        <RestoreFocus disabled={accessibility?.restoreFocus === false} />\n      </InternalContext.Provider>\n      <Accessibility\n        {...accessibility}\n        hiddenTextDescribedById={draggableDescribedById}\n      />\n    </DndMonitorContext.Provider>\n  );\n\n  function getAutoScrollerOptions() {\n    const activeSensorDisablesAutoscroll =\n      activeSensor?.autoScrollEnabled === false;\n    const autoScrollGloballyDisabled =\n      typeof autoScroll === 'object'\n        ? autoScroll.enabled === false\n        : autoScroll === false;\n    const enabled =\n      isInitialized &&\n      !activeSensorDisablesAutoscroll &&\n      !autoScrollGloballyDisabled;\n\n    if (typeof autoScroll === 'object') {\n      return {\n        ...autoScroll,\n        enabled,\n      };\n    }\n\n    return {enabled};\n  }\n});\n", "import {createContext, useContext, useMemo} from 'react';\nimport {\n  Transform,\n  useNodeRef,\n  useIsomorphicLayoutEffect,\n  useLatestValue,\n  useUniqueId,\n} from '@dnd-kit/utilities';\n\nimport {InternalContext, Data} from '../store';\nimport type {UniqueIdentifier} from '../types';\nimport {ActiveDraggableContext} from '../components/DndContext';\nimport {useSyntheticListeners, SyntheticListenerMap} from './utilities';\n\nexport interface UseDraggableArguments {\n  id: UniqueIdentifier;\n  data?: Data;\n  disabled?: boolean;\n  attributes?: {\n    role?: string;\n    roleDescription?: string;\n    tabIndex?: number;\n  };\n}\n\nexport interface DraggableAttributes {\n  role: string;\n  tabIndex: number;\n  'aria-disabled': boolean;\n  'aria-pressed': boolean | undefined;\n  'aria-roledescription': string;\n  'aria-describedby': string;\n}\n\nexport type DraggableSyntheticListeners = SyntheticListenerMap | undefined;\n\nconst NullContext = createContext<any>(null);\n\nconst defaultRole = 'button';\n\nconst ID_PREFIX = 'Draggable';\n\nexport function useDraggable({\n  id,\n  data,\n  disabled = false,\n  attributes,\n}: UseDraggableArguments) {\n  const key = useUniqueId(ID_PREFIX);\n  const {\n    activators,\n    activatorEvent,\n    active,\n    activeNodeRect,\n    ariaDescribedById,\n    draggableNodes,\n    over,\n  } = useContext(InternalContext);\n  const {\n    role = defaultRole,\n    roleDescription = 'draggable',\n    tabIndex = 0,\n  } = attributes ?? {};\n  const isDragging = active?.id === id;\n  const transform: Transform | null = useContext(\n    isDragging ? ActiveDraggableContext : NullContext\n  );\n  const [node, setNodeRef] = useNodeRef();\n  const [activatorNode, setActivatorNodeRef] = useNodeRef();\n  const listeners = useSyntheticListeners(activators, id);\n  const dataRef = useLatestValue(data);\n\n  useIsomorphicLayoutEffect(\n    () => {\n      draggableNodes.set(id, {id, key, node, activatorNode, data: dataRef});\n\n      return () => {\n        const node = draggableNodes.get(id);\n\n        if (node && node.key === key) {\n          draggableNodes.delete(id);\n        }\n      };\n    },\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    [draggableNodes, id]\n  );\n\n  const memoizedAttributes: DraggableAttributes = useMemo(\n    () => ({\n      role,\n      tabIndex,\n      'aria-disabled': disabled,\n      'aria-pressed': isDragging && role === defaultRole ? true : undefined,\n      'aria-roledescription': roleDescription,\n      'aria-describedby': ariaDescribedById.draggable,\n    }),\n    [\n      disabled,\n      role,\n      tabIndex,\n      isDragging,\n      roleDescription,\n      ariaDescribedById.draggable,\n    ]\n  );\n\n  return {\n    active,\n    activatorEvent,\n    activeNodeRect,\n    attributes: memoizedAttributes,\n    isDragging,\n    listeners: disabled ? undefined : listeners,\n    node,\n    over,\n    setNodeRef,\n    setActivatorNodeRef,\n    transform,\n  };\n}\n", "import {ContextType, useContext} from 'react';\nimport {PublicContext} from '../store';\n\nexport function useDndContext() {\n  return useContext(PublicContext);\n}\n\nexport type UseDndContextReturnValue = ContextType<typeof PublicContext>;\n", "import {useCallback, useContext, useEffect, useRef} from 'react';\nimport {useLatestValue, useNodeRef, useUniqueId} from '@dnd-kit/utilities';\n\nimport {InternalContext, Action, Data} from '../store';\nimport type {ClientRect, UniqueIdentifier} from '../types';\n\nimport {useResizeObserver} from './utilities';\n\ninterface ResizeObserverConfig {\n  /** Whether the ResizeObserver should be disabled entirely */\n  disabled?: boolean;\n  /** Resize events may affect the layout and position of other droppable containers.\n   * Specify an array of `UniqueIdentifier` of droppable containers that should also be re-measured\n   * when this droppable container resizes. Specifying an empty array re-measures all droppable containers.\n   */\n  updateMeasurementsFor?: UniqueIdentifier[];\n  /** Represents the debounce timeout between when resize events are observed and when elements are re-measured */\n  timeout?: number;\n}\n\nexport interface UseDroppableArguments {\n  id: UniqueIdentifier;\n  disabled?: boolean;\n  data?: Data;\n  resizeObserverConfig?: ResizeObserverConfig;\n}\n\nconst ID_PREFIX = 'Droppable';\n\nconst defaultResizeObserverConfig = {\n  timeout: 25,\n};\n\nexport function useDroppable({\n  data,\n  disabled = false,\n  id,\n  resizeObserverConfig,\n}: UseDroppableArguments) {\n  const key = useUniqueId(ID_PREFIX);\n  const {active, dispatch, over, measureDroppableContainers} =\n    useContext(InternalContext);\n  const previous = useRef({disabled});\n  const resizeObserverConnected = useRef(false);\n  const rect = useRef<ClientRect | null>(null);\n  const callbackId = useRef<NodeJS.Timeout | null>(null);\n  const {\n    disabled: resizeObserverDisabled,\n    updateMeasurementsFor,\n    timeout: resizeObserverTimeout,\n  } = {\n    ...defaultResizeObserverConfig,\n    ...resizeObserverConfig,\n  };\n  const ids = useLatestValue(updateMeasurementsFor ?? id);\n  const handleResize = useCallback(\n    () => {\n      if (!resizeObserverConnected.current) {\n        // ResizeObserver invokes the `handleResize` callback as soon as `observe` is called,\n        // assuming the element is rendered and displayed.\n        resizeObserverConnected.current = true;\n        return;\n      }\n\n      if (callbackId.current != null) {\n        clearTimeout(callbackId.current);\n      }\n\n      callbackId.current = setTimeout(() => {\n        measureDroppableContainers(\n          Array.isArray(ids.current) ? ids.current : [ids.current]\n        );\n        callbackId.current = null;\n      }, resizeObserverTimeout);\n    },\n    //eslint-disable-next-line react-hooks/exhaustive-deps\n    [resizeObserverTimeout]\n  );\n  const resizeObserver = useResizeObserver({\n    callback: handleResize,\n    disabled: resizeObserverDisabled || !active,\n  });\n  const handleNodeChange = useCallback(\n    (newElement: HTMLElement | null, previousElement: HTMLElement | null) => {\n      if (!resizeObserver) {\n        return;\n      }\n\n      if (previousElement) {\n        resizeObserver.unobserve(previousElement);\n        resizeObserverConnected.current = false;\n      }\n\n      if (newElement) {\n        resizeObserver.observe(newElement);\n      }\n    },\n    [resizeObserver]\n  );\n  const [nodeRef, setNodeRef] = useNodeRef(handleNodeChange);\n  const dataRef = useLatestValue(data);\n\n  useEffect(() => {\n    if (!resizeObserver || !nodeRef.current) {\n      return;\n    }\n\n    resizeObserver.disconnect();\n    resizeObserverConnected.current = false;\n    resizeObserver.observe(nodeRef.current);\n  }, [nodeRef, resizeObserver]);\n\n  useEffect(\n    () => {\n      dispatch({\n        type: Action.RegisterDroppable,\n        element: {\n          id,\n          key,\n          disabled,\n          node: nodeRef,\n          rect,\n          data: dataRef,\n        },\n      });\n\n      return () =>\n        dispatch({\n          type: Action.UnregisterDroppable,\n          key,\n          id,\n        });\n    },\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    [id]\n  );\n\n  useEffect(() => {\n    if (disabled !== previous.current.disabled) {\n      dispatch({\n        type: Action.SetDroppableDisabled,\n        id,\n        key,\n        disabled,\n      });\n\n      previous.current.disabled = disabled;\n    }\n  }, [id, key, disabled, dispatch]);\n\n  return {\n    active,\n    rect,\n    isOver: over?.id === id,\n    node: nodeRef,\n    over,\n    setNodeRef,\n  };\n}\n", "import React, {cloneElement, useState} from 'react';\nimport {useIsomorphicLayoutEffect, usePrevious} from '@dnd-kit/utilities';\n\nimport type {UniqueIdentifier} from '../../../../types';\n\nexport type Animation = (\n  key: UniqueIdentifier,\n  node: HTMLElement\n) => Promise<void> | void;\n\nexport interface Props {\n  animation: Animation;\n  children: React.ReactElement | null;\n}\n\nexport function AnimationManager({animation, children}: Props) {\n  const [\n    clonedChildren,\n    setClonedChildren,\n  ] = useState<React.ReactElement | null>(null);\n  const [element, setElement] = useState<HTMLElement | null>(null);\n  const previousChildren = usePrevious(children);\n\n  if (!children && !clonedChildren && previousChildren) {\n    setClonedChildren(previousChildren);\n  }\n\n  useIsomorphicLayoutEffect(() => {\n    if (!element) {\n      return;\n    }\n\n    const key = clonedChildren?.key;\n    const id = clonedChildren?.props.id;\n\n    if (key == null || id == null) {\n      setClonedChildren(null);\n      return;\n    }\n\n    Promise.resolve(animation(id, element)).then(() => {\n      setClonedChildren(null);\n    });\n  }, [animation, clonedChildren, element]);\n\n  return (\n    <>\n      {children}\n      {clonedChildren ? cloneElement(clonedChildren, {ref: setElement}) : null}\n    </>\n  );\n}\n", "import React from 'react';\nimport type {Transform} from '@dnd-kit/utilities';\n\nimport {InternalContext, defaultInternalContext} from '../../../../store';\nimport {ActiveDraggableContext} from '../../../DndContext';\n\ninterface Props {\n  children: React.ReactNode;\n}\n\nconst defaultTransform: Transform = {\n  x: 0,\n  y: 0,\n  scaleX: 1,\n  scaleY: 1,\n};\n\nexport function NullifiedContextProvider({children}: Props) {\n  return (\n    <InternalContext.Provider value={defaultInternalContext}>\n      <ActiveDraggableContext.Provider value={defaultTransform}>\n        {children}\n      </ActiveDraggableContext.Provider>\n    </InternalContext.Provider>\n  );\n}\n", "import React, {forwardRef} from 'react';\nimport {CSS, isKeyboardEvent} from '@dnd-kit/utilities';\n\nimport type {Transform} from '@dnd-kit/utilities';\n\nimport {getRelativeTransformOrigin} from '../../../../utilities';\nimport type {ClientRect, UniqueIdentifier} from '../../../../types';\n\ntype TransitionGetter = (\n  activatorEvent: Event | null\n) => React.CSSProperties['transition'] | undefined;\n\nexport interface Props {\n  as: keyof JSX.IntrinsicElements;\n  activatorEvent: Event | null;\n  adjustScale?: boolean;\n  children?: React.ReactNode;\n  className?: string;\n  id: UniqueIdentifier;\n  rect: ClientRect | null;\n  style?: React.CSSProperties;\n  transition?: string | TransitionGetter;\n  transform: Transform;\n}\n\nconst baseStyles: React.CSSProperties = {\n  position: 'fixed',\n  touchAction: 'none',\n};\n\nconst defaultTransition: TransitionGetter = (activatorEvent) => {\n  const isKeyboardActivator = isKeyboardEvent(activatorEvent);\n\n  return isKeyboardActivator ? 'transform 250ms ease' : undefined;\n};\n\nexport const PositionedOverlay = forwardRef<HTMLElement, Props>(\n  (\n    {\n      as,\n      activatorEvent,\n      adjustScale,\n      children,\n      className,\n      rect,\n      style,\n      transform,\n      transition = defaultTransition,\n    },\n    ref\n  ) => {\n    if (!rect) {\n      return null;\n    }\n\n    const scaleAdjustedTransform = adjustScale\n      ? transform\n      : {\n          ...transform,\n          scaleX: 1,\n          scaleY: 1,\n        };\n    const styles: React.CSSProperties | undefined = {\n      ...baseStyles,\n      width: rect.width,\n      height: rect.height,\n      top: rect.top,\n      left: rect.left,\n      transform: CSS.Transform.toString(scaleAdjustedTransform),\n      transformOrigin:\n        adjustScale && activatorEvent\n          ? getRelativeTransformOrigin(\n              activatorEvent as MouseEvent | KeyboardEvent | TouchEvent,\n              rect\n            )\n          : undefined,\n      transition:\n        typeof transition === 'function'\n          ? transition(activatorEvent)\n          : transition,\n      ...style,\n    };\n\n    return React.createElement(\n      as,\n      {\n        className,\n        style: styles,\n        ref,\n      },\n      children\n    );\n  }\n);\n", "import {CSS, useEvent, getWindow} from '@dnd-kit/utilities';\nimport type {DeepRequired, Transform} from '@dnd-kit/utilities';\n\nimport type {\n  Active,\n  DraggableNode,\n  DraggableNodes,\n  DroppableContainers,\n} from '../../../store';\nimport type {ClientRect, UniqueIdentifier} from '../../../types';\nimport {getMeasurableNode} from '../../../utilities/nodes';\nimport {scrollIntoViewIfNeeded} from '../../../utilities/scroll';\nimport {parseTransform} from '../../../utilities/transform';\nimport type {MeasuringConfiguration} from '../../DndContext';\nimport type {Animation} from '../components';\n\ninterface SharedParameters {\n  active: {\n    id: UniqueIdentifier;\n    data: Active['data'];\n    node: HTMLElement;\n    rect: ClientRect;\n  };\n  dragOverlay: {\n    node: HTMLElement;\n    rect: ClientRect;\n  };\n  draggableNodes: DraggableNodes;\n  droppableContainers: DroppableContainers;\n  measuringConfiguration: DeepRequired<MeasuringConfiguration>;\n}\n\nexport interface KeyframeResolverParameters extends SharedParameters {\n  transform: {\n    initial: Transform;\n    final: Transform;\n  };\n}\n\nexport type KeyframeResolver = (\n  parameters: KeyframeResolverParameters\n) => Keyframe[];\n\nexport interface DropAnimationOptions {\n  keyframes?: KeyframeResolver;\n  duration?: number;\n  easing?: string;\n  sideEffects?: DropAnimationSideEffects | null;\n}\n\nexport type DropAnimation = DropAnimationFunction | DropAnimationOptions;\n\ninterface Arguments {\n  draggableNodes: DraggableNodes;\n  droppableContainers: DroppableContainers;\n  measuringConfiguration: DeepRequired<MeasuringConfiguration>;\n  config?: DropAnimation | null;\n}\n\nexport interface DropAnimationFunctionArguments extends SharedParameters {\n  transform: Transform;\n}\n\nexport type DropAnimationFunction = (\n  args: DropAnimationFunctionArguments\n) => Promise<void> | void;\n\ntype CleanupFunction = () => void;\n\nexport interface DropAnimationSideEffectsParameters extends SharedParameters {}\n\nexport type DropAnimationSideEffects = (\n  parameters: DropAnimationSideEffectsParameters\n) => CleanupFunction | void;\n\ntype ExtractStringProperties<T> = {\n  [K in keyof T]?: T[K] extends string ? string : never;\n};\n\ntype Styles = ExtractStringProperties<CSSStyleDeclaration>;\n\ninterface DefaultDropAnimationSideEffectsOptions {\n  className?: {\n    active?: string;\n    dragOverlay?: string;\n  };\n  styles?: {\n    active?: Styles;\n    dragOverlay?: Styles;\n  };\n}\n\nexport const defaultDropAnimationSideEffects = (\n  options: DefaultDropAnimationSideEffectsOptions\n): DropAnimationSideEffects => ({active, dragOverlay}) => {\n  const originalStyles: Record<string, string> = {};\n  const {styles, className} = options;\n\n  if (styles?.active) {\n    for (const [key, value] of Object.entries(styles.active)) {\n      if (value === undefined) {\n        continue;\n      }\n\n      originalStyles[key] = active.node.style.getPropertyValue(key);\n      active.node.style.setProperty(key, value);\n    }\n  }\n\n  if (styles?.dragOverlay) {\n    for (const [key, value] of Object.entries(styles.dragOverlay)) {\n      if (value === undefined) {\n        continue;\n      }\n\n      dragOverlay.node.style.setProperty(key, value);\n    }\n  }\n\n  if (className?.active) {\n    active.node.classList.add(className.active);\n  }\n\n  if (className?.dragOverlay) {\n    dragOverlay.node.classList.add(className.dragOverlay);\n  }\n\n  return function cleanup() {\n    for (const [key, value] of Object.entries(originalStyles)) {\n      active.node.style.setProperty(key, value);\n    }\n\n    if (className?.active) {\n      active.node.classList.remove(className.active);\n    }\n  };\n};\n\nconst defaultKeyframeResolver: KeyframeResolver = ({\n  transform: {initial, final},\n}) => [\n  {\n    transform: CSS.Transform.toString(initial),\n  },\n  {\n    transform: CSS.Transform.toString(final),\n  },\n];\n\nexport const defaultDropAnimationConfiguration: Required<DropAnimationOptions> = {\n  duration: 250,\n  easing: 'ease',\n  keyframes: defaultKeyframeResolver,\n  sideEffects: defaultDropAnimationSideEffects({\n    styles: {\n      active: {\n        opacity: '0',\n      },\n    },\n  }),\n};\n\nexport function useDropAnimation({\n  config,\n  draggableNodes,\n  droppableContainers,\n  measuringConfiguration,\n}: Arguments) {\n  return useEvent<Animation>((id, node) => {\n    if (config === null) {\n      return;\n    }\n\n    const activeDraggable: DraggableNode | undefined = draggableNodes.get(id);\n\n    if (!activeDraggable) {\n      return;\n    }\n\n    const activeNode = activeDraggable.node.current;\n\n    if (!activeNode) {\n      return;\n    }\n\n    const measurableNode = getMeasurableNode(node);\n\n    if (!measurableNode) {\n      return;\n    }\n    const {transform} = getWindow(node).getComputedStyle(node);\n    const parsedTransform = parseTransform(transform);\n\n    if (!parsedTransform) {\n      return;\n    }\n\n    const animation: DropAnimationFunction =\n      typeof config === 'function'\n        ? config\n        : createDefaultDropAnimation(config);\n\n    scrollIntoViewIfNeeded(\n      activeNode,\n      measuringConfiguration.draggable.measure\n    );\n\n    return animation({\n      active: {\n        id,\n        data: activeDraggable.data,\n        node: activeNode,\n        rect: measuringConfiguration.draggable.measure(activeNode),\n      },\n      draggableNodes,\n      dragOverlay: {\n        node,\n        rect: measuringConfiguration.dragOverlay.measure(measurableNode),\n      },\n      droppableContainers,\n      measuringConfiguration,\n      transform: parsedTransform,\n    });\n  });\n}\n\nfunction createDefaultDropAnimation(\n  options: DropAnimationOptions | undefined\n): DropAnimationFunction {\n  const {duration, easing, sideEffects, keyframes} = {\n    ...defaultDropAnimationConfiguration,\n    ...options,\n  };\n\n  return ({active, dragOverlay, transform, ...rest}) => {\n    if (!duration) {\n      // Do not animate if animation duration is zero.\n      return;\n    }\n\n    const delta = {\n      x: dragOverlay.rect.left - active.rect.left,\n      y: dragOverlay.rect.top - active.rect.top,\n    };\n\n    const scale = {\n      scaleX:\n        transform.scaleX !== 1\n          ? (active.rect.width * transform.scaleX) / dragOverlay.rect.width\n          : 1,\n      scaleY:\n        transform.scaleY !== 1\n          ? (active.rect.height * transform.scaleY) / dragOverlay.rect.height\n          : 1,\n    };\n    const finalTransform = {\n      x: transform.x - delta.x,\n      y: transform.y - delta.y,\n      ...scale,\n    };\n\n    const animationKeyframes = keyframes({\n      ...rest,\n      active,\n      dragOverlay,\n      transform: {initial: transform, final: finalTransform},\n    });\n\n    const [firstKeyframe] = animationKeyframes;\n    const lastKeyframe = animationKeyframes[animationKeyframes.length - 1];\n\n    if (JSON.stringify(firstKeyframe) === JSON.stringify(lastKeyframe)) {\n      // The start and end keyframes are the same, infer that there is no animation needed.\n      return;\n    }\n\n    const cleanup = sideEffects?.({active, dragOverlay, ...rest});\n    const animation = dragOverlay.node.animate(animationKeyframes, {\n      duration,\n      easing,\n      fill: 'forwards',\n    });\n\n    return new Promise((resolve) => {\n      animation.onfinish = () => {\n        cleanup?.();\n        resolve();\n      };\n    });\n  };\n}\n", "import {useMemo} from 'react';\n\nimport type {UniqueIdentifier} from '../../../types';\n\nlet key = 0;\n\nexport function useKey(id: UniqueIdentifier | undefined) {\n  return useMemo(() => {\n    if (id == null) {\n      return;\n    }\n\n    key++;\n    return key;\n  }, [id]);\n}\n", "import React, {useContext} from 'react';\n\nimport {applyModifiers, Modifiers} from '../../modifiers';\nimport {ActiveDraggableContext} from '../DndContext';\nimport {useDndContext} from '../../hooks';\nimport {useInitialValue} from '../../hooks/utilities';\n\nimport {\n  AnimationManager,\n  NullifiedContextProvider,\n  PositionedOverlay,\n} from './components';\nimport type {PositionedOverlayProps} from './components';\n\nimport {useDropAnimation, useKey} from './hooks';\nimport type {DropAnimation} from './hooks';\n\nexport interface Props\n  extends Pick<\n    PositionedOverlayProps,\n    'adjustScale' | 'children' | 'className' | 'style' | 'transition'\n  > {\n  dropAnimation?: DropAnimation | null | undefined;\n  modifiers?: Modifiers;\n  wrapperElement?: keyof JSX.IntrinsicElements;\n  zIndex?: number;\n}\n\nexport const DragOverlay = React.memo(\n  ({\n    adjustScale = false,\n    children,\n    dropAnimation: dropAnimationConfig,\n    style,\n    transition,\n    modifiers,\n    wrapperElement = 'div',\n    className,\n    zIndex = 999,\n  }: Props) => {\n    const {\n      activatorEvent,\n      active,\n      activeNodeRect,\n      containerNodeRect,\n      draggableNodes,\n      droppableContainers,\n      dragOverlay,\n      over,\n      measuringConfiguration,\n      scrollableAncestors,\n      scrollableAncestorRects,\n      windowRect,\n    } = useDndContext();\n    const transform = useContext(ActiveDraggableContext);\n    const key = useKey(active?.id);\n    const modifiedTransform = applyModifiers(modifiers, {\n      activatorEvent,\n      active,\n      activeNodeRect,\n      containerNodeRect,\n      draggingNodeRect: dragOverlay.rect,\n      over,\n      overlayNodeRect: dragOverlay.rect,\n      scrollableAncestors,\n      scrollableAncestorRects,\n      transform,\n      windowRect,\n    });\n    const initialRect = useInitialValue(activeNodeRect);\n    const dropAnimation = useDropAnimation({\n      config: dropAnimationConfig,\n      draggableNodes,\n      droppableContainers,\n      measuringConfiguration,\n    });\n    // We need to wait for the active node to be measured before connecting the drag overlay ref\n    // otherwise collisions can be computed against a mispositioned drag overlay\n    const ref = initialRect ? dragOverlay.setRef : undefined;\n\n    return (\n      <NullifiedContextProvider>\n        <AnimationManager animation={dropAnimation}>\n          {active && key ? (\n            <PositionedOverlay\n              key={key}\n              id={active.id}\n              ref={ref}\n              as={wrapperElement}\n              activatorEvent={activatorEvent}\n              adjustScale={adjustScale}\n              className={className}\n              transition={transition}\n              rect={initialRect}\n              style={{\n                zIndex,\n                ...style,\n              }}\n              transform={modifiedTransform}\n            >\n              {children}\n            </PositionedOverlay>\n          ) : null}\n        </AnimationManager>\n      </NullifiedContextProvider>\n    );\n  }\n);\n"], "names": ["DndMonitorContext", "createContext", "useDndMonitor", "listener", "registerListener", "useContext", "useEffect", "Error", "unsubscribe", "useDndMonitorProvider", "listeners", "useState", "Set", "useCallback", "add", "delete", "dispatch", "type", "event", "for<PERSON>ach", "defaultScreenReaderInstructions", "draggable", "defaultAnnouncements", "onDragStart", "active", "id", "onDragOver", "over", "onDragEnd", "onDragCancel", "Accessibility", "announcements", "container", "hiddenTextDescribedById", "screenReaderInstructions", "announce", "announcement", "useAnnouncement", "liveRegionId", "useUniqueId", "mounted", "setMounted", "useMemo", "onDragMove", "markup", "React", "HiddenText", "value", "LiveRegion", "createPortal", "Action", "noop", "useSensor", "sensor", "options", "useSensors", "sensors", "filter", "defaultCoordinates", "Object", "freeze", "x", "y", "distanceBetween", "p1", "p2", "Math", "sqrt", "pow", "getRelativeTransformOrigin", "rect", "eventCoordinates", "getEventCoordinates", "transform<PERSON><PERSON>in", "left", "width", "top", "height", "sortCollisionsAsc", "data", "a", "b", "sortCollisionsDesc", "cornersOfRectangle", "getFirstCollision", "collisions", "property", "length", "firstCollision", "centerOfRectangle", "closestCenter", "collisionRect", "droppableRects", "droppableContainers", "centerRect", "droppableContainer", "get", "distBetween", "push", "sort", "closestCorners", "corners", "rectCorners", "distances", "reduce", "accumulator", "corner", "index", "effectiveDistance", "Number", "toFixed", "getIntersectionRatio", "entry", "target", "max", "right", "min", "bottom", "targetArea", "entryArea", "intersectionArea", "intersectionRatio", "rectIntersection", "isPointWithinRect", "point", "pointer<PERSON><PERSON><PERSON>", "pointerCoordinates", "adjustScale", "transform", "rect1", "rect2", "scaleX", "scaleY", "getRectDelta", "createRectAdjustmentFn", "modifier", "adjustClientRect", "adjustments", "acc", "adjustment", "getAdjustedRect", "parseTransform", "startsWith", "transformArray", "slice", "split", "inverseTransform", "parsedTransform", "translateX", "translateY", "parseFloat", "indexOf", "w", "h", "defaultOptions", "ignoreTransform", "getClientRect", "element", "getBoundingClientRect", "getWindow", "getComputedStyle", "getTransformAgnosticClientRect", "getWindowClientRect", "innerWidth", "innerHeight", "isFixed", "node", "computedStyle", "position", "isScrollable", "overflowRegex", "properties", "some", "test", "getScrollableAncestors", "limit", "scrollParents", "findScrollableAncestors", "isDocument", "scrollingElement", "includes", "isHTMLElement", "isSVGElement", "parentNode", "getFirstScrollableAncestor", "firstScrollableAncestor", "getScrollableElement", "canUseDOM", "isWindow", "isNode", "getOwnerDocument", "window", "getScrollXCoordinate", "scrollX", "scrollLeft", "getScrollYCoordinate", "scrollY", "scrollTop", "getScrollCoordinates", "Direction", "isDocumentScrollingElement", "document", "getScrollPosition", "scrollingContainer", "minScroll", "dimensions", "clientHeight", "clientWidth", "maxScroll", "scrollWidth", "scrollHeight", "isTop", "isLeft", "isBottom", "isRight", "defaultThreshold", "getScrollDirectionAndSpeed", "scrollContainer", "scrollContainerRect", "acceleration", "thresholdPercentage", "direction", "speed", "threshold", "Backward", "abs", "Forward", "getScrollElementRect", "getScrollOffsets", "scrollableAncestors", "getScrollXOffset", "getScrollYOffset", "scrollIntoViewIfNeeded", "measure", "scrollIntoView", "block", "inline", "Rect", "constructor", "scrollOffsets", "axis", "keys", "getScrollOffset", "key", "defineProperty", "currentOffsets", "scrollOffsetsDeltla", "enumerable", "Listeners", "removeAll", "removeEventListener", "eventName", "handler", "addEventListener", "getEventListenerTarget", "EventTarget", "hasExceededDistance", "delta", "measurement", "dx", "dy", "EventName", "preventDefault", "stopPropagation", "KeyboardCode", "defaultKeyboardCodes", "start", "Space", "Enter", "cancel", "Esc", "end", "Tab", "defaultKeyboardCoordinateGetter", "currentCoordinates", "code", "Right", "Left", "Down", "Up", "undefined", "KeyboardSensor", "props", "autoScrollEnabled", "referenceCoordinates", "windowListeners", "handleKeyDown", "bind", "handleCancel", "attach", "handleStart", "Resize", "VisibilityChange", "setTimeout", "Keydown", "activeNode", "onStart", "current", "isKeyboardEvent", "context", "keyboardCodes", "coordinateGetter", "scroll<PERSON>eh<PERSON>or", "handleEnd", "newCoordinates", "coordinates<PERSON><PERSON><PERSON>", "getCoordinatesDelta", "scrollDelta", "scrollElementRect", "clampedCoordinates", "canScrollX", "canScrollY", "newScrollCoordinates", "canScrollToNewCoordinates", "scrollTo", "behavior", "scrollBy", "handleMove", "getAdjustedCoordinates", "coordinates", "onMove", "onEnd", "detach", "onCancel", "activators", "onActivation", "nativeEvent", "activator", "activatorNode", "isDistanceConstraint", "constraint", "Boolean", "isDelayConstraint", "AbstractPointerSensor", "events", "<PERSON><PERSON><PERSON><PERSON>", "activated", "initialCoordinates", "timeoutId", "documentListeners", "handleKeydown", "removeTextSelection", "activationConstraint", "bypassActivationConstraint", "move", "name", "passive", "DragStart", "ContextMenu", "delay", "handlePending", "clearTimeout", "offset", "onPending", "Click", "capture", "SelectionChange", "tolerance", "distance", "cancelable", "onAbort", "getSelection", "removeAllRanges", "PointerSensor", "isPrimary", "button", "MouseB<PERSON>on", "MouseSensor", "RightClick", "TouchSensor", "setup", "teardown", "touches", "AutoScrollActivator", "TraversalOrder", "useAutoScroller", "Pointer", "canScroll", "draggingRect", "enabled", "interval", "order", "TreeOrder", "scrollableAncestorRects", "scrollIntent", "useScrollIntent", "disabled", "setAutoScrollInterval", "clearAutoScrollInterval", "useInterval", "scrollSpeed", "useRef", "scrollDirection", "DraggableRect", "scrollContainerRef", "autoScroll", "sortedScrollableAncestors", "reverse", "JSON", "stringify", "defaultScrollIntent", "previousDel<PERSON>", "usePrevious", "useLazyMemo", "previousIntent", "sign", "useCachedNode", "draggableNodes", "draggableNode", "cachedNode", "useCombineActivators", "getSyntheticHandler", "Sensor", "sensorActivators", "map", "MeasuringStrategy", "MeasuringFrequency", "defaultValue", "Map", "useDroppableMeasuring", "containers", "dragging", "dependencies", "config", "queue", "setQueue", "frequency", "strategy", "containersRef", "isDisabled", "disabledRef", "useLatestValue", "measureDroppableContainers", "ids", "concat", "previousValue", "set", "measuringScheduled", "Always", "BeforeDragging", "useInitialValue", "computeFn", "useInitialRect", "useMutationObserver", "callback", "handleMutations", "useEvent", "mutationObserver", "MutationObserver", "disconnect", "useResizeObserver", "handleResize", "resizeObserver", "ResizeObserver", "defaultMeasure", "useRect", "fallbackRect", "setRect", "measureRect", "currentRect", "isConnected", "newRect", "records", "record", "HTMLElement", "contains", "useIsomorphicLayoutEffect", "observe", "body", "childList", "subtree", "useRectDelta", "initialRect", "useScrollableAncestors", "previousNode", "ancestors", "useScrollOffsets", "elements", "scrollCoordinates", "setScrollCoordinates", "prevElements", "handleScroll", "previousElements", "cleanup", "entries", "scrollableElement", "Array", "from", "values", "useScrollOffsetsDelta", "initialScrollOffsets", "hasScrollOffsets", "subtract", "useSensorSetup", "teardownFns", "useSyntheticListeners", "useWindowRect", "useRects", "firstElement", "windowRect", "rects", "setRects", "measureRects", "getMeasurableNode", "children", "<PERSON><PERSON><PERSON><PERSON>", "useDragOverlayMeasuring", "handleNodeChange", "nodeRef", "setRef", "useNodeRef", "defaultSensors", "defaultData", "defaultMeasuringConfiguration", "droppable", "WhileDragging", "Optimized", "dragOverlay", "DroppableContainersMap", "toArray", "getEnabled", "getNodeFor", "defaultPublicContext", "activatorEvent", "activeNodeRect", "containerNodeRect", "measuringConfiguration", "defaultInternalContext", "ariaDescribedById", "InternalContext", "PublicContext", "getInitialState", "nodes", "translate", "reducer", "state", "action", "<PERSON><PERSON><PERSON><PERSON>", "DragEnd", "DragCancel", "RegisterDroppable", "SetDroppableDisabled", "UnregisterDroppable", "RestoreFocus", "previousActivatorEvent", "previousActiveId", "activeElement", "requestAnimationFrame", "focusableNode", "findFirstFocusableNode", "focus", "applyModifiers", "modifiers", "args", "useMeasuringConfiguration", "useLayoutShiftScrollCompensation", "initialized", "rectD<PERSON><PERSON>", "ActiveDraggableContext", "Status", "DndContext", "memo", "accessibility", "collisionDetection", "measuring", "store", "useReducer", "dispatchMonitorEvent", "registerMonitorListener", "status", "setStatus", "Uninitialized", "isInitialized", "Initialized", "activeId", "activeRects", "initial", "translated", "activeRef", "activeSensor", "setActiveSensor", "setActivatorEvent", "latestProps", "draggableDescribedById", "enabledDroppableContainers", "activationCoordinates", "autoScrollOptions", "getAutoScrollerOptions", "initialActiveNodeRect", "layoutShiftCompensation", "parentElement", "sensorContext", "draggingNode", "draggingNodeRect", "scrollAdjustedTranslate", "overNode", "usesDragOverlay", "nodeRectDelta", "modifiedTranslate", "overlayNodeRect", "scrollAdjustment", "activeNodeScrollDelta", "overId", "setOver", "appliedTranslate", "activeSensorRef", "instantiateSensor", "sensorInstance", "onDragAbort", "onDragPending", "unstable_batchedUpdates", "Initializing", "createHandler", "cancelDrop", "shouldCancel", "Promise", "resolve", "bindActivatorToSensorInstantiator", "activeDraggableNode", "dndKit", "defaultPrevented", "activationContext", "shouldActivate", "capturedBy", "over<PERSON><PERSON><PERSON>", "publicContext", "internalContext", "Provider", "restoreFocus", "activeSensorDisablesAutoscroll", "autoScrollGloballyDisabled", "NullContext", "defaultRole", "ID_PREFIX", "useDraggable", "attributes", "role", "roleDescription", "tabIndex", "isDragging", "setNodeRef", "setActivatorNodeRef", "dataRef", "memoizedAttributes", "useDndContext", "defaultResizeObserverConfig", "timeout", "useDroppable", "resizeObserverConfig", "previous", "resizeObserverConnected", "callbackId", "resizeObserverDisabled", "updateMeasurementsFor", "resizeObserverTimeout", "isArray", "newElement", "previousElement", "unobserve", "isOver", "AnimationManager", "animation", "cloned<PERSON><PERSON><PERSON><PERSON>", "setClonedChildren", "setElement", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "then", "cloneElement", "ref", "defaultTransform", "NullifiedContextProvider", "baseStyles", "touchAction", "defaultTransition", "isKeyboardActivator", "PositionedOverlay", "forwardRef", "as", "className", "style", "transition", "scaleAdjustedTransform", "styles", "CSS", "Transform", "toString", "createElement", "defaultDropAnimationSideEffects", "originalStyles", "getPropertyValue", "setProperty", "classList", "remove", "defaultKeyframeResolver", "final", "defaultDropAnimationConfiguration", "duration", "easing", "keyframes", "sideEffects", "opacity", "useDropAnimation", "activeDraggable", "measurableNode", "createDefaultDropAnimation", "rest", "scale", "finalTransform", "animationKeyframes", "firstKeyframe", "lastKeyframe", "animate", "fill", "onfinish", "useKey", "DragOverlay", "dropAnimation", "dropAnimationConfig", "wrapperElement", "zIndex", "modifiedTransform"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAIO,MAAMA,iBAAiB,GAAA,WAAA,GAAGC,kLAAa,AAAbA,EAAuC,IAA1B,CAAvC;SCCSC,cAAcC,QAAAA;IAC5B,MAAMC,gBAAgB,qKAAGC,aAAAA,AAAU,EAACL,iBAAD,CAAnC;IAEAM,8KAAAA,AAAS;mCAAC;YACR,IAAI,CAACF,gBAAL,EAAuB;gBACrB,MAAM,IAAIG,KAAJ,CACJ,8DADI,CAAN;;YAKF,MAAMC,WAAW,GAAGJ,gBAAgB,CAACD,QAAD,CAApC;YAEA,OAAOK,WAAP;SATO;kCAUN;QAACL,QAAD;QAAWC,gBAAX;KAVM,CAAT;AAWD;SCfeK;IACd,MAAM,CAACC,SAAD,CAAA,qKAAcC,WAAAA,AAAQ;0CAAC,IAAM,IAAIC,GAAJ,EAAP,CAA5B;;IAEA,MAAMR,gBAAgB,qKAAGS,cAAAA,AAAW;gEACjCV,QAAD;YACEO,SAAS,CAACI,GAAV,CAAcX,QAAd;YACA;uEAAO,IAAMO,SAAS,CAACK,MAAV,CAAiBZ,QAAjB,CAAb;;SAHgC;8DAKlC;QAACO,SAAD;KALkC,CAApC;IAQA,MAAMM,QAAQ,OAAGH,4KAAAA,AAAW;wDAC1B;gBAAC,EAACI,IAAD,EAAOC,KAAAA;YACNR,SAAS,CAACS,OAAV;gEAAmBhB,QAAD;oBAAA,IAAA;oBAAA,OAAA,CAAA,iBAAcA,QAAQ,CAACc,IAAD,CAAtB,KAAA,OAAA,KAAA,IAAc,eAAA,IAAA,CAAAd,QAAQ,EAASe,KAAT,CAAtB;iBAAlB;;SAFwB;sDAI1B;QAACR,SAAD;KAJ0B,CAA5B;IAOA,OAAO;QAACM,QAAD;QAAWZ,gBAAX;KAAP;AACD;MCrBYgB,+BAA+B,GAA6B;IACvEC,SAAS,EAAA;AAD8D,CAAlE;AAQP,MAAaC,oBAAoB,GAAkB;IACjDC,WAAW,EAAA,IAAA;YAAC,EAACC,MAAAA;QACX,OAAA,8BAAmCA,MAAM,CAACC,EAA1C,GAAA;KAF+C;IAIjDC,UAAU,EAAA,KAAA;YAAC,EAACF,MAAD,EAASG,IAAAA;QAClB,IAAIA,IAAJ,EAAU;YACR,OAAA,oBAAyBH,MAAM,CAACC,EAAhC,GAAA,oCAAoEE,IAAI,CAACF,EAAzE,GAAA;;QAGF,OAAA,oBAAyBD,MAAM,CAACC,EAAhC,GAAA;KAT+C;IAWjDG,SAAS,EAAA,KAAA;YAAC,EAACJ,MAAD,EAASG,IAAAA;QACjB,IAAIA,IAAJ,EAAU;YACR,OAAA,oBAAyBH,MAAM,CAACC,EAAhC,GAAA,sCAAsEE,IAAI,CAACF,EAA3E;;QAGF,OAAA,oBAAyBD,MAAM,CAACC,EAAhC,GAAA;KAhB+C;IAkBjDI,YAAY,EAAA,KAAA;YAAC,EAACL,MAAAA;QACZ,OAAA,4CAAiDA,MAAM,CAACC,EAAxD,GAAA;;AAnB+C,CAA5C;SCUSK,cAAAA,IAAAA;QAAc,EAC5BC,aAAa,GAAGT,oBADY,EAE5BU,SAF4B,EAG5BC,uBAH4B,EAI5BC,wBAAwB,GAAGd,+BAAAA;IAE3B,MAAM,EAACe,QAAD,EAAWC,YAAAA,MAAgBC,sMAAAA,AAAe,EAAhD;IACA,MAAMC,YAAY,gLAAGC,cAAAA,AAAW,EAAA,gBAAhC;IACA,MAAM,CAACC,OAAD,EAAUC,UAAV,CAAA,IAAwB9B,4KAAAA,AAAQ,EAAC,KAAD,CAAtC;sKAEAL,YAAS,AAATA;mCAAU;YACRmC,UAAU,CAAC,IAAD,CAAV;SADO;kCAEN,EAFM,CAAT;IAIAvC,aAAa,mKACXwC,UAAAA,AAAO;+CACL,IAAA,CAAO;gBACLnB,WAAW,EAAA,KAAA;wBAAC,EAACC,MAAAA;oBACXW,QAAQ,CAACJ,aAAa,CAACR,WAAd,CAA0B;wBAACC;qBAA3B,CAAD,CAAR;iBAFG;gBAILmB,UAAU,EAAA,KAAA;wBAAC,EAACnB,MAAD,EAASG,IAAAA;oBAClB,IAAII,aAAa,CAACY,UAAlB,EAA8B;wBAC5BR,QAAQ,CAACJ,aAAa,CAACY,UAAd,CAAyB;4BAACnB,MAAD;4BAASG;yBAAlC,CAAD,CAAR;;iBANC;gBASLD,UAAU,EAAA,KAAA;wBAAC,EAACF,MAAD,EAASG,IAAAA;oBAClBQ,QAAQ,CAACJ,aAAa,CAACL,UAAd,CAAyB;wBAACF,MAAD;wBAASG;qBAAlC,CAAD,CAAR;iBAVG;gBAYLC,SAAS,EAAA,KAAA;wBAAC,EAACJ,MAAD,EAASG,IAAAA;oBACjBQ,QAAQ,CAACJ,aAAa,CAACH,SAAd,CAAwB;wBAACJ,MAAD;wBAASG;qBAAjC,CAAD,CAAR;iBAbG;gBAeLE,YAAY,EAAA,KAAA;wBAAC,EAACL,MAAD,EAASG,IAAAA;oBACpBQ,QAAQ,CAACJ,aAAa,CAACF,YAAd,CAA2B;wBAACL,MAAD;wBAASG;qBAApC,CAAD,CAAR;;aAhBJ,CADK;8CAoBL;QAACQ,QAAD;QAAWJ,aAAX;KApBK,CADI,CAAb;IAyBA,IAAI,CAACS,OAAL,EAAc;QACZ,OAAO,IAAP;;IAGF,MAAMI,MAAM,GACVC,wKAAAA,CAAAA,aAAA,CAAA,6JAAA,CAAA,UAAA,CAAA,QAAA,EAAA,IAAA,gKACEA,UAAAA,CAAAA,aAAA,kLAACC,aAAD,EAAA;QACErB,EAAE,EAAEQ;QACJc,KAAK,EAAEb,wBAAwB,CAACb,SAAAA;KAFlC,CADF,gKAKEwB,UAAAA,CAAAA,aAAA,kLAACG,aAAD,EAAA;QAAYvB,EAAE,EAAEa;QAAcF,YAAY,EAAEA;KAA5C,CALF,CADF;IAUA,OAAOJ,SAAS,4KAAGiB,eAAAA,AAAY,EAACL,MAAD,EAASZ,SAAT,CAAf,GAAqCY,MAArD;AACD;ACvED,IAAYM,MAAZ;AAAA,CAAA,SAAYA,MAAAA;IACVA,MAAAA,CAAAA,YAAA,GAAA,WAAA;IACAA,MAAAA,CAAAA,WAAA,GAAA,UAAA;IACAA,MAAAA,CAAAA,UAAA,GAAA,SAAA;IACAA,MAAAA,CAAAA,aAAA,GAAA,YAAA;IACAA,MAAAA,CAAAA,WAAA,GAAA,UAAA;IACAA,MAAAA,CAAAA,oBAAA,GAAA,mBAAA;IACAA,MAAAA,CAAAA,uBAAA,GAAA,sBAAA;IACAA,MAAAA,CAAAA,sBAAA,GAAA,qBAAA;AACD,CATD,EAAYA,MAAM,IAAA,CAANA,MAAM,GAAA,CAAA,CAAA,CAAlB;SCHgBC,QAAAA;SCIAC,UACdC,MAAAA,EACAC,OAAAA;IAEA,yKAAOZ,UAAAA,AAAO;6BACZ,IAAA,CAAO;gBACLW,MADK;gBAELC,OAAO,EAAEA,OAAF,IAAA,OAAEA,OAAF,GAAc,CAAA;aAFvB,CADY;4BAMZ;QAACD,MAAD;QAASC,OAAT;KANY,CAAd;AAQD;SCZeC;qCACXC,UAAAA,IAAAA,MAAAA,OAAAA,OAAAA,GAAAA,OAAAA,MAAAA,OAAAA;QAAAA,OAAAA,CAAAA,KAAAA,GAAAA,SAAAA,CAAAA,KAAAA;;IAEH,yKAAOd,UAAO,AAAPA;8BACL,IACE,CAAC;mBAAGc,OAAJ;aAAA,CAAaC,MAAb;sCACGJ,MAAD,IAA6CA,MAAM,IAAI,IADzD,CAFU;;6BAMZ,CAAC;WAAGG,OAAJ;KANY,CAAd;AAQD;MCbYE,kBAAkB,GAAA,WAAA,GAAgBC,MAAM,CAACC,MAAP,CAAc;IAC3DC,CAAC,EAAE,CADwD;IAE3DC,CAAC,EAAE;AAFwD,CAAd,CAAxC;ACAP;;IAGA,SAAgBC,gBAAgBC,EAAAA,EAAiBC,EAAAA;IAC/C,OAAOC,IAAI,CAACC,IAAL,CAAUD,IAAI,CAACE,GAAL,CAASJ,EAAE,CAACH,CAAH,GAAOI,EAAE,CAACJ,CAAnB,EAAsB,CAAtB,IAA2BK,IAAI,CAACE,GAAL,CAASJ,EAAE,CAACF,CAAH,GAAOG,EAAE,CAACH,CAAnB,EAAsB,CAAtB,CAArC,CAAP;AACD;SCJeO,2BACdnD,KAAAA,EACAoD,IAAAA;IAEA,MAAMC,gBAAgB,gLAAGC,sBAAAA,AAAmB,EAACtD,KAAD,CAA5C;IAEA,IAAI,CAACqD,gBAAL,EAAuB;QACrB,OAAO,KAAP;;IAGF,MAAME,eAAe,GAAG;QACtBZ,CAAC,EAAG,CAACU,gBAAgB,CAACV,CAAjB,GAAqBS,IAAI,CAACI,IAA3B,IAAmCJ,IAAI,CAACK,KAAzC,GAAkD,GAD/B;QAEtBb,CAAC,EAAG,CAACS,gBAAgB,CAACT,CAAjB,GAAqBQ,IAAI,CAACM,GAA3B,IAAkCN,IAAI,CAACO,MAAxC,GAAkD;KAFvD;IAKA,OAAUJ,eAAe,CAACZ,CAA1B,GAAA,OAAgCY,eAAe,CAACX,CAAhD,GAAA;AACD;ACdD;;IAGA,SAAgBgB,kBAAAA,IAAAA,EAAAA,KAAAA;QACd,EAACC,IAAI,EAAE,EAAChC,KAAK,EAAEiC,CAAAA;QACf,EAACD,IAAI,EAAE,EAAChC,KAAK,EAAEkC,CAAAA;IAEf,OAAOD,CAAC,GAAGC,CAAX;AACD;AAED;;IAGA,SAAgBC,mBAAAA,KAAAA,EAAAA,KAAAA;QACd,EAACH,IAAI,EAAE,EAAChC,KAAK,EAAEiC,CAAAA;QACf,EAACD,IAAI,EAAE,EAAChC,KAAK,EAAEkC,CAAAA;IAEf,OAAOA,CAAC,GAAGD,CAAX;AACD;AAED;;;IAIA,SAAgBG,mBAAAA,KAAAA;QAAmB,EAACT,IAAD,EAAOE,GAAP,EAAYC,MAAZ,EAAoBF,KAAAA;IACrD,OAAO;QACL;YACEd,CAAC,EAAEa,IADL;YAEEZ,CAAC,EAAEc;SAHA;QAKL;YACEf,CAAC,EAAEa,IAAI,GAAGC,KADZ;YAEEb,CAAC,EAAEc;SAPA;QASL;YACEf,CAAC,EAAEa,IADL;YAEEZ,CAAC,EAAEc,GAAG,GAAGC;SAXN;QAaL;YACEhB,CAAC,EAAEa,IAAI,GAAGC,KADZ;YAEEb,CAAC,EAAEc,GAAG,GAAGC;SAfN;KAAP;AAkBD;AAaD,SAAgBO,kBACdC,UAAAA,EACAC,QAAAA;IAEA,IAAI,CAACD,UAAD,IAAeA,UAAU,CAACE,MAAX,KAAsB,CAAzC,EAA4C;QAC1C,OAAO,IAAP;;IAGF,MAAM,CAACC,cAAD,CAAA,GAAmBH,UAAzB;IAEA,OAAOC,QAAQ,GAAGE,cAAc,CAACF,QAAD,CAAjB,GAA8BE,cAA7C;AACD;AClED;;IAGA,SAASC,iBAAT,CACEnB,IADF,EAEEI,IAFF,EAGEE,GAHF;QAEEF,SAAAA,KAAAA,GAAAA;QAAAA,OAAOJ,IAAI,CAACI,IAAAA;;QACZE,QAAAA,KAAAA,GAAAA;QAAAA,MAAMN,IAAI,CAACM,GAAAA;;IAEX,OAAO;QACLf,CAAC,EAAEa,IAAI,GAAGJ,IAAI,CAACK,KAAL,GAAa,GADlB;QAELb,CAAC,EAAEc,GAAG,GAAGN,IAAI,CAACO,MAAL,GAAc;KAFzB;AAID;AAED;;;IAIA,MAAaa,aAAa,IAAuB;QAAC,EAChDC,aADgD,EAEhDC,cAFgD,EAGhDC,mBAAAA;IAEA,MAAMC,UAAU,GAAGL,iBAAiB,CAClCE,aADkC,EAElCA,aAAa,CAACjB,IAFoB,EAGlCiB,aAAa,CAACf,GAHoB,CAApC;IAKA,MAAMS,UAAU,GAA0B,EAA1C;IAEA,KAAK,MAAMU,kBAAX,IAAiCF,mBAAjC,CAAsD;QACpD,MAAM,EAACpE,EAAAA,KAAMsE,kBAAb;QACA,MAAMzB,IAAI,GAAGsB,cAAc,CAACI,GAAf,CAAmBvE,EAAnB,CAAb;QAEA,IAAI6C,IAAJ,EAAU;YACR,MAAM2B,WAAW,GAAGlC,eAAe,CAAC0B,iBAAiB,CAACnB,IAAD,CAAlB,EAA0BwB,UAA1B,CAAnC;YAEAT,UAAU,CAACa,IAAX,CAAgB;gBAACzE,EAAD;gBAAKsD,IAAI,EAAE;oBAACgB,kBAAD;oBAAqBhD,KAAK,EAAEkD;;aAAvD;;;IAIJ,OAAOZ,UAAU,CAACc,IAAX,CAAgBrB,iBAAhB,CAAP;AACD,CAxBM;ACnBP;;;IAIA,MAAasB,cAAc,IAAuB;QAAC,EACjDT,aADiD,EAEjDC,cAFiD,EAGjDC,mBAAAA;IAEA,MAAMQ,OAAO,GAAGlB,kBAAkB,CAACQ,aAAD,CAAlC;IACA,MAAMN,UAAU,GAA0B,EAA1C;IAEA,KAAK,MAAMU,kBAAX,IAAiCF,mBAAjC,CAAsD;QACpD,MAAM,EAACpE,EAAAA,KAAMsE,kBAAb;QACA,MAAMzB,IAAI,GAAGsB,cAAc,CAACI,GAAf,CAAmBvE,EAAnB,CAAb;QAEA,IAAI6C,IAAJ,EAAU;YACR,MAAMgC,WAAW,GAAGnB,kBAAkB,CAACb,IAAD,CAAtC;YACA,MAAMiC,SAAS,GAAGF,OAAO,CAACG,MAAR,CAAe,CAACC,WAAD,EAAcC,MAAd,EAAsBC,KAAtB;gBAC/B,OAAOF,WAAW,GAAG1C,eAAe,CAACuC,WAAW,CAACK,KAAD,CAAZ,EAAqBD,MAArB,CAApC;aADgB,EAEf,CAFe,CAAlB;YAGA,MAAME,iBAAiB,GAAGC,MAAM,CAAC,CAACN,SAAS,GAAG,CAAb,EAAgBO,OAAhB,CAAwB,CAAxB,CAAD,CAAhC;YAEAzB,UAAU,CAACa,IAAX,CAAgB;gBACdzE,EADc;gBAEdsD,IAAI,EAAE;oBAACgB,kBAAD;oBAAqBhD,KAAK,EAAE6D;;aAFpC;;;IAOJ,OAAOvB,UAAU,CAACc,IAAX,CAAgBrB,iBAAhB,CAAP;AACD,CA3BM;ACJP;;IAGA,SAAgBiC,qBACdC,KAAAA,EACAC,MAAAA;IAEA,MAAMrC,GAAG,GAAGV,IAAI,CAACgD,GAAL,CAASD,MAAM,CAACrC,GAAhB,EAAqBoC,KAAK,CAACpC,GAA3B,CAAZ;IACA,MAAMF,IAAI,GAAGR,IAAI,CAACgD,GAAL,CAASD,MAAM,CAACvC,IAAhB,EAAsBsC,KAAK,CAACtC,IAA5B,CAAb;IACA,MAAMyC,KAAK,GAAGjD,IAAI,CAACkD,GAAL,CAASH,MAAM,CAACvC,IAAP,GAAcuC,MAAM,CAACtC,KAA9B,EAAqCqC,KAAK,CAACtC,IAAN,GAAasC,KAAK,CAACrC,KAAxD,CAAd;IACA,MAAM0C,MAAM,GAAGnD,IAAI,CAACkD,GAAL,CAASH,MAAM,CAACrC,GAAP,GAAaqC,MAAM,CAACpC,MAA7B,EAAqCmC,KAAK,CAACpC,GAAN,GAAYoC,KAAK,CAACnC,MAAvD,CAAf;IACA,MAAMF,KAAK,GAAGwC,KAAK,GAAGzC,IAAtB;IACA,MAAMG,MAAM,GAAGwC,MAAM,GAAGzC,GAAxB;IAEA,IAAIF,IAAI,GAAGyC,KAAP,IAAgBvC,GAAG,GAAGyC,MAA1B,EAAkC;QAChC,MAAMC,UAAU,GAAGL,MAAM,CAACtC,KAAP,GAAesC,MAAM,CAACpC,MAAzC;QACA,MAAM0C,SAAS,GAAGP,KAAK,CAACrC,KAAN,GAAcqC,KAAK,CAACnC,MAAtC;QACA,MAAM2C,gBAAgB,GAAG7C,KAAK,GAAGE,MAAjC;QACA,MAAM4C,iBAAiB,GACrBD,gBAAgB,GAAA,CAAIF,UAAU,GAAGC,SAAb,GAAyBC,gBAA7B,CADlB;QAGA,OAAOX,MAAM,CAACY,iBAAiB,CAACX,OAAlB,CAA0B,CAA1B,CAAD,CAAb;;IAIF,OAAO,CAAP;AACD;AAED;;;IAIA,MAAaY,gBAAgB,IAAuB;QAAC,EACnD/B,aADmD,EAEnDC,cAFmD,EAGnDC,mBAAAA;IAEA,MAAMR,UAAU,GAA0B,EAA1C;IAEA,KAAK,MAAMU,kBAAX,IAAiCF,mBAAjC,CAAsD;QACpD,MAAM,EAACpE,EAAAA,KAAMsE,kBAAb;QACA,MAAMzB,IAAI,GAAGsB,cAAc,CAACI,GAAf,CAAmBvE,EAAnB,CAAb;QAEA,IAAI6C,IAAJ,EAAU;YACR,MAAMmD,iBAAiB,GAAGV,oBAAoB,CAACzC,IAAD,EAAOqB,aAAP,CAA9C;YAEA,IAAI8B,iBAAiB,GAAG,CAAxB,EAA2B;gBACzBpC,UAAU,CAACa,IAAX,CAAgB;oBACdzE,EADc;oBAEdsD,IAAI,EAAE;wBAACgB,kBAAD;wBAAqBhD,KAAK,EAAE0E;;iBAFpC;;;;IAQN,OAAOpC,UAAU,CAACc,IAAX,CAAgBjB,kBAAhB,CAAP;AACD,CAxBM;AC/BP;;IAGA,SAASyC,iBAAT,CAA2BC,KAA3B,EAA+CtD,IAA/C;IACE,MAAM,EAACM,GAAD,EAAMF,IAAN,EAAY2C,MAAZ,EAAoBF,KAAAA,KAAS7C,IAAnC;IAEA,OACEM,GAAG,IAAIgD,KAAK,CAAC9D,CAAb,IAAkB8D,KAAK,CAAC9D,CAAN,IAAWuD,MAA7B,IAAuC3C,IAAI,IAAIkD,KAAK,CAAC/D,CAArD,IAA0D+D,KAAK,CAAC/D,CAAN,IAAWsD,KADvE;AAGD;AAED;;IAGA,MAAaU,aAAa,IAAuB;QAAC,EAChDhC,mBADgD,EAEhDD,cAFgD,EAGhDkC,kBAAAA;IAEA,IAAI,CAACA,kBAAL,EAAyB;QACvB,OAAO,EAAP;;IAGF,MAAMzC,UAAU,GAA0B,EAA1C;IAEA,KAAK,MAAMU,kBAAX,IAAiCF,mBAAjC,CAAsD;QACpD,MAAM,EAACpE,EAAAA,KAAMsE,kBAAb;QACA,MAAMzB,IAAI,GAAGsB,cAAc,CAACI,GAAf,CAAmBvE,EAAnB,CAAb;QAEA,IAAI6C,IAAI,IAAIqD,iBAAiB,CAACG,kBAAD,EAAqBxD,IAArB,CAA7B,EAAyD;;;;;UAMvD,MAAM+B,OAAO,GAAGlB,kBAAkB,CAACb,IAAD,CAAlC;YACA,MAAMiC,SAAS,GAAGF,OAAO,CAACG,MAAR,CAAe,CAACC,WAAD,EAAcC,MAAd;gBAC/B,OAAOD,WAAW,GAAG1C,eAAe,CAAC+D,kBAAD,EAAqBpB,MAArB,CAApC;aADgB,EAEf,CAFe,CAAlB;YAGA,MAAME,iBAAiB,GAAGC,MAAM,CAAC,CAACN,SAAS,GAAG,CAAb,EAAgBO,OAAhB,CAAwB,CAAxB,CAAD,CAAhC;YAEAzB,UAAU,CAACa,IAAX,CAAgB;gBACdzE,EADc;gBAEdsD,IAAI,EAAE;oBAACgB,kBAAD;oBAAqBhD,KAAK,EAAE6D;;aAFpC;;;IAOJ,OAAOvB,UAAU,CAACc,IAAX,CAAgBrB,iBAAhB,CAAP;AACD,CAnCM;SCjBSiD,YACdC,SAAAA,EACAC,KAAAA,EACAC,KAAAA;IAEA,OAAO;QACL,GAAGF,SADE;QAELG,MAAM,EAAEF,KAAK,IAAIC,KAAT,GAAiBD,KAAK,CAACtD,KAAN,GAAcuD,KAAK,CAACvD,KAArC,GAA6C,CAFhD;QAGLyD,MAAM,EAAEH,KAAK,IAAIC,KAAT,GAAiBD,KAAK,CAACpD,MAAN,GAAeqD,KAAK,CAACrD,MAAtC,GAA+C;KAHzD;AAKD;SCVewD,aACdJ,KAAAA,EACAC,KAAAA;IAEA,OAAOD,KAAK,IAAIC,KAAT,GACH;QACErE,CAAC,EAAEoE,KAAK,CAACvD,IAAN,GAAawD,KAAK,CAACxD,IADxB;QAEEZ,CAAC,EAAEmE,KAAK,CAACrD,GAAN,GAAYsD,KAAK,CAACtD,GAAAA;KAHpB,GAKHlB,kBALJ;AAMD;SCXe4E,uBAAuBC,QAAAA;IACrC,OAAO,SAASC,gBAAT,CACLlE,IADK;yCAEFmE,cAAAA,IAAAA,MAAAA,OAAAA,IAAAA,OAAAA,IAAAA,IAAAA,OAAAA,GAAAA,OAAAA,MAAAA,OAAAA;YAAAA,WAAAA,CAAAA,OAAAA,EAAAA,GAAAA,SAAAA,CAAAA,KAAAA;;QAEH,OAAOA,WAAW,CAACjC,MAAZ,CACL,CAACkC,GAAD,EAAMC,UAAN,GAAA,CAAsB;gBACpB,GAAGD,GADiB;gBAEpB9D,GAAG,EAAE8D,GAAG,CAAC9D,GAAJ,GAAU2D,QAAQ,GAAGI,UAAU,CAAC7E,CAFjB;gBAGpBuD,MAAM,EAAEqB,GAAG,CAACrB,MAAJ,GAAakB,QAAQ,GAAGI,UAAU,CAAC7E,CAHvB;gBAIpBY,IAAI,EAAEgE,GAAG,CAAChE,IAAJ,GAAW6D,QAAQ,GAAGI,UAAU,CAAC9E,CAJnB;gBAKpBsD,KAAK,EAAEuB,GAAG,CAACvB,KAAJ,GAAYoB,QAAQ,GAAGI,UAAU,CAAC9E,CAAAA;aAL3C,CADK,EAQL;YAAC,GAAGS,IAAAA;SARC,CAAP;KAJF;AAeD;AAEM,MAAMsE,eAAe,GAAA,WAAA,GAAGN,sBAAsB,CAAC,CAAD,CAA9C;SClBSO,eAAeb,SAAAA;IAC7B,IAAIA,SAAS,CAACc,UAAV,CAAqB,WAArB,CAAJ,EAAuC;QACrC,MAAMC,cAAc,GAAGf,SAAS,CAACgB,KAAV,CAAgB,CAAhB,EAAmB,CAAC,CAApB,EAAuBC,KAAvB,CAA6B,IAA7B,CAAvB;QAEA,OAAO;YACLpF,CAAC,EAAE,CAACkF,cAAc,CAAC,EAAD,CADb;YAELjF,CAAC,EAAE,CAACiF,cAAc,CAAC,EAAD,CAFb;YAGLZ,MAAM,EAAE,CAACY,cAAc,CAAC,CAAD,CAHlB;YAILX,MAAM,EAAE,CAACW,cAAc,CAAC,CAAD,CAAA;SAJzB;KAHF,MASO,IAAIf,SAAS,CAACc,UAAV,CAAqB,SAArB,CAAJ,EAAqC;QAC1C,MAAMC,cAAc,GAAGf,SAAS,CAACgB,KAAV,CAAgB,CAAhB,EAAmB,CAAC,CAApB,EAAuBC,KAAvB,CAA6B,IAA7B,CAAvB;QAEA,OAAO;YACLpF,CAAC,EAAE,CAACkF,cAAc,CAAC,CAAD,CADb;YAELjF,CAAC,EAAE,CAACiF,cAAc,CAAC,CAAD,CAFb;YAGLZ,MAAM,EAAE,CAACY,cAAc,CAAC,CAAD,CAHlB;YAILX,MAAM,EAAE,CAACW,cAAc,CAAC,CAAD,CAAA;SAJzB;;IAQF,OAAO,IAAP;AACD;SCpBeG,iBACd5E,IAAAA,EACA0D,SAAAA,EACAvD,eAAAA;IAEA,MAAM0E,eAAe,GAAGN,cAAc,CAACb,SAAD,CAAtC;IAEA,IAAI,CAACmB,eAAL,EAAsB;QACpB,OAAO7E,IAAP;;IAGF,MAAM,EAAC6D,MAAD,EAASC,MAAT,EAAiBvE,CAAC,EAAEuF,UAApB,EAAgCtF,CAAC,EAAEuF,UAAAA,KAAcF,eAAvD;IAEA,MAAMtF,CAAC,GAAGS,IAAI,CAACI,IAAL,GAAY0E,UAAZ,GAAyB,CAAC,IAAIjB,MAAL,IAAemB,UAAU,CAAC7E,eAAD,CAA5D;IACA,MAAMX,CAAC,GACLQ,IAAI,CAACM,GAAL,GACAyE,UADA,GAEA,CAAC,IAAIjB,MAAL,IACEkB,UAAU,CAAC7E,eAAe,CAACuE,KAAhB,CAAsBvE,eAAe,CAAC8E,OAAhB,CAAwB,GAAxB,IAA+B,CAArD,CAAD,CAJd;IAKA,MAAMC,CAAC,GAAGrB,MAAM,GAAG7D,IAAI,CAACK,KAAL,GAAawD,MAAhB,GAAyB7D,IAAI,CAACK,KAA9C;IACA,MAAM8E,CAAC,GAAGrB,MAAM,GAAG9D,IAAI,CAACO,MAAL,GAAcuD,MAAjB,GAA0B9D,IAAI,CAACO,MAA/C;IAEA,OAAO;QACLF,KAAK,EAAE6E,CADF;QAEL3E,MAAM,EAAE4E,CAFH;QAGL7E,GAAG,EAAEd,CAHA;QAILqD,KAAK,EAAEtD,CAAC,GAAG2F,CAJN;QAKLnC,MAAM,EAAEvD,CAAC,GAAG2F,CALP;QAML/E,IAAI,EAAEb;KANR;AAQD;ACzBD,MAAM6F,cAAc,GAAY;IAACC,eAAe,EAAE;AAAlB,CAAhC;AAEA;;IAGA,SAAgBC,cACdC,OAAAA,EACAvG,OAAAA;QAAAA,YAAAA,KAAAA,GAAAA;QAAAA,UAAmBoG;;IAEnB,IAAIpF,IAAI,GAAeuF,OAAO,CAACC,qBAAR,EAAvB;IAEA,IAAIxG,OAAO,CAACqG,eAAZ,EAA6B;QAC3B,MAAM,EAAC3B,SAAD,EAAYvD,eAAAA,SAChBsF,qLAAAA,AAAS,EAACF,OAAD,CAAT,CAAmBG,gBAAnB,CAAoCH,OAApC,CADF;QAGA,IAAI7B,SAAJ,EAAe;YACb1D,IAAI,GAAG4E,gBAAgB,CAAC5E,IAAD,EAAO0D,SAAP,EAAkBvD,eAAlB,CAAvB;;;IAIJ,MAAM,EAACG,GAAD,EAAMF,IAAN,EAAYC,KAAZ,EAAmBE,MAAnB,EAA2BwC,MAA3B,EAAmCF,KAAAA,KAAS7C,IAAlD;IAEA,OAAO;QACLM,GADK;QAELF,IAFK;QAGLC,KAHK;QAILE,MAJK;QAKLwC,MALK;QAMLF;KANF;AAQD;AAED;;;;;;;IAQA,SAAgB8C,+BAA+BJ,OAAAA;IAC7C,OAAOD,aAAa,CAACC,OAAD,EAAU;QAACF,eAAe,EAAE;KAA5B,CAApB;AACD;SCjDeO,oBAAoBL,OAAAA;IAClC,MAAMlF,KAAK,GAAGkF,OAAO,CAACM,UAAtB;IACA,MAAMtF,MAAM,GAAGgF,OAAO,CAACO,WAAvB;IAEA,OAAO;QACLxF,GAAG,EAAE,CADA;QAELF,IAAI,EAAE,CAFD;QAGLyC,KAAK,EAAExC,KAHF;QAIL0C,MAAM,EAAExC,MAJH;QAKLF,KALK;QAMLE;KANF;AAQD;SCZewF,QACdC,IAAAA,EACAC,aAAAA;QAAAA,kBAAAA,KAAAA,GAAAA;QAAAA,6LAAqCR,YAAAA,AAAS,EAACO,IAAD,CAAT,CAAgBN,gBAAhB,CAAiCM,IAAjC;;IAErC,OAAOC,aAAa,CAACC,QAAd,KAA2B,OAAlC;AACD;SCLeC,aACdZ,OAAAA,EACAU,aAAAA;QAAAA,kBAAAA,KAAAA,GAAAA;QAAAA,6LAAqCR,YAAAA,AAAS,EAACF,OAAD,CAAT,CAAmBG,gBAAnB,CACnCH,OADmC;;IAIrC,MAAMa,aAAa,GAAG,uBAAtB;IACA,MAAMC,UAAU,GAAG;QAAC,UAAD;QAAa,WAAb;QAA0B,WAA1B;KAAnB;IAEA,OAAOA,UAAU,CAACC,IAAX,EAAiBtF,QAAD;QACrB,MAAMvC,KAAK,GAAGwH,aAAa,CAACjF,QAAD,CAA3B;QAEA,OAAO,OAAOvC,KAAP,KAAiB,QAAjB,GAA4B2H,aAAa,CAACG,IAAd,CAAmB9H,KAAnB,CAA5B,GAAwD,KAA/D;KAHK,CAAP;AAKD;SCNe+H,uBACdjB,OAAAA,EACAkB,KAAAA;IAEA,MAAMC,aAAa,GAAc,EAAjC;IAEA,SAASC,uBAAT,CAAiCX,IAAjC;QACE,IAAIS,KAAK,IAAI,IAAT,IAAiBC,aAAa,CAACzF,MAAd,IAAwBwF,KAA7C,EAAoD;YAClD,OAAOC,aAAP;;QAGF,IAAI,CAACV,IAAL,EAAW;YACT,OAAOU,aAAP;;QAGF,iLACEE,aAAAA,AAAU,EAACZ,IAAD,CAAV,IACAA,IAAI,CAACa,gBAAL,IAAyB,IADzB,IAEA,CAACH,aAAa,CAACI,QAAd,CAAuBd,IAAI,CAACa,gBAA5B,CAHH,EAIE;YACAH,aAAa,CAAC9E,IAAd,CAAmBoE,IAAI,CAACa,gBAAxB;YAEA,OAAOH,aAAP;;QAGF,IAAI,8KAACK,gBAAAA,AAAa,EAACf,IAAD,CAAd,gLAAwBgB,gBAAAA,AAAY,EAAChB,IAAD,CAAxC,EAAgD;YAC9C,OAAOU,aAAP;;QAGF,IAAIA,aAAa,CAACI,QAAd,CAAuBd,IAAvB,CAAJ,EAAkC;YAChC,OAAOU,aAAP;;QAGF,MAAMT,aAAa,gLAAGR,YAAAA,AAAS,EAACF,OAAD,CAAT,CAAmBG,gBAAnB,CAAoCM,IAApC,CAAtB;QAEA,IAAIA,IAAI,KAAKT,OAAb,EAAsB;YACpB,IAAIY,YAAY,CAACH,IAAD,EAAOC,aAAP,CAAhB,EAAuC;gBACrCS,aAAa,CAAC9E,IAAd,CAAmBoE,IAAnB;;;QAIJ,IAAID,OAAO,CAACC,IAAD,EAAOC,aAAP,CAAX,EAAkC;YAChC,OAAOS,aAAP;;QAGF,OAAOC,uBAAuB,CAACX,IAAI,CAACiB,UAAN,CAA9B;;IAGF,IAAI,CAAC1B,OAAL,EAAc;QACZ,OAAOmB,aAAP;;IAGF,OAAOC,uBAAuB,CAACpB,OAAD,CAA9B;AACD;AAED,SAAgB2B,2BAA2BlB,IAAAA;IACzC,MAAM,CAACmB,uBAAD,CAAA,GAA4BX,sBAAsB,CAACR,IAAD,EAAO,CAAP,CAAxD;IAEA,OAAOmB,uBAAP,IAAA,OAAOA,uBAAP,GAAkC,IAAlC;AACD;SC5DeC,qBAAqB7B,OAAAA;IACnC,IAAI,0KAAC8B,YAAD,IAAc,CAAC9B,OAAnB,EAA4B;QAC1B,OAAO,IAAP;;IAGF,gLAAI+B,YAAAA,AAAQ,EAAC/B,OAAD,CAAZ,EAAuB;QACrB,OAAOA,OAAP;;IAGF,IAAI,EAACgC,qLAAAA,AAAM,EAAChC,OAAD,CAAX,EAAsB;QACpB,OAAO,IAAP;;IAGF,iLACEqB,aAAAA,AAAU,EAACrB,OAAD,CAAV,IACAA,OAAO,KAAKiC,gMAAAA,AAAgB,EAACjC,OAAD,CAAhB,CAA0BsB,gBAFxC,EAGE;QACA,OAAOY,MAAP;;IAGF,iLAAIV,gBAAAA,AAAa,EAACxB,OAAD,CAAjB,EAA4B;QAC1B,OAAOA,OAAP;;IAGF,OAAO,IAAP;AACD;SC9BemC,qBAAqBnC,OAAAA;IACnC,iLAAI+B,WAAAA,AAAQ,EAAC/B,OAAD,CAAZ,EAAuB;QACrB,OAAOA,OAAO,CAACoC,OAAf;;IAGF,OAAOpC,OAAO,CAACqC,UAAf;AACD;AAED,SAAgBC,qBAAqBtC,OAAAA;IACnC,gLAAI+B,YAAAA,AAAQ,EAAC/B,OAAD,CAAZ,EAAuB;QACrB,OAAOA,OAAO,CAACuC,OAAf;;IAGF,OAAOvC,OAAO,CAACwC,SAAf;AACD;AAED,SAAgBC,qBACdzC,OAAAA;IAEA,OAAO;QACLhG,CAAC,EAAEmI,oBAAoB,CAACnC,OAAD,CADlB;QAEL/F,CAAC,EAAEqI,oBAAoB,CAACtC,OAAD;KAFzB;AAID;AC3BD,IAAY0C,SAAZ;AAAA,CAAA,SAAYA,SAAAA;IACVA,SAAAA,CAAAA,SAAAA,CAAAA,UAAAA,GAAAA,EAAA,GAAA,SAAA;IACAA,SAAAA,CAAAA,SAAAA,CAAAA,WAAAA,GAAAA,CAAAA,EAAA,GAAA,UAAA;AACD,CAHD,EAAYA,SAAS,IAAA,CAATA,SAAS,GAAA,CAAA,CAAA,CAArB;SCEgBC,2BAA2B3C,OAAAA;IACzC,IAAI,CAAC8B,qLAAD,IAAc,CAAC9B,OAAnB,EAA4B;QAC1B,OAAO,KAAP;;IAGF,OAAOA,OAAO,KAAK4C,QAAQ,CAACtB,gBAA5B;AACD;SCNeuB,kBAAkBC,kBAAAA;IAChC,MAAMC,SAAS,GAAG;QAChB/I,CAAC,EAAE,CADa;QAEhBC,CAAC,EAAE;KAFL;IAIA,MAAM+I,UAAU,GAAGL,0BAA0B,CAACG,kBAAD,CAA1B,GACf;QACE9H,MAAM,EAAEkH,MAAM,CAAC3B,WADjB;QAEEzF,KAAK,EAAEoH,MAAM,CAAC5B,UAAAA;KAHD,GAKf;QACEtF,MAAM,EAAE8H,kBAAkB,CAACG,YAD7B;QAEEnI,KAAK,EAAEgI,kBAAkB,CAACI,WAAAA;KAPhC;IASA,MAAMC,SAAS,GAAG;QAChBnJ,CAAC,EAAE8I,kBAAkB,CAACM,WAAnB,GAAiCJ,UAAU,CAAClI,KAD/B;QAEhBb,CAAC,EAAE6I,kBAAkB,CAACO,YAAnB,GAAkCL,UAAU,CAAChI,MAAAA;KAFlD;IAKA,MAAMsI,KAAK,GAAGR,kBAAkB,CAACN,SAAnB,IAAgCO,SAAS,CAAC9I,CAAxD;IACA,MAAMsJ,MAAM,GAAGT,kBAAkB,CAACT,UAAnB,IAAiCU,SAAS,CAAC/I,CAA1D;IACA,MAAMwJ,QAAQ,GAAGV,kBAAkB,CAACN,SAAnB,IAAgCW,SAAS,CAAClJ,CAA3D;IACA,MAAMwJ,OAAO,GAAGX,kBAAkB,CAACT,UAAnB,IAAiCc,SAAS,CAACnJ,CAA3D;IAEA,OAAO;QACLsJ,KADK;QAELC,MAFK;QAGLC,QAHK;QAILC,OAJK;QAKLN,SALK;QAMLJ;KANF;AAQD;AC5BD,MAAMW,gBAAgB,GAAG;IACvB1J,CAAC,EAAE,GADoB;IAEvBC,CAAC,EAAE;AAFoB,CAAzB;AAKA,SAAgB0J,2BACdC,eAAAA,EACAC,mBAAAA,EAAAA,IAAAA,EAEAC,YAAAA,EACAC,mBAAAA;QAFA,EAAChJ,GAAD,EAAMF,IAAN,EAAYyC,KAAZ,EAAmBE,MAAAA;QACnBsG,iBAAAA,KAAAA,GAAAA;QAAAA,eAAe;;QACfC,wBAAAA,KAAAA,GAAAA;QAAAA,sBAAsBL;;IAEtB,MAAM,EAACJ,KAAD,EAAQE,QAAR,EAAkBD,MAAlB,EAA0BE,OAAAA,KAAWZ,iBAAiB,CAACe,eAAD,CAA5D;IAEA,MAAMI,SAAS,GAAG;QAChBhK,CAAC,EAAE,CADa;QAEhBC,CAAC,EAAE;KAFL;IAIA,MAAMgK,KAAK,GAAG;QACZjK,CAAC,EAAE,CADS;QAEZC,CAAC,EAAE;KAFL;IAIA,MAAMiK,SAAS,GAAG;QAChBlJ,MAAM,EAAE6I,mBAAmB,CAAC7I,MAApB,GAA6B+I,mBAAmB,CAAC9J,CADzC;QAEhBa,KAAK,EAAE+I,mBAAmB,CAAC/I,KAApB,GAA4BiJ,mBAAmB,CAAC/J,CAAAA;KAFzD;IAKA,IAAI,CAACsJ,KAAD,IAAUvI,GAAG,IAAI8I,mBAAmB,CAAC9I,GAApB,GAA0BmJ,SAAS,CAAClJ,MAAzD,EAAiE;;QAE/DgJ,SAAS,CAAC/J,CAAV,GAAcyI,SAAS,CAACyB,QAAxB;QACAF,KAAK,CAAChK,CAAN,GACE6J,YAAY,GACZzJ,IAAI,CAAC+J,GAAL,CACE,CAACP,mBAAmB,CAAC9I,GAApB,GAA0BmJ,SAAS,CAAClJ,MAApC,GAA6CD,GAA9C,IAAqDmJ,SAAS,CAAClJ,MADjE,CAFF;KAHF,MAQO,IACL,CAACwI,QAAD,IACAhG,MAAM,IAAIqG,mBAAmB,CAACrG,MAApB,GAA6B0G,SAAS,CAAClJ,MAF5C,EAGL;;QAEAgJ,SAAS,CAAC/J,CAAV,GAAcyI,SAAS,CAAC2B,OAAxB;QACAJ,KAAK,CAAChK,CAAN,GACE6J,YAAY,GACZzJ,IAAI,CAAC+J,GAAL,CACE,CAACP,mBAAmB,CAACrG,MAApB,GAA6B0G,SAAS,CAAClJ,MAAvC,GAAgDwC,MAAjD,IACE0G,SAAS,CAAClJ,MAFd,CAFF;;IAQF,IAAI,CAACyI,OAAD,IAAYnG,KAAK,IAAIuG,mBAAmB,CAACvG,KAApB,GAA4B4G,SAAS,CAACpJ,KAA/D,EAAsE;;QAEpEkJ,SAAS,CAAChK,CAAV,GAAc0I,SAAS,CAAC2B,OAAxB;QACAJ,KAAK,CAACjK,CAAN,GACE8J,YAAY,GACZzJ,IAAI,CAAC+J,GAAL,CACE,CAACP,mBAAmB,CAACvG,KAApB,GAA4B4G,SAAS,CAACpJ,KAAtC,GAA8CwC,KAA/C,IAAwD4G,SAAS,CAACpJ,KADpE,CAFF;KAHF,MAQO,IAAI,CAACyI,MAAD,IAAW1I,IAAI,IAAIgJ,mBAAmB,CAAChJ,IAApB,GAA2BqJ,SAAS,CAACpJ,KAA5D,EAAmE;;QAExEkJ,SAAS,CAAChK,CAAV,GAAc0I,SAAS,CAACyB,QAAxB;QACAF,KAAK,CAACjK,CAAN,GACE8J,YAAY,GACZzJ,IAAI,CAAC+J,GAAL,CACE,CAACP,mBAAmB,CAAChJ,IAApB,GAA2BqJ,SAAS,CAACpJ,KAArC,GAA6CD,IAA9C,IAAsDqJ,SAAS,CAACpJ,KADlE,CAFF;;IAOF,OAAO;QACLkJ,SADK;QAELC;KAFF;AAID;SC7EeK,qBAAqBtE,OAAAA;IACnC,IAAIA,OAAO,KAAK4C,QAAQ,CAACtB,gBAAzB,EAA2C;QACzC,MAAM,EAAChB,UAAD,EAAaC,WAAAA,KAAe2B,MAAlC;QAEA,OAAO;YACLnH,GAAG,EAAE,CADA;YAELF,IAAI,EAAE,CAFD;YAGLyC,KAAK,EAAEgD,UAHF;YAIL9C,MAAM,EAAE+C,WAJH;YAKLzF,KAAK,EAAEwF,UALF;YAMLtF,MAAM,EAAEuF;SANV;;IAUF,MAAM,EAACxF,GAAD,EAAMF,IAAN,EAAYyC,KAAZ,EAAmBE,MAAAA,KAAUwC,OAAO,CAACC,qBAAR,EAAnC;IAEA,OAAO;QACLlF,GADK;QAELF,IAFK;QAGLyC,KAHK;QAILE,MAJK;QAKL1C,KAAK,EAAEkF,OAAO,CAACkD,WALV;QAMLlI,MAAM,EAAEgF,OAAO,CAACiD,YAAAA;KANlB;AAQD;SCdesB,iBAAiBC,mBAAAA;IAC/B,OAAOA,mBAAmB,CAAC7H,MAApB,CAAwC,CAACkC,GAAD,EAAM4B,IAAN;QAC7C,oLAAOxJ,MAAAA,AAAG,EAAC4H,GAAD,EAAM4D,oBAAoB,CAAChC,IAAD,CAA1B,CAAV;KADK,EAEJ5G,kBAFI,CAAP;AAGD;AAED,SAAgB4K,iBAAiBD,mBAAAA;IAC/B,OAAOA,mBAAmB,CAAC7H,MAApB,CAAmC,CAACkC,GAAD,EAAM4B,IAAN;QACxC,OAAO5B,GAAG,GAAGsD,oBAAoB,CAAC1B,IAAD,CAAjC;KADK,EAEJ,CAFI,CAAP;AAGD;AAED,SAAgBiE,iBAAiBF,mBAAAA;IAC/B,OAAOA,mBAAmB,CAAC7H,MAApB,CAAmC,CAACkC,GAAD,EAAM4B,IAAN;QACxC,OAAO5B,GAAG,GAAGyD,oBAAoB,CAAC7B,IAAD,CAAjC;KADK,EAEJ,CAFI,CAAP;AAGD;SCtBekE,uBACd3E,OAAAA,EACA4E,OAAAA;QAAAA,YAAAA,KAAAA,GAAAA;QAAAA,UAA6C7E;;IAE7C,IAAI,CAACC,OAAL,EAAc;QACZ;;IAGF,MAAM,EAACjF,GAAD,EAAMF,IAAN,EAAY2C,MAAZ,EAAoBF,KAAAA,KAASsH,OAAO,CAAC5E,OAAD,CAA1C;IACA,MAAM4B,uBAAuB,GAAGD,0BAA0B,CAAC3B,OAAD,CAA1D;IAEA,IAAI,CAAC4B,uBAAL,EAA8B;QAC5B;;IAGF,IACEpE,MAAM,IAAI,CAAV,IACAF,KAAK,IAAI,CADT,IAEAvC,GAAG,IAAImH,MAAM,CAAC3B,WAFd,IAGA1F,IAAI,IAAIqH,MAAM,CAAC5B,UAJjB,EAKE;QACAN,OAAO,CAAC6E,cAAR,CAAuB;YACrBC,KAAK,EAAE,QADc;YAErBC,MAAM,EAAE;SAFV;;AAKH;ACtBD,MAAMjE,UAAU,GAAG;IACjB;QAAC,GAAD;QAAM;YAAC,MAAD;YAAS,OAAT;SAAN;QAAyB2D,gBAAzB;KADiB;IAEjB;QAAC,GAAD;QAAM;YAAC,KAAD;YAAQ,QAAR;SAAN;QAAyBC,gBAAzB;KAFiB;CAAnB;AAKA,MAAaM;IACXC,YAAYxK,IAAAA,EAAkBuF,OAAAA,CAAAA;aAyBtBvF,IAAAA,GAAAA,KAAAA;aAEDK,KAAAA,GAAAA,KAAAA;aAEAE,MAAAA,GAAAA,KAAAA;aAIAD,GAAAA,GAAAA,KAAAA;aAEAyC,MAAAA,GAAAA,KAAAA;aAEAF,KAAAA,GAAAA,KAAAA;aAEAzC,IAAAA,GAAAA,KAAAA;QAtCL,MAAM2J,mBAAmB,GAAGvD,sBAAsB,CAACjB,OAAD,CAAlD;QACA,MAAMkF,aAAa,GAAGX,gBAAgB,CAACC,mBAAD,CAAtC;QAEA,IAAA,CAAK/J,IAAL,GAAY;YAAC,GAAGA,IAAAA;SAAhB;QACA,IAAA,CAAKK,KAAL,GAAaL,IAAI,CAACK,KAAlB;QACA,IAAA,CAAKE,MAAL,GAAcP,IAAI,CAACO,MAAnB;QAEA,KAAK,MAAM,CAACmK,IAAD,EAAOC,IAAP,EAAaC,eAAb,CAAX,IAA4CvE,UAA5C,CAAwD;YACtD,KAAK,MAAMwE,GAAX,IAAkBF,IAAlB,CAAwB;gBACtBtL,MAAM,CAACyL,cAAP,CAAsB,IAAtB,EAA4BD,GAA5B,EAAiC;oBAC/BnJ,GAAG,EAAE;wBACH,MAAMqJ,cAAc,GAAGH,eAAe,CAACb,mBAAD,CAAtC;wBACA,MAAMiB,mBAAmB,GAAGP,aAAa,CAACC,IAAD,CAAb,GAAsBK,cAAlD;wBAEA,OAAO,IAAA,CAAK/K,IAAL,CAAU6K,GAAV,CAAA,GAAiBG,mBAAxB;qBAL6B;oBAO/BC,UAAU,EAAE;iBAPd;;;QAYJ5L,MAAM,CAACyL,cAAP,CAAsB,IAAtB,EAA4B,MAA5B,EAAoC;YAACG,UAAU,EAAE;SAAjD;;;MCpCSC;IAOXV,YAAoB7H,MAAAA,CAAAA;aAAAA,MAAAA,GAAAA,KAAAA;aANZvG,SAAAA,GAIF,EAAA;aAaC+O,SAAAA,GAAY;YACjB,IAAA,CAAK/O,SAAL,CAAeS,OAAf,EAAwBhB,QAAD;gBAAA,IAAA;gBAAA,OAAA,CAAA,eACrB,IAAA,CAAK8G,MADgB,KAAA,OAAA,KAAA,IACrB,aAAayI,mBAAb,CAAiC,GAAGvP,QAApC,CADqB;aAAvB;;QAZkB,IAAA,CAAA,MAAA,GAAA8G,MAAA;;IAEbnG,GAAG,CACR6O,SADQ,EAERC,OAFQ,EAGRtM,OAHQ,EAAA;;QAKR,CAAA,gBAAA,IAAA,CAAK2D,MAAL,KAAA,OAAA,KAAA,IAAA,cAAa4I,gBAAb,CAA8BF,SAA9B,EAAyCC,OAAzC,EAAmEtM,OAAnE;QACA,IAAA,CAAK5C,SAAL,CAAewF,IAAf,CAAoB;YAACyJ,SAAD;YAAYC,OAAZ;YAAsCtM,OAAtC;SAApB;;;SCbYwM,uBACd7I,MAAAA;;;;;;IAQA,MAAM,EAAC8I,WAAAA,kLAAehG,YAAAA,AAAS,EAAC9C,MAAD,CAA/B;IAEA,OAAOA,MAAM,YAAY8I,WAAlB,GAAgC9I,MAAhC,GAAyC6E,gMAAAA,AAAgB,EAAC7E,MAAD,CAAhE;AACD;SCZe+I,oBACdC,KAAAA,EACAC,WAAAA;IAEA,MAAMC,EAAE,GAAGjM,IAAI,CAAC+J,GAAL,CAASgC,KAAK,CAACpM,CAAf,CAAX;IACA,MAAMuM,EAAE,GAAGlM,IAAI,CAAC+J,GAAL,CAASgC,KAAK,CAACnM,CAAf,CAAX;IAEA,IAAI,OAAOoM,WAAP,KAAuB,QAA3B,EAAqC;QACnC,OAAOhM,IAAI,CAACC,IAAL,CAAUgM,EAAE,IAAI,CAAN,GAAUC,EAAE,IAAI,CAA1B,IAA+BF,WAAtC;;IAGF,IAAI,OAAOA,WAAP,IAAsB,OAAOA,WAAjC,EAA8C;QAC5C,OAAOC,EAAE,GAAGD,WAAW,CAACrM,CAAjB,IAAsBuM,EAAE,GAAGF,WAAW,CAACpM,CAA9C;;IAGF,IAAI,OAAOoM,WAAX,EAAwB;QACtB,OAAOC,EAAE,GAAGD,WAAW,CAACrM,CAAxB;;IAGF,IAAI,OAAOqM,WAAX,EAAwB;QACtB,OAAOE,EAAE,GAAGF,WAAW,CAACpM,CAAxB;;IAGF,OAAO,KAAP;AACD;AC1BD,IAAYuM,SAAZ;AAAA,CAAA,SAAYA,SAAAA;IACVA,SAAAA,CAAAA,QAAA,GAAA,OAAA;IACAA,SAAAA,CAAAA,YAAA,GAAA,WAAA;IACAA,SAAAA,CAAAA,UAAA,GAAA,SAAA;IACAA,SAAAA,CAAAA,cAAA,GAAA,aAAA;IACAA,SAAAA,CAAAA,SAAA,GAAA,QAAA;IACAA,SAAAA,CAAAA,kBAAA,GAAA,iBAAA;IACAA,SAAAA,CAAAA,mBAAA,GAAA,kBAAA;AACD,CARD,EAAYA,SAAS,IAAA,CAATA,SAAS,GAAA,CAAA,CAAA,CAArB;AAUA,SAAgBC,eAAepP,KAAAA;IAC7BA,KAAK,CAACoP,cAAN;AACD;AAED,SAAgBC,gBAAgBrP,KAAAA;IAC9BA,KAAK,CAACqP,eAAN;AACD;ICbWC,YAAZ;AAAA,CAAA,SAAYA,YAAAA;IACVA,YAAAA,CAAAA,QAAA,GAAA,OAAA;IACAA,YAAAA,CAAAA,OAAA,GAAA,WAAA;IACAA,YAAAA,CAAAA,QAAA,GAAA,YAAA;IACAA,YAAAA,CAAAA,OAAA,GAAA,WAAA;IACAA,YAAAA,CAAAA,KAAA,GAAA,SAAA;IACAA,YAAAA,CAAAA,MAAA,GAAA,QAAA;IACAA,YAAAA,CAAAA,QAAA,GAAA,OAAA;IACAA,YAAAA,CAAAA,MAAA,GAAA,KAAA;AACD,CATD,EAAYA,YAAY,IAAA,CAAZA,YAAY,GAAA,CAAA,CAAA,CAAxB;ACDO,MAAMC,oBAAoB,GAAkB;IACjDC,KAAK,EAAE;QAACF,YAAY,CAACG,KAAd;QAAqBH,YAAY,CAACI,KAAlC;KAD0C;IAEjDC,MAAM,EAAE;QAACL,YAAY,CAACM,GAAd;KAFyC;IAGjDC,GAAG,EAAE;QAACP,YAAY,CAACG,KAAd;QAAqBH,YAAY,CAACI,KAAlC;QAAyCJ,YAAY,CAACQ,GAAtD;KAAA;AAH4C,CAA5C;AAMP,MAAaC,+BAA+B,GAA6B,CACvE/P,KADuE,EAAA;QAEvE,EAACgQ,kBAAAA;IAED,OAAQhQ,KAAK,CAACiQ,IAAd;QACE,KAAKX,YAAY,CAACY,KAAlB;YACE,OAAO;gBACL,GAAGF,kBADE;gBAELrN,CAAC,EAAEqN,kBAAkB,CAACrN,CAAnB,GAAuB;aAF5B;QAIF,KAAK2M,YAAY,CAACa,IAAlB;YACE,OAAO;gBACL,GAAGH,kBADE;gBAELrN,CAAC,EAAEqN,kBAAkB,CAACrN,CAAnB,GAAuB;aAF5B;QAIF,KAAK2M,YAAY,CAACc,IAAlB;YACE,OAAO;gBACL,GAAGJ,kBADE;gBAELpN,CAAC,EAAEoN,kBAAkB,CAACpN,CAAnB,GAAuB;aAF5B;QAIF,KAAK0M,YAAY,CAACe,EAAlB;YACE,OAAO;gBACL,GAAGL,kBADE;gBAELpN,CAAC,EAAEoN,kBAAkB,CAACpN,CAAnB,GAAuB;aAF5B;;IAMJ,OAAO0N,SAAP;AACD,CA5BM;MC+BMC;IAMX3C,YAAoB4C,KAAAA,CAAAA;aAAAA,KAAAA,GAAAA,KAAAA;aALbC,iBAAAA,GAAoB;aACnBC,oBAAAA,GAAAA,KAAAA;aACAlR,SAAAA,GAAAA,KAAAA;aACAmR,eAAAA,GAAAA,KAAAA;QAEY,IAAA,CAAA,KAAA,GAAAH,KAAA;QAClB,MAAM,EACJxQ,KAAK,EAAE,EAAC+F,MAAAA,OACNyK,KAFJ;QAIA,IAAA,CAAKA,KAAL,GAAaA,KAAb;QACA,IAAA,CAAKhR,SAAL,GAAiB,IAAI8O,SAAJ,8KAAc1D,mBAAAA,AAAgB,EAAC7E,MAAD,CAA9B,CAAjB;QACA,IAAA,CAAK4K,eAAL,GAAuB,IAAIrC,SAAJ,8KAAczF,YAAAA,AAAS,EAAC9C,MAAD,CAAvB,CAAvB;QACA,IAAA,CAAK6K,aAAL,GAAqB,IAAA,CAAKA,aAAL,CAAmBC,IAAnB,CAAwB,IAAxB,CAArB;QACA,IAAA,CAAKC,YAAL,GAAoB,IAAA,CAAKA,YAAL,CAAkBD,IAAlB,CAAuB,IAAvB,CAApB;QAEA,IAAA,CAAKE,MAAL;;IAGMA,MAAM,GAAA;QACZ,IAAA,CAAKC,WAAL;QAEA,IAAA,CAAKL,eAAL,CAAqB/Q,GAArB,CAAyBuP,SAAS,CAAC8B,MAAnC,EAA2C,IAAA,CAAKH,YAAhD;QACA,IAAA,CAAKH,eAAL,CAAqB/Q,GAArB,CAAyBuP,SAAS,CAAC+B,gBAAnC,EAAqD,IAAA,CAAKJ,YAA1D;QAEAK,UAAU,CAAC,IAAM,IAAA,CAAK3R,SAAL,CAAeI,GAAf,CAAmBuP,SAAS,CAACiC,OAA7B,EAAsC,IAAA,CAAKR,aAA3C,CAAP,CAAV;;IAGMI,WAAW,GAAA;QACjB,MAAM,EAACK,UAAD,EAAaC,OAAAA,KAAW,IAAA,CAAKd,KAAnC;QACA,MAAMpH,IAAI,GAAGiI,UAAU,CAACjI,IAAX,CAAgBmI,OAA7B;QAEA,IAAInI,IAAJ,EAAU;YACRkE,sBAAsB,CAAClE,IAAD,CAAtB;;QAGFkI,OAAO,CAAC9O,kBAAD,CAAP;;IAGMoO,aAAa,CAAC5Q,KAAD,EAAA;QACnB,IAAIwR,+LAAAA,AAAe,EAACxR,KAAD,CAAnB,EAA4B;YAC1B,MAAM,EAACM,MAAD,EAASmR,OAAT,EAAkBrP,OAAAA,KAAW,IAAA,CAAKoO,KAAxC;YACA,MAAM,EACJkB,aAAa,GAAGnC,oBADZ,EAEJoC,gBAAgB,GAAG5B,+BAFf,EAGJ6B,cAAc,GAAG,QAAA,KACfxP,OAJJ;YAKA,MAAM,EAAC6N,IAAAA,KAAQjQ,KAAf;YAEA,IAAI0R,aAAa,CAAC7B,GAAd,CAAkB3F,QAAlB,CAA2B+F,IAA3B,CAAJ,EAAsC;gBACpC,IAAA,CAAK4B,SAAL,CAAe7R,KAAf;gBACA;;YAGF,IAAI0R,aAAa,CAAC/B,MAAd,CAAqBzF,QAArB,CAA8B+F,IAA9B,CAAJ,EAAyC;gBACvC,IAAA,CAAKa,YAAL,CAAkB9Q,KAAlB;gBACA;;YAGF,MAAM,EAACyE,aAAAA,KAAiBgN,OAAO,CAACF,OAAhC;YACA,MAAMvB,kBAAkB,GAAGvL,aAAa,GACpC;gBAAC9B,CAAC,EAAE8B,aAAa,CAACjB,IAAlB;gBAAwBZ,CAAC,EAAE6B,aAAa,CAACf,GAAAA;aADL,GAEpClB,kBAFJ;YAIA,IAAI,CAAC,IAAA,CAAKkO,oBAAV,EAAgC;gBAC9B,IAAA,CAAKA,oBAAL,GAA4BV,kBAA5B;;YAGF,MAAM8B,cAAc,GAAGH,gBAAgB,CAAC3R,KAAD,EAAQ;gBAC7CM,MAD6C;gBAE7CmR,OAAO,EAAEA,OAAO,CAACF,OAF4B;gBAG7CvB;aAHqC,CAAvC;YAMA,IAAI8B,cAAJ,EAAoB;gBAClB,MAAMC,gBAAgB,IAAGC,uLAAAA,AAAmB,EAC1CF,cAD0C,EAE1C9B,kBAF0C,CAA5C;gBAIA,MAAMiC,WAAW,GAAG;oBAClBtP,CAAC,EAAE,CADe;oBAElBC,CAAC,EAAE;iBAFL;gBAIA,MAAM,EAACuK,mBAAAA,KAAuBsE,OAAO,CAACF,OAAtC;gBAEA,KAAK,MAAMhF,eAAX,IAA8BY,mBAA9B,CAAmD;oBACjD,MAAMR,SAAS,GAAG3M,KAAK,CAACiQ,IAAxB;oBACA,MAAM,EAAChE,KAAD,EAAQG,OAAR,EAAiBF,MAAjB,EAAyBC,QAAzB,EAAmCL,SAAnC,EAA8CJ,SAAAA,KAClDF,iBAAiB,CAACe,eAAD,CADnB;oBAEA,MAAM2F,iBAAiB,GAAGjF,oBAAoB,CAACV,eAAD,CAA9C;oBAEA,MAAM4F,kBAAkB,GAAG;wBACzBxP,CAAC,EAAEK,IAAI,CAACkD,GAAL,CACDyG,SAAS,KAAK2C,YAAY,CAACY,KAA3B,GACIgC,iBAAiB,CAACjM,KAAlB,GAA0BiM,iBAAiB,CAACzO,KAAlB,GAA0B,CADxD,GAEIyO,iBAAiB,CAACjM,KAHrB,EAIDjD,IAAI,CAACgD,GAAL,CACE2G,SAAS,KAAK2C,YAAY,CAACY,KAA3B,GACIgC,iBAAiB,CAAC1O,IADtB,GAEI0O,iBAAiB,CAAC1O,IAAlB,GAAyB0O,iBAAiB,CAACzO,KAAlB,GAA0B,CAHzD,EAIEqO,cAAc,CAACnP,CAJjB,CAJC,CADsB;wBAYzBC,CAAC,EAAEI,IAAI,CAACkD,GAAL,CACDyG,SAAS,KAAK2C,YAAY,CAACc,IAA3B,GACI8B,iBAAiB,CAAC/L,MAAlB,GAA2B+L,iBAAiB,CAACvO,MAAlB,GAA2B,CAD1D,GAEIuO,iBAAiB,CAAC/L,MAHrB,EAIDnD,IAAI,CAACgD,GAAL,CACE2G,SAAS,KAAK2C,YAAY,CAACc,IAA3B,GACI8B,iBAAiB,CAACxO,GADtB,GAEIwO,iBAAiB,CAACxO,GAAlB,GAAwBwO,iBAAiB,CAACvO,MAAlB,GAA2B,CAHzD,EAIEmO,cAAc,CAAClP,CAJjB,CAJC;qBAZL;oBAyBA,MAAMwP,UAAU,GACbzF,SAAS,KAAK2C,YAAY,CAACY,KAA3B,IAAoC,CAAC9D,OAAtC,IACCO,SAAS,KAAK2C,YAAY,CAACa,IAA3B,IAAmC,CAACjE,MAFvC;oBAGA,MAAMmG,UAAU,GACb1F,SAAS,KAAK2C,YAAY,CAACc,IAA3B,IAAmC,CAACjE,QAArC,IACCQ,SAAS,KAAK2C,YAAY,CAACe,EAA3B,IAAiC,CAACpE,KAFrC;oBAIA,IAAImG,UAAU,IAAID,kBAAkB,CAACxP,CAAnB,KAAyBmP,cAAc,CAACnP,CAA1D,EAA6D;wBAC3D,MAAM2P,oBAAoB,GACxB/F,eAAe,CAACvB,UAAhB,GAA6B+G,gBAAgB,CAACpP,CADhD;wBAEA,MAAM4P,yBAAyB,GAC5B5F,SAAS,KAAK2C,YAAY,CAACY,KAA3B,IACCoC,oBAAoB,IAAIxG,SAAS,CAACnJ,CADpC,IAECgK,SAAS,KAAK2C,YAAY,CAACa,IAA3B,IACCmC,oBAAoB,IAAI5G,SAAS,CAAC/I,CAJtC;wBAMA,IAAI4P,yBAAyB,IAAI,CAACR,gBAAgB,CAACnP,CAAnD,EAAsD;;;4BAGpD2J,eAAe,CAACiG,QAAhB,CAAyB;gCACvBhP,IAAI,EAAE8O,oBADiB;gCAEvBG,QAAQ,EAAEb;6BAFZ;4BAIA;;wBAGF,IAAIW,yBAAJ,EAA+B;4BAC7BN,WAAW,CAACtP,CAAZ,GAAgB4J,eAAe,CAACvB,UAAhB,GAA6BsH,oBAA7C;yBADF,MAEO;4BACLL,WAAW,CAACtP,CAAZ,GACEgK,SAAS,KAAK2C,YAAY,CAACY,KAA3B,GACI3D,eAAe,CAACvB,UAAhB,GAA6Bc,SAAS,CAACnJ,CAD3C,GAEI4J,eAAe,CAACvB,UAAhB,GAA6BU,SAAS,CAAC/I,CAH7C;;wBAMF,IAAIsP,WAAW,CAACtP,CAAhB,EAAmB;4BACjB4J,eAAe,CAACmG,QAAhB,CAAyB;gCACvBlP,IAAI,EAAE,CAACyO,WAAW,CAACtP,CADI;gCAEvB8P,QAAQ,EAAEb;6BAFZ;;wBAKF;qBAlCF,MAmCO,IAAIS,UAAU,IAAIF,kBAAkB,CAACvP,CAAnB,KAAyBkP,cAAc,CAAClP,CAA1D,EAA6D;wBAClE,MAAM0P,oBAAoB,GACxB/F,eAAe,CAACpB,SAAhB,GAA4B4G,gBAAgB,CAACnP,CAD/C;wBAEA,MAAM2P,yBAAyB,GAC5B5F,SAAS,KAAK2C,YAAY,CAACc,IAA3B,IACCkC,oBAAoB,IAAIxG,SAAS,CAAClJ,CADpC,IAEC+J,SAAS,KAAK2C,YAAY,CAACe,EAA3B,IACCiC,oBAAoB,IAAI5G,SAAS,CAAC9I,CAJtC;wBAMA,IAAI2P,yBAAyB,IAAI,CAACR,gBAAgB,CAACpP,CAAnD,EAAsD;;;4BAGpD4J,eAAe,CAACiG,QAAhB,CAAyB;gCACvB9O,GAAG,EAAE4O,oBADkB;gCAEvBG,QAAQ,EAAEb;6BAFZ;4BAIA;;wBAGF,IAAIW,yBAAJ,EAA+B;4BAC7BN,WAAW,CAACrP,CAAZ,GAAgB2J,eAAe,CAACpB,SAAhB,GAA4BmH,oBAA5C;yBADF,MAEO;4BACLL,WAAW,CAACrP,CAAZ,GACE+J,SAAS,KAAK2C,YAAY,CAACc,IAA3B,GACI7D,eAAe,CAACpB,SAAhB,GAA4BW,SAAS,CAAClJ,CAD1C,GAEI2J,eAAe,CAACpB,SAAhB,GAA4BO,SAAS,CAAC9I,CAH5C;;wBAMF,IAAIqP,WAAW,CAACrP,CAAhB,EAAmB;4BACjB2J,eAAe,CAACmG,QAAhB,CAAyB;gCACvBhP,GAAG,EAAE,CAACuO,WAAW,CAACrP,CADK;gCAEvB6P,QAAQ,EAAEb;6BAFZ;;wBAMF;;;gBAIJ,IAAA,CAAKe,UAAL,CACE3S,KADF,+KAEE4S,MAAAA,AAAsB,EACpBZ,wLAAAA,AAAmB,EAACF,cAAD,EAAiB,IAAA,CAAKpB,oBAAtB,CADC,EAEpBuB,WAFoB,CAFxB;;;;IAWEU,UAAU,CAAC3S,KAAD,EAAe6S,WAAf,EAAA;QAChB,MAAM,EAACC,MAAAA,KAAU,IAAA,CAAKtC,KAAtB;QAEAxQ,KAAK,CAACoP,cAAN;QACA0D,MAAM,CAACD,WAAD,CAAN;;IAGMhB,SAAS,CAAC7R,KAAD,EAAA;QACf,MAAM,EAAC+S,KAAAA,KAAS,IAAA,CAAKvC,KAArB;QAEAxQ,KAAK,CAACoP,cAAN;QACA,IAAA,CAAK4D,MAAL;QACAD,KAAK;;IAGCjC,YAAY,CAAC9Q,KAAD,EAAA;QAClB,MAAM,EAACiT,QAAAA,KAAY,IAAA,CAAKzC,KAAxB;QAEAxQ,KAAK,CAACoP,cAAN;QACA,IAAA,CAAK4D,MAAL;QACAC,QAAQ;;IAGFD,MAAM,GAAA;QACZ,IAAA,CAAKxT,SAAL,CAAe+O,SAAf;QACA,IAAA,CAAKoC,eAAL,CAAqBpC,SAArB;;;AA1OSgC,eA6OJ2C,UAAAA,GAAgD;IACrD;QACEzE,SAAS,EAAE,WADb;QAEEC,OAAO,EAAE,CACP1O,KADO,EAAA,MAAA;gBAEP,EAAC0R,aAAa,GAAGnC,oBAAjB,EAAuC4D,YAAAA;gBACvC,EAAC7S,MAAAA;YAED,MAAM,EAAC2P,IAAAA,KAAQjQ,KAAK,CAACoT,WAArB;YAEA,IAAI1B,aAAa,CAAClC,KAAd,CAAoBtF,QAApB,CAA6B+F,IAA7B,CAAJ,EAAwC;gBACtC,MAAMoD,SAAS,GAAG/S,MAAM,CAACgT,aAAP,CAAqB/B,OAAvC;gBAEA,IAAI8B,SAAS,IAAIrT,KAAK,CAAC+F,MAAN,KAAiBsN,SAAlC,EAA6C;oBAC3C,OAAO,KAAP;;gBAGFrT,KAAK,CAACoP,cAAN;gBAEA+D,YAAY,IAAA,IAAZ,GAAA,KAAA,IAAAA,YAAY,CAAG;oBAACnT,KAAK,EAAEA,KAAK,CAACoT,WAAAA;iBAAjB,CAAZ;gBAEA,OAAO,IAAP;;YAGF,OAAO,KAAP;;IAvBJ,CADqD;CAAA;ACxOzD,SAASG,oBAAT,CACEC,UADF;IAGE,OAAOC,OAAO,CAACD,UAAU,IAAI,cAAcA,UAA7B,CAAd;AACD;AAED,SAASE,iBAAT,CACEF,UADF;IAGE,OAAOC,OAAO,CAACD,UAAU,IAAI,WAAWA,UAA1B,CAAd;AACD;AAaD,MAAaG;IAUX/F,YACU4C,KAAAA,EACAoD,MAAAA,EACRC,cAAAA,CAAAA;;YAAAA,mBAAAA,KAAAA,GAAAA;YAAAA,iBAAiBjF,sBAAsB,CAAC4B,KAAK,CAACxQ,KAAN,CAAY+F,MAAb;;aAF/ByK,KAAAA,GAAAA,KAAAA;aACAoD,MAAAA,GAAAA,KAAAA;aAXHnD,iBAAAA,GAAoB;aACnBlF,QAAAA,GAAAA,KAAAA;aACAuI,SAAAA,GAAqB;aACrBC,kBAAAA,GAAAA,KAAAA;aACAC,SAAAA,GAAmC;aACnCxU,SAAAA,GAAAA,KAAAA;aACAyU,iBAAAA,GAAAA,KAAAA;aACAtD,eAAAA,GAAAA,KAAAA;QAGE,IAAA,CAAA,KAAA,GAAAH,KAAA;QACA,IAAA,CAAA,MAAA,GAAAoD,MAAA;QAGR,MAAM,EAAC5T,KAAAA,KAASwQ,KAAhB;QACA,MAAM,EAACzK,MAAAA,KAAU/F,KAAjB;QAEA,IAAA,CAAKwQ,KAAL,GAAaA,KAAb;QACA,IAAA,CAAKoD,MAAL,GAAcA,MAAd;QACA,IAAA,CAAKrI,QAAL,+KAAgBX,oBAAAA,AAAgB,EAAC7E,MAAD,CAAhC;QACA,IAAA,CAAKkO,iBAAL,GAAyB,IAAI3F,SAAJ,CAAc,IAAA,CAAK/C,QAAnB,CAAzB;QACA,IAAA,CAAK/L,SAAL,GAAiB,IAAI8O,SAAJ,CAAcuF,cAAd,CAAjB;QACA,IAAA,CAAKlD,eAAL,GAAuB,IAAIrC,SAAJ,8KAAczF,YAAAA,AAAS,EAAC9C,MAAD,CAAvB,CAAvB;QACA,IAAA,CAAKgO,kBAAL,GAAA,CAAA,oMAA0BzQ,sBAAAA,AAAmB,EAACtD,KAAD,CAA7C,KAAA,OAAA,uBAAwDwC,kBAAxD;QACA,IAAA,CAAKwO,WAAL,GAAmB,IAAA,CAAKA,WAAL,CAAiBH,IAAjB,CAAsB,IAAtB,CAAnB;QACA,IAAA,CAAK8B,UAAL,GAAkB,IAAA,CAAKA,UAAL,CAAgB9B,IAAhB,CAAqB,IAArB,CAAlB;QACA,IAAA,CAAKgB,SAAL,GAAiB,IAAA,CAAKA,SAAL,CAAehB,IAAf,CAAoB,IAApB,CAAjB;QACA,IAAA,CAAKC,YAAL,GAAoB,IAAA,CAAKA,YAAL,CAAkBD,IAAlB,CAAuB,IAAvB,CAApB;QACA,IAAA,CAAKqD,aAAL,GAAqB,IAAA,CAAKA,aAAL,CAAmBrD,IAAnB,CAAwB,IAAxB,CAArB;QACA,IAAA,CAAKsD,mBAAL,GAA2B,IAAA,CAAKA,mBAAL,CAAyBtD,IAAzB,CAA8B,IAA9B,CAA3B;QAEA,IAAA,CAAKE,MAAL;;IAGMA,MAAM,GAAA;QACZ,MAAM,EACJ6C,MADI,EAEJpD,KAAK,EAAE,EACLpO,OAAO,EAAE,EAACgS,oBAAD,EAAuBC,0BAAAA,SAEhC,IALJ;QAOA,IAAA,CAAK7U,SAAL,CAAeI,GAAf,CAAmBgU,MAAM,CAACU,IAAP,CAAYC,IAA/B,EAAqC,IAAA,CAAK5B,UAA1C,EAAsD;YAAC6B,OAAO,EAAE;SAAhE;QACA,IAAA,CAAKhV,SAAL,CAAeI,GAAf,CAAmBgU,MAAM,CAAC/D,GAAP,CAAW0E,IAA9B,EAAoC,IAAA,CAAK1C,SAAzC;QAEA,IAAI+B,MAAM,CAACjE,MAAX,EAAmB;YACjB,IAAA,CAAKnQ,SAAL,CAAeI,GAAf,CAAmBgU,MAAM,CAACjE,MAAP,CAAc4E,IAAjC,EAAuC,IAAA,CAAKzD,YAA5C;;QAGF,IAAA,CAAKH,eAAL,CAAqB/Q,GAArB,CAAyBuP,SAAS,CAAC8B,MAAnC,EAA2C,IAAA,CAAKH,YAAhD;QACA,IAAA,CAAKH,eAAL,CAAqB/Q,GAArB,CAAyBuP,SAAS,CAACsF,SAAnC,EAA8CrF,cAA9C;QACA,IAAA,CAAKuB,eAAL,CAAqB/Q,GAArB,CAAyBuP,SAAS,CAAC+B,gBAAnC,EAAqD,IAAA,CAAKJ,YAA1D;QACA,IAAA,CAAKH,eAAL,CAAqB/Q,GAArB,CAAyBuP,SAAS,CAACuF,WAAnC,EAAgDtF,cAAhD;QACA,IAAA,CAAK6E,iBAAL,CAAuBrU,GAAvB,CAA2BuP,SAAS,CAACiC,OAArC,EAA8C,IAAA,CAAK8C,aAAnD;QAEA,IAAIE,oBAAJ,EAA0B;YACxB,IACEC,0BADF,IAAA,QACEA,0BAA0B,CAAG;gBAC3BrU,KAAK,EAAE,IAAA,CAAKwQ,KAAL,CAAWxQ,KADS;gBAE3BqR,UAAU,EAAE,IAAA,CAAKb,KAAL,CAAWa,UAFI;gBAG3BjP,OAAO,EAAE,IAAA,CAAKoO,KAAL,CAAWpO,OAAAA;aAHI,CAD5B,EAME;gBACA,OAAO,IAAA,CAAK4O,WAAL,EAAP;;YAGF,IAAI0C,iBAAiB,CAACU,oBAAD,CAArB,EAA6C;gBAC3C,IAAA,CAAKJ,SAAL,GAAiB7C,UAAU,CACzB,IAAA,CAAKH,WADoB,EAEzBoD,oBAAoB,CAACO,KAFI,CAA3B;gBAIA,IAAA,CAAKC,aAAL,CAAmBR,oBAAnB;gBACA;;YAGF,IAAIb,oBAAoB,CAACa,oBAAD,CAAxB,EAAgD;gBAC9C,IAAA,CAAKQ,aAAL,CAAmBR,oBAAnB;gBACA;;;QAIJ,IAAA,CAAKpD,WAAL;;IAGMgC,MAAM,GAAA;QACZ,IAAA,CAAKxT,SAAL,CAAe+O,SAAf;QACA,IAAA,CAAKoC,eAAL,CAAqBpC,SAArB,IAAA,oEAAA;;QAIA4C,UAAU,CAAC,IAAA,CAAK8C,iBAAL,CAAuB1F,SAAxB,EAAmC,EAAnC,CAAV;QAEA,IAAI,IAAA,CAAKyF,SAAL,KAAmB,IAAvB,EAA6B;YAC3Ba,YAAY,CAAC,IAAA,CAAKb,SAAN,CAAZ;YACA,IAAA,CAAKA,SAAL,GAAiB,IAAjB;;;IAIIY,aAAa,CACnBpB,UADmB,EAEnBsB,MAFmB,EAAA;QAInB,MAAM,EAACxU,MAAD,EAASyU,SAAAA,KAAa,IAAA,CAAKvE,KAAjC;QACAuE,SAAS,CAACzU,MAAD,EAASkT,UAAT,EAAqB,IAAA,CAAKO,kBAA1B,EAA8Ce,MAA9C,CAAT;;IAGM9D,WAAW,GAAA;QACjB,MAAM,EAAC+C,kBAAAA,KAAsB,IAA7B;QACA,MAAM,EAACzC,OAAAA,KAAW,IAAA,CAAKd,KAAvB;QAEA,IAAIuD,kBAAJ,EAAwB;YACtB,IAAA,CAAKD,SAAL,GAAiB,IAAjB,CADsB,CAAA,uEAAA;YAItB,IAAA,CAAKG,iBAAL,CAAuBrU,GAAvB,CAA2BuP,SAAS,CAAC6F,KAArC,EAA4C3F,eAA5C,EAA6D;gBAC3D4F,OAAO,EAAE;aADX,EAJsB,CAAA,8CAAA;YAStB,IAAA,CAAKd,mBAAL,GATsB,CAAA,gDAAA;YAYtB,IAAA,CAAKF,iBAAL,CAAuBrU,GAAvB,CACEuP,SAAS,CAAC+F,eADZ,EAEE,IAAA,CAAKf,mBAFP;YAKA7C,OAAO,CAACyC,kBAAD,CAAP;;;IAIIpB,UAAU,CAAC3S,KAAD,EAAA;;QAChB,MAAM,EAAC8T,SAAD,EAAYC,kBAAZ,EAAgCvD,KAAAA,KAAS,IAA/C;QACA,MAAM,EACJsC,MADI,EAEJ1Q,OAAO,EAAE,EAACgS,oBAAAA,OACR5D,KAHJ;QAKA,IAAI,CAACuD,kBAAL,EAAyB;YACvB;;QAGF,MAAMlB,WAAW,GAAA,CAAA,qMAAGvP,sBAAAA,AAAmB,EAACtD,KAAD,CAAtB,KAAA,OAAA,wBAAiCwC,kBAAlD;QACA,MAAMuM,KAAK,+KAAGiD,YAAAA,AAAmB,EAAC+B,kBAAD,EAAqBlB,WAArB,CAAjC,EAAA,wBAAA;QAGA,IAAI,CAACiB,SAAD,IAAcM,oBAAlB,EAAwC;YACtC,IAAIb,oBAAoB,CAACa,oBAAD,CAAxB,EAAgD;gBAC9C,IACEA,oBAAoB,CAACe,SAArB,IAAkC,IAAlC,IACArG,mBAAmB,CAACC,KAAD,EAAQqF,oBAAoB,CAACe,SAA7B,CAFrB,EAGE;oBACA,OAAO,IAAA,CAAKrE,YAAL,EAAP;;gBAGF,IAAIhC,mBAAmB,CAACC,KAAD,EAAQqF,oBAAoB,CAACgB,QAA7B,CAAvB,EAA+D;oBAC7D,OAAO,IAAA,CAAKpE,WAAL,EAAP;;;YAIJ,IAAI0C,iBAAiB,CAACU,oBAAD,CAArB,EAA6C;gBAC3C,IAAItF,mBAAmB,CAACC,KAAD,EAAQqF,oBAAoB,CAACe,SAA7B,CAAvB,EAAgE;oBAC9D,OAAO,IAAA,CAAKrE,YAAL,EAAP;;;YAIJ,IAAA,CAAK8D,aAAL,CAAmBR,oBAAnB,EAAyCrF,KAAzC;YACA;;QAGF,IAAI/O,KAAK,CAACqV,UAAV,EAAsB;YACpBrV,KAAK,CAACoP,cAAN;;QAGF0D,MAAM,CAACD,WAAD,CAAN;;IAGMhB,SAAS,GAAA;QACf,MAAM,EAACyD,OAAD,EAAUvC,KAAAA,KAAS,IAAA,CAAKvC,KAA9B;QAEA,IAAA,CAAKwC,MAAL;QACA,IAAI,CAAC,IAAA,CAAKc,SAAV,EAAqB;YACnBwB,OAAO,CAAC,IAAA,CAAK9E,KAAL,CAAWlQ,MAAZ,CAAP;;QAEFyS,KAAK;;IAGCjC,YAAY,GAAA;QAClB,MAAM,EAACwE,OAAD,EAAUrC,QAAAA,KAAY,IAAA,CAAKzC,KAAjC;QAEA,IAAA,CAAKwC,MAAL;QACA,IAAI,CAAC,IAAA,CAAKc,SAAV,EAAqB;YACnBwB,OAAO,CAAC,IAAA,CAAK9E,KAAL,CAAWlQ,MAAZ,CAAP;;QAEF2S,QAAQ;;IAGFiB,aAAa,CAAClU,KAAD,EAAA;QACnB,IAAIA,KAAK,CAACiQ,IAAN,KAAeX,YAAY,CAACM,GAAhC,EAAqC;YACnC,IAAA,CAAKkB,YAAL;;;IAIIqD,mBAAmB,GAAA;;QACzB,CAAA,wBAAA,IAAA,CAAK5I,QAAL,CAAcgK,YAAd,EAAA,KAAA,OAAA,KAAA,IAAA,sBAA8BC,eAA9B;;;ACtQJ,MAAM5B,MAAM,GAAyB;IACnCjE,MAAM,EAAE;QAAC4E,IAAI,EAAE;KADoB;IAEnCD,IAAI,EAAE;QAACC,IAAI,EAAE;KAFsB;IAGnC1E,GAAG,EAAE;QAAC0E,IAAI,EAAE;;AAHuB,CAArC;AAUA,MAAakB,sBAAsB9B;IACjC/F,YAAY4C,KAAAA,CAAAA;QACV,MAAM,EAACxQ,KAAAA,KAASwQ,KAAhB,EAAA,uEAAA;;QAGA,MAAMqD,cAAc,gLAAGjJ,mBAAAA,AAAgB,EAAC5K,KAAK,CAAC+F,MAAP,CAAvC;QAEA,KAAA,CAAMyK,KAAN,EAAaoD,MAAb,EAAqBC,cAArB;;;AAPS4B,cAUJvC,UAAAA,GAAa;IAClB;QACEzE,SAAS,EAAE,eADb;QAEEC,OAAO,EAAE,CAAA,MAAA;gBACP,EAAC0E,WAAW,EAAEpT,KAAAA;gBACd,EAACmT,YAAAA;YAED,IAAI,CAACnT,KAAK,CAAC0V,SAAP,IAAoB1V,KAAK,CAAC2V,MAAN,KAAiB,CAAzC,EAA4C;gBAC1C,OAAO,KAAP;;YAGFxC,YAAY,IAAA,IAAZ,GAAA,KAAA,IAAAA,YAAY,CAAG;gBAACnT;aAAJ,CAAZ;YAEA,OAAO,IAAP;;IAZJ,CADkB;CAAA;ACpBtB,MAAM4T,QAAM,GAAyB;IACnCU,IAAI,EAAE;QAACC,IAAI,EAAE;KADsB;IAEnC1E,GAAG,EAAE;QAAC0E,IAAI,EAAE;;AAFuB,CAArC;AAKA,IAAKqB,WAAL;AAAA,CAAA,SAAKA,WAAAA;IACHA,WAAAA,CAAAA,WAAAA,CAAAA,aAAAA,GAAAA,EAAA,GAAA,YAAA;AACD,CAFD,EAAKA,WAAW,IAAA,CAAXA,WAAW,GAAA,CAAA,CAAA,CAAhB;AAQA,MAAaC,oBAAoBlC;IAC/B/F,YAAY4C,KAAAA,CAAAA;QACV,KAAA,CAAMA,KAAN,EAAaoD,QAAb,+KAAqBhJ,mBAAAA,AAAgB,EAAC4F,KAAK,CAACxQ,KAAN,CAAY+F,MAAb,CAArC;;;AAFS8P,YAKJ3C,UAAAA,GAAa;IAClB;QACEzE,SAAS,EAAE,aADb;QAEEC,OAAO,EAAE,CAAA,MAAA;gBACP,EAAC0E,WAAW,EAAEpT,KAAAA;gBACd,EAACmT,YAAAA;YAED,IAAInT,KAAK,CAAC2V,MAAN,KAAiBC,WAAW,CAACE,UAAjC,EAA6C;gBAC3C,OAAO,KAAP;;YAGF3C,YAAY,IAAA,IAAZ,GAAA,KAAA,IAAAA,YAAY,CAAG;gBAACnT;aAAJ,CAAZ;YAEA,OAAO,IAAP;;IAZJ,CADkB;CAAA;AClBtB,MAAM4T,QAAM,GAAyB;IACnCjE,MAAM,EAAE;QAAC4E,IAAI,EAAE;KADoB;IAEnCD,IAAI,EAAE;QAACC,IAAI,EAAE;KAFsB;IAGnC1E,GAAG,EAAE;QAAC0E,IAAI,EAAE;;AAHuB,CAArC;AAUA,MAAawB,oBAAoBpC;IAC/B/F,YAAY4C,KAAAA,CAAAA;QACV,KAAA,CAAMA,KAAN,EAAaoD,QAAb;;IAuBU,OAALoC,KAAK,GAAA;;;;QAIVnL,MAAM,CAAC8D,gBAAP,CAAwBiF,QAAM,CAACU,IAAP,CAAYC,IAApC,EAA0CtS,IAA1C,EAAgD;YAC9CgT,OAAO,EAAE,KADqC;YAE9CT,OAAO,EAAE;SAFX;QAKA,OAAO,SAASyB,QAAT;YACLpL,MAAM,CAAC2D,mBAAP,CAA2BoF,QAAM,CAACU,IAAP,CAAYC,IAAvC,EAA6CtS,IAA7C;SADF,EAAA,0EAAA;;;QAMA,SAASA,IAAT,IAAA;;;AAxCS8T,YAKJ7C,UAAAA,GAAa;IAClB;QACEzE,SAAS,EAAE,cADb;QAEEC,OAAO,EAAE,CAAA,MAAA;gBACP,EAAC0E,WAAW,EAAEpT,KAAAA;gBACd,EAACmT,YAAAA;YAED,MAAM,EAAC+C,OAAAA,KAAWlW,KAAlB;YAEA,IAAIkW,OAAO,CAAC7R,MAAR,GAAiB,CAArB,EAAwB;gBACtB,OAAO,KAAP;;YAGF8O,YAAY,IAAA,IAAZ,GAAA,KAAA,IAAAA,YAAY,CAAG;gBAACnT;aAAJ,CAAZ;YAEA,OAAO,IAAP;;IAdJ,CADkB;CAAA;IChBVmW,mBAAZ;AAAA,CAAA,SAAYA,mBAAAA;IACVA,mBAAAA,CAAAA,mBAAAA,CAAAA,UAAAA,GAAAA,EAAA,GAAA,SAAA;IACAA,mBAAAA,CAAAA,mBAAAA,CAAAA,gBAAAA,GAAAA,EAAA,GAAA,eAAA;AACD,CAHD,EAAYA,mBAAmB,IAAA,CAAnBA,mBAAmB,GAAA,CAAA,CAAA,CAA/B;AAmCA,IAAYC,cAAZ;AAAA,CAAA,SAAYA,cAAAA;IACVA,cAAAA,CAAAA,cAAAA,CAAAA,YAAAA,GAAAA,EAAA,GAAA,WAAA;IACAA,cAAAA,CAAAA,cAAAA,CAAAA,oBAAAA,GAAAA,EAAA,GAAA,mBAAA;AACD,CAHD,EAAYA,cAAc,IAAA,CAAdA,cAAc,GAAA,CAAA,CAAA,CAA1B;AAUA,SAAgBC,gBAAAA,IAAAA;QAAgB,EAC9B5J,YAD8B,EAE9B4G,SAAS,GAAG8C,mBAAmB,CAACG,OAFF,EAG9BC,SAH8B,EAI9BC,YAJ8B,EAK9BC,OAL8B,EAM9BC,QAAQ,GAAG,CANmB,EAO9BC,KAAK,GAAGP,cAAc,CAACQ,SAPO,EAQ9BhQ,kBAR8B,EAS9BuG,mBAT8B,EAU9B0J,uBAV8B,EAW9B9H,KAX8B,EAY9BlC,SAAAA;IAEA,MAAMiK,YAAY,GAAGC,eAAe,CAAC;QAAChI,KAAD;QAAQiI,QAAQ,EAAE,CAACP;KAApB,CAApC;IACA,MAAM,CAACQ,qBAAD,EAAwBC,uBAAxB,CAAA,gLAAmDC,cAAAA,AAAW,EAApE;IACA,MAAMC,WAAW,qKAAGC,SAAAA,AAAM,EAAc;QAAC1U,CAAC,EAAE,CAAJ;QAAOC,CAAC,EAAE;KAAxB,CAA1B;IACA,MAAM0U,eAAe,qKAAGD,SAAAA,AAAM,EAAkB;QAAC1U,CAAC,EAAE,CAAJ;QAAOC,CAAC,EAAE;KAA5B,CAA9B;IACA,MAAMQ,IAAI,qKAAG5B,UAAAA,AAAO;yCAAC;YACnB,OAAQ6R,SAAR;gBACE,KAAK8C,mBAAmB,CAACG,OAAzB;oBACE,OAAO1P,kBAAkB,GACrB;wBACElD,GAAG,EAAEkD,kBAAkB,CAAChE,CAD1B;wBAEEuD,MAAM,EAAES,kBAAkB,CAAChE,CAF7B;wBAGEY,IAAI,EAAEoD,kBAAkB,CAACjE,CAH3B;wBAIEsD,KAAK,EAAEW,kBAAkB,CAACjE,CAAAA;qBALP,GAOrB,IAPJ;gBAQF,KAAKwT,mBAAmB,CAACoB,aAAzB;oBACE,OAAOf,YAAP;;SAZc;wCAcjB;QAACnD,SAAD;QAAYmD,YAAZ;QAA0B5P,kBAA1B;KAdiB,CAApB;IAeA,MAAM4Q,kBAAkB,qKAAGH,SAAM,AAANA,EAAuB,IAAjB,CAAjC;IACA,MAAMI,UAAU,GAAG9X,gLAAAA,AAAW;mDAAC;YAC7B,MAAM4M,eAAe,GAAGiL,kBAAkB,CAACjG,OAA3C;YAEA,IAAI,CAAChF,eAAL,EAAsB;gBACpB;;YAGF,MAAMvB,UAAU,GAAGoM,WAAW,CAAC7F,OAAZ,CAAoB5O,CAApB,GAAwB2U,eAAe,CAAC/F,OAAhB,CAAwB5O,CAAnE;YACA,MAAMwI,SAAS,GAAGiM,WAAW,CAAC7F,OAAZ,CAAoB3O,CAApB,GAAwB0U,eAAe,CAAC/F,OAAhB,CAAwB3O,CAAlE;YAEA2J,eAAe,CAACmG,QAAhB,CAAyB1H,UAAzB,EAAqCG,SAArC;SAV4B;kDAW3B,EAX2B,CAA9B;IAYA,MAAMuM,yBAAyB,qKAAGlW,UAAAA,AAAO;8DACvC,IACEmV,KAAK,KAAKP,cAAc,CAACQ,SAAzB,GACI,CAAC;mBAAGzJ,mBAAJ;aAAA,CAAyBwK,OAAzB,EADJ,GAEIxK,mBAJiC;6DAKvC;QAACwJ,KAAD;QAAQxJ,mBAAR;KALuC,CAAzC;sKAQA/N,YAAAA,AAAS;qCACP;YACE,IAAI,CAACqX,OAAD,IAAY,CAACtJ,mBAAmB,CAAC9I,MAAjC,IAA2C,CAACjB,IAAhD,EAAsD;gBACpD8T,uBAAuB;gBACvB;;YAGF,KAAK,MAAM3K,eAAX,IAA8BmL,yBAA9B,CAAyD;gBACvD,IAAI,CAAAnB,SAAS,IAAA,IAAT,GAAA,KAAA,IAAAA,SAAS,CAAGhK,eAAH,CAAT,MAAiC,KAArC,EAA4C;oBAC1C;;gBAGF,MAAM9G,KAAK,GAAG0H,mBAAmB,CAAC9E,OAApB,CAA4BkE,eAA5B,CAAd;gBACA,MAAMC,mBAAmB,GAAGqK,uBAAuB,CAACpR,KAAD,CAAnD;gBAEA,IAAI,CAAC+G,mBAAL,EAA0B;oBACxB;;gBAGF,MAAM,EAACG,SAAD,EAAYC,KAAAA,KAASN,0BAA0B,CACnDC,eADmD,EAEnDC,mBAFmD,EAGnDpJ,IAHmD,EAInDqJ,YAJmD,EAKnDI,SALmD,CAArD;gBAQA,KAAK,MAAMiB,IAAX,IAAmB;oBAAC,GAAD;oBAAM,GAAN;iBAAnB,CAAwC;oBACtC,IAAI,CAACgJ,YAAY,CAAChJ,IAAD,CAAZ,CAAmBnB,SAAS,CAACmB,IAAD,CAA5B,CAAL,EAAuD;wBACrDlB,KAAK,CAACkB,IAAD,CAAL,GAAc,CAAd;wBACAnB,SAAS,CAACmB,IAAD,CAAT,GAAkB,CAAlB;;;gBAIJ,IAAIlB,KAAK,CAACjK,CAAN,GAAU,CAAV,IAAeiK,KAAK,CAAChK,CAAN,GAAU,CAA7B,EAAgC;oBAC9BsU,uBAAuB;oBAEvBM,kBAAkB,CAACjG,OAAnB,GAA6BhF,eAA7B;oBACA0K,qBAAqB,CAACQ,UAAD,EAAaf,QAAb,CAArB;oBAEAU,WAAW,CAAC7F,OAAZ,GAAsB3E,KAAtB;oBACA0K,eAAe,CAAC/F,OAAhB,GAA0B5E,SAA1B;oBAEA;;;YAIJyK,WAAW,CAAC7F,OAAZ,GAAsB;gBAAC5O,CAAC,EAAE,CAAJ;gBAAOC,CAAC,EAAE;aAAhC;YACA0U,eAAe,CAAC/F,OAAhB,GAA0B;gBAAC5O,CAAC,EAAE,CAAJ;gBAAOC,CAAC,EAAE;aAApC;YACAsU,uBAAuB;SAjDlB;oCAoDP;QACEzK,YADF;QAEEgL,UAFF;QAGElB,SAHF;QAIEW,uBAJF;QAKET,OALF;QAMEC,QANF;QAQEkB,IAAI,CAACC,SAAL,CAAezU,IAAf,CARF;QAUEwU,IAAI,CAACC,SAAL,CAAef,YAAf,CAVF;QAWEG,qBAXF;QAYE9J,mBAZF;QAaEuK,yBAbF;QAcEb,uBAdF;QAgBEe,IAAI,CAACC,SAAL,CAAehL,SAAf,CAhBF;KApDO,CAAT;AAuED;AAOD,MAAMiL,mBAAmB,GAAiB;IACxCnV,CAAC,EAAE;QAAC,CAAC0I,SAAS,CAACyB,QAAX,CAAA,EAAsB,KAAvB;QAA8B,CAACzB,SAAS,CAAC2B,OAAX,CAAA,EAAqB;KADd;IAExCpK,CAAC,EAAE;QAAC,CAACyI,SAAS,CAACyB,QAAX,CAAA,EAAsB,KAAvB;QAA8B,CAACzB,SAAS,CAAC2B,OAAX,CAAA,EAAqB;;AAFd,CAA1C;AAKA,SAAS+J,eAAT,CAAA,KAAA;QAAyB,EACvBhI,KADuB,EAEvBiI,QAAAA;IAKA,MAAMe,aAAa,OAAGC,uLAAAA,AAAW,EAACjJ,KAAD,CAAjC;IAEA,oLAAOkJ,cAAAA,AAAW;wCACfC,cAAD;YACE,IAAIlB,QAAQ,IAAI,CAACe,aAAb,IAA8B,CAACG,cAAnC,EAAmD;;gBAEjD,OAAOJ,mBAAP;;YAGF,MAAMnL,SAAS,GAAG;gBAChBhK,CAAC,EAAEK,IAAI,CAACmV,IAAL,CAAUpJ,KAAK,CAACpM,CAAN,GAAUoV,aAAa,CAACpV,CAAlC,CADa;gBAEhBC,CAAC,EAAEI,IAAI,CAACmV,IAAL,CAAUpJ,KAAK,CAACnM,CAAN,GAAUmV,aAAa,CAACnV,CAAlC;aAFL,EAAA,0EAAA;YAMA,OAAO;gBACLD,CAAC,EAAE;oBACD,CAAC0I,SAAS,CAACyB,QAAX,CAAA,EACEoL,cAAc,CAACvV,CAAf,CAAiB0I,SAAS,CAACyB,QAA3B,CAAA,IAAwCH,SAAS,CAAChK,CAAV,KAAgB,CAAC,CAF1D;oBAGD,CAAC0I,SAAS,CAAC2B,OAAX,CAAA,EACEkL,cAAc,CAACvV,CAAf,CAAiB0I,SAAS,CAAC2B,OAA3B,CAAA,IAAuCL,SAAS,CAAChK,CAAV,KAAgB;iBALtD;gBAOLC,CAAC,EAAE;oBACD,CAACyI,SAAS,CAACyB,QAAX,CAAA,EACEoL,cAAc,CAACtV,CAAf,CAAiByI,SAAS,CAACyB,QAA3B,CAAA,IAAwCH,SAAS,CAAC/J,CAAV,KAAgB,CAAC,CAF1D;oBAGD,CAACyI,SAAS,CAAC2B,OAAX,CAAA,EACEkL,cAAc,CAACtV,CAAf,CAAiByI,SAAS,CAAC2B,OAA3B,CAAA,IAAuCL,SAAS,CAAC/J,CAAV,KAAgB;;aAX7D;SAbc;sCA4BhB;QAACoU,QAAD;QAAWjI,KAAX;QAAkBgJ,aAAlB;KA5BgB,CAAlB;AA8BD;SCjOeK,cACdC,cAAAA,EACA9X,EAAAA;IAEA,MAAM+X,aAAa,GAAG/X,EAAE,IAAI,IAAN,GAAa8X,cAAc,CAACvT,GAAf,CAAmBvE,EAAnB,CAAb,GAAsC+P,SAA5D;IACA,MAAMlH,IAAI,GAAGkP,aAAa,GAAGA,aAAa,CAAClP,IAAd,CAAmBmI,OAAtB,GAAgC,IAA1D;IAEA,OAAO0G,2LAAAA,AAAW;sCACfM,UAAD;;YACE,IAAIhY,EAAE,IAAI,IAAV,EAAgB;gBACd,OAAO,IAAP;;;;YAMF,OAAA,CAAA,OAAO6I,IAAP,IAAA,OAAOA,IAAP,GAAemP,UAAf,KAAA,OAAA,OAA6B,IAA7B;SATc;oCAWhB;QAACnP,IAAD;QAAO7I,EAAP;KAXgB,CAAlB;AAaD;SCjBeiY,qBACdlW,OAAAA,EACAmW,mBAAAA;IAKA,yKAAOjX,UAAAA,AAAO;wCACZ,IACEc,OAAO,CAACgD,MAAR;gDAAmC,CAACC,WAAD,EAAcpD,MAAd;oBACjC,MAAM,EAACA,MAAM,EAAEuW,MAAAA,KAAUvW,MAAzB;oBAEA,MAAMwW,gBAAgB,GAAGD,MAAM,CAACxF,UAAP,CAAkB0F,GAAlB;0EAAuBvF,SAAD,GAAA,CAAgB;gCAC7D5E,SAAS,EAAE4E,SAAS,CAAC5E,SADwC;gCAE7DC,OAAO,EAAE+J,mBAAmB,CAACpF,SAAS,CAAC3E,OAAX,EAAoBvM,MAApB;6BAFiB,CAAtB,CAAzB;;oBAKA,OAAO,CAAC;2BAAGoD,WAAJ,EAAiB;2BAAGoT,gBAApB;qBAAP;iBARF;+CASG,EATH,CAFU;uCAYZ;QAACrW,OAAD;QAAUmW,mBAAV;KAZY,CAAd;AAcD;IChBWI,iBAAZ;AAAA,CAAA,SAAYA,iBAAAA;IACVA,iBAAAA,CAAAA,iBAAAA,CAAAA,SAAAA,GAAAA,EAAA,GAAA,QAAA;IACAA,iBAAAA,CAAAA,iBAAAA,CAAAA,iBAAAA,GAAAA,EAAA,GAAA,gBAAA;IACAA,iBAAAA,CAAAA,iBAAAA,CAAAA,gBAAAA,GAAAA,EAAA,GAAA,eAAA;AACD,CAJD,EAAYA,iBAAiB,IAAA,CAAjBA,iBAAiB,GAAA,CAAA,CAAA,CAA7B;AAMA,IAAYC,kBAAZ;AAAA,CAAA,SAAYA,kBAAAA;IACVA,kBAAAA,CAAAA,YAAA,GAAA,WAAA;AACD,CAFD,EAAYA,kBAAkB,IAAA,CAAlBA,kBAAkB,GAAA,CAAA,CAAA,CAA9B;AAYA,MAAMC,YAAY,GAAA,WAAA,GAAY,IAAIC,GAAJ,EAA9B;AAEA,SAAgBC,sBACdC,UAAAA,EAAAA,IAAAA;QACA,EAACC,QAAD,EAAWC,YAAX,EAAyBC,MAAAA;IAEzB,MAAM,CAACC,KAAD,EAAQC,QAAR,CAAA,qKAAoB9Z,WAAAA,AAAQ,EAA4B,IAA5B,CAAlC;IACA,MAAM,EAAC+Z,SAAD,EAAYjM,OAAZ,EAAqBkM,QAAAA,KAAYJ,MAAvC;IACA,MAAMK,aAAa,qKAAGrC,SAAAA,AAAM,EAAC6B,UAAD,CAA5B;IACA,MAAMlC,QAAQ,GAAG2C,UAAU,EAA3B;IACA,MAAMC,WAAW,IAAGC,6LAAAA,AAAc,EAAC7C,QAAD,CAAlC;IACA,MAAM8C,0BAA0B,qKAAGna,cAAAA,AAAW;yEAC5C,SAACoa,GAAD;gBAACA,QAAAA,KAAAA,GAAAA;gBAAAA,MAA0B,EAAA;;YACzB,IAAIH,WAAW,CAACrI,OAAhB,EAAyB;gBACvB;;YAGFgI,QAAQ;kFAAE1X,KAAD;oBACP,IAAIA,KAAK,KAAK,IAAd,EAAoB;wBAClB,OAAOkY,GAAP;;oBAGF,OAAOlY,KAAK,CAACmY,MAAN,CAAaD,GAAG,CAACxX,MAAJ;0FAAYhC,EAAD,GAAQ,CAACsB,KAAK,CAACqI,QAAN,CAAe3J,EAAf,CAApB,CAAb,CAAP;;iBALM,CAAR;;SAN0C;wEAc5C;QAACqZ,WAAD;KAd4C,CAA9C;IAgBA,MAAM5F,SAAS,qKAAGqD,SAAAA,AAAM,EAAwB,IAAxB,CAAxB;IACA,MAAM3S,cAAc,gLAAGuT,cAAW,AAAXA;8DACpBgC,aAAD;YACE,IAAIjD,QAAQ,IAAI,CAACmC,QAAjB,EAA2B;gBACzB,OAAOJ,YAAP;;YAGF,IACE,CAACkB,aAAD,IACAA,aAAa,KAAKlB,YADlB,IAEAW,aAAa,CAACnI,OAAd,KAA0B2H,UAF1B,IAGAI,KAAK,IAAI,IAJX,EAKE;gBACA,MAAMV,GAAG,GAAY,IAAII,GAAJ,EAArB;gBAEA,KAAK,IAAIlY,SAAT,IAAsBoY,UAAtB,CAAkC;oBAChC,IAAI,CAACpY,SAAL,EAAgB;wBACd;;oBAGF,IACEwY,KAAK,IACLA,KAAK,CAACjV,MAAN,GAAe,CADf,IAEA,CAACiV,KAAK,CAACpP,QAAN,CAAepJ,SAAS,CAACP,EAAzB,CAFD,IAGAO,SAAS,CAACsC,IAAV,CAAemO,OAJjB,EAKE;;wBAEAqH,GAAG,CAACsB,GAAJ,CAAQpZ,SAAS,CAACP,EAAlB,EAAsBO,SAAS,CAACsC,IAAV,CAAemO,OAArC;wBACA;;oBAGF,MAAMnI,IAAI,GAAGtI,SAAS,CAACsI,IAAV,CAAemI,OAA5B;oBACA,MAAMnO,IAAI,GAAGgG,IAAI,GAAG,IAAIuE,IAAJ,CAASJ,OAAO,CAACnE,IAAD,CAAhB,EAAwBA,IAAxB,CAAH,GAAmC,IAApD;oBAEAtI,SAAS,CAACsC,IAAV,CAAemO,OAAf,GAAyBnO,IAAzB;oBAEA,IAAIA,IAAJ,EAAU;wBACRwV,GAAG,CAACsB,GAAJ,CAAQpZ,SAAS,CAACP,EAAlB,EAAsB6C,IAAtB;;;gBAIJ,OAAOwV,GAAP;;YAGF,OAAOqB,aAAP;SA3C8B;4DA6ChC;QAACf,UAAD;QAAaI,KAAb;QAAoBH,QAApB;QAA8BnC,QAA9B;QAAwCzJ,OAAxC;KA7CgC,CAAlC;sKAgDAnO,YAAAA,AAAS;2CAAC;YACRsa,aAAa,CAACnI,OAAd,GAAwB2H,UAAxB;SADO;0CAEN;QAACA,UAAD;KAFM,CAAT;sKAIA9Z,YAAAA,AAAS;2CACP;YACE,IAAI4X,QAAJ,EAAc;gBACZ;;YAGF8C,0BAA0B;SANrB;0CASP;QAACX,QAAD;QAAWnC,QAAX;KATO,CAAT;sKAYA5X,YAAAA,AAAS;2CACP;YACE,IAAIka,KAAK,IAAIA,KAAK,CAACjV,MAAN,GAAe,CAA5B,EAA+B;gBAC7BkV,QAAQ,CAAC,IAAD,CAAR;;SAHG;0CAOP;QAAC3B,IAAI,CAACC,SAAL,CAAeyB,KAAf,CAAD;KAPO,CAAT;sKAUAla,YAAAA,AAAS;2CACP;YACE,IACE4X,QAAQ,IACR,OAAOwC,SAAP,KAAqB,QADrB,IAEAxF,SAAS,CAACzC,OAAV,KAAsB,IAHxB,EAIE;gBACA;;YAGFyC,SAAS,CAACzC,OAAV,GAAoBJ,UAAU;mDAAC;oBAC7B2I,0BAA0B;oBAC1B9F,SAAS,CAACzC,OAAV,GAAoB,IAApB;iBAF4B;kDAG3BiI,SAH2B,CAA9B;SAVK;0CAgBP;QAACA,SAAD;QAAYxC,QAAZ;QAAsB8C,0BAAtB,EAAkD;WAAGV,YAArD;KAhBO,CAAT;IAmBA,OAAO;QACL1U,cADK;QAELoV,0BAFK;QAGLK,kBAAkB,EAAEb,KAAK,IAAI;KAH/B;;IAMA,SAASK,UAAT;QACE,OAAQF,QAAR;YACE,KAAKZ,iBAAiB,CAACuB,MAAvB;gBACE,OAAO,KAAP;YACF,KAAKvB,iBAAiB,CAACwB,cAAvB;gBACE,OAAOlB,QAAP;YACF;gBACE,OAAO,CAACA,QAAR;;;AAGP;SCpKemB,gBAIdzY,KAAAA,EACA0Y,SAAAA;IAEA,oLAAOtC,cAAAA,AAAW;wCACfgC,aAAD;YACE,IAAI,CAACpY,KAAL,EAAY;gBACV,OAAO,IAAP;;YAGF,IAAIoY,aAAJ,EAAmB;gBACjB,OAAOA,aAAP;;YAGF,OAAO,OAAOM,SAAP,KAAqB,UAArB,GAAkCA,SAAS,CAAC1Y,KAAD,CAA3C,GAAqDA,KAA5D;SAVc;sCAYhB;QAAC0Y,SAAD;QAAY1Y,KAAZ;KAZgB,CAAlB;AAcD;SCtBe2Y,eACdpR,IAAAA,EACAmE,OAAAA;IAEA,OAAO+M,eAAe,CAAClR,IAAD,EAAOmE,OAAP,CAAtB;AACD;ACAD;;;IAIA,SAAgBkN,oBAAAA,IAAAA;QAAoB,EAACC,QAAD,EAAW1D,QAAAA;IAC7C,MAAM2D,eAAe,gLAAGC,WAAAA,AAAQ,EAACF,QAAD,CAAhC;IACA,MAAMG,gBAAgB,qKAAGrZ,UAAAA,AAAO;yDAAC;YAC/B,IACEwV,QAAQ,IACR,OAAOnM,MAAP,KAAkB,WADlB,IAEA,OAAOA,MAAM,CAACiQ,gBAAd,KAAmC,WAHrC,EAIE;gBACA,OAAOxK,SAAP;;YAGF,MAAM,EAACwK,gBAAAA,KAAoBjQ,MAA3B;YAEA,OAAO,IAAIiQ,gBAAJ,CAAqBH,eAArB,CAAP;SAX8B;wDAY7B;QAACA,eAAD;QAAkB3D,QAAlB;KAZ6B,CAAhC;QAcA5X,0KAAAA,AAAS;yCAAC;YACR;iDAAO,IAAMyb,gBAAN,IAAA,OAAA,KAAA,IAAMA,gBAAgB,CAAEE,UAAlB,EAAb;;SADO;wCAEN;QAACF,gBAAD;KAFM,CAAT;IAIA,OAAOA,gBAAP;AACD;ACzBD;;;IAIA,SAAgBG,kBAAAA,IAAAA;QAAkB,EAACN,QAAD,EAAW1D,QAAAA;IAC3C,MAAMiE,YAAY,gLAAGL,WAAAA,AAAQ,EAACF,QAAD,CAA7B;IACA,MAAMQ,cAAc,qKAAG1Z,UAAAA,AAAO;qDAC5B;YACE,IACEwV,QAAQ,IACR,OAAOnM,MAAP,KAAkB,WADlB,IAEA,OAAOA,MAAM,CAACsQ,cAAd,KAAiC,WAHnC,EAIE;gBACA,OAAO7K,SAAP;;YAGF,MAAM,EAAC6K,cAAAA,KAAkBtQ,MAAzB;YAEA,OAAO,IAAIsQ,cAAJ,CAAmBF,YAAnB,CAAP;SAZ0B;oDAe5B;QAACjE,QAAD;KAf4B,CAA9B;qKAkBA5X,aAAAA,AAAS;uCAAC;YACR;+CAAO,IAAM8b,cAAN,IAAA,OAAA,KAAA,IAAMA,cAAc,CAAEH,UAAhB,EAAb;;SADO;sCAEN;QAACG,cAAD;KAFM,CAAT;IAIA,OAAOA,cAAP;AACD;AC5BD,SAASE,cAAT,CAAwBzS,OAAxB;IACE,OAAO,IAAIgF,IAAJ,CAASjF,aAAa,CAACC,OAAD,CAAtB,EAAiCA,OAAjC,CAAP;AACD;AAED,SAAgB0S,QACd1S,OAAAA,EACA4E,OAAAA,EACA+N,YAAAA;QADA/N,YAAAA,KAAAA,GAAAA;QAAAA,UAAgD6N;;IAGhD,MAAM,CAAChY,IAAD,EAAOmY,OAAP,CAAA,qKAAkB9b,WAAAA,AAAQ,EAAoB,IAApB,CAAhC;IAEA,SAAS+b,WAAT;QACED,OAAO,EAAEE,WAAD;YACN,IAAI,CAAC9S,OAAL,EAAc;gBACZ,OAAO,IAAP;;YAGF,IAAIA,OAAO,CAAC+S,WAAR,KAAwB,KAA5B,EAAmC;gBAAA,IAAA;;;gBAGjC,OAAA,CAAA,OAAOD,WAAP,IAAA,OAAOA,WAAP,GAAsBH,YAAtB,KAAA,OAAA,OAAsC,IAAtC;;YAGF,MAAMK,OAAO,GAAGpO,OAAO,CAAC5E,OAAD,CAAvB;YAEA,IAAIiP,IAAI,CAACC,SAAL,CAAe4D,WAAf,MAAgC7D,IAAI,CAACC,SAAL,CAAe8D,OAAf,CAApC,EAA6D;gBAC3D,OAAOF,WAAP;;YAGF,OAAOE,OAAP;SAjBK,CAAP;;IAqBF,MAAMd,gBAAgB,GAAGJ,mBAAmB,CAAC;QAC3CC,QAAQ,EAACkB,OAAD;YACN,IAAI,CAACjT,OAAL,EAAc;gBACZ;;YAGF,KAAK,MAAMkT,MAAX,IAAqBD,OAArB,CAA8B;gBAC5B,MAAM,EAAC7b,IAAD,EAAOgG,MAAAA,KAAU8V,MAAvB;gBAEA,IACE9b,IAAI,KAAK,WAAT,IACAgG,MAAM,YAAY+V,WADlB,IAEA/V,MAAM,CAACgW,QAAP,CAAgBpT,OAAhB,CAHF,EAIE;oBACA6S,WAAW;oBACX;;;;KAfoC,CAA5C;IAoBA,MAAMN,cAAc,GAAGF,iBAAiB,CAAC;QAACN,QAAQ,EAAEc;KAAZ,CAAxC;iLAEAQ,4BAAAA,AAAyB;6CAAC;YACxBR,WAAW;YAEX,IAAI7S,OAAJ,EAAa;gBACXuS,cAAc,IAAA,IAAd,GAAA,KAAA,IAAAA,cAAc,CAAEe,OAAhB,CAAwBtT,OAAxB;gBACAkS,gBAAgB,IAAA,IAAhB,GAAA,KAAA,IAAAA,gBAAgB,CAAEoB,OAAlB,CAA0B1Q,QAAQ,CAAC2Q,IAAnC,EAAyC;oBACvCC,SAAS,EAAE,IAD4B;oBAEvCC,OAAO,EAAE;iBAFX;aAFF,MAMO;gBACLlB,cAAc,IAAA,IAAd,GAAA,KAAA,IAAAA,cAAc,CAAEH,UAAhB;gBACAF,gBAAgB,IAAA,IAAhB,GAAA,KAAA,IAAAA,gBAAgB,CAAEE,UAAlB;;SAXqB;4CAatB;QAACpS,OAAD;KAbsB,CAAzB;IAeA,OAAOvF,IAAP;AACD;SC3EeiZ,aAAajZ,IAAAA;IAC3B,MAAMkZ,WAAW,GAAGhC,eAAe,CAAClX,IAAD,CAAnC;IAEA,OAAO+D,YAAY,CAAC/D,IAAD,EAAOkZ,WAAP,CAAnB;AACD;ACJD,MAAMvD,cAAY,GAAc,EAAhC;AAEA,SAAgBwD,uBAAuBnT,IAAAA;IACrC,MAAMoT,YAAY,qKAAGnF,SAAAA,AAAM,EAACjO,IAAD,CAA3B;IAEA,MAAMqT,SAAS,gLAAGxE,cAAAA,AAAW;0DAC1BgC,aAAD;YACE,IAAI,CAAC7Q,IAAL,EAAW;gBACT,OAAO2P,cAAP;;YAGF,IACEkB,aAAa,IACbA,aAAa,KAAKlB,cADlB,IAEA3P,IAFA,IAGAoT,YAAY,CAACjL,OAHb,IAIAnI,IAAI,CAACiB,UAAL,KAAoBmS,YAAY,CAACjL,OAAb,CAAqBlH,UAL3C,EAME;gBACA,OAAO4P,aAAP;;YAGF,OAAOrQ,sBAAsB,CAACR,IAAD,CAA7B;SAhByB;wDAkB3B;QAACA,IAAD;KAlB2B,CAA7B;IAqBAhK,8KAAAA,AAAS;4CAAC;YACRod,YAAY,CAACjL,OAAb,GAAuBnI,IAAvB;SADO;2CAEN;QAACA,IAAD;KAFM,CAAT;IAIA,OAAOqT,SAAP;AACD;SCvBeC,iBAAiBC,QAAAA;IAC/B,MAAM,CACJC,iBADI,EAEJC,oBAFI,CAAA,GAGFpd,6KAAAA,AAAQ,EAA2B,IAA3B,CAHZ;IAIA,MAAMqd,YAAY,GAAGzF,2KAAAA,AAAM,EAACsF,QAAD,CAA3B,EAAA,4CAAA;IAGA,MAAMI,YAAY,IAAGpd,+KAAAA,AAAW;uDAAEK,KAAD;YAC/B,MAAMiK,gBAAgB,GAAGO,oBAAoB,CAACxK,KAAK,CAAC+F,MAAP,CAA7C;YAEA,IAAI,CAACkE,gBAAL,EAAuB;gBACrB;;YAGF4S,oBAAoB;+DAAED,iBAAD;oBACnB,IAAI,CAACA,iBAAL,EAAwB;wBACtB,OAAO,IAAP;;oBAGFA,iBAAiB,CAAC1C,GAAlB,CACEjQ,gBADF,EAEEmB,oBAAoB,CAACnB,gBAAD,CAFtB;oBAKA,OAAO,IAAI+O,GAAJ,CAAQ4D,iBAAR,CAAP;iBAVkB,CAApB;;SAP8B;qDAmB7B,EAnB6B,CAAhC;IAqBAxd,8KAAAA,AAAS;sCAAC;YACR,MAAM4d,gBAAgB,GAAGF,YAAY,CAACvL,OAAtC;YAEA,IAAIoL,QAAQ,KAAKK,gBAAjB,EAAmC;gBACjCC,OAAO,CAACD,gBAAD,CAAP;gBAEA,MAAME,OAAO,GAAGP,QAAQ,CACrB/D,GADa;2DACRjQ,OAAD;wBACH,MAAMwU,iBAAiB,GAAG3S,oBAAoB,CAAC7B,OAAD,CAA9C;wBAEA,IAAIwU,iBAAJ,EAAuB;4BACrBA,iBAAiB,CAACxO,gBAAlB,CAAmC,QAAnC,EAA6CoO,YAA7C,EAA2D;gCACzDvI,OAAO,EAAE;6BADX;4BAIA,OAAO;gCACL2I,iBADK;gCAEL/R,oBAAoB,CAAC+R,iBAAD,CAFf;6BAAP;;wBAMF,OAAO,IAAP;qBAfY;yDAiBb5a,MAjBa;0DAmBVuD,KADF,IAKKA,KAAK,IAAI,IAvBF,CAAhB;;gBA0BA+W,oBAAoB,CAACK,OAAO,CAAC7Y,MAAR,GAAiB,IAAI2U,GAAJ,CAAQkE,OAAR,CAAjB,GAAoC,IAArC,CAApB;gBAEAJ,YAAY,CAACvL,OAAb,GAAuBoL,QAAvB;;YAGF;8CAAO;oBACLM,OAAO,CAACN,QAAD,CAAP;oBACAM,OAAO,CAACD,gBAAD,CAAP;iBAFF;;;YAKA,SAASC,OAAT,CAAiBN,QAAjB;gBACEA,QAAQ,CAAC1c,OAAT;2DAAkB0I,OAAD;wBACf,MAAMwU,iBAAiB,GAAG3S,oBAAoB,CAAC7B,OAAD,CAA9C;wBAEAwU,iBAAiB,IAAA,IAAjB,GAAA,KAAA,IAAAA,iBAAiB,CAAE3O,mBAAnB,CAAuC,QAAvC,EAAiDuO,YAAjD;qBAHF;;;SA3CK;qCAiDN;QAACA,YAAD;QAAeJ,QAAf;KAjDM,CAAT;IAmDA,yKAAOnb,UAAAA,AAAO;oCAAC;YACb,IAAImb,QAAQ,CAACtY,MAAb,EAAqB;gBACnB,OAAOuY,iBAAiB,GACpBQ,KAAK,CAACC,IAAN,CAAWT,iBAAiB,CAACU,MAAlB,EAAX,EAAuChY,MAAvC;gDACE,CAACkC,GAAD,EAAMqL,WAAN,OAAsBjT,+KAAG,AAAHA,EAAI4H,GAAD,EAAMqL,WAAN,CAD3B;+CAEErQ,kBAFF,CADoB,GAKpB0K,gBAAgB,CAACyP,QAAD,CALpB;;YAQF,OAAOna,kBAAP;SAVY;mCAWX;QAACma,QAAD;QAAWC,iBAAX;KAXW,CAAd;AAYD;SCpGeW,sBACd1P,aAAAA,EACAuL,YAAAA;QAAAA,iBAAAA,KAAAA,GAAAA;QAAAA,eAAsB,EAAA;;IAEtB,MAAMoE,oBAAoB,qKAAGnG,SAAAA,AAAM,EAAqB,IAArB,CAAnC;qKAEAjY,aAAAA,AAAS;2CACP;YACEoe,oBAAoB,CAACjM,OAArB,GAA+B,IAA/B;SAFK;0CAKP6H,YALO,CAAT;sKAQAha,YAAS,AAATA;2CAAU;YACR,MAAMqe,gBAAgB,GAAG5P,aAAa,KAAKrL,kBAA3C;YAEA,IAAIib,gBAAgB,IAAI,CAACD,oBAAoB,CAACjM,OAA9C,EAAuD;gBACrDiM,oBAAoB,CAACjM,OAArB,GAA+B1D,aAA/B;;YAGF,IAAI,CAAC4P,gBAAD,IAAqBD,oBAAoB,CAACjM,OAA9C,EAAuD;gBACrDiM,oBAAoB,CAACjM,OAArB,GAA+B,IAA/B;;SARK;0CAUN;QAAC1D,aAAD;KAVM,CAAT;IAYA,OAAO2P,oBAAoB,CAACjM,OAArB,GACHmM,wLAAAA,AAAQ,EAAC7P,aAAD,EAAgB2P,oBAAoB,CAACjM,OAArC,CADL,GAEH/O,kBAFJ;AAGD;SC7Bemb,eAAerb,OAAAA;sKAC7BlD,YAAAA,AAAS;oCACP;YACE,IAAI,0KAACqL,YAAL,EAAgB;gBACd;;YAGF,MAAMmT,WAAW,GAAGtb,OAAO,CAACsW,GAAR;wDAAY;oBAAA,IAAC,EAACzW,MAAAA,EAAF,GAAA;oBAAA,OAAcA,MAAM,CAAC6T,KAArB,IAAA,OAAA,KAAA,IAAc7T,MAAM,CAAC6T,KAAP,EAAd;iBAAZ,CAApB;;YAEA;4CAAO;oBACL,KAAK,MAAMC,QAAX,IAAuB2H,WAAvB,CAAoC;wBAClC3H,QAAQ,IAAA,IAAR,GAAA,KAAA,IAAAA,QAAQ;;iBAFZ;;SARK;;IAgBP3T,OAAO,CAACsW,GAAR;qCAAY;YAAA,IAAC,EAACzW,MAAAA,EAAF,GAAA;YAAA,OAAcA,MAAd;SAAZ,CAhBO,CAAT;;AAkBD;SCXe0b,sBACdre,SAAAA,EACAe,EAAAA;IAEA,yKAAOiB,UAAO,AAAPA;yCAAQ;YACb,OAAOhC,SAAS,CAAC8F,MAAV;iDACL,CAACkC,GAAD,EAAA;wBAAM,EAACiH,SAAD,EAAYC,OAAAA;oBAChBlH,GAAG,CAACiH,SAAD,CAAH;0DAAkBzO,KAAD;4BACf0O,OAAO,CAAC1O,KAAD,EAAQO,EAAR,CAAP;yBADF;;oBAIA,OAAOiH,GAAP;iBANG;gDAQL,CAAA,CARK,CAAP;SADY;wCAWX;QAAChI,SAAD;QAAYe,EAAZ;KAXW,CAAd;AAYD;SCzBeud,cAAcnV,OAAAA;IAC5B,yKAAOnH,UAAAA,AAAO;iCAAC,IAAOmH,OAAO,GAAGK,mBAAmB,CAACL,OAAD,CAAtB,GAAkC,IAAjD;gCAAwD;QACpEA,OADoE;KAAxD,CAAd;AAGD;ACED,MAAMoQ,cAAY,GAAW,EAA7B;AAEA,SAAgBgF,SACdpB,QAAAA,EACApP,OAAAA;QAAAA,YAAAA,KAAAA,GAAAA;QAAAA,UAA4C7E;;IAE5C,MAAM,CAACsV,YAAD,CAAA,GAAiBrB,QAAvB;IACA,MAAMsB,UAAU,GAAGH,aAAa,CAC9BE,YAAY,gLAAGnV,YAAAA,AAAS,EAACmV,YAAD,CAAZ,GAA6B,IADX,CAAhC;IAGA,MAAM,CAACE,KAAD,EAAQC,QAAR,CAAA,GAAoB1e,6KAAAA,AAAQ,EAAesZ,cAAf,CAAlC;IAEA,SAASqF,YAAT;QACED,QAAQ,CAAC;YACP,IAAI,CAACxB,QAAQ,CAACtY,MAAd,EAAsB;gBACpB,OAAO0U,cAAP;;YAGF,OAAO4D,QAAQ,CAAC/D,GAAT,EAAcjQ,OAAD,GAClB2C,0BAA0B,CAAC3C,OAAD,CAA1B,GACKsV,UADL,GAEI,IAAItQ,IAAJ,CAASJ,OAAO,CAAC5E,OAAD,CAAhB,EAA2BA,OAA3B,CAHC,CAAP;SALM,CAAR;;IAaF,MAAMuS,cAAc,GAAGF,iBAAiB,CAAC;QAACN,QAAQ,EAAE0D;KAAZ,CAAxC;iLAEApC,4BAAAA,AAAyB;8CAAC;YACxBd,cAAc,IAAA,IAAd,GAAA,KAAA,IAAAA,cAAc,CAAEH,UAAhB;YACAqD,YAAY;YACZzB,QAAQ,CAAC1c,OAAT;uDAAkB0I,OAAD,GAAauS,cAAb,IAAA,OAAA,KAAA,IAAaA,cAAc,CAAEe,OAAhB,CAAwBtT,OAAxB,CAA9B;;SAHuB;6CAItB;QAACgU,QAAD;KAJsB,CAAzB;IAMA,OAAOuB,KAAP;AACD;SC3CeG,kBACdjV,IAAAA;IAEA,IAAI,CAACA,IAAL,EAAW;QACT,OAAO,IAAP;;IAGF,IAAIA,IAAI,CAACkV,QAAL,CAAcja,MAAd,GAAuB,CAA3B,EAA8B;QAC5B,OAAO+E,IAAP;;IAEF,MAAMmV,UAAU,GAAGnV,IAAI,CAACkV,QAAL,CAAc,CAAd,CAAnB;IAEA,oLAAOnU,gBAAa,AAAbA,EAAcoU,UAAD,CAAb,GAA4BA,UAA5B,GAAyCnV,IAAhD;AACD;SCHeoV,wBAAAA,IAAAA;QAAwB,EACtCjR,OAAAA;IAEA,MAAM,CAACnK,IAAD,EAAOmY,OAAP,CAAA,qKAAkB9b,WAAAA,AAAQ,EAAoB,IAApB,CAAhC;IACA,MAAMwb,YAAY,qKAAGtb,cAAAA,AAAW;8DAC7Bud,OAAD;YACE,KAAK,MAAM,EAACnX,MAAAA,EAAZ,IAAuBmX,OAAvB,CAAgC;gBAC9B,IAAI/S,6LAAa,AAAbA,EAAcpE,MAAD,CAAjB,EAA2B;oBACzBwV,OAAO;8EAAEnY,IAAD;4BACN,MAAMuY,OAAO,GAAGpO,OAAO,CAACxH,MAAD,CAAvB;4BAEA,OAAO3C,IAAI,GACP;gCAAC,GAAGA,IAAJ;gCAAUK,KAAK,EAAEkY,OAAO,CAAClY,KAAzB;gCAAgCE,MAAM,EAAEgY,OAAO,CAAChY,MAAAA;6BADzC,GAEPgY,OAFJ;yBAHK,CAAP;;oBAOA;;;SAXwB;4DAe9B;QAACpO,OAAD;KAf8B,CAAhC;IAiBA,MAAM2N,cAAc,GAAGF,iBAAiB,CAAC;QAACN,QAAQ,EAAEO;KAAZ,CAAxC;IACA,MAAMwD,gBAAgB,oKAAG9e,eAAAA,AAAW;kEACjCgJ,OAAD;YACE,MAAMS,IAAI,GAAGiV,iBAAiB,CAAC1V,OAAD,CAA9B;YAEAuS,cAAc,IAAA,IAAd,GAAA,KAAA,IAAAA,cAAc,CAAEH,UAAhB;YAEA,IAAI3R,IAAJ,EAAU;gBACR8R,cAAc,IAAA,IAAd,GAAA,KAAA,IAAAA,cAAc,CAAEe,OAAhB,CAAwB7S,IAAxB;;YAGFmS,OAAO,CAACnS,IAAI,GAAGmE,OAAO,CAACnE,IAAD,CAAV,GAAmB,IAAxB,CAAP;SAVgC;gEAYlC;QAACmE,OAAD;QAAU2N,cAAV;KAZkC,CAApC;IAcA,MAAM,CAACwD,OAAD,EAAUC,MAAV,CAAA,gLAAoBC,aAAAA,AAAU,EAACH,gBAAD,CAApC;IAEA,yKAAOjd,UAAO,AAAPA;2CACL,IAAA,CAAO;gBACLkd,OADK;gBAELtb,IAFK;gBAGLub;aAHF,CADY;0CAMZ;QAACvb,IAAD;QAAOsb,OAAP;QAAgBC,MAAhB;KANY,CAAd;AAQD;AC9CM,MAAME,cAAc,GAAG;IAC5B;QAAC1c,MAAM,EAAEsT,aAAT;QAAwBrT,OAAO,EAAE,CAAA;IAAjC,CAD4B;IAE5B;QAACD,MAAM,EAAEoO,cAAT;QAAyBnO,OAAO,EAAE,CAAA;IAAlC,CAF4B;CAAvB;AAKA,MAAM0c,WAAW,GAAY;IAACvN,OAAO,EAAE,CAAA;AAAV,CAA7B;AAEA,MAAMwN,6BAA6B,GAAyC;IACjF5e,SAAS,EAAE;QACToN,OAAO,EAAExE;KAFsE;IAIjFiW,SAAS,EAAE;QACTzR,OAAO,EAAExE,8BADA;QAET0Q,QAAQ,EAAEZ,iBAAiB,CAACoG,aAFnB;QAGTzF,SAAS,EAAEV,kBAAkB,CAACoG,SAAAA;KAPiD;IASjFC,WAAW,EAAE;QACX5R,OAAO,EAAE7E;;AAVsE,CAA5E;MCdM0W,+BAA+BpG;IAI1ClU,GAAG,CAACvE,EAAD,EAAA;;QACD,OAAOA,EAAE,IAAI,IAAN,GAAA,CAAA,aAAa,KAAA,CAAMuE,GAAN,CAAUvE,EAAV,CAAb,KAAA,OAAA,aAA8B+P,SAA9B,GAA0CA,SAAjD;;IAGF+O,OAAO,GAAA;QACL,OAAOjC,KAAK,CAACC,IAAN,CAAW,IAAA,CAAKC,MAAL,EAAX,CAAP;;IAGFgC,UAAU,GAAA;QACR,OAAO,IAAA,CAAKD,OAAL,GAAe9c,MAAf,EAAsB;YAAA,IAAC,EAACyU,QAAAA,EAAF,GAAA;YAAA,OAAgB,CAACA,QAAjB;SAAtB,CAAP;;IAGFuI,UAAU,CAAChf,EAAD,EAAA;;QACR,OAAA,CAAA,wBAAA,CAAA,YAAO,IAAA,CAAKuE,GAAL,CAASvE,EAAT,CAAP,KAAA,OAAA,KAAA,IAAO,UAAc6I,IAAd,CAAmBmI,OAA1B,KAAA,OAAA,wBAAqCjB,SAArC;;;ACfG,MAAMkP,oBAAoB,GAA4B;IAC3DC,cAAc,EAAE,IAD2C;IAE3Dnf,MAAM,EAAE,IAFmD;IAG3D+Q,UAAU,EAAE,IAH+C;IAI3DqO,cAAc,EAAE,IAJ2C;IAK3Dvb,UAAU,EAAE,IAL+C;IAM3Dwb,iBAAiB,EAAE,IANwC;IAO3DtH,cAAc,EAAA,WAAA,GAAE,IAAIW,GAAJ,EAP2C;IAQ3DtU,cAAc,EAAA,WAAA,GAAE,IAAIsU,GAAJ,EAR2C;IAS3DrU,mBAAmB,EAAA,WAAA,GAAE,IAAIya,sBAAJ,EATsC;IAU3D3e,IAAI,EAAE,IAVqD;IAW3D0e,WAAW,EAAE;QACXT,OAAO,EAAE;YACPnN,OAAO,EAAE;SAFA;QAIXnO,IAAI,EAAE,IAJK;QAKXub,MAAM,EAAE1c;KAhBiD;IAkB3DkL,mBAAmB,EAAE,EAlBsC;IAmB3D0J,uBAAuB,EAAE,EAnBkC;IAoB3D+I,sBAAsB,EAAEb,6BApBmC;IAqB3DjF,0BAA0B,EAAE7X,IArB+B;IAsB3Dgc,UAAU,EAAE,IAtB+C;IAuB3D9D,kBAAkB,EAAE;AAvBuC,CAAtD;AA0BA,MAAM0F,sBAAsB,GAA8B;IAC/DJ,cAAc,EAAE,IAD+C;IAE/DvM,UAAU,EAAE,EAFmD;IAG/D5S,MAAM,EAAE,IAHuD;IAI/Dof,cAAc,EAAE,IAJ+C;IAK/DI,iBAAiB,EAAE;QACjB3f,SAAS,EAAE;KANkD;IAQ/DL,QAAQ,EAAEmC,IARqD;IAS/DoW,cAAc,EAAA,WAAA,GAAE,IAAIW,GAAJ,EAT+C;IAU/DvY,IAAI,EAAE,IAVyD;IAW/DqZ,0BAA0B,EAAE7X;AAXmC,CAA1D;AAcA,MAAM8d,eAAe,GAAA,WAAA,GAAGhhB,kLAAAA,AAAa,EAC1C8gB,sBAD0C,CAArC;AAIA,MAAMG,aAAa,GAAA,WAAA,qKAAGjhB,gBAAAA,AAAa,EACxCygB,oBADwC,CAAnC;SC/CSS;IACd,OAAO;QACL9f,SAAS,EAAE;YACTG,MAAM,EAAE,IADC;YAETyT,kBAAkB,EAAE;gBAACpR,CAAC,EAAE,CAAJ;gBAAOC,CAAC,EAAE;aAFrB;YAGTsd,KAAK,EAAE,IAAIlH,GAAJ,EAHE;YAITmH,SAAS,EAAE;gBAACxd,CAAC,EAAE,CAAJ;gBAAOC,CAAC,EAAE;;SALlB;QAOLoc,SAAS,EAAE;YACT9F,UAAU,EAAE,IAAIkG,sBAAJ;;KARhB;AAWD;AAED,SAAgBgB,QAAQC,KAAAA,EAAcC,MAAAA;IACpC,OAAQA,MAAM,CAACvgB,IAAf;QACE,KAAKiC,MAAM,CAACyS,SAAZ;YACE,OAAO;gBACL,GAAG4L,KADE;gBAELlgB,SAAS,EAAE;oBACT,GAAGkgB,KAAK,CAAClgB,SADA;oBAET4T,kBAAkB,EAAEuM,MAAM,CAACvM,kBAFlB;oBAGTzT,MAAM,EAAEggB,MAAM,CAAChgB,MAAAA;;aALnB;QAQF,KAAK0B,MAAM,CAACue,QAAZ;YACE,IAAIF,KAAK,CAAClgB,SAAN,CAAgBG,MAAhB,IAA0B,IAA9B,EAAoC;gBAClC,OAAO+f,KAAP;;YAGF,OAAO;gBACL,GAAGA,KADE;gBAELlgB,SAAS,EAAE;oBACT,GAAGkgB,KAAK,CAAClgB,SADA;oBAETggB,SAAS,EAAE;wBACTxd,CAAC,EAAE2d,MAAM,CAACzN,WAAP,CAAmBlQ,CAAnB,GAAuB0d,KAAK,CAAClgB,SAAN,CAAgB4T,kBAAhB,CAAmCpR,CADpD;wBAETC,CAAC,EAAE0d,MAAM,CAACzN,WAAP,CAAmBjQ,CAAnB,GAAuByd,KAAK,CAAClgB,SAAN,CAAgB4T,kBAAhB,CAAmCnR,CAAAA;;;aANnE;QAUF,KAAKZ,MAAM,CAACwe,OAAZ;QACA,KAAKxe,MAAM,CAACye,UAAZ;YACE,OAAO;gBACL,GAAGJ,KADE;gBAELlgB,SAAS,EAAE;oBACT,GAAGkgB,KAAK,CAAClgB,SADA;oBAETG,MAAM,EAAE,IAFC;oBAGTyT,kBAAkB,EAAE;wBAACpR,CAAC,EAAE,CAAJ;wBAAOC,CAAC,EAAE;qBAHrB;oBAITud,SAAS,EAAE;wBAACxd,CAAC,EAAE,CAAJ;wBAAOC,CAAC,EAAE;;;aANzB;QAUF,KAAKZ,MAAM,CAAC0e,iBAAZ;YAA+B;gBAC7B,MAAM,EAAC/X,OAAAA,KAAW2X,MAAlB;gBACA,MAAM,EAAC/f,EAAAA,KAAMoI,OAAb;gBACA,MAAMuQ,UAAU,GAAG,IAAIkG,sBAAJ,CAA2BiB,KAAK,CAACrB,SAAN,CAAgB9F,UAA3C,CAAnB;gBACAA,UAAU,CAACgB,GAAX,CAAe3Z,EAAf,EAAmBoI,OAAnB;gBAEA,OAAO;oBACL,GAAG0X,KADE;oBAELrB,SAAS,EAAE;wBACT,GAAGqB,KAAK,CAACrB,SADA;wBAET9F;;iBAJJ;;QASF,KAAKlX,MAAM,CAAC2e,oBAAZ;YAAkC;gBAChC,MAAM,EAACpgB,EAAD,EAAK0N,GAAL,EAAU+I,QAAAA,KAAYsJ,MAA5B;gBACA,MAAM3X,OAAO,GAAG0X,KAAK,CAACrB,SAAN,CAAgB9F,UAAhB,CAA2BpU,GAA3B,CAA+BvE,EAA/B,CAAhB;gBAEA,IAAI,CAACoI,OAAD,IAAYsF,GAAG,KAAKtF,OAAO,CAACsF,GAAhC,EAAqC;oBACnC,OAAOoS,KAAP;;gBAGF,MAAMnH,UAAU,GAAG,IAAIkG,sBAAJ,CAA2BiB,KAAK,CAACrB,SAAN,CAAgB9F,UAA3C,CAAnB;gBACAA,UAAU,CAACgB,GAAX,CAAe3Z,EAAf,EAAmB;oBACjB,GAAGoI,OADc;oBAEjBqO;iBAFF;gBAKA,OAAO;oBACL,GAAGqJ,KADE;oBAELrB,SAAS,EAAE;wBACT,GAAGqB,KAAK,CAACrB,SADA;wBAET9F;;iBAJJ;;QASF,KAAKlX,MAAM,CAAC4e,mBAAZ;YAAiC;gBAC/B,MAAM,EAACrgB,EAAD,EAAK0N,GAAAA,KAAOqS,MAAlB;gBACA,MAAM3X,OAAO,GAAG0X,KAAK,CAACrB,SAAN,CAAgB9F,UAAhB,CAA2BpU,GAA3B,CAA+BvE,EAA/B,CAAhB;gBAEA,IAAI,CAACoI,OAAD,IAAYsF,GAAG,KAAKtF,OAAO,CAACsF,GAAhC,EAAqC;oBACnC,OAAOoS,KAAP;;gBAGF,MAAMnH,UAAU,GAAG,IAAIkG,sBAAJ,CAA2BiB,KAAK,CAACrB,SAAN,CAAgB9F,UAA3C,CAAnB;gBACAA,UAAU,CAACrZ,MAAX,CAAkBU,EAAlB;gBAEA,OAAO;oBACL,GAAG8f,KADE;oBAELrB,SAAS,EAAE;wBACT,GAAGqB,KAAK,CAACrB,SADA;wBAET9F;;iBAJJ;;QASF;YAAS;gBACP,OAAOmH,KAAP;;;AAGL;SCzGeQ,aAAAA,IAAAA;QAAa,EAAC7J,QAAAA;IAC5B,MAAM,EAAC1W,MAAD,EAASmf,cAAT,EAAyBpH,cAAAA,uKAAkBlZ,aAAAA,AAAU,EAAC4gB,eAAD,CAA3D;IACA,MAAMe,sBAAsB,gLAAG9I,cAAAA,AAAW,EAACyH,cAAD,CAA1C;IACA,MAAMsB,gBAAgB,IAAG/I,0LAAAA,AAAW,EAAC1X,MAAD,IAAA,OAAA,KAAA,IAACA,MAAM,CAAEC,EAAT,CAApC,EAAA,+CAAA;qKAGAnB,aAAS,AAATA;kCAAU;YACR,IAAI4X,QAAJ,EAAc;gBACZ;;YAGF,IAAI,CAACyI,cAAD,IAAmBqB,sBAAnB,IAA6CC,gBAAgB,IAAI,IAArE,EAA2E;gBACzE,IAAI,8KAACvP,kBAAAA,AAAe,EAACsP,sBAAD,CAApB,EAA8C;oBAC5C;;gBAGF,IAAIvV,QAAQ,CAACyV,aAAT,KAA2BF,sBAAsB,CAAC/a,MAAtD,EAA8D;;oBAE5D;;gBAGF,MAAMuS,aAAa,GAAGD,cAAc,CAACvT,GAAf,CAAmBic,gBAAnB,CAAtB;gBAEA,IAAI,CAACzI,aAAL,EAAoB;oBAClB;;gBAGF,MAAM,EAAChF,aAAD,EAAgBlK,IAAAA,KAAQkP,aAA9B;gBAEA,IAAI,CAAChF,aAAa,CAAC/B,OAAf,IAA0B,CAACnI,IAAI,CAACmI,OAApC,EAA6C;oBAC3C;;gBAGF0P,qBAAqB;8CAAC;wBACpB,KAAK,MAAMtY,OAAX,IAAsB;4BAAC2K,aAAa,CAAC/B,OAAf;4BAAwBnI,IAAI,CAACmI,OAA7B;yBAAtB,CAA6D;4BAC3D,IAAI,CAAC5I,OAAL,EAAc;gCACZ;;4BAGF,MAAMuY,aAAa,gLAAGC,yBAAAA,AAAsB,EAACxY,OAAD,CAA5C;4BAEA,IAAIuY,aAAJ,EAAmB;gCACjBA,aAAa,CAACE,KAAd;gCACA;;;qBAVe,CAArB;;;SA3BK;iCA0CN;QACD3B,cADC;QAEDzI,QAFC;QAGDqB,cAHC;QAID0I,gBAJC;QAKDD,sBALC;KA1CM,CAAT;IAkDA,OAAO,IAAP;AACD;SClEeO,eACdC,SAAAA,EAAAA,IAAAA;QACA,EAACxa,SAAD,EAAY,GAAGya;IAEf,OAAOD,SAAS,IAAA,IAAT,IAAAA,SAAS,CAAEjd,MAAX,GACHid,SAAS,CAAChc,MAAV,CAA4B,CAACC,WAAD,EAAc8B,QAAd;QAC1B,OAAOA,QAAQ,CAAC;YACdP,SAAS,EAAEvB,WADG;YAEd,GAAGgc,IAAAA;SAFU,CAAf;KADF,EAKGza,SALH,CADG,GAOHA,SAPJ;AAQD;SCVe0a,0BACdnI,MAAAA;IAEA,OAAO7X,4KAAAA,AAAO;6CACZ,IAAA,CAAO;gBACLrB,SAAS,EAAE;oBACT,GAAG4e,6BAA6B,CAAC5e,SADxB;oBAET,GAAGkZ,MAAH,IAAA,OAAA,KAAA,IAAGA,MAAM,CAAElZ,SAAX;iBAHG;gBAKL6e,SAAS,EAAE;oBACT,GAAGD,6BAA6B,CAACC,SADxB;oBAET,GAAG3F,MAAH,IAAA,OAAA,KAAA,IAAGA,MAAM,CAAE2F,SAAX;iBAPG;gBASLG,WAAW,EAAE;oBACX,GAAGJ,6BAA6B,CAACI,WADtB;oBAEX,GAAG9F,MAAH,IAAA,OAAA,KAAA,IAAGA,MAAM,CAAE8F,WAAX;;aAXJ,CADY;4CAgBZ;QAAC9F,MAAD,IAAA,OAAA,KAAA,IAACA,MAAM,CAAElZ,SAAT;QAAoBkZ,MAApB,IAAA,OAAA,KAAA,IAAoBA,MAAM,CAAE2F,SAA5B;QAAuC3F,MAAvC,IAAA,OAAA,KAAA,IAAuCA,MAAM,CAAE8F,WAA/C;KAhBY,CAAd;AAkBD;SCXesC,iCAAAA,IAAAA;QAAiC,EAC/CpQ,UAD+C,EAE/C9D,OAF+C,EAG/C+O,WAH+C,EAI/CjD,MAAM,GAAG,IAAA;IAET,MAAMqI,WAAW,OAAGrK,uKAAAA,AAAM,EAAC,KAAD,CAA1B;IACA,MAAM,EAAC1U,CAAD,EAAIC,CAAAA,KAAK,OAAOyW,MAAP,KAAkB,SAAlB,GAA8B;QAAC1W,CAAC,EAAE0W,MAAJ;QAAYzW,CAAC,EAAEyW;KAA7C,GAAuDA,MAAtE;iLAEA2C,4BAAAA,AAAyB;sEAAC;YACxB,MAAMhF,QAAQ,GAAG,CAACrU,CAAD,IAAM,CAACC,CAAxB;YAEA,IAAIoU,QAAQ,IAAI,CAAC3F,UAAjB,EAA6B;gBAC3BqQ,WAAW,CAACnQ,OAAZ,GAAsB,KAAtB;gBACA;;YAGF,IAAImQ,WAAW,CAACnQ,OAAZ,IAAuB,CAAC+K,WAA5B,EAAyC;;;gBAGvC;;YAIF,MAAMlT,IAAI,GAAGiI,UAAH,IAAA,OAAA,KAAA,IAAGA,UAAU,CAAEjI,IAAZ,CAAiBmI,OAA9B;YAEA,IAAI,CAACnI,IAAD,IAASA,IAAI,CAACsS,WAAL,KAAqB,KAAlC,EAAyC;;;gBAGvC;;YAGF,MAAMtY,IAAI,GAAGmK,OAAO,CAACnE,IAAD,CAApB;YACA,MAAMuY,SAAS,GAAGxa,YAAY,CAAC/D,IAAD,EAAOkZ,WAAP,CAA9B;YAEA,IAAI,CAAC3Z,CAAL,EAAQ;gBACNgf,SAAS,CAAChf,CAAV,GAAc,CAAd;;YAGF,IAAI,CAACC,CAAL,EAAQ;gBACN+e,SAAS,CAAC/e,CAAV,GAAc,CAAd;;YAIF8e,WAAW,CAACnQ,OAAZ,GAAsB,IAAtB;YAEA,IAAIvO,IAAI,CAAC+J,GAAL,CAAS4U,SAAS,CAAChf,CAAnB,IAAwB,CAAxB,IAA6BK,IAAI,CAAC+J,GAAL,CAAS4U,SAAS,CAAC/e,CAAnB,IAAwB,CAAzD,EAA4D;gBAC1D,MAAM2H,uBAAuB,GAAGD,0BAA0B,CAAClB,IAAD,CAA1D;gBAEA,IAAImB,uBAAJ,EAA6B;oBAC3BA,uBAAuB,CAACmI,QAAxB,CAAiC;wBAC/BhP,GAAG,EAAEie,SAAS,CAAC/e,CADgB;wBAE/BY,IAAI,EAAEme,SAAS,CAAChf,CAAAA;qBAFlB;;;SAzCmB;qEA+CtB;QAAC0O,UAAD;QAAa1O,CAAb;QAAgBC,CAAhB;QAAmB0Z,WAAnB;QAAgC/O,OAAhC;KA/CsB,CAAzB;AAgDD;ACoDM,MAAMqU,sBAAsB,GAAA,WAAA,qKAAG7iB,gBAAAA,AAAa,EAAY;IAC7D,GAAGyD,kBAD0D;IAE7DyE,MAAM,EAAE,CAFqD;IAG7DC,MAAM,EAAE;AAHqD,CAAZ,CAA5C;AAMP,IAAK2a,MAAL;AAAA,CAAA,SAAKA,MAAAA;IACHA,MAAAA,CAAAA,MAAAA,CAAAA,gBAAAA,GAAAA,EAAA,GAAA,eAAA;IACAA,MAAAA,CAAAA,MAAAA,CAAAA,eAAAA,GAAAA,EAAA,GAAA,cAAA;IACAA,MAAAA,CAAAA,MAAAA,CAAAA,cAAAA,GAAAA,EAAA,GAAA,aAAA;AACD,CAJD,EAAKA,MAAM,IAAA,CAANA,MAAM,GAAA,CAAA,CAAA,CAAX;AAMA,MAAaC,UAAU,GAAA,WAAA,qKAAGC,OAAAA,AAAI,EAAC,SAASD,UAAT,CAAA,IAAA;;QAAoB,EACjDvhB,EADiD,EAEjDyhB,aAFiD,EAGjDvK,UAAU,GAAG,IAHoC,EAIjD6G,QAJiD,EAKjDhc,OAAO,GAAGuc,cALuC,EAMjDoD,kBAAkB,GAAGzb,gBAN4B,EAOjD0b,SAPiD,EAQjDZ,SARiD,EASjD,GAAG9Q;IAEH,MAAM2R,KAAK,qKAAGC,aAAAA,AAAU,EAAChC,OAAD,EAAU9P,SAAV,EAAqB2P,eAArB,CAAxB;IACA,MAAM,CAACI,KAAD,EAAQvgB,QAAR,CAAA,GAAoBqiB,KAA1B;IACA,MAAM,CAACE,oBAAD,EAAuBC,uBAAvB,CAAA,GACJ/iB,qBAAqB,EADvB;IAEA,MAAM,CAACgjB,MAAD,EAASC,SAAT,CAAA,OAAsB/iB,yKAAAA,AAAQ,EAASoiB,MAAM,CAACY,aAAhB,CAApC;IACA,MAAMC,aAAa,GAAGH,MAAM,KAAKV,MAAM,CAACc,WAAxC;IACA,MAAM,EACJxiB,SAAS,EAAE,EAACG,MAAM,EAAEsiB,QAAT,EAAmB1C,KAAK,EAAE7H,cAA1B,EAA0C8H,SAAAA,EADjD,EAEJnB,SAAS,EAAE,EAAC9F,UAAU,EAAEvU,mBAAAA,OACtB0b,KAHJ;IAIA,MAAMjX,IAAI,GAAGwZ,QAAQ,IAAI,IAAZ,GAAmBvK,cAAc,CAACvT,GAAf,CAAmB8d,QAAnB,CAAnB,GAAkD,IAA/D;IACA,MAAMC,WAAW,GAAGxL,2KAAAA,AAAM,EAA4B;QACpDyL,OAAO,EAAE,IAD2C;QAEpDC,UAAU,EAAE;KAFY,CAA1B;IAIA,MAAMziB,MAAM,qKAAGkB,UAAO,AAAPA;iDACb;YAAA,IAAA;YAAA,OACEohB,QAAQ,IAAI,IAAZ,GACI;gBACEriB,EAAE,EAAEqiB,QADN;;gBAGE/e,IAAI,EAAA,CAAA,aAAEuF,IAAF,IAAA,OAAA,KAAA,IAAEA,IAAI,CAAEvF,IAAR,KAAA,OAAA,aAAgBib,WAHtB;gBAIE1b,IAAI,EAAEyf;aALZ,GAOI,IARN;SADoB;gDAUpB;QAACD,QAAD;QAAWxZ,IAAX;KAVoB,CAAtB;IAYA,MAAM4Z,SAAS,GAAG3L,2KAAAA,AAAM,EAA0B,IAA1B,CAAxB;IACA,MAAM,CAAC4L,YAAD,EAAeC,eAAf,CAAA,qKAAkCzjB,WAAAA,AAAQ,EAAwB,IAAxB,CAAhD;IACA,MAAM,CAACggB,cAAD,EAAiB0D,iBAAjB,CAAA,oKAAsC1jB,YAAAA,AAAQ,EAAe,IAAf,CAApD;IACA,MAAM2jB,WAAW,gLAAGvJ,iBAAAA,AAAc,EAACrJ,KAAD,EAAQ/N,MAAM,CAAC6a,MAAP,CAAc9M,KAAd,CAAR,CAAlC;IACA,MAAM6S,sBAAsB,gLAAGhiB,cAAAA,AAAW,EAAA,kBAAmBd,EAAnB,CAA1C;IACA,MAAM+iB,0BAA0B,qKAAG9hB,UAAAA,AAAO;qEACxC,IAAMmD,mBAAmB,CAAC2a,UAApB,EADkC;oEAExC;QAAC3a,mBAAD;KAFwC,CAA1C;IAIA,MAAMib,sBAAsB,GAAG4B,yBAAyB,CAACU,SAAD,CAAxD;IACA,MAAM,EAACxd,cAAD,EAAiBoV,0BAAjB,EAA6CK,kBAAAA,KACjDlB,qBAAqB,CAACqK,0BAAD,EAA6B;QAChDnK,QAAQ,EAAEuJ,aADsC;QAEhDtJ,YAAY,EAAE;YAAC+G,SAAS,CAACxd,CAAX;YAAcwd,SAAS,CAACvd,CAAxB;SAFkC;QAGhDyW,MAAM,EAAEuG,sBAAsB,CAACZ,SAAAA;KAHZ,CADvB;IAMA,MAAM3N,UAAU,GAAG+G,aAAa,CAACC,cAAD,EAAiBuK,QAAjB,CAAhC;IACA,MAAMW,qBAAqB,qKAAG/hB,UAAAA,AAAO;gEACnC,IAAOie,cAAc,gLAAGnc,sBAAAA,AAAmB,EAACmc,cAAD,CAAtB,GAAyC,IAD3B;+DAEnC;QAACA,cAAD;KAFmC,CAArC;IAIA,MAAM+D,iBAAiB,GAAGC,sBAAsB,EAAhD;IACA,MAAMC,qBAAqB,GAAGlJ,cAAc,CAC1CnJ,UAD0C,EAE1CuO,sBAAsB,CAACzf,SAAvB,CAAiCoN,OAFS,CAA5C;IAKAkU,gCAAgC,CAAC;QAC/BpQ,UAAU,EAAEuR,QAAQ,IAAI,IAAZ,GAAmBvK,cAAc,CAACvT,GAAf,CAAmB8d,QAAnB,CAAnB,GAAkD,IAD/B;QAE/BvJ,MAAM,EAAEmK,iBAAiB,CAACG,uBAFK;QAG/BrH,WAAW,EAAEoH,qBAHkB;QAI/BnW,OAAO,EAAEqS,sBAAsB,CAACzf,SAAvB,CAAiCoN,OAAAA;KAJZ,CAAhC;IAOA,MAAMmS,cAAc,GAAGrE,OAAO,CAC5BhK,UAD4B,EAE5BuO,sBAAsB,CAACzf,SAAvB,CAAiCoN,OAFL,EAG5BmW,qBAH4B,CAA9B;IAKA,MAAM/D,iBAAiB,GAAGtE,OAAO,CAC/BhK,UAAU,GAAGA,UAAU,CAACuS,aAAd,GAA8B,IADT,CAAjC;IAGA,MAAMC,aAAa,qKAAGxM,SAAAA,AAAM,EAAgB;QAC1CoI,cAAc,EAAE,IAD0B;QAE1Cnf,MAAM,EAAE,IAFkC;QAG1C+Q,UAH0C;QAI1C5M,aAAa,EAAE,IAJ2B;QAK1CN,UAAU,EAAE,IAL8B;QAM1CO,cAN0C;QAO1C2T,cAP0C;QAQ1CyL,YAAY,EAAE,IAR4B;QAS1CC,gBAAgB,EAAE,IATwB;QAU1Cpf,mBAV0C;QAW1ClE,IAAI,EAAE,IAXoC;QAY1C0M,mBAAmB,EAAE,EAZqB;QAa1C6W,uBAAuB,EAAE;KAbC,CAA5B;IAeA,MAAMC,QAAQ,GAAGtf,mBAAmB,CAAC4a,UAApB,CAAA,CAAA,wBACfsE,aAAa,CAACtS,OAAd,CAAsB9Q,IADP,KAAA,OAAA,KAAA,IACf,sBAA4BF,EADb,CAAjB;IAGA,MAAM4e,WAAW,GAAGX,uBAAuB,CAAC;QAC1CjR,OAAO,EAAEqS,sBAAsB,CAACT,WAAvB,CAAmC5R,OAAAA;KADH,CAA3C,EAAA,oDAAA;IAKA,MAAMuW,YAAY,GAAA,CAAA,wBAAG3E,WAAW,CAACT,OAAZ,CAAoBnN,OAAvB,KAAA,OAAA,wBAAkCF,UAApD;IACA,MAAM0S,gBAAgB,GAAGrB,aAAa,GAAA,CAAA,oBAClCvD,WAAW,CAAC/b,IADsB,KAAA,OAAA,oBACdsc,cADc,GAElC,IAFJ;IAGA,MAAMwE,eAAe,GAAGzQ,OAAO,CAC7B0L,WAAW,CAACT,OAAZ,CAAoBnN,OAApB,IAA+B4N,WAAW,CAAC/b,IADd,CAA/B,EAAA,wEAAA;;IAKA,MAAM+gB,aAAa,GAAG9H,YAAY,CAAC6H,eAAe,GAAG,IAAH,GAAUxE,cAA1B,CAAlC,EAAA,2CAAA;IAGA,MAAMzB,UAAU,GAAGH,aAAa,CAC9BgG,YAAY,gLAAGjb,YAAAA,AAAS,EAACib,YAAD,CAAZ,GAA6B,IADX,CAAhC,EAAA,gDAAA;IAKA,MAAM3W,mBAAmB,GAAGoP,sBAAsB,CAChDmG,aAAa,GAAGuB,QAAH,IAAA,OAAGA,QAAH,GAAe5S,UAAf,GAA4B,IADO,CAAlD;IAGA,MAAMwF,uBAAuB,GAAGkH,QAAQ,CAAC5Q,mBAAD,CAAxC,EAAA,kBAAA;IAGA,MAAMiX,iBAAiB,GAAG/C,cAAc,CAACC,SAAD,EAAY;QAClDxa,SAAS,EAAE;YACTnE,CAAC,EAAEwd,SAAS,CAACxd,CAAV,GAAcwhB,aAAa,CAACxhB,CADtB;YAETC,CAAC,EAAEud,SAAS,CAACvd,CAAV,GAAcuhB,aAAa,CAACvhB,CAFtB;YAGTqE,MAAM,EAAE,CAHC;YAITC,MAAM,EAAE;SALwC;QAOlDuY,cAPkD;QAQlDnf,MARkD;QASlDof,cATkD;QAUlDC,iBAVkD;QAWlDoE,gBAXkD;QAYlDtjB,IAAI,EAAEojB,aAAa,CAACtS,OAAd,CAAsB9Q,IAZsB;QAalD4jB,eAAe,EAAElF,WAAW,CAAC/b,IAbqB;QAclD+J,mBAdkD;QAelD0J,uBAfkD;QAgBlDoH;KAhBsC,CAAxC;IAmBA,MAAMrX,kBAAkB,GAAG2c,qBAAqB,OAC5C3jB,+KAAAA,AAAG,EAAC2jB,qBAAD,EAAwBpD,SAAxB,CADyC,GAE5C,IAFJ;IAIA,MAAMtS,aAAa,GAAG6O,gBAAgB,CAACvP,mBAAD,CAAtC,EAAA,2DAAA;IAEA,MAAMmX,gBAAgB,GAAG/G,qBAAqB,CAAC1P,aAAD,CAA9C,EAAA,oFAAA;IAEA,MAAM0W,qBAAqB,GAAGhH,qBAAqB,CAAC1P,aAAD,EAAgB;QACjE6R,cADiE;KAAhB,CAAnD;IAIA,MAAMsE,uBAAuB,gLAAGpkB,MAAAA,AAAG,EAACwkB,iBAAD,EAAoBE,gBAApB,CAAnC;IAEA,MAAM7f,aAAa,GAAGsf,gBAAgB,GAClCrc,eAAe,CAACqc,gBAAD,EAAmBK,iBAAnB,CADmB,GAElC,IAFJ;IAIA,MAAMjgB,UAAU,GACd7D,MAAM,IAAImE,aAAV,GACIwd,kBAAkB,CAAC;QACjB3hB,MADiB;QAEjBmE,aAFiB;QAGjBC,cAHiB;QAIjBC,mBAAmB,EAAE2e,0BAJJ;QAKjB1c;KALgB,CADtB,GAQI,IATN;IAUA,MAAM4d,MAAM,GAAGtgB,iBAAiB,CAACC,UAAD,EAAa,IAAb,CAAhC;IACA,MAAM,CAAC1D,IAAD,EAAOgkB,OAAP,CAAA,qKAAkBhlB,WAAAA,AAAQ,EAAc,IAAd,CAAhC,EAAA,iEAAA;;IAIA,MAAMilB,gBAAgB,GAAGR,eAAe,GACpCE,iBADoC,gLAEpCxkB,MAAAA,AAAG,EAACwkB,iBAAD,EAAoBG,qBAApB,CAFP;IAIA,MAAMzd,SAAS,GAAGD,WAAW,CAC3B6d,gBAD2B,EAAA,CAAA,aAE3BjkB,IAF2B,IAAA,OAAA,KAAA,IAE3BA,IAAI,CAAE2C,IAFqB,KAAA,OAAA,aAEb,IAFa,EAG3Bsc,cAH2B,CAA7B;IAMA,MAAMiF,eAAe,qKAAGtN,SAAAA,AAAM,EAAwB,IAAxB,CAA9B;IACA,MAAMuN,iBAAiB,OAAGjlB,4KAAAA,AAAW;gEACnC,CACEK,KADF,EAAA;gBAEE,EAACmC,MAAM,EAAEuW,MAAT,EAAiBtW,OAAAA;YAEjB,IAAI4gB,SAAS,CAACzR,OAAV,IAAqB,IAAzB,EAA+B;gBAC7B;;YAGF,MAAMF,UAAU,GAAGgH,cAAc,CAACvT,GAAf,CAAmBke,SAAS,CAACzR,OAA7B,CAAnB;YAEA,IAAI,CAACF,UAAL,EAAiB;gBACf;;YAGF,MAAMoO,cAAc,GAAGzf,KAAK,CAACoT,WAA7B;YAEA,MAAMyR,cAAc,GAAG,IAAInM,MAAJ,CAAW;gBAChCpY,MAAM,EAAE0iB,SAAS,CAACzR,OADc;gBAEhCF,UAFgC;gBAGhCrR,KAAK,EAAEyf,cAHyB;gBAIhCrd,OAJgC;;;gBAOhCqP,OAAO,EAAEoS,aAPuB;gBAQhCvO,OAAO,EAAC/U,EAAD;oBACL,MAAM+X,aAAa,GAAGD,cAAc,CAACvT,GAAf,CAAmBvE,EAAnB,CAAtB;oBAEA,IAAI,CAAC+X,aAAL,EAAoB;wBAClB;;oBAGF,MAAM,EAACwM,WAAAA,KAAe1B,WAAW,CAAC7R,OAAlC;oBACA,MAAMvR,KAAK,GAAmB;wBAACO;qBAA/B;oBACAukB,WAAW,IAAA,IAAX,GAAA,KAAA,IAAAA,WAAW,CAAG9kB,KAAH,CAAX;oBACAqiB,oBAAoB,CAAC;wBAACtiB,IAAI,EAAE,aAAP;wBAAsBC;qBAAvB,CAApB;iBAlB8B;gBAoBhC+U,SAAS,EAACxU,EAAD,EAAKiT,UAAL,EAAiBO,kBAAjB,EAAqCe,MAArC;oBACP,MAAMwD,aAAa,GAAGD,cAAc,CAACvT,GAAf,CAAmBvE,EAAnB,CAAtB;oBAEA,IAAI,CAAC+X,aAAL,EAAoB;wBAClB;;oBAGF,MAAM,EAACyM,aAAAA,KAAiB3B,WAAW,CAAC7R,OAApC;oBACA,MAAMvR,KAAK,GAAqB;wBAC9BO,EAD8B;wBAE9BiT,UAF8B;wBAG9BO,kBAH8B;wBAI9Be;qBAJF;oBAOAiQ,aAAa,IAAA,IAAb,GAAA,KAAA,IAAAA,aAAa,CAAG/kB,KAAH,CAAb;oBACAqiB,oBAAoB,CAAC;wBAACtiB,IAAI,EAAE,eAAP;wBAAwBC;qBAAzB,CAApB;iBApC8B;gBAsChCsR,OAAO,EAACyC,kBAAD;oBACL,MAAMxT,EAAE,GAAGyiB,SAAS,CAACzR,OAArB;oBAEA,IAAIhR,EAAE,IAAI,IAAV,EAAgB;wBACd;;oBAGF,MAAM+X,aAAa,GAAGD,cAAc,CAACvT,GAAf,CAAmBvE,EAAnB,CAAtB;oBAEA,IAAI,CAAC+X,aAAL,EAAoB;wBAClB;;oBAGF,MAAM,EAACjY,WAAAA,KAAe+iB,WAAW,CAAC7R,OAAlC;oBACA,MAAMvR,KAAK,GAAmB;wBAC5Byf,cAD4B;wBAE5Bnf,MAAM,EAAE;4BAACC,EAAD;4BAAKsD,IAAI,EAAEyU,aAAa,CAACzU,IAAzB;4BAA+BT,IAAI,EAAEyf;;qBAF/C;6LAKAmC,0BAAAA,AAAuB;gFAAC;4BACtB3kB,WAAW,IAAA,IAAX,GAAA,KAAA,IAAAA,WAAW,CAAGL,KAAH,CAAX;4BACAwiB,SAAS,CAACX,MAAM,CAACoD,YAAR,CAAT;4BACAnlB,QAAQ,CAAC;gCACPC,IAAI,EAAEiC,MAAM,CAACyS,SADN;gCAEPV,kBAFO;gCAGPzT,MAAM,EAAEC;6BAHF,CAAR;4BAKA8hB,oBAAoB,CAAC;gCAACtiB,IAAI,EAAE,aAAP;gCAAsBC;6BAAvB,CAApB;4BACAkjB,eAAe,CAACyB,eAAe,CAACpT,OAAjB,CAAf;4BACA4R,iBAAiB,CAAC1D,cAAD,CAAjB;yBAVqB,CAAvB;;iBAzD8B;gBAsEhC3M,MAAM,EAACD,WAAD;oBACJ/S,QAAQ,CAAC;wBACPC,IAAI,EAAEiC,MAAM,CAACue,QADN;wBAEP1N;qBAFM,CAAR;iBAvE8B;gBA4EhCE,KAAK,EAAEmS,aAAa,CAACljB,MAAM,CAACwe,OAAR,CA5EY;gBA6EhCvN,QAAQ,EAAEiS,aAAa,CAACljB,MAAM,CAACye,UAAR;aA7EF,CAAvB;YAgFAkE,eAAe,CAACpT,OAAhB,GAA0BsT,cAA1B;YAEA,SAASK,aAAT,CAAuBnlB,IAAvB;gBACE,OAAO,eAAe2O,OAAf;oBACL,MAAM,EAACpO,MAAD,EAAS6D,UAAT,EAAqB1D,IAArB,EAA2BujB,uBAAAA,KAC/BH,aAAa,CAACtS,OADhB;oBAEA,IAAIvR,KAAK,GAAwB,IAAjC;oBAEA,IAAIM,MAAM,IAAI0jB,uBAAd,EAAuC;wBACrC,MAAM,EAACmB,UAAAA,KAAc/B,WAAW,CAAC7R,OAAjC;wBAEAvR,KAAK,GAAG;4BACNyf,cADM;4BAENnf,MAAM,EAAEA,MAFF;4BAGN6D,UAHM;4BAIN4K,KAAK,EAAEiV,uBAJD;4BAKNvjB;yBALF;wBAQA,IAAIV,IAAI,KAAKiC,MAAM,CAACwe,OAAhB,IAA2B,OAAO2E,UAAP,KAAsB,UAArD,EAAiE;4BAC/D,MAAMC,YAAY,GAAG,MAAMC,OAAO,CAACC,OAAR,CAAgBH,UAAU,CAACnlB,KAAD,CAA1B,CAA3B;4BAEA,IAAIolB,YAAJ,EAAkB;gCAChBrlB,IAAI,GAAGiC,MAAM,CAACye,UAAd;;;;oBAKNuC,SAAS,CAACzR,OAAV,GAAoB,IAApB;6LAEAyT,0BAAAA,AAAuB;sGAAC;4BACtBllB,QAAQ,CAAC;gCAACC;6BAAF,CAAR;4BACAyiB,SAAS,CAACX,MAAM,CAACY,aAAR,CAAT;4BACAgC,OAAO,CAAC,IAAD,CAAP;4BACAvB,eAAe,CAAC,IAAD,CAAf;4BACAC,iBAAiB,CAAC,IAAD,CAAjB;4BACAwB,eAAe,CAACpT,OAAhB,GAA0B,IAA1B;4BAEA,MAAM9C,SAAS,GACb1O,IAAI,KAAKiC,MAAM,CAACwe,OAAhB,GAA0B,WAA1B,GAAwC,cAD1C;4BAGA,IAAIxgB,KAAJ,EAAW;gCACT,MAAM0O,OAAO,GAAG0U,WAAW,CAAC7R,OAAZ,CAAoB9C,SAApB,CAAhB;gCAEAC,OAAO,IAAA,IAAP,GAAA,KAAA,IAAAA,OAAO,CAAG1O,KAAH,CAAP;gCACAqiB,oBAAoB,CAAC;oCAACtiB,IAAI,EAAE0O,SAAP;oCAAkBzO;iCAAnB,CAApB;;yBAfmB,CAAvB;;iBA3BF;;SApG+B;+DAqJnC;QAACqY,cAAD;KArJmC,CAArC;IAwJA,MAAMkN,iCAAiC,IAAG5lB,+KAAAA,AAAW;gFACnD,CACE+O,OADF,EAEEvM,MAFF;YAIE;wFAAO,CAACnC,KAAD,EAAQM,MAAR;oBACL,MAAM8S,WAAW,GAAGpT,KAAK,CAACoT,WAA1B;oBACA,MAAMoS,mBAAmB,GAAGnN,cAAc,CAACvT,GAAf,CAAmBxE,MAAnB,CAA5B;oBAEA,IAEE0iB,SAAS,CAACzR,OAAV,KAAsB,IAAtB,IAAA,sBAAA;oBAEA,CAACiU,mBAFD,IAAA,kCAAA;oBAIApS,WAAW,CAACqS,MAJZ,IAKArS,WAAW,CAACsS,gBAPd,EAQE;wBACA;;oBAGF,MAAMC,iBAAiB,GAAG;wBACxBrlB,MAAM,EAAEklB;qBADV;oBAGA,MAAMI,cAAc,GAAGlX,OAAO,CAC5B1O,KAD4B,EAE5BmC,MAAM,CAACC,OAFqB,EAG5BujB,iBAH4B,CAA9B;oBAMA,IAAIC,cAAc,KAAK,IAAvB,EAA6B;wBAC3BxS,WAAW,CAACqS,MAAZ,GAAqB;4BACnBI,UAAU,EAAE1jB,MAAM,CAACA,MAAAA;yBADrB;wBAIA6gB,SAAS,CAACzR,OAAV,GAAoBjR,MAApB;wBACAskB,iBAAiB,CAAC5kB,KAAD,EAAQmC,MAAR,CAAjB;;iBA/BJ;;SALiD;+EAwCnD;QAACkW,cAAD;QAAiBuM,iBAAjB;KAxCmD,CAArD;IA2CA,MAAM1R,UAAU,GAAGsF,oBAAoB,CACrClW,OADqC,EAErCijB,iCAFqC,CAAvC;IAKA5H,cAAc,CAACrb,OAAD,CAAd;iLAEA0Z,4BAAAA,AAAyB;2DAAC;YACxB,IAAI0D,cAAc,IAAI6C,MAAM,KAAKV,MAAM,CAACoD,YAAxC,EAAsD;gBACpDzC,SAAS,CAACX,MAAM,CAACc,WAAR,CAAT;;SAFqB;0DAItB;QAACjD,cAAD;QAAiB6C,MAAjB;KAJsB,CAAzB;KAMAnjB,6KAAS,AAATA;2CACE;YACE,MAAM,EAACqC,UAAAA,KAAc2hB,WAAW,CAAC7R,OAAjC;YACA,MAAM,EAACjR,MAAD,EAASmf,cAAT,EAAyBtb,UAAzB,EAAqC1D,IAAAA,KAAQojB,aAAa,CAACtS,OAAjE;YAEA,IAAI,CAACjR,MAAD,IAAW,CAACmf,cAAhB,EAAgC;gBAC9B;;YAGF,MAAMzf,KAAK,GAAkB;gBAC3BM,MAD2B;gBAE3Bmf,cAF2B;gBAG3Btb,UAH2B;gBAI3B4K,KAAK,EAAE;oBACLpM,CAAC,EAAEqhB,uBAAuB,CAACrhB,CADtB;oBAELC,CAAC,EAAEohB,uBAAuB,CAACphB,CAAAA;iBANF;gBAQ3BnC;aARF;YAWAukB,mMAAAA,AAAuB;mDAAC;oBACtBvjB,UAAU,IAAA,IAAV,GAAA,KAAA,IAAAA,UAAU,CAAGzB,KAAH,CAAV;oBACAqiB,oBAAoB,CAAC;wBAACtiB,IAAI,EAAE,YAAP;wBAAqBC;qBAAtB,CAApB;iBAFqB,CAAvB;;SApBK;0CA0BP;QAACgkB,uBAAuB,CAACrhB,CAAzB;QAA4BqhB,uBAAuB,CAACphB,CAApD;KA1BO,CAAT;IA6BAxD,8KAAAA,AAAS;2CACP;YACE,MAAM,EACJkB,MADI,EAEJmf,cAFI,EAGJtb,UAHI,EAIJQ,mBAJI,EAKJqf,uBAAAA,KACEH,aAAa,CAACtS,OANlB;YAQA,IACE,CAACjR,MAAD,IACA0iB,SAAS,CAACzR,OAAV,IAAqB,IADrB,IAEA,CAACkO,cAFD,IAGA,CAACuE,uBAJH,EAKE;gBACA;;YAGF,MAAM,EAACxjB,UAAAA,KAAc4iB,WAAW,CAAC7R,OAAjC;YACA,MAAMuU,aAAa,GAAGnhB,mBAAmB,CAACG,GAApB,CAAwB0f,MAAxB,CAAtB;YACA,MAAM/jB,IAAI,GACRqlB,aAAa,IAAIA,aAAa,CAAC1iB,IAAd,CAAmBmO,OAApC,GACI;gBACEhR,EAAE,EAAEulB,aAAa,CAACvlB,EADpB;gBAEE6C,IAAI,EAAE0iB,aAAa,CAAC1iB,IAAd,CAAmBmO,OAF3B;gBAGE1N,IAAI,EAAEiiB,aAAa,CAACjiB,IAHtB;gBAIEmT,QAAQ,EAAE8O,aAAa,CAAC9O,QAAAA;aAL9B,GAOI,IARN;YASA,MAAMhX,KAAK,GAAkB;gBAC3BM,MAD2B;gBAE3Bmf,cAF2B;gBAG3Btb,UAH2B;gBAI3B4K,KAAK,EAAE;oBACLpM,CAAC,EAAEqhB,uBAAuB,CAACrhB,CADtB;oBAELC,CAAC,EAAEohB,uBAAuB,CAACphB,CAAAA;iBANF;gBAQ3BnC;aARF;qLAWAukB,0BAAAA,AAAuB;mDAAC;oBACtBP,OAAO,CAAChkB,IAAD,CAAP;oBACAD,UAAU,IAAA,IAAV,GAAA,KAAA,IAAAA,UAAU,CAAGR,KAAH,CAAV;oBACAqiB,oBAAoB,CAAC;wBAACtiB,IAAI,EAAE,YAAP;wBAAqBC;qBAAtB,CAApB;iBAHqB,CAAvB;;SAzCK;0CAgDP;QAACwkB,MAAD;KAhDO,CAAT;iLAmDAxI,4BAAAA,AAAyB;2DAAC;YACxB6H,aAAa,CAACtS,OAAd,GAAwB;gBACtBkO,cADsB;gBAEtBnf,MAFsB;gBAGtB+Q,UAHsB;gBAItB5M,aAJsB;gBAKtBN,UALsB;gBAMtBO,cANsB;gBAOtB2T,cAPsB;gBAQtByL,YARsB;gBAStBC,gBATsB;gBAUtBpf,mBAVsB;gBAWtBlE,IAXsB;gBAYtB0M,mBAZsB;gBAatB6W;aAbF;YAgBAnB,WAAW,CAACtR,OAAZ,GAAsB;gBACpBuR,OAAO,EAAEiB,gBADW;gBAEpBhB,UAAU,EAAEte;aAFd;SAjBuB;0DAqBtB;QACDnE,MADC;QAED+Q,UAFC;QAGDlN,UAHC;QAIDM,aAJC;QAKD4T,cALC;QAMDyL,YANC;QAODC,gBAPC;QAQDrf,cARC;QASDC,mBATC;QAUDlE,IAVC;QAWD0M,mBAXC;QAYD6W,uBAZC;KArBsB,CAAzB;IAoCA3N,eAAe,CAAC;QACd,GAAGmN,iBADW;QAEdzU,KAAK,EAAEoR,SAFO;QAGd3J,YAAY,EAAE/R,aAHA;QAIdmC,kBAJc;QAKduG,mBALc;QAMd0J;KANa,CAAf;IASA,MAAMkP,aAAa,qKAAGvkB,UAAAA,AAAO;wDAAC;YAC5B,MAAMiQ,OAAO,GAA4B;gBACvCnR,MADuC;gBAEvC+Q,UAFuC;gBAGvCqO,cAHuC;gBAIvCD,cAJuC;gBAKvCtb,UALuC;gBAMvCwb,iBANuC;gBAOvCR,WAPuC;gBAQvC9G,cARuC;gBASvC1T,mBATuC;gBAUvCD,cAVuC;gBAWvCjE,IAXuC;gBAYvCqZ,0BAZuC;gBAavC3M,mBAbuC;gBAcvC0J,uBAduC;gBAevC+I,sBAfuC;gBAgBvCzF,kBAhBuC;gBAiBvC8D;aAjBF;YAoBA,OAAOxM,OAAP;SArB2B;uDAsB1B;QACDnR,MADC;QAED+Q,UAFC;QAGDqO,cAHC;QAIDD,cAJC;QAKDtb,UALC;QAMDwb,iBANC;QAODR,WAPC;QAQD9G,cARC;QASD1T,mBATC;QAUDD,cAVC;QAWDjE,IAXC;QAYDqZ,0BAZC;QAaD3M,mBAbC;QAcD0J,uBAdC;QAeD+I,sBAfC;QAgBDzF,kBAhBC;QAiBD8D,UAjBC;KAtB0B,CAA7B;IA0CA,MAAM+H,eAAe,qKAAGxkB,UAAAA,AAAO;0DAAC;YAC9B,MAAMiQ,OAAO,GAA8B;gBACzCgO,cADyC;gBAEzCvM,UAFyC;gBAGzC5S,MAHyC;gBAIzCof,cAJyC;gBAKzCI,iBAAiB,EAAE;oBACjB3f,SAAS,EAAEkjB;iBAN4B;gBAQzCvjB,QARyC;gBASzCuY,cATyC;gBAUzC5X,IAVyC;gBAWzCqZ;aAXF;YAcA,OAAOrI,OAAP;SAf6B;yDAgB5B;QACDgO,cADC;QAEDvM,UAFC;QAGD5S,MAHC;QAIDof,cAJC;QAKD5f,QALC;QAMDujB,sBANC;QAODhL,cAPC;QAQD5X,IARC;QASDqZ,0BATC;KAhB4B,CAA/B;IA4BA,qKACEnY,UAAAA,CAAAA,aAAA,CAAC7C,iBAAiB,CAACmnB,QAAnB,EAAA;QAA4BpkB,KAAK,EAAEygB;KAAnC,EACE3gB,wKAAAA,CAAAA,aAAA,CAACoe,eAAe,CAACkG,QAAjB,EAAA;QAA0BpkB,KAAK,EAAEmkB;KAAjC,gKACErkB,UAAAA,CAAAA,aAAA,CAACqe,aAAa,CAACiG,QAAf,EAAA;QAAwBpkB,KAAK,EAAEkkB;KAA/B,gKACEpkB,UAAAA,CAAAA,aAAA,CAACigB,sBAAsB,CAACqE,QAAxB,EAAA;QAAiCpkB,KAAK,EAAEiF;KAAxC,EACGwX,QADH,CADF,CADF,gKAME3c,UAAAA,CAAAA,aAAA,CAACkf,YAAD,EAAA;QAAc7J,QAAQ,EAAE,CAAAgL,aAAa,IAAA,IAAb,GAAA,KAAA,IAAAA,aAAa,CAAEkE,YAAf,MAAgC;KAAxD,CANF,CADF,EASEvkB,wKAAAA,CAAAA,aAAA,CAACf,aAAD,EAAA;QAAA,GACMohB,aAAAA;QACJjhB,uBAAuB,EAAEsiB;KAF3B,CATF,CADF;;IAiBA,SAASI,sBAAT;QACE,MAAM0C,8BAA8B,GAClC,CAAAlD,YAAY,IAAA,IAAZ,GAAA,KAAA,IAAAA,YAAY,CAAExS,iBAAd,MAAoC,KADtC;QAEA,MAAM2V,0BAA0B,GAC9B,OAAO3O,UAAP,KAAsB,QAAtB,GACIA,UAAU,CAAChB,OAAX,KAAuB,KAD3B,GAEIgB,UAAU,KAAK,KAHrB;QAIA,MAAMhB,OAAO,GACXiM,aAAa,IACb,CAACyD,8BADD,IAEA,CAACC,0BAHH;QAKA,IAAI,OAAO3O,UAAP,KAAsB,QAA1B,EAAoC;YAClC,OAAO;gBACL,GAAGA,UADE;gBAELhB;aAFF;;QAMF,OAAO;YAACA;SAAR;;AAEH,CAtnB6B,CAAvB;ACrGP,MAAM4P,WAAW,GAAA,WAAA,qKAAGtnB,gBAAAA,AAAa,EAAM,IAAN,CAAjC;AAEA,MAAMunB,WAAW,GAAG,QAApB;AAEA,MAAMC,SAAS,GAAG,WAAlB;AAEA,SAAgBC,aAAAA,IAAAA;QAAa,EAC3BjmB,EAD2B,EAE3BsD,IAF2B,EAG3BmT,QAAQ,GAAG,KAHgB,EAI3ByP,UAAAA;IAEA,MAAMxY,GAAG,gLAAG5M,cAAAA,AAAW,EAACklB,SAAD,CAAvB;IACA,MAAM,EACJrT,UADI,EAEJuM,cAFI,EAGJnf,MAHI,EAIJof,cAJI,EAKJI,iBALI,EAMJzH,cANI,EAOJ5X,IAAAA,uKACEtB,aAAAA,AAAU,EAAC4gB,eAAD,CARd;IASA,MAAM,EACJ2G,IAAI,GAAGJ,WADH,EAEJK,eAAe,GAAG,WAFd,EAGJC,QAAQ,GAAG,CAAA,KACTH,UAJE,IAAA,OAIFA,UAJE,GAIY,CAAA,CAJlB;IAKA,MAAMI,UAAU,GAAG,CAAAvmB,MAAM,IAAA,IAAN,GAAA,KAAA,IAAAA,MAAM,CAAEC,EAAR,MAAeA,EAAlC;IACA,MAAMuG,SAAS,OAAqB3H,2KAAAA,AAAU,EAC5C0nB,UAAU,GAAGjF,sBAAH,GAA4ByE,WADM,CAA9C;IAGA,MAAM,CAACjd,IAAD,EAAO0d,UAAP,CAAA,OAAqBlI,sLAAAA,AAAU,EAArC;IACA,MAAM,CAACtL,aAAD,EAAgByT,mBAAhB,CAAA,gLAAuCnI,aAAAA,AAAU,EAAvD;IACA,MAAMpf,SAAS,GAAGqe,qBAAqB,CAAC3K,UAAD,EAAa3S,EAAb,CAAvC;IACA,MAAMymB,OAAO,+KAAGnN,kBAAAA,AAAc,EAAChW,IAAD,CAA9B;iLAEAmY,4BAAAA,AAAyB;kDACvB;YACE3D,cAAc,CAAC6B,GAAf,CAAmB3Z,EAAnB,EAAuB;gBAACA,EAAD;gBAAK0N,GAAL;gBAAU7E,IAAV;gBAAgBkK,aAAhB;gBAA+BzP,IAAI,EAAEmjB;aAA5D;YAEA;0DAAO;oBACL,MAAM5d,IAAI,GAAGiP,cAAc,CAACvT,GAAf,CAAmBvE,EAAnB,CAAb;oBAEA,IAAI6I,IAAI,IAAIA,IAAI,CAAC6E,GAAL,KAAaA,GAAzB,EAA8B;wBAC5BoK,cAAc,CAACxY,MAAf,CAAsBU,EAAtB;;iBAJJ;;SAJqB;iDAavB;QAAC8X,cAAD;QAAiB9X,EAAjB;KAbuB,CAAzB;IAgBA,MAAM0mB,kBAAkB,OAAwBzlB,wKAAAA,AAAO;oDACrD,IAAA,CAAO;gBACLklB,IADK;gBAELE,QAFK;gBAGL,iBAAiB5P,QAHZ;gBAIL,gBAAgB6P,UAAU,IAAIH,IAAI,KAAKJ,WAAvB,GAAqC,IAArC,GAA4ChW,SAJvD;gBAKL,wBAAwBqW,eALnB;gBAML,oBAAoB7G,iBAAiB,CAAC3f,SAAAA;aANxC,CADqD;mDASrD;QACE6W,QADF;QAEE0P,IAFF;QAGEE,QAHF;QAIEC,UAJF;QAKEF,eALF;QAME7G,iBAAiB,CAAC3f,SANpB;KATqD,CAAvD;IAmBA,OAAO;QACLG,MADK;QAELmf,cAFK;QAGLC,cAHK;QAIL+G,UAAU,EAAEQ,kBAJP;QAKLJ,UALK;QAMLrnB,SAAS,EAAEwX,QAAQ,GAAG1G,SAAH,GAAe9Q,SAN7B;QAOL4J,IAPK;QAQL3I,IARK;QASLqmB,UATK;QAULC,mBAVK;QAWLjgB;KAXF;AAaD;SCrHeogB;IACd,yKAAO/nB,aAAAA,AAAU,EAAC6gB,aAAD,CAAjB;AACD;ACsBD,MAAMuG,WAAS,GAAG,WAAlB;AAEA,MAAMY,2BAA2B,GAAG;IAClCC,OAAO,EAAE;AADyB,CAApC;AAIA,SAAgBC,aAAAA,IAAAA;QAAa,EAC3BxjB,IAD2B,EAE3BmT,QAAQ,GAAG,KAFgB,EAG3BzW,EAH2B,EAI3B+mB,oBAAAA;IAEA,MAAMrZ,GAAG,gLAAG5M,cAAAA,AAAW,EAACklB,WAAD,CAAvB;IACA,MAAM,EAACjmB,MAAD,EAASR,QAAT,EAAmBW,IAAnB,EAAyBqZ,0BAAAA,uKAC7B3a,aAAAA,AAAU,EAAC4gB,eAAD,CADZ;IAEA,MAAMwH,QAAQ,qKAAGlQ,SAAAA,AAAM,EAAC;QAACL;KAAF,CAAvB;IACA,MAAMwQ,uBAAuB,qKAAGnQ,SAAAA,AAAM,EAAC,KAAD,CAAtC;IACA,MAAMjU,IAAI,qKAAGiU,SAAAA,AAAM,EAAoB,IAApB,CAAnB;IACA,MAAMoQ,UAAU,qKAAGpQ,SAAAA,AAAM,EAAwB,IAAxB,CAAzB;IACA,MAAM,EACJL,QAAQ,EAAE0Q,sBADN,EAEJC,qBAFI,EAGJP,OAAO,EAAEQ,qBAAAA,KACP;QACF,GAAGT,2BADD;QAEF,GAAGG,oBAAAA;KANL;IAQA,MAAMvN,GAAG,gLAAGF,iBAAAA,AAAc,EAAC8N,qBAAD,IAAA,OAACA,qBAAD,GAA0BpnB,EAA1B,CAA1B;IACA,MAAM0a,YAAY,OAAGtb,4KAAAA,AAAW;kDAC9B;YACE,IAAI,CAAC6nB,uBAAuB,CAACjW,OAA7B,EAAsC;;;gBAGpCiW,uBAAuB,CAACjW,OAAxB,GAAkC,IAAlC;gBACA;;YAGF,IAAIkW,UAAU,CAAClW,OAAX,IAAsB,IAA1B,EAAgC;gBAC9BsD,YAAY,CAAC4S,UAAU,CAAClW,OAAZ,CAAZ;;YAGFkW,UAAU,CAAClW,OAAX,GAAqBJ,UAAU;0DAAC;oBAC9B2I,0BAA0B,CACxBsD,KAAK,CAACyK,OAAN,CAAc9N,GAAG,CAACxI,OAAlB,IAA6BwI,GAAG,CAACxI,OAAjC,GAA2C;wBAACwI,GAAG,CAACxI,OAAL;qBADnB,CAA1B;oBAGAkW,UAAU,CAAClW,OAAX,GAAqB,IAArB;iBAJ6B;yDAK5BqW,qBAL4B,CAA/B;SAb4B;iDAqB9B;QAACA,qBAAD;KArB8B,CAAhC;IAuBA,MAAM1M,cAAc,GAAGF,iBAAiB,CAAC;QACvCN,QAAQ,EAAEO,YAD6B;QAEvCjE,QAAQ,EAAE0Q,sBAAsB,IAAI,CAACpnB;KAFC,CAAxC;IAIA,MAAMme,gBAAgB,qKAAG9e,cAAAA,AAAW;sDAClC,CAACmoB,UAAD,EAAiCC,eAAjC;YACE,IAAI,CAAC7M,cAAL,EAAqB;gBACnB;;YAGF,IAAI6M,eAAJ,EAAqB;gBACnB7M,cAAc,CAAC8M,SAAf,CAAyBD,eAAzB;gBACAP,uBAAuB,CAACjW,OAAxB,GAAkC,KAAlC;;YAGF,IAAIuW,UAAJ,EAAgB;gBACd5M,cAAc,CAACe,OAAf,CAAuB6L,UAAvB;;SAZ8B;qDAelC;QAAC5M,cAAD;KAfkC,CAApC;IAiBA,MAAM,CAACwD,OAAD,EAAUoI,UAAV,CAAA,+KAAwBlI,cAAAA,AAAU,EAACH,gBAAD,CAAxC;IACA,MAAMuI,OAAO,gLAAGnN,iBAAc,AAAdA,EAAehW,IAAD,CAA9B;IAEAzE,8KAAAA,AAAS;kCAAC;YACR,IAAI,CAAC8b,cAAD,IAAmB,CAACwD,OAAO,CAACnN,OAAhC,EAAyC;gBACvC;;YAGF2J,cAAc,CAACH,UAAf;YACAyM,uBAAuB,CAACjW,OAAxB,GAAkC,KAAlC;YACA2J,cAAc,CAACe,OAAf,CAAuByC,OAAO,CAACnN,OAA/B;SAPO;iCAQN;QAACmN,OAAD;QAAUxD,cAAV;KARM,CAAT;QAUA9b,0KAAS,AAATA;kCACE;YACEU,QAAQ,CAAC;gBACPC,IAAI,EAAEiC,MAAM,CAAC0e,iBADN;gBAEP/X,OAAO,EAAE;oBACPpI,EADO;oBAEP0N,GAFO;oBAGP+I,QAHO;oBAIP5N,IAAI,EAAEsV,OAJC;oBAKPtb,IALO;oBAMPS,IAAI,EAAEmjB;;aARF,CAAR;YAYA;0CAAO,IACLlnB,QAAQ,CAAC;wBACPC,IAAI,EAAEiC,MAAM,CAAC4e,mBADN;wBAEP3S,GAFO;wBAGP1N;qBAHM,CADV;;SAdK;iCAsBP;QAACA,EAAD;KAtBO,CAAT;sKAyBAnB,YAAAA,AAAS;kCAAC;YACR,IAAI4X,QAAQ,KAAKuQ,QAAQ,CAAChW,OAAT,CAAiByF,QAAlC,EAA4C;gBAC1ClX,QAAQ,CAAC;oBACPC,IAAI,EAAEiC,MAAM,CAAC2e,oBADN;oBAEPpgB,EAFO;oBAGP0N,GAHO;oBAIP+I;iBAJM,CAAR;gBAOAuQ,QAAQ,CAAChW,OAAT,CAAiByF,QAAjB,GAA4BA,QAA5B;;SATK;iCAWN;QAACzW,EAAD;QAAK0N,GAAL;QAAU+I,QAAV;QAAoBlX,QAApB;KAXM,CAAT;IAaA,OAAO;QACLQ,MADK;QAEL8C,IAFK;QAGL6kB,MAAM,EAAE,CAAAxnB,IAAI,IAAA,IAAJ,GAAA,KAAA,IAAAA,IAAI,CAAEF,EAAN,MAAaA,EAHhB;QAIL6I,IAAI,EAAEsV,OAJD;QAKLje,IALK;QAMLqmB;KANF;AAQD;SC/IeoB,iBAAAA,IAAAA;QAAiB,EAACC,SAAD,EAAY7J,QAAAA;IAC3C,MAAM,CACJ8J,cADI,EAEJC,iBAFI,CAAA,qKAGF5oB,WAAAA,AAAQ,EAA4B,IAA5B,CAHZ;IAIA,MAAM,CAACkJ,OAAD,EAAU2f,UAAV,CAAA,qKAAwB7oB,WAAAA,AAAQ,EAAqB,IAArB,CAAtC;IACA,MAAM8oB,gBAAgB,gLAAGvQ,cAAW,AAAXA,EAAYsG,QAAD,CAApC;IAEA,IAAI,CAACA,QAAD,IAAa,CAAC8J,cAAd,IAAgCG,gBAApC,EAAsD;QACpDF,iBAAiB,CAACE,gBAAD,CAAjB;;iLAGFvM,4BAAAA,AAAyB;sDAAC;YACxB,IAAI,CAACrT,OAAL,EAAc;gBACZ;;YAGF,MAAMsF,GAAG,GAAGma,cAAH,IAAA,OAAA,KAAA,IAAGA,cAAc,CAAEna,GAA5B;YACA,MAAM1N,EAAE,GAAG6nB,cAAH,IAAA,OAAA,KAAA,IAAGA,cAAc,CAAE5X,KAAhB,CAAsBjQ,EAAjC;YAEA,IAAI0N,GAAG,IAAI,IAAP,IAAe1N,EAAE,IAAI,IAAzB,EAA+B;gBAC7B8nB,iBAAiB,CAAC,IAAD,CAAjB;gBACA;;YAGFhD,OAAO,CAACC,OAAR,CAAgB6C,SAAS,CAAC5nB,EAAD,EAAKoI,OAAL,CAAzB,EAAwC6f,IAAxC;8DAA6C;oBAC3CH,iBAAiB,CAAC,IAAD,CAAjB;iBADF;;SAbuB;qDAgBtB;QAACF,SAAD;QAAYC,cAAZ;QAA4Bzf,OAA5B;KAhBsB,CAAzB;IAkBA,qKACEhH,UAAAA,CAAAA,aAAA,CAAA,6JAAA,CAAA,UAAA,CAAA,QAAA,EAAA,IAAA,EACG2c,QADH,EAEG8J,cAAc,qKAAGK,eAAAA,AAAY,EAACL,cAAD,EAAiB;QAACM,GAAG,EAAEJ;KAAvB,CAAf,GAAqD,IAFtE,CADF;AAMD;ACzCD,MAAMK,gBAAgB,GAAc;IAClChmB,CAAC,EAAE,CAD+B;IAElCC,CAAC,EAAE,CAF+B;IAGlCqE,MAAM,EAAE,CAH0B;IAIlCC,MAAM,EAAE;AAJ0B,CAApC;AAOA,SAAgB0hB,yBAAAA,IAAAA;QAAyB,EAACtK,QAAAA;IACxC,qKACE3c,UAAAA,CAAAA,aAAA,CAACoe,eAAe,CAACkG,QAAjB,EAAA;QAA0BpkB,KAAK,EAAEge;KAAjC,gKACEle,UAAAA,CAAAA,aAAA,CAACigB,sBAAsB,CAACqE,QAAxB,EAAA;QAAiCpkB,KAAK,EAAE8mB;KAAxC,EACGrK,QADH,CADF,CADF;AAOD;ACAD,MAAMuK,UAAU,GAAwB;IACtCvf,QAAQ,EAAE,OAD4B;IAEtCwf,WAAW,EAAE;AAFyB,CAAxC;AAKA,MAAMC,iBAAiB,IAAsBtJ,cAAD;IAC1C,MAAMuJ,mBAAmB,gLAAGxX,kBAAe,AAAfA,EAAgBiO,cAAD,CAA3C;IAEA,OAAOuJ,mBAAmB,GAAG,sBAAH,GAA4B1Y,SAAtD;AACD,CAJD;AAMO,MAAM2Y,iBAAiB,GAAA,WAAA,qKAAGC,aAAAA,AAAU,EACzC,CAAA,MAYER,GAZF;QACE,EACES,EADF,EAEE1J,cAFF,EAGE5Y,WAHF,EAIEyX,QAJF,EAKE8K,SALF,EAMEhmB,IANF,EAOEimB,KAPF,EAQEviB,SARF,EASEwiB,UAAU,GAAGP,iBAAAA;IAIf,IAAI,CAAC3lB,IAAL,EAAW;QACT,OAAO,IAAP;;IAGF,MAAMmmB,sBAAsB,GAAG1iB,WAAW,GACtCC,SADsC,GAEtC;QACE,GAAGA,SADL;QAEEG,MAAM,EAAE,CAFV;QAGEC,MAAM,EAAE;KALd;IAOA,MAAMsiB,MAAM,GAAoC;QAC9C,GAAGX,UAD2C;QAE9CplB,KAAK,EAAEL,IAAI,CAACK,KAFkC;QAG9CE,MAAM,EAAEP,IAAI,CAACO,MAHiC;QAI9CD,GAAG,EAAEN,IAAI,CAACM,GAJoC;QAK9CF,IAAI,EAAEJ,IAAI,CAACI,IALmC;QAM9CsD,SAAS,2KAAE2iB,MAAG,CAACC,SAAJ,CAAcC,QAAd,CAAuBJ,sBAAvB,CANmC;QAO9ChmB,eAAe,EACbsD,WAAW,IAAI4Y,cAAf,GACItc,0BAA0B,CACxBsc,cADwB,EAExBrc,IAFwB,CAD9B,GAKIkN,SAbwC;QAc9CgZ,UAAU,EACR,OAAOA,UAAP,KAAsB,UAAtB,GACIA,UAAU,CAAC7J,cAAD,CADd,GAEI6J,UAjBwC;QAkB9C,GAAGD,KAAAA;KAlBL;IAqBA,qKAAO1nB,UAAK,CAACioB,aAAN,CACLT,EADK,EAEL;QACEC,SADF;QAEEC,KAAK,EAAEG,MAFT;QAGEd;KALG,EAOLpK,QAPK,CAAP;AASD,CAxDwC,CAApC;MCwDMuL,+BAA+B,GAC1CznB,OAD6C,KAEhB;YAAC,EAAC9B,MAAD,EAAS6e,WAAAA;QACvC,MAAM2K,cAAc,GAA2B,CAAA,CAA/C;QACA,MAAM,EAACN,MAAD,EAASJ,SAAAA,KAAahnB,OAA5B;QAEA,IAAIonB,MAAJ,IAAA,QAAIA,MAAM,CAAElpB,MAAZ,EAAoB;YAClB,KAAK,MAAM,CAAC2N,GAAD,EAAMpM,KAAN,CAAX,IAA2BY,MAAM,CAACya,OAAP,CAAesM,MAAM,CAAClpB,MAAtB,CAA3B,CAA0D;gBACxD,IAAIuB,KAAK,KAAKyO,SAAd,EAAyB;oBACvB;;gBAGFwZ,cAAc,CAAC7b,GAAD,CAAd,GAAsB3N,MAAM,CAAC8I,IAAP,CAAYigB,KAAZ,CAAkBU,gBAAlB,CAAmC9b,GAAnC,CAAtB;gBACA3N,MAAM,CAAC8I,IAAP,CAAYigB,KAAZ,CAAkBW,WAAlB,CAA8B/b,GAA9B,EAAmCpM,KAAnC;;;QAIJ,IAAI2nB,MAAJ,IAAA,QAAIA,MAAM,CAAErK,WAAZ,EAAyB;YACvB,KAAK,MAAM,CAAClR,GAAD,EAAMpM,KAAN,CAAX,IAA2BY,MAAM,CAACya,OAAP,CAAesM,MAAM,CAACrK,WAAtB,CAA3B,CAA+D;gBAC7D,IAAItd,KAAK,KAAKyO,SAAd,EAAyB;oBACvB;;gBAGF6O,WAAW,CAAC/V,IAAZ,CAAiBigB,KAAjB,CAAuBW,WAAvB,CAAmC/b,GAAnC,EAAwCpM,KAAxC;;;QAIJ,IAAIunB,SAAJ,IAAA,QAAIA,SAAS,CAAE9oB,MAAf,EAAuB;YACrBA,MAAM,CAAC8I,IAAP,CAAY6gB,SAAZ,CAAsBrqB,GAAtB,CAA0BwpB,SAAS,CAAC9oB,MAApC;;QAGF,IAAI8oB,SAAJ,IAAA,QAAIA,SAAS,CAAEjK,WAAf,EAA4B;YAC1BA,WAAW,CAAC/V,IAAZ,CAAiB6gB,SAAjB,CAA2BrqB,GAA3B,CAA+BwpB,SAAS,CAACjK,WAAzC;;QAGF,OAAO,SAASlC,OAAT;YACL,KAAK,MAAM,CAAChP,GAAD,EAAMpM,KAAN,CAAX,IAA2BY,MAAM,CAACya,OAAP,CAAe4M,cAAf,CAA3B,CAA2D;gBACzDxpB,MAAM,CAAC8I,IAAP,CAAYigB,KAAZ,CAAkBW,WAAlB,CAA8B/b,GAA9B,EAAmCpM,KAAnC;;YAGF,IAAIunB,SAAJ,IAAA,QAAIA,SAAS,CAAE9oB,MAAf,EAAuB;gBACrBA,MAAM,CAAC8I,IAAP,CAAY6gB,SAAZ,CAAsBC,MAAtB,CAA6Bd,SAAS,CAAC9oB,MAAvC;;SANJ;IASD,CA5CM;AA8CP,MAAM6pB,uBAAuB,IAAqB;IAAA,IAAC,EACjDrjB,SAAS,EAAE,EAACgc,OAAD,EAAUsH,KAAAA,IAD2B,GAAA;IAAA,OAE5C;QACJ;YACEtjB,SAAS,2KAAE2iB,MAAG,CAACC,SAAJ,CAAcC,QAAd,CAAuB7G,OAAvB;SAFT;QAIJ;YACEhc,SAAS,EAAE2iB,+KAAG,CAACC,SAAJ,CAAcC,QAAd,CAAuBS,KAAvB;SALT;KAF4C;AAAA,CAAlD;AAWA,MAAaC,iCAAiC,GAAmC;IAC/EC,QAAQ,EAAE,GADqE;IAE/EC,MAAM,EAAE,MAFuE;IAG/EC,SAAS,EAAEL,uBAHoE;IAI/EM,WAAW,EAAA,WAAA,GAAEZ,+BAA+B,CAAC;QAC3CL,MAAM,EAAE;YACNlpB,MAAM,EAAE;gBACNoqB,OAAO,EAAE;;;KAH6B;AAJmC,CAA1E;AAaP,SAAgBC,iBAAAA,KAAAA;QAAiB,EAC/BtR,MAD+B,EAE/BhB,cAF+B,EAG/B1T,mBAH+B,EAI/Bib,sBAAAA;IAEA,oLAAOhF,WAAAA,AAAQ;qCAAY,CAACra,EAAD,EAAK6I,IAAL;YACzB,IAAIiQ,MAAM,KAAK,IAAf,EAAqB;gBACnB;;YAGF,MAAMuR,eAAe,GAA8BvS,cAAc,CAACvT,GAAf,CAAmBvE,EAAnB,CAAnD;YAEA,IAAI,CAACqqB,eAAL,EAAsB;gBACpB;;YAGF,MAAMvZ,UAAU,GAAGuZ,eAAe,CAACxhB,IAAhB,CAAqBmI,OAAxC;YAEA,IAAI,CAACF,UAAL,EAAiB;gBACf;;YAGF,MAAMwZ,cAAc,GAAGxM,iBAAiB,CAACjV,IAAD,CAAxC;YAEA,IAAI,CAACyhB,cAAL,EAAqB;gBACnB;;YAEF,MAAM,EAAC/jB,SAAAA,KAAa+B,yLAAAA,AAAS,EAACO,IAAD,CAAT,CAAgBN,gBAAhB,CAAiCM,IAAjC,CAApB;YACA,MAAMnB,eAAe,GAAGN,cAAc,CAACb,SAAD,CAAtC;YAEA,IAAI,CAACmB,eAAL,EAAsB;gBACpB;;YAGF,MAAMkgB,SAAS,GACb,OAAO9O,MAAP,KAAkB,UAAlB,GACIA,MADJ,GAEIyR,0BAA0B,CAACzR,MAAD,CAHhC;YAKA/L,sBAAsB,CACpB+D,UADoB,EAEpBuO,sBAAsB,CAACzf,SAAvB,CAAiCoN,OAFb,CAAtB;YAKA,OAAO4a,SAAS,CAAC;gBACf7nB,MAAM,EAAE;oBACNC,EADM;oBAENsD,IAAI,EAAE+mB,eAAe,CAAC/mB,IAFhB;oBAGNuF,IAAI,EAAEiI,UAHA;oBAINjO,IAAI,EAAEwc,sBAAsB,CAACzf,SAAvB,CAAiCoN,OAAjC,CAAyC8D,UAAzC;iBALO;gBAOfgH,cAPe;gBAQf8G,WAAW,EAAE;oBACX/V,IADW;oBAEXhG,IAAI,EAAEwc,sBAAsB,CAACT,WAAvB,CAAmC5R,OAAnC,CAA2Csd,cAA3C;iBAVO;gBAYflmB,mBAZe;gBAafib,sBAbe;gBAcf9Y,SAAS,EAAEmB;aAdG,CAAhB;SAvCa,CAAf;;AAwDD;AAED,SAAS6iB,0BAAT,CACE1oB,OADF;IAGE,MAAM,EAACkoB,QAAD,EAAWC,MAAX,EAAmBE,WAAnB,EAAgCD,SAAAA,KAAa;QACjD,GAAGH,iCAD8C;QAEjD,GAAGjoB,OAAAA;KAFL;IAKA,QAAO;YAAC,EAAC9B,MAAD,EAAS6e,WAAT,EAAsBrY,SAAtB,EAAiC,GAAGikB;QAC1C,IAAI,CAACT,QAAL,EAAe;;YAEb;;QAGF,MAAMvb,KAAK,GAAG;YACZpM,CAAC,EAAEwc,WAAW,CAAC/b,IAAZ,CAAiBI,IAAjB,GAAwBlD,MAAM,CAAC8C,IAAP,CAAYI,IAD3B;YAEZZ,CAAC,EAAEuc,WAAW,CAAC/b,IAAZ,CAAiBM,GAAjB,GAAuBpD,MAAM,CAAC8C,IAAP,CAAYM,GAAAA;SAFxC;QAKA,MAAMsnB,KAAK,GAAG;YACZ/jB,MAAM,EACJH,SAAS,CAACG,MAAV,KAAqB,CAArB,GACK3G,MAAM,CAAC8C,IAAP,CAAYK,KAAZ,GAAoBqD,SAAS,CAACG,MAA/B,GAAyCkY,WAAW,CAAC/b,IAAZ,CAAiBK,KAD9D,GAEI,CAJM;YAKZyD,MAAM,EACJJ,SAAS,CAACI,MAAV,KAAqB,CAArB,GACK5G,MAAM,CAAC8C,IAAP,CAAYO,MAAZ,GAAqBmD,SAAS,CAACI,MAAhC,GAA0CiY,WAAW,CAAC/b,IAAZ,CAAiBO,MAD/D,GAEI;SARR;QAUA,MAAMsnB,cAAc,GAAG;YACrBtoB,CAAC,EAAEmE,SAAS,CAACnE,CAAV,GAAcoM,KAAK,CAACpM,CADF;YAErBC,CAAC,EAAEkE,SAAS,CAAClE,CAAV,GAAcmM,KAAK,CAACnM,CAFF;YAGrB,GAAGooB,KAAAA;SAHL;QAMA,MAAME,kBAAkB,GAAGV,SAAS,CAAC;YACnC,GAAGO,IADgC;YAEnCzqB,MAFmC;YAGnC6e,WAHmC;YAInCrY,SAAS,EAAE;gBAACgc,OAAO,EAAEhc,SAAV;gBAAqBsjB,KAAK,EAAEa;;SAJL,CAApC;QAOA,MAAM,CAACE,aAAD,CAAA,GAAkBD,kBAAxB;QACA,MAAME,YAAY,GAAGF,kBAAkB,CAACA,kBAAkB,CAAC7mB,MAAnB,GAA4B,CAA7B,CAAvC;QAEA,IAAIuT,IAAI,CAACC,SAAL,CAAesT,aAAf,MAAkCvT,IAAI,CAACC,SAAL,CAAeuT,YAAf,CAAtC,EAAoE;;YAElE;;QAGF,MAAMnO,OAAO,GAAGwN,WAAH,IAAA,OAAA,KAAA,IAAGA,WAAW,CAAG;YAACnqB,MAAD;YAAS6e,WAAT;YAAsB,GAAG4L,IAAAA;SAA5B,CAA3B;QACA,MAAM5C,SAAS,GAAGhJ,WAAW,CAAC/V,IAAZ,CAAiBiiB,OAAjB,CAAyBH,kBAAzB,EAA6C;YAC7DZ,QAD6D;YAE7DC,MAF6D;YAG7De,IAAI,EAAE;SAHU,CAAlB;QAMA,OAAO,IAAIjG,OAAJ,EAAaC,OAAD;YACjB6C,SAAS,CAACoD,QAAV,GAAqB;gBACnBtO,OAAO,IAAA,IAAP,GAAA,KAAA,IAAAA,OAAO;gBACPqI,OAAO;aAFT;SADK,CAAP;KAjDF;AAwDD;AC9RD,IAAIrX,GAAG,GAAG,CAAV;AAEA,SAAgBud,OAAOjrB,EAAAA;IACrB,yKAAOiB,UAAAA,AAAO;0BAAC;YACb,IAAIjB,EAAE,IAAI,IAAV,EAAgB;gBACd;;YAGF0N,GAAG;YACH,OAAOA,GAAP;SANY;yBAOX;QAAC1N,EAAD;KAPW,CAAd;AAQD;MCaYkrB,WAAW,GAAA,WAAA,iKAAG9pB,UAAK,CAACogB,IAAN,EACzB;QAAC,EACClb,WAAW,GAAG,KADf,EAECyX,QAFD,EAGCoN,aAAa,EAAEC,mBAHhB,EAICtC,KAJD,EAKCC,UALD,EAMChI,SAND,EAOCsK,cAAc,GAAG,KAPlB,EAQCxC,SARD,EASCyC,MAAM,GAAG,GAAA;IAET,MAAM,EACJpM,cADI,EAEJnf,MAFI,EAGJof,cAHI,EAIJC,iBAJI,EAKJtH,cALI,EAMJ1T,mBANI,EAOJwa,WAPI,EAQJ1e,IARI,EASJmf,sBATI,EAUJzS,mBAVI,EAWJ0J,uBAXI,EAYJoH,UAAAA,KACEiJ,aAAa,EAbjB;IAcA,MAAMpgB,SAAS,qKAAG3H,aAAAA,AAAU,EAACyiB,sBAAD,CAA5B;IACA,MAAM3T,GAAG,GAAGud,MAAM,CAAClrB,MAAD,IAAA,OAAA,KAAA,IAACA,MAAM,CAAEC,EAAT,CAAlB;IACA,MAAMurB,iBAAiB,GAAGzK,cAAc,CAACC,SAAD,EAAY;QAClD7B,cADkD;QAElDnf,MAFkD;QAGlDof,cAHkD;QAIlDC,iBAJkD;QAKlDoE,gBAAgB,EAAE5E,WAAW,CAAC/b,IALoB;QAMlD3C,IANkD;QAOlD4jB,eAAe,EAAElF,WAAW,CAAC/b,IAPqB;QAQlD+J,mBARkD;QASlD0J,uBATkD;QAUlD/P,SAVkD;QAWlDmX;KAXsC,CAAxC;IAaA,MAAM3B,WAAW,GAAGhC,eAAe,CAACoF,cAAD,CAAnC;IACA,MAAMgM,aAAa,GAAGf,gBAAgB,CAAC;QACrCtR,MAAM,EAAEsS,mBAD6B;QAErCtT,cAFqC;QAGrC1T,mBAHqC;QAIrCib;KAJoC,CAAtC,EAAA,4FAAA;;IAQA,MAAM8I,GAAG,GAAGpM,WAAW,GAAG6C,WAAW,CAACR,MAAf,GAAwBrO,SAA/C;IAEA,OACE3O,wKAAAA,CAAAA,aAAA,CAACinB,wBAAD,EAAA,IAAA,gKACEjnB,UAAAA,CAAAA,aAAA,CAACumB,gBAAD,EAAA;QAAkBC,SAAS,EAAEuD;KAA7B,EACGprB,MAAM,IAAI2N,GAAV,iKACCtM,UAAAA,CAAAA,aAAA,CAACsnB,iBAAD,EAAA;QACEhb,GAAG,EAAEA;QACL1N,EAAE,EAAED,MAAM,CAACC,EAAAA;QACXmoB,GAAG,EAAEA;QACLS,EAAE,EAAEyC;QACJnM,cAAc,EAAEA;QAChB5Y,WAAW,EAAEA;QACbuiB,SAAS,EAAEA;QACXE,UAAU,EAAEA;QACZlmB,IAAI,EAAEkZ;QACN+M,KAAK,EAAE;YACLwC,MADK;YAEL,GAAGxC,KAAAA;;QAELviB,SAAS,EAAEglB;KAdb,EAgBGxN,QAhBH,CADD,GAmBG,IApBN,CADF,CADF;AA0BD,CA9EwB,CAApB", "ignoreList": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84], "debugId": null}}]}
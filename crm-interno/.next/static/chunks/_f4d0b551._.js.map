{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/AI_Projects/CRMcriadores/crm-interno/app/actions/sheetsActions.ts"], "sourcesContent": ["'use server';\n\nimport { google } from 'googleapis';\n\n// Configuração da autenticação\nconst getGoogleSheetsAuth = () => {\n  const credentials = {\n    type: 'service_account',\n    project_id: process.env.GOOGLE_PROJECT_ID,\n    private_key_id: process.env.GOOGLE_PRIVATE_KEY_ID,\n    private_key: process.env.GOOGLE_PRIVATE_KEY?.replace(/\\\\n/g, '\\n'),\n    client_email: process.env.GOOGLE_CLIENT_EMAIL,\n    client_id: process.env.GOOGLE_CLIENT_ID,\n    auth_uri: 'https://accounts.google.com/o/oauth2/auth',\n    token_uri: 'https://oauth2.googleapis.com/token',\n    auth_provider_x509_cert_url: 'https://www.googleapis.com/oauth2/v1/certs',\n    client_x509_cert_url: `https://www.googleapis.com/robot/v1/metadata/x509/${process.env.GOOGLE_CLIENT_EMAIL}`,\n  };\n\n  const auth = new google.auth.GoogleAuth({\n    credentials,\n    scopes: ['https://www.googleapis.com/auth/spreadsheets'],\n  });\n\n  return auth;\n};\n\n// Função para ler dados da planilha\nexport async function getData(sheetName: string) {\n  try {\n    const auth = getGoogleSheetsAuth();\n    const sheets = google.sheets({ version: 'v4', auth });\n    \n    const spreadsheetId = process.env.GOOGLE_SPREADSHEET_ID;\n    \n    if (!spreadsheetId) {\n      throw new Error('GOOGLE_SPREADSHEET_ID não está configurado');\n    }\n\n    const response = await sheets.spreadsheets.values.get({\n      spreadsheetId,\n      range: `${sheetName}!A:Z`, // Lê todas as colunas de A até Z\n    });\n\n    return response.data.values || [];\n  } catch (error) {\n    console.error('Erro ao ler dados da planilha:', error);\n    throw new Error('Falha ao ler dados da planilha');\n  }\n}\n\n// Função para adicionar dados à planilha\nexport async function appendData(sheetName: string, rowData: any[]) {\n  try {\n    const auth = getGoogleSheetsAuth();\n    const sheets = google.sheets({ version: 'v4', auth });\n    \n    const spreadsheetId = process.env.GOOGLE_SPREADSHEET_ID;\n    \n    if (!spreadsheetId) {\n      throw new Error('GOOGLE_SPREADSHEET_ID não está configurado');\n    }\n\n    const response = await sheets.spreadsheets.values.append({\n      spreadsheetId,\n      range: `${sheetName}!A:Z`,\n      valueInputOption: 'USER_ENTERED',\n      requestBody: {\n        values: [rowData],\n      },\n    });\n\n    return response.data;\n  } catch (error) {\n    console.error('Erro ao adicionar dados à planilha:', error);\n    throw new Error('Falha ao adicionar dados à planilha');\n  }\n}\n\n// Função para atualizar dados na planilha\nexport async function updateData(sheetName: string, range: string, rowData: any[]) {\n  try {\n    const auth = getGoogleSheetsAuth();\n    const sheets = google.sheets({ version: 'v4', auth });\n\n    const spreadsheetId = process.env.GOOGLE_SPREADSHEET_ID;\n\n    if (!spreadsheetId) {\n      throw new Error('GOOGLE_SPREADSHEET_ID não está configurado');\n    }\n\n    const response = await sheets.spreadsheets.values.update({\n      spreadsheetId,\n      range: `${sheetName}!${range}`,\n      valueInputOption: 'USER_ENTERED',\n      requestBody: {\n        values: [rowData],\n      },\n    });\n\n    return response.data;\n  } catch (error) {\n    console.error('Erro ao atualizar dados da planilha:', error);\n    throw new Error('Falha ao atualizar dados da planilha');\n  }\n}\n\n// Função específica para atualizar o estágio da jornada de um negócio\nexport async function updateBusinessStage(businessId: string, newStage: string) {\n  try {\n    // Se Google Sheets não estiver configurado, simula sucesso\n    const spreadsheetId = process.env.GOOGLE_SPREADSHEET_ID;\n\n    if (!spreadsheetId) {\n      console.log(`Simulando atualização: Negócio ${businessId} movido para ${newStage}`);\n      return { success: true, message: 'Atualização simulada com sucesso' };\n    }\n\n    const auth = getGoogleSheetsAuth();\n    const sheets = google.sheets({ version: 'v4', auth });\n\n    // Primeiro, busca todos os dados para encontrar a linha do negócio\n    const allData = await sheets.spreadsheets.values.get({\n      spreadsheetId,\n      range: 'Businesses!A:Z',\n    });\n\n    const rows = allData.data.values || [];\n    if (rows.length === 0) {\n      throw new Error('Nenhum dado encontrado na planilha');\n    }\n\n    // Encontra a linha do negócio pelo ID\n    const headers = rows[0];\n    const idColumnIndex = headers.findIndex((header: string) =>\n      header.toLowerCase().includes('id')\n    );\n    const stageColumnIndex = headers.findIndex((header: string) =>\n      header.toLowerCase().includes('stage') || header.toLowerCase().includes('estágio')\n    );\n\n    if (idColumnIndex === -1 || stageColumnIndex === -1) {\n      throw new Error('Colunas ID ou Stage não encontradas');\n    }\n\n    // Procura pela linha do negócio\n    let targetRowIndex = -1;\n    for (let i = 1; i < rows.length; i++) {\n      if (rows[i][idColumnIndex] === businessId) {\n        targetRowIndex = i + 1; // +1 porque as linhas do Sheets são 1-indexed\n        break;\n      }\n    }\n\n    if (targetRowIndex === -1) {\n      throw new Error(`Negócio com ID ${businessId} não encontrado`);\n    }\n\n    // Atualiza apenas a célula do estágio\n    const stageColumn = String.fromCharCode(65 + stageColumnIndex); // Converte índice para letra (A, B, C...)\n    const range = `${stageColumn}${targetRowIndex}`;\n\n    const response = await sheets.spreadsheets.values.update({\n      spreadsheetId,\n      range: `Businesses!${range}`,\n      valueInputOption: 'USER_ENTERED',\n      requestBody: {\n        values: [[newStage]],\n      },\n    });\n\n    return { success: true, data: response.data };\n  } catch (error) {\n    console.error('Erro ao atualizar estágio do negócio:', error);\n    throw new Error('Falha ao atualizar estágio do negócio');\n  }\n}\n"], "names": [], "mappings": ";;;;;;IA4BsB,UAAA,WAAA,GAAA,CAAA,GAAA,yNAAA,CAAA,wBAAA,EAAA,8CAAA,yNAAA,CAAA,aAAA,EAAA,KAAA,GAAA,yNAAA,CAAA,mBAAA,EAAA", "debugId": null}}, {"offset": {"line": 23, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/AI_Projects/CRMcriadores/crm-interno/lib/utils.ts"], "sourcesContent": ["/**\n * Transforma dados de array de arrays (formato do Google Sheets) \n * em array de objetos JSON usando a primeira linha como cabeçalhos\n */\nexport function transformData(data: any[][]): Record<string, any>[] {\n  if (!data || data.length === 0) {\n    return [];\n  }\n\n  // A primeira linha contém os cabeçalhos\n  const headers = data[0];\n  \n  // As linhas restantes contêm os dados\n  const rows = data.slice(1);\n\n  return rows.map((row) => {\n    const obj: Record<string, any> = {};\n    \n    headers.forEach((header, index) => {\n      // Usa o cabeçalho como chave e o valor da linha correspondente\n      obj[header] = row[index] || '';\n    });\n\n    return obj;\n  });\n}\n\n/**\n * Converte um objeto em array de valores na ordem dos cabeçalhos fornecidos\n */\nexport function objectToRowData(obj: Record<string, any>, headers: string[]): any[] {\n  return headers.map(header => obj[header] || '');\n}\n\n/**\n * Valida se os dados têm a estrutura esperada\n */\nexport function validateSheetData(data: any[][]): boolean {\n  return Array.isArray(data) && data.length > 0 && Array.isArray(data[0]);\n}\n\n/**\n * Limpa e normaliza strings vindas do Google Sheets\n */\nexport function cleanSheetValue(value: any): string {\n  if (value === null || value === undefined) {\n    return '';\n  }\n  \n  return String(value).trim();\n}\n\n/**\n * Converte valores de string para tipos apropriados\n */\nexport function parseSheetValue(value: string, type: 'string' | 'number' | 'boolean' | 'date' = 'string'): any {\n  const cleanValue = cleanSheetValue(value);\n  \n  if (cleanValue === '') {\n    return type === 'number' ? 0 : type === 'boolean' ? false : '';\n  }\n\n  switch (type) {\n    case 'number':\n      const num = parseFloat(cleanValue);\n      return isNaN(num) ? 0 : num;\n    \n    case 'boolean':\n      return cleanValue.toLowerCase() === 'true' || cleanValue === '1';\n    \n    case 'date':\n      const date = new Date(cleanValue);\n      return isNaN(date.getTime()) ? null : date;\n    \n    default:\n      return cleanValue;\n  }\n}\n\n/**\n * Formata dados para exibição\n */\nexport function formatDisplayValue(value: any, type: 'currency' | 'percentage' | 'date' | 'number' | 'text' = 'text'): string {\n  if (value === null || value === undefined || value === '') {\n    return '-';\n  }\n\n  switch (type) {\n    case 'currency':\n      const numValue = typeof value === 'number' ? value : parseFloat(value);\n      return isNaN(numValue) ? '-' : new Intl.NumberFormat('pt-BR', {\n        style: 'currency',\n        currency: 'BRL'\n      }).format(numValue);\n    \n    case 'percentage':\n      const pctValue = typeof value === 'number' ? value : parseFloat(value);\n      return isNaN(pctValue) ? '-' : `${pctValue.toFixed(1)}%`;\n    \n    case 'date':\n      const date = value instanceof Date ? value : new Date(value);\n      return isNaN(date.getTime()) ? '-' : date.toLocaleDateString('pt-BR');\n    \n    case 'number':\n      const numberValue = typeof value === 'number' ? value : parseFloat(value);\n      return isNaN(numberValue) ? '-' : new Intl.NumberFormat('pt-BR').format(numberValue);\n    \n    default:\n      return String(value);\n  }\n}\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;;;;AACM,SAAS,cAAc,IAAa;IACzC,IAAI,CAAC,QAAQ,KAAK,MAAM,KAAK,GAAG;QAC9B,OAAO,EAAE;IACX;IAEA,wCAAwC;IACxC,MAAM,UAAU,IAAI,CAAC,EAAE;IAEvB,sCAAsC;IACtC,MAAM,OAAO,KAAK,KAAK,CAAC;IAExB,OAAO,KAAK,GAAG,CAAC,CAAC;QACf,MAAM,MAA2B,CAAC;QAElC,QAAQ,OAAO,CAAC,CAAC,QAAQ;YACvB,+DAA+D;YAC/D,GAAG,CAAC,OAAO,GAAG,GAAG,CAAC,MAAM,IAAI;QAC9B;QAEA,OAAO;IACT;AACF;AAKO,SAAS,gBAAgB,GAAwB,EAAE,OAAiB;IACzE,OAAO,QAAQ,GAAG,CAAC,CAAA,SAAU,GAAG,CAAC,OAAO,IAAI;AAC9C;AAKO,SAAS,kBAAkB,IAAa;IAC7C,OAAO,MAAM,OAAO,CAAC,SAAS,KAAK,MAAM,GAAG,KAAK,MAAM,OAAO,CAAC,IAAI,CAAC,EAAE;AACxE;AAKO,SAAS,gBAAgB,KAAU;IACxC,IAAI,UAAU,QAAQ,UAAU,WAAW;QACzC,OAAO;IACT;IAEA,OAAO,OAAO,OAAO,IAAI;AAC3B;AAKO,SAAS,gBAAgB,KAAa,EAAE,OAAiD,QAAQ;IACtG,MAAM,aAAa,gBAAgB;IAEnC,IAAI,eAAe,IAAI;QACrB,OAAO,SAAS,WAAW,IAAI,SAAS,YAAY,QAAQ;IAC9D;IAEA,OAAQ;QACN,KAAK;YACH,MAAM,MAAM,WAAW;YACvB,OAAO,MAAM,OAAO,IAAI;QAE1B,KAAK;YACH,OAAO,WAAW,WAAW,OAAO,UAAU,eAAe;QAE/D,KAAK;YACH,MAAM,OAAO,IAAI,KAAK;YACtB,OAAO,MAAM,KAAK,OAAO,MAAM,OAAO;QAExC;YACE,OAAO;IACX;AACF;AAKO,SAAS,mBAAmB,KAAU,EAAE,OAA+D,MAAM;IAClH,IAAI,UAAU,QAAQ,UAAU,aAAa,UAAU,IAAI;QACzD,OAAO;IACT;IAEA,OAAQ;QACN,KAAK;YACH,MAAM,WAAW,OAAO,UAAU,WAAW,QAAQ,WAAW;YAChE,OAAO,MAAM,YAAY,MAAM,IAAI,KAAK,YAAY,CAAC,SAAS;gBAC5D,OAAO;gBACP,UAAU;YACZ,GAAG,MAAM,CAAC;QAEZ,KAAK;YACH,MAAM,WAAW,OAAO,UAAU,WAAW,QAAQ,WAAW;YAChE,OAAO,MAAM,YAAY,MAAM,GAAG,SAAS,OAAO,CAAC,GAAG,CAAC,CAAC;QAE1D,KAAK;YACH,MAAM,OAAO,iBAAiB,OAAO,QAAQ,IAAI,KAAK;YACtD,OAAO,MAAM,KAAK,OAAO,MAAM,MAAM,KAAK,kBAAkB,CAAC;QAE/D,KAAK;YACH,MAAM,cAAc,OAAO,UAAU,WAAW,QAAQ,WAAW;YACnE,OAAO,MAAM,eAAe,MAAM,IAAI,KAAK,YAAY,CAAC,SAAS,MAAM,CAAC;QAE1E;YACE,OAAO,OAAO;IAClB;AACF", "debugId": null}}, {"offset": {"line": 114, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/AI_Projects/CRMcriadores/crm-interno/components/BusinessDetailModal.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport Image from 'next/image';\n\ninterface Influencer {\n  name: string;\n  username: string;\n  followers: number;\n  engagementRate: number;\n}\n\ninterface Campaign {\n  title: string;\n  status: string;\n  startDate: string;\n  endDate: string;\n}\n\ninterface Business {\n  id: number;\n  businessName: string;\n  journeyStage: string;\n  nextAction: string;\n  contactDate: string;\n  value: number;\n  description: string;\n  influencers: Influencer[];\n  campaigns: Campaign[];\n}\n\ninterface BusinessDetailModalProps {\n  business: Business | null;\n  isOpen: boolean;\n  onClose: () => void;\n}\n\nexport default function BusinessDetailModal({ business, isOpen, onClose }: BusinessDetailModalProps) {\n  if (!isOpen || !business) return null;\n\n  const formatFollowers = (count: number): string => {\n    if (count >= 1000000) {\n      return `${(count / 1000000).toFixed(1)}M`;\n    } else if (count >= 1000) {\n      return `${(count / 1000).toFixed(1)}K`;\n    }\n    return count.toString();\n  };\n\n  const getStatusColor = (status: string): string => {\n    const statusColors: Record<string, string> = {\n      'Ativa': 'bg-green-100 text-green-800',\n      'Planejamento': 'bg-blue-100 text-blue-800',\n      'Em Aprovação': 'bg-yellow-100 text-yellow-800',\n      'Pausada': 'bg-orange-100 text-orange-800',\n      'Finalizada': 'bg-gray-100 text-gray-800',\n      'Cancelada': 'bg-red-100 text-red-800',\n    };\n    return statusColors[status] || 'bg-gray-100 text-gray-800';\n  };\n\n  return (\n    <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\">\n      <div className=\"bg-surface rounded-2xl max-w-4xl w-full max-h-[90vh] overflow-y-auto shadow-2xl\">\n        {/* Header */}\n        <div className=\"sticky top-0 bg-surface border-b border-outline-variant p-6 flex items-center justify-between\">\n          <div>\n            <h2 className=\"text-2xl font-bold text-on-surface\">{business.businessName}</h2>\n            <p className=\"text-on-surface-variant mt-1\">Detalhes do Projeto</p>\n          </div>\n          <button\n            onClick={onClose}\n            className=\"p-2 hover:bg-surface-container rounded-full transition-colors\"\n          >\n            <span className=\"text-2xl\">✕</span>\n          </button>\n        </div>\n\n        {/* Content */}\n        <div className=\"p-6 space-y-6\">\n          {/* Informações Gerais */}\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n            <div className=\"card-elevated p-4\">\n              <h3 className=\"font-semibold text-on-surface mb-3\">📊 Informações do Projeto</h3>\n              <div className=\"space-y-3\">\n                <div>\n                  <span className=\"text-sm text-on-surface-variant\">Fase Atual:</span>\n                  <p className=\"font-medium text-on-surface\">{business.journeyStage}</p>\n                </div>\n                <div>\n                  <span className=\"text-sm text-on-surface-variant\">Valor do Projeto:</span>\n                  <p className=\"font-medium text-primary text-lg\">\n                    R$ {business.value.toLocaleString('pt-BR')}\n                  </p>\n                </div>\n                <div>\n                  <span className=\"text-sm text-on-surface-variant\">Data de Contato:</span>\n                  <p className=\"font-medium text-on-surface\">\n                    {new Date(business.contactDate).toLocaleDateString('pt-BR')}\n                  </p>\n                </div>\n              </div>\n            </div>\n\n            <div className=\"card-elevated p-4\">\n              <h3 className=\"font-semibold text-on-surface mb-3\">🎯 Próxima Ação</h3>\n              <p className=\"text-on-surface\">{business.nextAction}</p>\n            </div>\n          </div>\n\n          {/* Descrição */}\n          <div className=\"card-elevated p-4\">\n            <h3 className=\"font-semibold text-on-surface mb-3\">📝 Descrição do Projeto</h3>\n            <p className=\"text-on-surface leading-relaxed\">{business.description}</p>\n          </div>\n\n          {/* Influenciadores */}\n          <div className=\"card-elevated p-4\">\n            <h3 className=\"font-semibold text-on-surface mb-4\">\n              👥 Influenciadores Contratados ({business.influencers.length})\n            </h3>\n            \n            {business.influencers.length > 0 ? (\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                {business.influencers.map((influencer, index) => (\n                  <div key={index} className=\"bg-surface-container rounded-lg p-4\">\n                    <div className=\"flex items-center space-x-3\">\n                      <div className=\"relative w-12 h-12\">\n                        <Image\n                          src=\"/placeholder-avatar.svg\"\n                          alt={`Avatar de ${influencer.name}`}\n                          fill\n                          className=\"rounded-full object-cover\"\n                          sizes=\"48px\"\n                        />\n                      </div>\n                      <div className=\"flex-1\">\n                        <h4 className=\"font-medium text-on-surface\">{influencer.name}</h4>\n                        <p className=\"text-sm text-on-surface-variant\">@{influencer.username}</p>\n                      </div>\n                    </div>\n                    \n                    <div className=\"grid grid-cols-2 gap-4 mt-3\">\n                      <div className=\"text-center\">\n                        <div className=\"text-lg font-bold text-primary\">\n                          {formatFollowers(influencer.followers)}\n                        </div>\n                        <div className=\"text-xs text-on-surface-variant\">Seguidores</div>\n                      </div>\n                      <div className=\"text-center\">\n                        <div className=\"text-lg font-bold text-secondary\">\n                          {influencer.engagementRate.toFixed(1)}%\n                        </div>\n                        <div className=\"text-xs text-on-surface-variant\">Engajamento</div>\n                      </div>\n                    </div>\n                  </div>\n                ))}\n              </div>\n            ) : (\n              <div className=\"text-center py-8 text-on-surface-variant\">\n                <div className=\"text-4xl mb-2\">👥</div>\n                <p>Nenhum influenciador contratado ainda</p>\n              </div>\n            )}\n          </div>\n\n          {/* Campanhas */}\n          <div className=\"card-elevated p-4\">\n            <h3 className=\"font-semibold text-on-surface mb-4\">\n              📢 Campanhas Relacionadas ({business.campaigns.length})\n            </h3>\n            \n            {business.campaigns.length > 0 ? (\n              <div className=\"space-y-3\">\n                {business.campaigns.map((campaign, index) => (\n                  <div key={index} className=\"bg-surface-container rounded-lg p-4\">\n                    <div className=\"flex items-center justify-between mb-2\">\n                      <h4 className=\"font-medium text-on-surface\">{campaign.title}</h4>\n                      <span className={`px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(campaign.status)}`}>\n                        {campaign.status}\n                      </span>\n                    </div>\n                    <div className=\"grid grid-cols-2 gap-4 text-sm\">\n                      <div>\n                        <span className=\"text-on-surface-variant\">Início:</span>\n                        <span className=\"ml-2 text-on-surface\">\n                          {new Date(campaign.startDate).toLocaleDateString('pt-BR')}\n                        </span>\n                      </div>\n                      <div>\n                        <span className=\"text-on-surface-variant\">Fim:</span>\n                        <span className=\"ml-2 text-on-surface\">\n                          {new Date(campaign.endDate).toLocaleDateString('pt-BR')}\n                        </span>\n                      </div>\n                    </div>\n                  </div>\n                ))}\n              </div>\n            ) : (\n              <div className=\"text-center py-8 text-on-surface-variant\">\n                <div className=\"text-4xl mb-2\">📢</div>\n                <p>Nenhuma campanha criada ainda</p>\n              </div>\n            )}\n          </div>\n        </div>\n\n        {/* Footer */}\n        <div className=\"sticky bottom-0 bg-surface border-t border-outline-variant p-6 flex justify-end space-x-3\">\n          <button onClick={onClose} className=\"btn-outlined\">\n            Fechar\n          </button>\n          <button className=\"btn-primary\">\n            <span className=\"mr-2\">✏️</span>\n            Editar Projeto\n          </button>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAGA;AAHA;;;AAqCe,SAAS,oBAAoB,EAAE,QAAQ,EAAE,MAAM,EAAE,OAAO,EAA4B;IACjG,IAAI,CAAC,UAAU,CAAC,UAAU,OAAO;IAEjC,MAAM,kBAAkB,CAAC;QACvB,IAAI,SAAS,SAAS;YACpB,OAAO,GAAG,CAAC,QAAQ,OAAO,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC;QAC3C,OAAO,IAAI,SAAS,MAAM;YACxB,OAAO,GAAG,CAAC,QAAQ,IAAI,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC;QACxC;QACA,OAAO,MAAM,QAAQ;IACvB;IAEA,MAAM,iBAAiB,CAAC;QACtB,MAAM,eAAuC;YAC3C,SAAS;YACT,gBAAgB;YAChB,gBAAgB;YAChB,WAAW;YACX,cAAc;YACd,aAAa;QACf;QACA,OAAO,YAAY,CAAC,OAAO,IAAI;IACjC;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;;8CACC,6LAAC;oCAAG,WAAU;8CAAsC,SAAS,YAAY;;;;;;8CACzE,6LAAC;oCAAE,WAAU;8CAA+B;;;;;;;;;;;;sCAE9C,6LAAC;4BACC,SAAS;4BACT,WAAU;sCAEV,cAAA,6LAAC;gCAAK,WAAU;0CAAW;;;;;;;;;;;;;;;;;8BAK/B,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAAqC;;;;;;sDACnD,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;;sEACC,6LAAC;4DAAK,WAAU;sEAAkC;;;;;;sEAClD,6LAAC;4DAAE,WAAU;sEAA+B,SAAS,YAAY;;;;;;;;;;;;8DAEnE,6LAAC;;sEACC,6LAAC;4DAAK,WAAU;sEAAkC;;;;;;sEAClD,6LAAC;4DAAE,WAAU;;gEAAmC;gEAC1C,SAAS,KAAK,CAAC,cAAc,CAAC;;;;;;;;;;;;;8DAGtC,6LAAC;;sEACC,6LAAC;4DAAK,WAAU;sEAAkC;;;;;;sEAClD,6LAAC;4DAAE,WAAU;sEACV,IAAI,KAAK,SAAS,WAAW,EAAE,kBAAkB,CAAC;;;;;;;;;;;;;;;;;;;;;;;;8CAM3D,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAAqC;;;;;;sDACnD,6LAAC;4CAAE,WAAU;sDAAmB,SAAS,UAAU;;;;;;;;;;;;;;;;;;sCAKvD,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAAqC;;;;;;8CACnD,6LAAC;oCAAE,WAAU;8CAAmC,SAAS,WAAW;;;;;;;;;;;;sCAItE,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;;wCAAqC;wCAChB,SAAS,WAAW,CAAC,MAAM;wCAAC;;;;;;;gCAG9D,SAAS,WAAW,CAAC,MAAM,GAAG,kBAC7B,6LAAC;oCAAI,WAAU;8CACZ,SAAS,WAAW,CAAC,GAAG,CAAC,CAAC,YAAY,sBACrC,6LAAC;4CAAgB,WAAU;;8DACzB,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC,gIAAA,CAAA,UAAK;gEACJ,KAAI;gEACJ,KAAK,CAAC,UAAU,EAAE,WAAW,IAAI,EAAE;gEACnC,IAAI;gEACJ,WAAU;gEACV,OAAM;;;;;;;;;;;sEAGV,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAG,WAAU;8EAA+B,WAAW,IAAI;;;;;;8EAC5D,6LAAC;oEAAE,WAAU;;wEAAkC;wEAAE,WAAW,QAAQ;;;;;;;;;;;;;;;;;;;8DAIxE,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;8EACZ,gBAAgB,WAAW,SAAS;;;;;;8EAEvC,6LAAC;oEAAI,WAAU;8EAAkC;;;;;;;;;;;;sEAEnD,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;;wEACZ,WAAW,cAAc,CAAC,OAAO,CAAC;wEAAG;;;;;;;8EAExC,6LAAC;oEAAI,WAAU;8EAAkC;;;;;;;;;;;;;;;;;;;2CA5B7C;;;;;;;;;yDAmCd,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDAAgB;;;;;;sDAC/B,6LAAC;sDAAE;;;;;;;;;;;;;;;;;;sCAMT,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;;wCAAqC;wCACrB,SAAS,SAAS,CAAC,MAAM;wCAAC;;;;;;;gCAGvD,SAAS,SAAS,CAAC,MAAM,GAAG,kBAC3B,6LAAC;oCAAI,WAAU;8CACZ,SAAS,SAAS,CAAC,GAAG,CAAC,CAAC,UAAU,sBACjC,6LAAC;4CAAgB,WAAU;;8DACzB,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAG,WAAU;sEAA+B,SAAS,KAAK;;;;;;sEAC3D,6LAAC;4DAAK,WAAW,CAAC,2CAA2C,EAAE,eAAe,SAAS,MAAM,GAAG;sEAC7F,SAAS,MAAM;;;;;;;;;;;;8DAGpB,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;;8EACC,6LAAC;oEAAK,WAAU;8EAA0B;;;;;;8EAC1C,6LAAC;oEAAK,WAAU;8EACb,IAAI,KAAK,SAAS,SAAS,EAAE,kBAAkB,CAAC;;;;;;;;;;;;sEAGrD,6LAAC;;8EACC,6LAAC;oEAAK,WAAU;8EAA0B;;;;;;8EAC1C,6LAAC;oEAAK,WAAU;8EACb,IAAI,KAAK,SAAS,OAAO,EAAE,kBAAkB,CAAC;;;;;;;;;;;;;;;;;;;2CAjB7C;;;;;;;;;yDAyBd,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDAAgB;;;;;;sDAC/B,6LAAC;sDAAE;;;;;;;;;;;;;;;;;;;;;;;;8BAOX,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAO,SAAS;4BAAS,WAAU;sCAAe;;;;;;sCAGnD,6LAAC;4BAAO,WAAU;;8CAChB,6LAAC;oCAAK,WAAU;8CAAO;;;;;;gCAAS;;;;;;;;;;;;;;;;;;;;;;;;AAO5C;KAzLwB", "debugId": null}}, {"offset": {"line": 745, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/AI_Projects/CRMcriadores/crm-interno/app/%28dashboard%29/jornada/page.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport { getData, updateBusinessStage } from '@/app/actions/sheetsActions';\nimport { transformData } from '@/lib/utils';\nimport BusinessDetailModal from '@/components/BusinessDetailModal';\nimport {\n  DndContext,\n  DragEndEvent,\n  DragOverlay,\n  DragStartEvent,\n  PointerSensor,\n  useSensor,\n  useSensors,\n  closestCorners,\n} from '@dnd-kit/core';\nimport {\n  SortableContext,\n  verticalListSortingStrategy,\n} from '@dnd-kit/sortable';\nimport DraggableBusinessCard from '@/components/DraggableBusinessCard';\n\n// Dados de exemplo para demonstração (serão substituídos pelos dados do Google Sheets)\nconst mockBusinesses = [\n  {\n    id: 1,\n    businessName: 'Loja de Roupas Fashion',\n    journeyStage: 'Agendamentos',\n    nextAction: 'Agendar sessões de fotos com influenciadores',\n    contactDate: '2024-01-15',\n    value: 15000,\n    description: '<PERSON><PERSON><PERSON> de verão focada em roupas casuais para jovens de 18-30 anos',\n    influencers: [\n      { name: '<PERSON> <PERSON>', username: 'an<PERSON><PERSON><PERSON>', followers: 125000, engagementRate: 4.2 },\n      { name: '<PERSON>', username: 'carlossantos', followers: 89000, engagementRate: 6.8 }\n    ],\n    campaigns: [\n      { title: 'Campanha Verão 2024', status: 'Ativa', startDate: '2024-01-15', endDate: '2024-03-15' }\n    ]\n  },\n  {\n    id: 2,\n    businessName: 'Restaurante Gourmet',\n    journeyStage: 'Reunião Briefing',\n    nextAction: 'Definir estratégia de conteúdo gastronômico',\n    contactDate: '2024-01-10',\n    value: 8000,\n    description: 'Divulgação de pratos especiais e experiência gastronômica única',\n    influencers: [\n      { name: 'Maria Oliveira', username: 'mariaoliveira', followers: 234000, engagementRate: 3.1 }\n    ],\n    campaigns: []\n  },\n  {\n    id: 3,\n    businessName: 'Academia Fitness Plus',\n    journeyStage: 'Entrega Final',\n    nextAction: 'Finalizar edição dos vídeos de treino',\n    contactDate: '2024-01-20',\n    value: 25000,\n    description: 'Campanha de motivação fitness com foco em resultados reais',\n    influencers: [\n      { name: 'João Fitness', username: 'joaofitness', followers: 156000, engagementRate: 5.4 },\n      { name: 'Carla Strong', username: 'carlastrong', followers: 98000, engagementRate: 7.2 },\n      { name: 'Pedro Muscle', username: 'pedromuscle', followers: 67000, engagementRate: 4.8 }\n    ],\n    campaigns: [\n      { title: 'Transformação 90 Dias', status: 'Ativa', startDate: '2024-01-01', endDate: '2024-03-31' }\n    ]\n  },\n  {\n    id: 4,\n    businessName: 'Clínica de Estética',\n    journeyStage: 'Reunião Briefing',\n    nextAction: 'Alinhar diretrizes de comunicação sobre procedimentos',\n    contactDate: '2024-01-12',\n    value: 12000,\n    description: 'Divulgação de tratamentos estéticos com foco em naturalidade',\n    influencers: [\n      { name: 'Bella Beauty', username: 'bellabeauty', followers: 189000, engagementRate: 6.1 }\n    ],\n    campaigns: []\n  },\n  {\n    id: 5,\n    businessName: 'Loja de Eletrônicos',\n    journeyStage: 'Agendamentos',\n    nextAction: 'Coordenar reviews de produtos com tech influencers',\n    contactDate: '2024-01-08',\n    value: 18000,\n    description: 'Reviews autênticos de gadgets e eletrônicos inovadores',\n    influencers: [\n      { name: 'Tech Master', username: 'techmaster', followers: 145000, engagementRate: 5.9 },\n      { name: 'Gamer Pro', username: 'gamerpro', followers: 203000, engagementRate: 4.5 }\n    ],\n    campaigns: [\n      { title: 'Tech Reviews 2024', status: 'Planejamento', startDate: '2024-02-01', endDate: '2024-04-30' }\n    ]\n  }\n];\n\n// Definir as fases da jornada (3 fases principais)\nconst journeyStages = [\n  { id: 'Reunião Briefing', label: 'Reunião Briefing', color: 'bg-blue-100 text-blue-800', icon: '📋' },\n  { id: 'Agendamentos', label: 'Agendamentos', color: 'bg-yellow-100 text-yellow-800', icon: '📅' },\n  { id: 'Entrega Final', label: 'Entrega Final', color: 'bg-green-100 text-green-800', icon: '✅' }\n];\n\nexport default function JornadaPage() {\n  const [businesses, setBusinesses] = useState(mockBusinesses);\n  const [selectedBusiness, setSelectedBusiness] = useState<any>(null);\n  const [isModalOpen, setIsModalOpen] = useState(false);\n  const [activeId, setActiveId] = useState<string | null>(null);\n  const [isUpdating, setIsUpdating] = useState(false);\n\n  // Configurar sensores para drag & drop\n  const sensors = useSensors(\n    useSensor(PointerSensor, {\n      activationConstraint: {\n        distance: 8, // Mínimo de 8px de movimento para ativar o drag\n      },\n    })\n  );\n\n  // Carregar dados do Google Sheets\n  React.useEffect(() => {\n    async function loadData() {\n      try {\n        const rawData = await getData('Businesses');\n        if (rawData && rawData.length > 0) {\n          const transformedData = transformData(rawData);\n\n          // Mapeia os dados transformados para o formato esperado pelo componente\n          const businessesData = transformedData.map((item: any) => ({\n            id: item.id || Math.random(),\n            businessName: item.businessName || item.Nome || item['Nome do Negócio'] || 'Negócio não informado',\n            journeyStage: item.journeyStage || item.Estágio || item['Estágio da Jornada'] || 'Reunião Briefing',\n            nextAction: item.nextAction || item.Ação || item['Próxima Ação'] || 'Definir próxima ação',\n            contactDate: item.contactDate || item.Data || item['Data de Contato'] || new Date().toISOString().split('T')[0],\n            value: parseInt(item.value || item.Valor || item['Valor do Negócio'] || '0'),\n            description: item.description || item.Descrição || 'Descrição não informada',\n            influencers: JSON.parse(item.influencers || '[]'),\n            campaigns: JSON.parse(item.campaigns || '[]')\n          }));\n          setBusinesses(businessesData);\n        }\n      } catch (error) {\n        console.log('Usando dados de exemplo - Google Sheets não configurado ainda');\n      }\n    }\n\n    loadData();\n  }, []);\n\n  const handleBusinessClick = (business: any) => {\n    setSelectedBusiness(business);\n    setIsModalOpen(true);\n  };\n\n  const handleCloseModal = () => {\n    setIsModalOpen(false);\n    setSelectedBusiness(null);\n  };\n\n\n\n  // Agrupar negócios por fase da jornada\n  const businessesByStage = journeyStages.map(stage => ({\n    ...stage,\n    businesses: businesses.filter(business => business.journeyStage === stage.id),\n    totalValue: businesses\n      .filter(business => business.journeyStage === stage.id)\n      .reduce((sum, business) => sum + business.value, 0)\n  }));\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header com estatísticas gerais */}\n      <div className=\"grid grid-cols-1 md:grid-cols-4 gap-4\">\n        <div className=\"card-elevated p-4\">\n          <div className=\"flex items-center justify-between\">\n            <div>\n              <p className=\"text-sm text-on-surface-variant\">Total de Negócios</p>\n              <p className=\"text-2xl font-bold text-on-surface\">{businesses.length}</p>\n            </div>\n            <div className=\"text-2xl\">🏢</div>\n          </div>\n        </div>\n        \n        <div className=\"card-elevated p-4\">\n          <div className=\"flex items-center justify-between\">\n            <div>\n              <p className=\"text-sm text-on-surface-variant\">Valor Total</p>\n              <p className=\"text-2xl font-bold text-primary\">\n                R$ {(businesses.reduce((sum, b) => sum + b.value, 0) / 1000).toFixed(0)}K\n              </p>\n            </div>\n            <div className=\"text-2xl\">💰</div>\n          </div>\n        </div>\n        \n        <div className=\"card-elevated p-4\">\n          <div className=\"flex items-center justify-between\">\n            <div>\n              <p className=\"text-sm text-on-surface-variant\">Em Fechamento</p>\n              <p className=\"text-2xl font-bold text-green-600\">\n                {businesses.filter(b => b.journeyStage === 'Fechamento').length}\n              </p>\n            </div>\n            <div className=\"text-2xl\">🎯</div>\n          </div>\n        </div>\n        \n        <div className=\"card-elevated p-4\">\n          <div className=\"flex items-center justify-between\">\n            <div>\n              <p className=\"text-sm text-on-surface-variant\">Taxa Conversão</p>\n              <p className=\"text-2xl font-bold text-secondary\">\n                {businesses.length > 0 ? Math.round((businesses.filter(b => b.journeyStage === 'Pós-venda').length / businesses.length) * 100) : 0}%\n              </p>\n            </div>\n            <div className=\"text-2xl\">📈</div>\n          </div>\n        </div>\n      </div>\n\n      {/* Kanban da Jornada - 3 Colunas */}\n      <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-6\">\n        {businessesByStage.map((stage) => (\n          <div key={stage.id} className=\"card-elevated p-6 min-h-96\">\n            {/* Header da coluna */}\n            <div className=\"mb-6\">\n              <div className=\"flex items-center justify-between mb-3\">\n                <div className=\"flex items-center\">\n                  <span className=\"text-2xl mr-3\">{stage.icon}</span>\n                  <h3 className=\"text-lg font-semibold text-on-surface\">{stage.label}</h3>\n                </div>\n                <span className=\"text-sm bg-surface-container px-3 py-1 rounded-full font-medium\">\n                  {stage.businesses.length}\n                </span>\n              </div>\n\n              <div className=\"text-sm text-on-surface-variant\">\n                Total: R$ {(stage.totalValue / 1000).toFixed(0)}K\n              </div>\n            </div>\n\n            {/* Cards dos negócios */}\n            <div className=\"space-y-4\">\n              {stage.businesses.map((business, index) => (\n                <div\n                  key={index}\n                  className=\"bg-surface-container rounded-lg p-4 hover:shadow-md transition-all duration-200 cursor-pointer border-l-4 border-primary\"\n                  onClick={() => handleBusinessClick(business)}\n                >\n                  <h4 className=\"font-semibold text-on-surface mb-2\">\n                    {business.businessName}\n                  </h4>\n\n                  <p className=\"text-sm text-on-surface-variant mb-3 line-clamp-2\">\n                    {business.nextAction}\n                  </p>\n\n                  {/* Influenciadores */}\n                  <div className=\"flex items-center mb-3\">\n                    <span className=\"text-xs text-on-surface-variant mr-2\">👥</span>\n                    <span className=\"text-sm font-medium text-secondary\">\n                      {business.influencers.length} influenciador{business.influencers.length !== 1 ? 'es' : ''}\n                    </span>\n                  </div>\n\n                  <div className=\"flex items-center justify-between text-sm\">\n                    <span className=\"text-on-surface-variant\">\n                      {new Date(business.contactDate).toLocaleDateString('pt-BR')}\n                    </span>\n                    <span className=\"font-bold text-primary\">\n                      R$ {(business.value / 1000).toFixed(0)}K\n                    </span>\n                  </div>\n\n                  {/* Indicador de clique */}\n                  <div className=\"mt-2 text-xs text-on-surface-variant opacity-70\">\n                    Clique para ver detalhes →\n                  </div>\n                </div>\n              ))}\n\n              {stage.businesses.length === 0 && (\n                <div className=\"text-center py-12 text-on-surface-variant\">\n                  <div className=\"text-4xl mb-3\">{stage.icon}</div>\n                  <p className=\"text-sm\">Nenhum negócio nesta fase</p>\n                  <p className=\"text-xs mt-1\">Arraste projetos para cá</p>\n                </div>\n              )}\n            </div>\n          </div>\n        ))}\n      </div>\n\n      {/* Modal de Detalhes */}\n      <BusinessDetailModal\n        business={selectedBusiness}\n        isOpen={isModalOpen}\n        onClose={handleCloseModal}\n      />\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;;;AANA;;;;;;AAsBA,uFAAuF;AACvF,MAAM,iBAAiB;IACrB;QACE,IAAI;QACJ,cAAc;QACd,cAAc;QACd,YAAY;QACZ,aAAa;QACb,OAAO;QACP,aAAa;QACb,aAAa;YACX;gBAAE,MAAM;gBAAa,UAAU;gBAAY,WAAW;gBAAQ,gBAAgB;YAAI;YAClF;gBAAE,MAAM;gBAAiB,UAAU;gBAAgB,WAAW;gBAAO,gBAAgB;YAAI;SAC1F;QACD,WAAW;YACT;gBAAE,OAAO;gBAAuB,QAAQ;gBAAS,WAAW;gBAAc,SAAS;YAAa;SACjG;IACH;IACA;QACE,IAAI;QACJ,cAAc;QACd,cAAc;QACd,YAAY;QACZ,aAAa;QACb,OAAO;QACP,aAAa;QACb,aAAa;YACX;gBAAE,MAAM;gBAAkB,UAAU;gBAAiB,WAAW;gBAAQ,gBAAgB;YAAI;SAC7F;QACD,WAAW,EAAE;IACf;IACA;QACE,IAAI;QACJ,cAAc;QACd,cAAc;QACd,YAAY;QACZ,aAAa;QACb,OAAO;QACP,aAAa;QACb,aAAa;YACX;gBAAE,MAAM;gBAAgB,UAAU;gBAAe,WAAW;gBAAQ,gBAAgB;YAAI;YACxF;gBAAE,MAAM;gBAAgB,UAAU;gBAAe,WAAW;gBAAO,gBAAgB;YAAI;YACvF;gBAAE,MAAM;gBAAgB,UAAU;gBAAe,WAAW;gBAAO,gBAAgB;YAAI;SACxF;QACD,WAAW;YACT;gBAAE,OAAO;gBAAyB,QAAQ;gBAAS,WAAW;gBAAc,SAAS;YAAa;SACnG;IACH;IACA;QACE,IAAI;QACJ,cAAc;QACd,cAAc;QACd,YAAY;QACZ,aAAa;QACb,OAAO;QACP,aAAa;QACb,aAAa;YACX;gBAAE,MAAM;gBAAgB,UAAU;gBAAe,WAAW;gBAAQ,gBAAgB;YAAI;SACzF;QACD,WAAW,EAAE;IACf;IACA;QACE,IAAI;QACJ,cAAc;QACd,cAAc;QACd,YAAY;QACZ,aAAa;QACb,OAAO;QACP,aAAa;QACb,aAAa;YACX;gBAAE,MAAM;gBAAe,UAAU;gBAAc,WAAW;gBAAQ,gBAAgB;YAAI;YACtF;gBAAE,MAAM;gBAAa,UAAU;gBAAY,WAAW;gBAAQ,gBAAgB;YAAI;SACnF;QACD,WAAW;YACT;gBAAE,OAAO;gBAAqB,QAAQ;gBAAgB,WAAW;gBAAc,SAAS;YAAa;SACtG;IACH;CACD;AAED,mDAAmD;AACnD,MAAM,gBAAgB;IACpB;QAAE,IAAI;QAAoB,OAAO;QAAoB,OAAO;QAA6B,MAAM;IAAK;IACpG;QAAE,IAAI;QAAgB,OAAO;QAAgB,OAAO;QAAiC,MAAM;IAAK;IAChG;QAAE,IAAI;QAAiB,OAAO;QAAiB,OAAO;QAA+B,MAAM;IAAI;CAChG;AAEc,SAAS;;IACtB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAO;IAC9D,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IACxD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,uCAAuC;IACvC,MAAM,UAAU,CAAA,GAAA,8JAAA,CAAA,aAAU,AAAD,EACvB,CAAA,GAAA,8JAAA,CAAA,YAAS,AAAD,EAAE,8JAAA,CAAA,gBAAa,EAAE;QACvB,sBAAsB;YACpB,UAAU;QACZ;IACF;IAGF,kCAAkC;IAClC,6JAAA,CAAA,UAAK,CAAC,SAAS;iCAAC;YACd,eAAe;gBACb,IAAI;oBACF,MAAM,UAAU,MAAM,CAAA,GAAA,yJAAA,CAAA,UAAO,AAAD,EAAE;oBAC9B,IAAI,WAAW,QAAQ,MAAM,GAAG,GAAG;wBACjC,MAAM,kBAAkB,CAAA,GAAA,+GAAA,CAAA,gBAAa,AAAD,EAAE;wBAEtC,wEAAwE;wBACxE,MAAM,iBAAiB,gBAAgB,GAAG;6EAAC,CAAC,OAAc,CAAC;oCACzD,IAAI,KAAK,EAAE,IAAI,KAAK,MAAM;oCAC1B,cAAc,KAAK,YAAY,IAAI,KAAK,IAAI,IAAI,IAAI,CAAC,kBAAkB,IAAI;oCAC3E,cAAc,KAAK,YAAY,IAAI,KAAK,OAAO,IAAI,IAAI,CAAC,qBAAqB,IAAI;oCACjF,YAAY,KAAK,UAAU,IAAI,KAAK,IAAI,IAAI,IAAI,CAAC,eAAe,IAAI;oCACpE,aAAa,KAAK,WAAW,IAAI,KAAK,IAAI,IAAI,IAAI,CAAC,kBAAkB,IAAI,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;oCAC/G,OAAO,SAAS,KAAK,KAAK,IAAI,KAAK,KAAK,IAAI,IAAI,CAAC,mBAAmB,IAAI;oCACxE,aAAa,KAAK,WAAW,IAAI,KAAK,SAAS,IAAI;oCACnD,aAAa,KAAK,KAAK,CAAC,KAAK,WAAW,IAAI;oCAC5C,WAAW,KAAK,KAAK,CAAC,KAAK,SAAS,IAAI;gCAC1C,CAAC;;wBACD,cAAc;oBAChB;gBACF,EAAE,OAAO,OAAO;oBACd,QAAQ,GAAG,CAAC;gBACd;YACF;YAEA;QACF;gCAAG,EAAE;IAEL,MAAM,sBAAsB,CAAC;QAC3B,oBAAoB;QACpB,eAAe;IACjB;IAEA,MAAM,mBAAmB;QACvB,eAAe;QACf,oBAAoB;IACtB;IAIA,uCAAuC;IACvC,MAAM,oBAAoB,cAAc,GAAG,CAAC,CAAA,QAAS,CAAC;YACpD,GAAG,KAAK;YACR,YAAY,WAAW,MAAM,CAAC,CAAA,WAAY,SAAS,YAAY,KAAK,MAAM,EAAE;YAC5E,YAAY,WACT,MAAM,CAAC,CAAA,WAAY,SAAS,YAAY,KAAK,MAAM,EAAE,EACrD,MAAM,CAAC,CAAC,KAAK,WAAa,MAAM,SAAS,KAAK,EAAE;QACrD,CAAC;IAED,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;;sDACC,6LAAC;4CAAE,WAAU;sDAAkC;;;;;;sDAC/C,6LAAC;4CAAE,WAAU;sDAAsC,WAAW,MAAM;;;;;;;;;;;;8CAEtE,6LAAC;oCAAI,WAAU;8CAAW;;;;;;;;;;;;;;;;;kCAI9B,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;;sDACC,6LAAC;4CAAE,WAAU;sDAAkC;;;;;;sDAC/C,6LAAC;4CAAE,WAAU;;gDAAkC;gDACzC,CAAC,WAAW,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,EAAE,KAAK,EAAE,KAAK,IAAI,EAAE,OAAO,CAAC;gDAAG;;;;;;;;;;;;;8CAG5E,6LAAC;oCAAI,WAAU;8CAAW;;;;;;;;;;;;;;;;;kCAI9B,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;;sDACC,6LAAC;4CAAE,WAAU;sDAAkC;;;;;;sDAC/C,6LAAC;4CAAE,WAAU;sDACV,WAAW,MAAM,CAAC,CAAA,IAAK,EAAE,YAAY,KAAK,cAAc,MAAM;;;;;;;;;;;;8CAGnE,6LAAC;oCAAI,WAAU;8CAAW;;;;;;;;;;;;;;;;;kCAI9B,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;;sDACC,6LAAC;4CAAE,WAAU;sDAAkC;;;;;;sDAC/C,6LAAC;4CAAE,WAAU;;gDACV,WAAW,MAAM,GAAG,IAAI,KAAK,KAAK,CAAC,AAAC,WAAW,MAAM,CAAC,CAAA,IAAK,EAAE,YAAY,KAAK,aAAa,MAAM,GAAG,WAAW,MAAM,GAAI,OAAO;gDAAE;;;;;;;;;;;;;8CAGvI,6LAAC;oCAAI,WAAU;8CAAW;;;;;;;;;;;;;;;;;;;;;;;0BAMhC,6LAAC;gBAAI,WAAU;0BACZ,kBAAkB,GAAG,CAAC,CAAC,sBACtB,6LAAC;wBAAmB,WAAU;;0CAE5B,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAK,WAAU;kEAAiB,MAAM,IAAI;;;;;;kEAC3C,6LAAC;wDAAG,WAAU;kEAAyC,MAAM,KAAK;;;;;;;;;;;;0DAEpE,6LAAC;gDAAK,WAAU;0DACb,MAAM,UAAU,CAAC,MAAM;;;;;;;;;;;;kDAI5B,6LAAC;wCAAI,WAAU;;4CAAkC;4CACpC,CAAC,MAAM,UAAU,GAAG,IAAI,EAAE,OAAO,CAAC;4CAAG;;;;;;;;;;;;;0CAKpD,6LAAC;gCAAI,WAAU;;oCACZ,MAAM,UAAU,CAAC,GAAG,CAAC,CAAC,UAAU,sBAC/B,6LAAC;4CAEC,WAAU;4CACV,SAAS,IAAM,oBAAoB;;8DAEnC,6LAAC;oDAAG,WAAU;8DACX,SAAS,YAAY;;;;;;8DAGxB,6LAAC;oDAAE,WAAU;8DACV,SAAS,UAAU;;;;;;8DAItB,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAK,WAAU;sEAAuC;;;;;;sEACvD,6LAAC;4DAAK,WAAU;;gEACb,SAAS,WAAW,CAAC,MAAM;gEAAC;gEAAe,SAAS,WAAW,CAAC,MAAM,KAAK,IAAI,OAAO;;;;;;;;;;;;;8DAI3F,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAK,WAAU;sEACb,IAAI,KAAK,SAAS,WAAW,EAAE,kBAAkB,CAAC;;;;;;sEAErD,6LAAC;4DAAK,WAAU;;gEAAyB;gEACnC,CAAC,SAAS,KAAK,GAAG,IAAI,EAAE,OAAO,CAAC;gEAAG;;;;;;;;;;;;;8DAK3C,6LAAC;oDAAI,WAAU;8DAAkD;;;;;;;2CA9B5D;;;;;oCAoCR,MAAM,UAAU,CAAC,MAAM,KAAK,mBAC3B,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DAAiB,MAAM,IAAI;;;;;;0DAC1C,6LAAC;gDAAE,WAAU;0DAAU;;;;;;0DACvB,6LAAC;gDAAE,WAAU;0DAAe;;;;;;;;;;;;;;;;;;;uBA9D1B,MAAM,EAAE;;;;;;;;;;0BAuEtB,6LAAC,qIAAA,CAAA,UAAmB;gBAClB,UAAU;gBACV,QAAQ;gBACR,SAAS;;;;;;;;;;;;AAIjB;GAvMwB;;QAQN,8JAAA,CAAA,aAAU;;;KARJ", "debugId": null}}]}
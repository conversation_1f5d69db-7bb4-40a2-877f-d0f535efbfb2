(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/app/actions/data:6d9d53 [app-client] (ecmascript) <text/javascript>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
/* __next_internal_action_entry_do_not_use__ [{"40416fe263e60966356356bb294165af55bf27c98c":"getData"},"app/actions/sheetsActions.ts",""] */ __turbopack_context__.s({
    "getData": (()=>getData)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/webpack/loaders/next-flight-loader/action-client-wrapper.js [app-client] (ecmascript)");
"use turbopack no side effects";
;
var getData = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createServerReference"])("40416fe263e60966356356bb294165af55bf27c98c", __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["callServer"], void 0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["findSourceMapURL"], "getData"); //# sourceMappingURL=data:application/json;base64,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
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/app/actions/data:3ad5f4 [app-client] (ecmascript) <text/javascript>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
/* __next_internal_action_entry_do_not_use__ [{"60c2e3b2963d1dbcaed9ada159154eae380101e11a":"updateBusinessStage"},"app/actions/sheetsActions.ts",""] */ __turbopack_context__.s({
    "updateBusinessStage": (()=>updateBusinessStage)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/webpack/loaders/next-flight-loader/action-client-wrapper.js [app-client] (ecmascript)");
"use turbopack no side effects";
;
var updateBusinessStage = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createServerReference"])("60c2e3b2963d1dbcaed9ada159154eae380101e11a", __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["callServer"], void 0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["findSourceMapURL"], "updateBusinessStage"); //# sourceMappingURL=data:application/json;base64,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
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/lib/utils.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
/**
 * Transforma dados de array de arrays (formato do Google Sheets) 
 * em array de objetos JSON usando a primeira linha como cabeçalhos
 */ __turbopack_context__.s({
    "cleanSheetValue": (()=>cleanSheetValue),
    "formatDisplayValue": (()=>formatDisplayValue),
    "objectToRowData": (()=>objectToRowData),
    "parseSheetValue": (()=>parseSheetValue),
    "transformData": (()=>transformData),
    "validateSheetData": (()=>validateSheetData)
});
function transformData(data) {
    if (!data || data.length === 0) {
        return [];
    }
    // A primeira linha contém os cabeçalhos
    const headers = data[0];
    // As linhas restantes contêm os dados
    const rows = data.slice(1);
    return rows.map((row)=>{
        const obj = {};
        headers.forEach((header, index)=>{
            // Usa o cabeçalho como chave e o valor da linha correspondente
            obj[header] = row[index] || '';
        });
        return obj;
    });
}
function objectToRowData(obj, headers) {
    return headers.map((header)=>obj[header] || '');
}
function validateSheetData(data) {
    return Array.isArray(data) && data.length > 0 && Array.isArray(data[0]);
}
function cleanSheetValue(value) {
    if (value === null || value === undefined) {
        return '';
    }
    return String(value).trim();
}
function parseSheetValue(value, type = 'string') {
    const cleanValue = cleanSheetValue(value);
    if (cleanValue === '') {
        return type === 'number' ? 0 : type === 'boolean' ? false : '';
    }
    switch(type){
        case 'number':
            const num = parseFloat(cleanValue);
            return isNaN(num) ? 0 : num;
        case 'boolean':
            return cleanValue.toLowerCase() === 'true' || cleanValue === '1';
        case 'date':
            const date = new Date(cleanValue);
            return isNaN(date.getTime()) ? null : date;
        default:
            return cleanValue;
    }
}
function formatDisplayValue(value, type = 'text') {
    if (value === null || value === undefined || value === '') {
        return '-';
    }
    switch(type){
        case 'currency':
            const numValue = typeof value === 'number' ? value : parseFloat(value);
            return isNaN(numValue) ? '-' : new Intl.NumberFormat('pt-BR', {
                style: 'currency',
                currency: 'BRL'
            }).format(numValue);
        case 'percentage':
            const pctValue = typeof value === 'number' ? value : parseFloat(value);
            return isNaN(pctValue) ? '-' : `${pctValue.toFixed(1)}%`;
        case 'date':
            const date = value instanceof Date ? value : new Date(value);
            return isNaN(date.getTime()) ? '-' : date.toLocaleDateString('pt-BR');
        case 'number':
            const numberValue = typeof value === 'number' ? value : parseFloat(value);
            return isNaN(numberValue) ? '-' : new Intl.NumberFormat('pt-BR').format(numberValue);
        default:
            return String(value);
    }
}
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/components/BusinessDetailModal.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>BusinessDetailModal)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$image$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/image.js [app-client] (ecmascript)");
'use client';
;
;
function BusinessDetailModal({ business, isOpen, onClose }) {
    if (!isOpen || !business) return null;
    const formatFollowers = (count)=>{
        if (count >= 1000000) {
            return `${(count / 1000000).toFixed(1)}M`;
        } else if (count >= 1000) {
            return `${(count / 1000).toFixed(1)}K`;
        }
        return count.toString();
    };
    const getStatusColor = (status)=>{
        const statusColors = {
            'Ativa': 'bg-green-100 text-green-800',
            'Planejamento': 'bg-blue-100 text-blue-800',
            'Em Aprovação': 'bg-yellow-100 text-yellow-800',
            'Pausada': 'bg-orange-100 text-orange-800',
            'Finalizada': 'bg-gray-100 text-gray-800',
            'Cancelada': 'bg-red-100 text-red-800'
        };
        return statusColors[status] || 'bg-gray-100 text-gray-800';
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "bg-surface rounded-2xl max-w-4xl w-full max-h-[90vh] overflow-y-auto shadow-2xl",
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "sticky top-0 bg-surface border-b border-outline-variant p-6 flex items-center justify-between",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h2", {
                                    className: "text-2xl font-bold text-on-surface",
                                    children: business.businessName
                                }, void 0, false, {
                                    fileName: "[project]/components/BusinessDetailModal.tsx",
                                    lineNumber: 68,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                    className: "text-on-surface-variant mt-1",
                                    children: "Detalhes do Projeto"
                                }, void 0, false, {
                                    fileName: "[project]/components/BusinessDetailModal.tsx",
                                    lineNumber: 69,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/components/BusinessDetailModal.tsx",
                            lineNumber: 67,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                            onClick: onClose,
                            className: "p-2 hover:bg-surface-container rounded-full transition-colors",
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                className: "text-2xl",
                                children: "✕"
                            }, void 0, false, {
                                fileName: "[project]/components/BusinessDetailModal.tsx",
                                lineNumber: 75,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/components/BusinessDetailModal.tsx",
                            lineNumber: 71,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/components/BusinessDetailModal.tsx",
                    lineNumber: 66,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "p-6 space-y-6",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "grid grid-cols-1 md:grid-cols-2 gap-6",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "card-elevated p-4",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                            className: "font-semibold text-on-surface mb-3",
                                            children: "📊 Informações do Projeto"
                                        }, void 0, false, {
                                            fileName: "[project]/components/BusinessDetailModal.tsx",
                                            lineNumber: 84,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "space-y-3",
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                            className: "text-sm text-on-surface-variant",
                                                            children: "Fase Atual:"
                                                        }, void 0, false, {
                                                            fileName: "[project]/components/BusinessDetailModal.tsx",
                                                            lineNumber: 87,
                                                            columnNumber: 19
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                            className: "font-medium text-on-surface",
                                                            children: business.journeyStage
                                                        }, void 0, false, {
                                                            fileName: "[project]/components/BusinessDetailModal.tsx",
                                                            lineNumber: 88,
                                                            columnNumber: 19
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/components/BusinessDetailModal.tsx",
                                                    lineNumber: 86,
                                                    columnNumber: 17
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                            className: "text-sm text-on-surface-variant",
                                                            children: "Valor do Projeto:"
                                                        }, void 0, false, {
                                                            fileName: "[project]/components/BusinessDetailModal.tsx",
                                                            lineNumber: 91,
                                                            columnNumber: 19
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                            className: "font-medium text-primary text-lg",
                                                            children: [
                                                                "R$ ",
                                                                business.value.toLocaleString('pt-BR')
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "[project]/components/BusinessDetailModal.tsx",
                                                            lineNumber: 92,
                                                            columnNumber: 19
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/components/BusinessDetailModal.tsx",
                                                    lineNumber: 90,
                                                    columnNumber: 17
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                            className: "text-sm text-on-surface-variant",
                                                            children: "Data de Contato:"
                                                        }, void 0, false, {
                                                            fileName: "[project]/components/BusinessDetailModal.tsx",
                                                            lineNumber: 97,
                                                            columnNumber: 19
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                            className: "font-medium text-on-surface",
                                                            children: new Date(business.contactDate).toLocaleDateString('pt-BR')
                                                        }, void 0, false, {
                                                            fileName: "[project]/components/BusinessDetailModal.tsx",
                                                            lineNumber: 98,
                                                            columnNumber: 19
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/components/BusinessDetailModal.tsx",
                                                    lineNumber: 96,
                                                    columnNumber: 17
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/components/BusinessDetailModal.tsx",
                                            lineNumber: 85,
                                            columnNumber: 15
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/components/BusinessDetailModal.tsx",
                                    lineNumber: 83,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "card-elevated p-4",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                            className: "font-semibold text-on-surface mb-3",
                                            children: "🎯 Próxima Ação"
                                        }, void 0, false, {
                                            fileName: "[project]/components/BusinessDetailModal.tsx",
                                            lineNumber: 106,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                            className: "text-on-surface",
                                            children: business.nextAction
                                        }, void 0, false, {
                                            fileName: "[project]/components/BusinessDetailModal.tsx",
                                            lineNumber: 107,
                                            columnNumber: 15
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/components/BusinessDetailModal.tsx",
                                    lineNumber: 105,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/components/BusinessDetailModal.tsx",
                            lineNumber: 82,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "card-elevated p-4",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                    className: "font-semibold text-on-surface mb-3",
                                    children: "📝 Descrição do Projeto"
                                }, void 0, false, {
                                    fileName: "[project]/components/BusinessDetailModal.tsx",
                                    lineNumber: 113,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                    className: "text-on-surface leading-relaxed",
                                    children: business.description
                                }, void 0, false, {
                                    fileName: "[project]/components/BusinessDetailModal.tsx",
                                    lineNumber: 114,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/components/BusinessDetailModal.tsx",
                            lineNumber: 112,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "card-elevated p-4",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                    className: "font-semibold text-on-surface mb-4",
                                    children: [
                                        "👥 Influenciadores Contratados (",
                                        business.influencers.length,
                                        ")"
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/components/BusinessDetailModal.tsx",
                                    lineNumber: 119,
                                    columnNumber: 13
                                }, this),
                                business.influencers.length > 0 ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "grid grid-cols-1 md:grid-cols-2 gap-4",
                                    children: business.influencers.map((influencer, index)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "bg-surface-container rounded-lg p-4",
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: "flex items-center space-x-3",
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                            className: "relative w-12 h-12",
                                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$image$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                                                src: "/placeholder-avatar.svg",
                                                                alt: `Avatar de ${influencer.name}`,
                                                                fill: true,
                                                                className: "rounded-full object-cover",
                                                                sizes: "48px"
                                                            }, void 0, false, {
                                                                fileName: "[project]/components/BusinessDetailModal.tsx",
                                                                lineNumber: 129,
                                                                columnNumber: 25
                                                            }, this)
                                                        }, void 0, false, {
                                                            fileName: "[project]/components/BusinessDetailModal.tsx",
                                                            lineNumber: 128,
                                                            columnNumber: 23
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                            className: "flex-1",
                                                            children: [
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h4", {
                                                                    className: "font-medium text-on-surface",
                                                                    children: influencer.name
                                                                }, void 0, false, {
                                                                    fileName: "[project]/components/BusinessDetailModal.tsx",
                                                                    lineNumber: 138,
                                                                    columnNumber: 25
                                                                }, this),
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                                    className: "text-sm text-on-surface-variant",
                                                                    children: [
                                                                        "@",
                                                                        influencer.username
                                                                    ]
                                                                }, void 0, true, {
                                                                    fileName: "[project]/components/BusinessDetailModal.tsx",
                                                                    lineNumber: 139,
                                                                    columnNumber: 25
                                                                }, this)
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "[project]/components/BusinessDetailModal.tsx",
                                                            lineNumber: 137,
                                                            columnNumber: 23
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/components/BusinessDetailModal.tsx",
                                                    lineNumber: 127,
                                                    columnNumber: 21
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: "grid grid-cols-2 gap-4 mt-3",
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                            className: "text-center",
                                                            children: [
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                    className: "text-lg font-bold text-primary",
                                                                    children: formatFollowers(influencer.followers)
                                                                }, void 0, false, {
                                                                    fileName: "[project]/components/BusinessDetailModal.tsx",
                                                                    lineNumber: 145,
                                                                    columnNumber: 25
                                                                }, this),
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                    className: "text-xs text-on-surface-variant",
                                                                    children: "Seguidores"
                                                                }, void 0, false, {
                                                                    fileName: "[project]/components/BusinessDetailModal.tsx",
                                                                    lineNumber: 148,
                                                                    columnNumber: 25
                                                                }, this)
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "[project]/components/BusinessDetailModal.tsx",
                                                            lineNumber: 144,
                                                            columnNumber: 23
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                            className: "text-center",
                                                            children: [
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                    className: "text-lg font-bold text-secondary",
                                                                    children: [
                                                                        influencer.engagementRate.toFixed(1),
                                                                        "%"
                                                                    ]
                                                                }, void 0, true, {
                                                                    fileName: "[project]/components/BusinessDetailModal.tsx",
                                                                    lineNumber: 151,
                                                                    columnNumber: 25
                                                                }, this),
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                    className: "text-xs text-on-surface-variant",
                                                                    children: "Engajamento"
                                                                }, void 0, false, {
                                                                    fileName: "[project]/components/BusinessDetailModal.tsx",
                                                                    lineNumber: 154,
                                                                    columnNumber: 25
                                                                }, this)
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "[project]/components/BusinessDetailModal.tsx",
                                                            lineNumber: 150,
                                                            columnNumber: 23
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/components/BusinessDetailModal.tsx",
                                                    lineNumber: 143,
                                                    columnNumber: 21
                                                }, this)
                                            ]
                                        }, index, true, {
                                            fileName: "[project]/components/BusinessDetailModal.tsx",
                                            lineNumber: 126,
                                            columnNumber: 19
                                        }, this))
                                }, void 0, false, {
                                    fileName: "[project]/components/BusinessDetailModal.tsx",
                                    lineNumber: 124,
                                    columnNumber: 15
                                }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "text-center py-8 text-on-surface-variant",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "text-4xl mb-2",
                                            children: "👥"
                                        }, void 0, false, {
                                            fileName: "[project]/components/BusinessDetailModal.tsx",
                                            lineNumber: 162,
                                            columnNumber: 17
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                            children: "Nenhum influenciador contratado ainda"
                                        }, void 0, false, {
                                            fileName: "[project]/components/BusinessDetailModal.tsx",
                                            lineNumber: 163,
                                            columnNumber: 17
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/components/BusinessDetailModal.tsx",
                                    lineNumber: 161,
                                    columnNumber: 15
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/components/BusinessDetailModal.tsx",
                            lineNumber: 118,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "card-elevated p-4",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                    className: "font-semibold text-on-surface mb-4",
                                    children: [
                                        "📢 Campanhas Relacionadas (",
                                        business.campaigns.length,
                                        ")"
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/components/BusinessDetailModal.tsx",
                                    lineNumber: 170,
                                    columnNumber: 13
                                }, this),
                                business.campaigns.length > 0 ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "space-y-3",
                                    children: business.campaigns.map((campaign, index)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "bg-surface-container rounded-lg p-4",
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: "flex items-center justify-between mb-2",
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h4", {
                                                            className: "font-medium text-on-surface",
                                                            children: campaign.title
                                                        }, void 0, false, {
                                                            fileName: "[project]/components/BusinessDetailModal.tsx",
                                                            lineNumber: 179,
                                                            columnNumber: 23
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                            className: `px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(campaign.status)}`,
                                                            children: campaign.status
                                                        }, void 0, false, {
                                                            fileName: "[project]/components/BusinessDetailModal.tsx",
                                                            lineNumber: 180,
                                                            columnNumber: 23
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/components/BusinessDetailModal.tsx",
                                                    lineNumber: 178,
                                                    columnNumber: 21
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: "grid grid-cols-2 gap-4 text-sm",
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                            children: [
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                    className: "text-on-surface-variant",
                                                                    children: "Início:"
                                                                }, void 0, false, {
                                                                    fileName: "[project]/components/BusinessDetailModal.tsx",
                                                                    lineNumber: 186,
                                                                    columnNumber: 25
                                                                }, this),
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                    className: "ml-2 text-on-surface",
                                                                    children: new Date(campaign.startDate).toLocaleDateString('pt-BR')
                                                                }, void 0, false, {
                                                                    fileName: "[project]/components/BusinessDetailModal.tsx",
                                                                    lineNumber: 187,
                                                                    columnNumber: 25
                                                                }, this)
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "[project]/components/BusinessDetailModal.tsx",
                                                            lineNumber: 185,
                                                            columnNumber: 23
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                            children: [
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                    className: "text-on-surface-variant",
                                                                    children: "Fim:"
                                                                }, void 0, false, {
                                                                    fileName: "[project]/components/BusinessDetailModal.tsx",
                                                                    lineNumber: 192,
                                                                    columnNumber: 25
                                                                }, this),
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                    className: "ml-2 text-on-surface",
                                                                    children: new Date(campaign.endDate).toLocaleDateString('pt-BR')
                                                                }, void 0, false, {
                                                                    fileName: "[project]/components/BusinessDetailModal.tsx",
                                                                    lineNumber: 193,
                                                                    columnNumber: 25
                                                                }, this)
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "[project]/components/BusinessDetailModal.tsx",
                                                            lineNumber: 191,
                                                            columnNumber: 23
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/components/BusinessDetailModal.tsx",
                                                    lineNumber: 184,
                                                    columnNumber: 21
                                                }, this)
                                            ]
                                        }, index, true, {
                                            fileName: "[project]/components/BusinessDetailModal.tsx",
                                            lineNumber: 177,
                                            columnNumber: 19
                                        }, this))
                                }, void 0, false, {
                                    fileName: "[project]/components/BusinessDetailModal.tsx",
                                    lineNumber: 175,
                                    columnNumber: 15
                                }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "text-center py-8 text-on-surface-variant",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "text-4xl mb-2",
                                            children: "📢"
                                        }, void 0, false, {
                                            fileName: "[project]/components/BusinessDetailModal.tsx",
                                            lineNumber: 203,
                                            columnNumber: 17
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                            children: "Nenhuma campanha criada ainda"
                                        }, void 0, false, {
                                            fileName: "[project]/components/BusinessDetailModal.tsx",
                                            lineNumber: 204,
                                            columnNumber: 17
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/components/BusinessDetailModal.tsx",
                                    lineNumber: 202,
                                    columnNumber: 15
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/components/BusinessDetailModal.tsx",
                            lineNumber: 169,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/components/BusinessDetailModal.tsx",
                    lineNumber: 80,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "sticky bottom-0 bg-surface border-t border-outline-variant p-6 flex justify-end space-x-3",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                            onClick: onClose,
                            className: "btn-outlined",
                            children: "Fechar"
                        }, void 0, false, {
                            fileName: "[project]/components/BusinessDetailModal.tsx",
                            lineNumber: 212,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                            className: "btn-primary",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                    className: "mr-2",
                                    children: "✏️"
                                }, void 0, false, {
                                    fileName: "[project]/components/BusinessDetailModal.tsx",
                                    lineNumber: 216,
                                    columnNumber: 13
                                }, this),
                                "Editar Projeto"
                            ]
                        }, void 0, true, {
                            fileName: "[project]/components/BusinessDetailModal.tsx",
                            lineNumber: 215,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/components/BusinessDetailModal.tsx",
                    lineNumber: 211,
                    columnNumber: 9
                }, this)
            ]
        }, void 0, true, {
            fileName: "[project]/components/BusinessDetailModal.tsx",
            lineNumber: 64,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/components/BusinessDetailModal.tsx",
        lineNumber: 63,
        columnNumber: 5
    }, this);
}
_c = BusinessDetailModal;
var _c;
__turbopack_context__.k.register(_c, "BusinessDetailModal");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/components/DraggableBusinessCard.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "BusinessCardOverlay": (()=>BusinessCardOverlay),
    "default": (()=>DraggableBusinessCard)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$dnd$2d$kit$2f$sortable$2f$dist$2f$sortable$2e$esm$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@dnd-kit/sortable/dist/sortable.esm.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$dnd$2d$kit$2f$utilities$2f$dist$2f$utilities$2e$esm$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@dnd-kit/utilities/dist/utilities.esm.js [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
'use client';
;
;
function DraggableBusinessCard({ business, onClick, isDragging = false }) {
    _s();
    const { attributes, listeners, setNodeRef, transform, transition, isDragging: isSortableDragging } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$dnd$2d$kit$2f$sortable$2f$dist$2f$sortable$2e$esm$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useSortable"])({
        id: business.id.toString()
    });
    const style = {
        transform: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$dnd$2d$kit$2f$utilities$2f$dist$2f$utilities$2e$esm$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CSS"].Transform.toString(transform),
        transition,
        opacity: isSortableDragging ? 0.5 : 1
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        ref: setNodeRef,
        style: style,
        ...attributes,
        ...listeners,
        className: `
        bg-surface-container rounded-lg p-4 transition-all duration-200 cursor-grab active:cursor-grabbing
        border-l-4 border-primary hover:shadow-md
        ${isSortableDragging ? 'shadow-lg scale-105 rotate-2' : ''}
        ${isDragging ? 'opacity-50' : ''}
      `,
        onClick: (e)=>{
            // Só chama onClick se não estiver arrastando
            if (!isSortableDragging) {
                onClick();
            }
        },
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "flex items-start justify-between mb-3",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h4", {
                        className: "font-semibold text-on-surface flex-1 mr-2",
                        children: business.businessName
                    }, void 0, false, {
                        fileName: "[project]/components/DraggableBusinessCard.tsx",
                        lineNumber: 68,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "text-on-surface-variant opacity-50",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
                            width: "16",
                            height: "16",
                            viewBox: "0 0 16 16",
                            fill: "currentColor",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("circle", {
                                    cx: "3",
                                    cy: "3",
                                    r: "1"
                                }, void 0, false, {
                                    fileName: "[project]/components/DraggableBusinessCard.tsx",
                                    lineNumber: 75,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("circle", {
                                    cx: "8",
                                    cy: "3",
                                    r: "1"
                                }, void 0, false, {
                                    fileName: "[project]/components/DraggableBusinessCard.tsx",
                                    lineNumber: 76,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("circle", {
                                    cx: "13",
                                    cy: "3",
                                    r: "1"
                                }, void 0, false, {
                                    fileName: "[project]/components/DraggableBusinessCard.tsx",
                                    lineNumber: 77,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("circle", {
                                    cx: "3",
                                    cy: "8",
                                    r: "1"
                                }, void 0, false, {
                                    fileName: "[project]/components/DraggableBusinessCard.tsx",
                                    lineNumber: 78,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("circle", {
                                    cx: "8",
                                    cy: "8",
                                    r: "1"
                                }, void 0, false, {
                                    fileName: "[project]/components/DraggableBusinessCard.tsx",
                                    lineNumber: 79,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("circle", {
                                    cx: "13",
                                    cy: "8",
                                    r: "1"
                                }, void 0, false, {
                                    fileName: "[project]/components/DraggableBusinessCard.tsx",
                                    lineNumber: 80,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("circle", {
                                    cx: "3",
                                    cy: "13",
                                    r: "1"
                                }, void 0, false, {
                                    fileName: "[project]/components/DraggableBusinessCard.tsx",
                                    lineNumber: 81,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("circle", {
                                    cx: "8",
                                    cy: "13",
                                    r: "1"
                                }, void 0, false, {
                                    fileName: "[project]/components/DraggableBusinessCard.tsx",
                                    lineNumber: 82,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("circle", {
                                    cx: "13",
                                    cy: "13",
                                    r: "1"
                                }, void 0, false, {
                                    fileName: "[project]/components/DraggableBusinessCard.tsx",
                                    lineNumber: 83,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/components/DraggableBusinessCard.tsx",
                            lineNumber: 74,
                            columnNumber: 11
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/components/DraggableBusinessCard.tsx",
                        lineNumber: 73,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/components/DraggableBusinessCard.tsx",
                lineNumber: 67,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                className: "text-sm text-on-surface-variant mb-3 line-clamp-2",
                children: business.nextAction
            }, void 0, false, {
                fileName: "[project]/components/DraggableBusinessCard.tsx",
                lineNumber: 89,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "flex items-center mb-3",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                        className: "text-xs text-on-surface-variant mr-2",
                        children: "👥"
                    }, void 0, false, {
                        fileName: "[project]/components/DraggableBusinessCard.tsx",
                        lineNumber: 95,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                        className: "text-sm font-medium text-secondary",
                        children: [
                            business.influencers.length,
                            " influenciador",
                            business.influencers.length !== 1 ? 'es' : ''
                        ]
                    }, void 0, true, {
                        fileName: "[project]/components/DraggableBusinessCard.tsx",
                        lineNumber: 96,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/components/DraggableBusinessCard.tsx",
                lineNumber: 94,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "flex items-center justify-between text-sm",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                        className: "text-on-surface-variant",
                        children: new Date(business.contactDate).toLocaleDateString('pt-BR')
                    }, void 0, false, {
                        fileName: "[project]/components/DraggableBusinessCard.tsx",
                        lineNumber: 103,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                        className: "font-bold text-primary",
                        children: [
                            "R$ ",
                            (business.value / 1000).toFixed(0),
                            "K"
                        ]
                    }, void 0, true, {
                        fileName: "[project]/components/DraggableBusinessCard.tsx",
                        lineNumber: 106,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/components/DraggableBusinessCard.tsx",
                lineNumber: 102,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "mt-2 text-xs text-on-surface-variant opacity-70",
                children: "Arraste para mover • Clique para detalhes"
            }, void 0, false, {
                fileName: "[project]/components/DraggableBusinessCard.tsx",
                lineNumber: 112,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/components/DraggableBusinessCard.tsx",
        lineNumber: 48,
        columnNumber: 5
    }, this);
}
_s(DraggableBusinessCard, "xwLS6dN6tbin4lwkFDjbZUYsyyo=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$dnd$2d$kit$2f$sortable$2f$dist$2f$sortable$2e$esm$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useSortable"]
    ];
});
_c = DraggableBusinessCard;
function BusinessCardOverlay({ business }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "bg-surface-container rounded-lg p-4 shadow-2xl border-l-4 border-primary rotate-3 scale-105",
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h4", {
                className: "font-semibold text-on-surface mb-2",
                children: business.businessName
            }, void 0, false, {
                fileName: "[project]/components/DraggableBusinessCard.tsx",
                lineNumber: 123,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                className: "text-sm text-on-surface-variant mb-3 line-clamp-2",
                children: business.nextAction
            }, void 0, false, {
                fileName: "[project]/components/DraggableBusinessCard.tsx",
                lineNumber: 126,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "flex items-center justify-between text-sm",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                        className: "text-on-surface-variant",
                        children: [
                            "👥 ",
                            business.influencers.length,
                            " influenciador",
                            business.influencers.length !== 1 ? 'es' : ''
                        ]
                    }, void 0, true, {
                        fileName: "[project]/components/DraggableBusinessCard.tsx",
                        lineNumber: 130,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                        className: "font-bold text-primary",
                        children: [
                            "R$ ",
                            (business.value / 1000).toFixed(0),
                            "K"
                        ]
                    }, void 0, true, {
                        fileName: "[project]/components/DraggableBusinessCard.tsx",
                        lineNumber: 133,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/components/DraggableBusinessCard.tsx",
                lineNumber: 129,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/components/DraggableBusinessCard.tsx",
        lineNumber: 122,
        columnNumber: 5
    }, this);
}
_c1 = BusinessCardOverlay;
var _c, _c1;
__turbopack_context__.k.register(_c, "DraggableBusinessCard");
__turbopack_context__.k.register(_c1, "BusinessCardOverlay");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/components/DroppableColumn.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>DroppableColumn)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$dnd$2d$kit$2f$core$2f$dist$2f$core$2e$esm$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@dnd-kit/core/dist/core.esm.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$dnd$2d$kit$2f$sortable$2f$dist$2f$sortable$2e$esm$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@dnd-kit/sortable/dist/sortable.esm.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$DraggableBusinessCard$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/components/DraggableBusinessCard.tsx [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
'use client';
;
;
;
function DroppableColumn({ id, title, icon, businesses, totalValue, onBusinessClick, isUpdating = false }) {
    _s();
    const { setNodeRef, isOver } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$dnd$2d$kit$2f$core$2f$dist$2f$core$2e$esm$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useDroppable"])({
        id
    });
    const businessIds = businesses.map((b)=>b.id.toString());
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        ref: setNodeRef,
        className: `
        card-elevated p-6 min-h-96 transition-all duration-200
        ${isOver ? 'bg-primary-container border-2 border-primary border-dashed' : ''}
        ${isUpdating ? 'opacity-75' : ''}
      `,
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "mb-6",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "flex items-center justify-between mb-3",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "flex items-center",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                        className: "text-2xl mr-3",
                                        children: icon
                                    }, void 0, false, {
                                        fileName: "[project]/components/DroppableColumn.tsx",
                                        lineNumber: 58,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                        className: "text-lg font-semibold text-on-surface",
                                        children: title
                                    }, void 0, false, {
                                        fileName: "[project]/components/DroppableColumn.tsx",
                                        lineNumber: 59,
                                        columnNumber: 13
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/components/DroppableColumn.tsx",
                                lineNumber: 57,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                className: "text-sm bg-surface-container px-3 py-1 rounded-full font-medium",
                                children: businesses.length
                            }, void 0, false, {
                                fileName: "[project]/components/DroppableColumn.tsx",
                                lineNumber: 61,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/components/DroppableColumn.tsx",
                        lineNumber: 56,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "text-sm text-on-surface-variant",
                        children: [
                            "Total: R$ ",
                            (totalValue / 1000).toFixed(0),
                            "K"
                        ]
                    }, void 0, true, {
                        fileName: "[project]/components/DroppableColumn.tsx",
                        lineNumber: 66,
                        columnNumber: 9
                    }, this),
                    isOver && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "mt-3 text-sm text-primary font-medium animate-pulse",
                        children: "↓ Solte aqui para mover"
                    }, void 0, false, {
                        fileName: "[project]/components/DroppableColumn.tsx",
                        lineNumber: 72,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/components/DroppableColumn.tsx",
                lineNumber: 55,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$dnd$2d$kit$2f$sortable$2f$dist$2f$sortable$2e$esm$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SortableContext"], {
                items: businessIds,
                strategy: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$dnd$2d$kit$2f$sortable$2f$dist$2f$sortable$2e$esm$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["verticalListSortingStrategy"],
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "space-y-4",
                    children: [
                        businesses.map((business)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$DraggableBusinessCard$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                business: business,
                                onClick: ()=>onBusinessClick(business)
                            }, business.id, false, {
                                fileName: "[project]/components/DroppableColumn.tsx",
                                lineNumber: 82,
                                columnNumber: 13
                            }, this)),
                        businesses.length === 0 && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "text-center py-12 text-on-surface-variant",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "text-4xl mb-3",
                                    children: icon
                                }, void 0, false, {
                                    fileName: "[project]/components/DroppableColumn.tsx",
                                    lineNumber: 91,
                                    columnNumber: 15
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                    className: "text-sm",
                                    children: "Nenhum negócio nesta fase"
                                }, void 0, false, {
                                    fileName: "[project]/components/DroppableColumn.tsx",
                                    lineNumber: 92,
                                    columnNumber: 15
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                    className: "text-xs mt-1 opacity-70",
                                    children: isOver ? 'Solte aqui para adicionar' : 'Arraste projetos para cá'
                                }, void 0, false, {
                                    fileName: "[project]/components/DroppableColumn.tsx",
                                    lineNumber: 93,
                                    columnNumber: 15
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/components/DroppableColumn.tsx",
                            lineNumber: 90,
                            columnNumber: 13
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/components/DroppableColumn.tsx",
                    lineNumber: 80,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/components/DroppableColumn.tsx",
                lineNumber: 79,
                columnNumber: 7
            }, this),
            isUpdating && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "absolute inset-0 bg-surface bg-opacity-50 flex items-center justify-center rounded-lg",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "flex items-center space-x-2 text-primary",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "animate-spin rounded-full h-4 w-4 border-2 border-primary border-t-transparent"
                        }, void 0, false, {
                            fileName: "[project]/components/DroppableColumn.tsx",
                            lineNumber: 105,
                            columnNumber: 13
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                            className: "text-sm font-medium",
                            children: "Atualizando..."
                        }, void 0, false, {
                            fileName: "[project]/components/DroppableColumn.tsx",
                            lineNumber: 106,
                            columnNumber: 13
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/components/DroppableColumn.tsx",
                    lineNumber: 104,
                    columnNumber: 11
                }, this)
            }, void 0, false, {
                fileName: "[project]/components/DroppableColumn.tsx",
                lineNumber: 103,
                columnNumber: 9
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/components/DroppableColumn.tsx",
        lineNumber: 46,
        columnNumber: 5
    }, this);
}
_s(DroppableColumn, "DmJTTt6A5xWIX/faBiFge3FOLrw=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$dnd$2d$kit$2f$core$2f$dist$2f$core$2e$esm$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useDroppable"]
    ];
});
_c = DroppableColumn;
var _c;
__turbopack_context__.k.register(_c, "DroppableColumn");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/app/(dashboard)/jornada/page.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>JornadaPage)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$actions$2f$data$3a$6d9d53__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$text$2f$javascript$3e$__ = __turbopack_context__.i("[project]/app/actions/data:6d9d53 [app-client] (ecmascript) <text/javascript>");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$actions$2f$data$3a$3ad5f4__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$text$2f$javascript$3e$__ = __turbopack_context__.i("[project]/app/actions/data:3ad5f4 [app-client] (ecmascript) <text/javascript>");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/utils.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$BusinessDetailModal$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/components/BusinessDetailModal.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$dnd$2d$kit$2f$core$2f$dist$2f$core$2e$esm$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@dnd-kit/core/dist/core.esm.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$DraggableBusinessCard$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/components/DraggableBusinessCard.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$DroppableColumn$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/components/DroppableColumn.tsx [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
'use client';
;
;
;
;
;
;
;
// Dados de exemplo para demonstração (serão substituídos pelos dados do Google Sheets)
const mockBusinesses = [
    {
        id: 1,
        businessName: 'Loja de Roupas Fashion',
        journeyStage: 'Agendamentos',
        nextAction: 'Agendar sessões de fotos com influenciadores',
        contactDate: '2024-01-15',
        value: 15000,
        description: 'Campanha de verão focada em roupas casuais para jovens de 18-30 anos',
        influencers: [
            {
                name: 'Ana Silva',
                username: 'anasilva',
                followers: 125000,
                engagementRate: 4.2
            },
            {
                name: 'Carlos Santos',
                username: 'carlossantos',
                followers: 89000,
                engagementRate: 6.8
            }
        ],
        campaigns: [
            {
                title: 'Campanha Verão 2024',
                status: 'Ativa',
                startDate: '2024-01-15',
                endDate: '2024-03-15'
            }
        ]
    },
    {
        id: 2,
        businessName: 'Restaurante Gourmet',
        journeyStage: 'Reunião Briefing',
        nextAction: 'Definir estratégia de conteúdo gastronômico',
        contactDate: '2024-01-10',
        value: 8000,
        description: 'Divulgação de pratos especiais e experiência gastronômica única',
        influencers: [
            {
                name: 'Maria Oliveira',
                username: 'mariaoliveira',
                followers: 234000,
                engagementRate: 3.1
            }
        ],
        campaigns: []
    },
    {
        id: 3,
        businessName: 'Academia Fitness Plus',
        journeyStage: 'Entrega Final',
        nextAction: 'Finalizar edição dos vídeos de treino',
        contactDate: '2024-01-20',
        value: 25000,
        description: 'Campanha de motivação fitness com foco em resultados reais',
        influencers: [
            {
                name: 'João Fitness',
                username: 'joaofitness',
                followers: 156000,
                engagementRate: 5.4
            },
            {
                name: 'Carla Strong',
                username: 'carlastrong',
                followers: 98000,
                engagementRate: 7.2
            },
            {
                name: 'Pedro Muscle',
                username: 'pedromuscle',
                followers: 67000,
                engagementRate: 4.8
            }
        ],
        campaigns: [
            {
                title: 'Transformação 90 Dias',
                status: 'Ativa',
                startDate: '2024-01-01',
                endDate: '2024-03-31'
            }
        ]
    },
    {
        id: 4,
        businessName: 'Clínica de Estética',
        journeyStage: 'Reunião Briefing',
        nextAction: 'Alinhar diretrizes de comunicação sobre procedimentos',
        contactDate: '2024-01-12',
        value: 12000,
        description: 'Divulgação de tratamentos estéticos com foco em naturalidade',
        influencers: [
            {
                name: 'Bella Beauty',
                username: 'bellabeauty',
                followers: 189000,
                engagementRate: 6.1
            }
        ],
        campaigns: []
    },
    {
        id: 5,
        businessName: 'Loja de Eletrônicos',
        journeyStage: 'Agendamentos',
        nextAction: 'Coordenar reviews de produtos com tech influencers',
        contactDate: '2024-01-08',
        value: 18000,
        description: 'Reviews autênticos de gadgets e eletrônicos inovadores',
        influencers: [
            {
                name: 'Tech Master',
                username: 'techmaster',
                followers: 145000,
                engagementRate: 5.9
            },
            {
                name: 'Gamer Pro',
                username: 'gamerpro',
                followers: 203000,
                engagementRate: 4.5
            }
        ],
        campaigns: [
            {
                title: 'Tech Reviews 2024',
                status: 'Planejamento',
                startDate: '2024-02-01',
                endDate: '2024-04-30'
            }
        ]
    }
];
// Definir as fases da jornada (3 fases principais)
const journeyStages = [
    {
        id: 'Reunião Briefing',
        label: 'Reunião Briefing',
        color: 'bg-blue-100 text-blue-800',
        icon: '📋'
    },
    {
        id: 'Agendamentos',
        label: 'Agendamentos',
        color: 'bg-yellow-100 text-yellow-800',
        icon: '📅'
    },
    {
        id: 'Entrega Final',
        label: 'Entrega Final',
        color: 'bg-green-100 text-green-800',
        icon: '✅'
    }
];
function JornadaPage() {
    _s();
    const [businesses, setBusinesses] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(mockBusinesses);
    const [selectedBusiness, setSelectedBusiness] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    const [isModalOpen, setIsModalOpen] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const [activeId, setActiveId] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    const [isUpdating, setIsUpdating] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    // Configurar sensores para drag & drop
    const sensors = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$dnd$2d$kit$2f$core$2f$dist$2f$core$2e$esm$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useSensors"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$dnd$2d$kit$2f$core$2f$dist$2f$core$2e$esm$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useSensor"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$dnd$2d$kit$2f$core$2f$dist$2f$core$2e$esm$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PointerSensor"], {
        activationConstraint: {
            distance: 8
        }
    }));
    // Carregar dados do Google Sheets
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].useEffect({
        "JornadaPage.useEffect": ()=>{
            async function loadData() {
                try {
                    const rawData = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$actions$2f$data$3a$6d9d53__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$text$2f$javascript$3e$__["getData"])('Businesses');
                    if (rawData && rawData.length > 0) {
                        const transformedData = (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["transformData"])(rawData);
                        // Mapeia os dados transformados para o formato esperado pelo componente
                        const businessesData = transformedData.map({
                            "JornadaPage.useEffect.loadData.businessesData": (item)=>({
                                    id: item.id || Math.random(),
                                    businessName: item.businessName || item.Nome || item['Nome do Negócio'] || 'Negócio não informado',
                                    journeyStage: item.journeyStage || item.Estágio || item['Estágio da Jornada'] || 'Reunião Briefing',
                                    nextAction: item.nextAction || item.Ação || item['Próxima Ação'] || 'Definir próxima ação',
                                    contactDate: item.contactDate || item.Data || item['Data de Contato'] || new Date().toISOString().split('T')[0],
                                    value: parseInt(item.value || item.Valor || item['Valor do Negócio'] || '0'),
                                    description: item.description || item.Descrição || 'Descrição não informada',
                                    influencers: JSON.parse(item.influencers || '[]'),
                                    campaigns: JSON.parse(item.campaigns || '[]')
                                })
                        }["JornadaPage.useEffect.loadData.businessesData"]);
                        setBusinesses(businessesData);
                    }
                } catch (error) {
                    console.log('Usando dados de exemplo - Google Sheets não configurado ainda');
                }
            }
            loadData();
        }
    }["JornadaPage.useEffect"], []);
    const handleBusinessClick = (business)=>{
        setSelectedBusiness(business);
        setIsModalOpen(true);
    };
    const handleCloseModal = ()=>{
        setIsModalOpen(false);
        setSelectedBusiness(null);
    };
    // Função chamada quando o drag inicia
    const handleDragStart = (event)=>{
        setActiveId(event.active.id);
    };
    // Função chamada quando o drag termina
    const handleDragEnd = async (event)=>{
        const { active, over } = event;
        setActiveId(null);
        if (!over) return;
        const businessId = active.id;
        const newStage = over.id;
        // Encontra o negócio que está sendo movido
        const business = businesses.find((b)=>b.id.toString() === businessId);
        if (!business || business.journeyStage === newStage) return;
        // Atualiza o estado local imediatamente para feedback visual
        setBusinesses((prev)=>prev.map((b)=>b.id.toString() === businessId ? {
                    ...b,
                    journeyStage: newStage
                } : b));
        // Atualiza no banco de dados
        setIsUpdating(true);
        try {
            await (0, __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$actions$2f$data$3a$3ad5f4__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$text$2f$javascript$3e$__["updateBusinessStage"])(businessId, newStage);
            console.log(`Negócio ${business.businessName} movido para ${newStage}`);
        // Aqui você pode adicionar uma notificação de sucesso
        // toast.success(`${business.businessName} movido para ${newStage}`);
        } catch (error) {
            console.error('Erro ao atualizar estágio:', error);
            // Reverte a mudança local em caso de erro
            setBusinesses((prev)=>prev.map((b)=>b.id.toString() === businessId ? {
                        ...b,
                        journeyStage: business.journeyStage
                    } : b));
        // Aqui você pode adicionar uma notificação de erro
        // toast.error('Erro ao mover negócio. Tente novamente.');
        } finally{
            setIsUpdating(false);
        }
    };
    // Encontra o negócio que está sendo arrastado para o overlay
    const activeBusiness = activeId ? businesses.find((b)=>b.id.toString() === activeId) : null;
    // Agrupar negócios por fase da jornada
    const businessesByStage = journeyStages.map((stage)=>({
            ...stage,
            businesses: businesses.filter((business)=>business.journeyStage === stage.id),
            totalValue: businesses.filter((business)=>business.journeyStage === stage.id).reduce((sum, business)=>sum + business.value, 0)
        }));
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "space-y-6",
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "grid grid-cols-1 md:grid-cols-4 gap-4",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "card-elevated p-4",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "flex items-center justify-between",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                            className: "text-sm text-on-surface-variant",
                                            children: "Total de Negócios"
                                        }, void 0, false, {
                                            fileName: "[project]/app/(dashboard)/jornada/page.tsx",
                                            lineNumber: 245,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                            className: "text-2xl font-bold text-on-surface",
                                            children: businesses.length
                                        }, void 0, false, {
                                            fileName: "[project]/app/(dashboard)/jornada/page.tsx",
                                            lineNumber: 246,
                                            columnNumber: 15
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/app/(dashboard)/jornada/page.tsx",
                                    lineNumber: 244,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "text-2xl",
                                    children: "🏢"
                                }, void 0, false, {
                                    fileName: "[project]/app/(dashboard)/jornada/page.tsx",
                                    lineNumber: 248,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/app/(dashboard)/jornada/page.tsx",
                            lineNumber: 243,
                            columnNumber: 11
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/app/(dashboard)/jornada/page.tsx",
                        lineNumber: 242,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "card-elevated p-4",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "flex items-center justify-between",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                            className: "text-sm text-on-surface-variant",
                                            children: "Valor Total"
                                        }, void 0, false, {
                                            fileName: "[project]/app/(dashboard)/jornada/page.tsx",
                                            lineNumber: 255,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                            className: "text-2xl font-bold text-primary",
                                            children: [
                                                "R$ ",
                                                (businesses.reduce((sum, b)=>sum + b.value, 0) / 1000).toFixed(0),
                                                "K"
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/app/(dashboard)/jornada/page.tsx",
                                            lineNumber: 256,
                                            columnNumber: 15
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/app/(dashboard)/jornada/page.tsx",
                                    lineNumber: 254,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "text-2xl",
                                    children: "💰"
                                }, void 0, false, {
                                    fileName: "[project]/app/(dashboard)/jornada/page.tsx",
                                    lineNumber: 260,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/app/(dashboard)/jornada/page.tsx",
                            lineNumber: 253,
                            columnNumber: 11
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/app/(dashboard)/jornada/page.tsx",
                        lineNumber: 252,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "card-elevated p-4",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "flex items-center justify-between",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                            className: "text-sm text-on-surface-variant",
                                            children: "Em Fechamento"
                                        }, void 0, false, {
                                            fileName: "[project]/app/(dashboard)/jornada/page.tsx",
                                            lineNumber: 267,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                            className: "text-2xl font-bold text-green-600",
                                            children: businesses.filter((b)=>b.journeyStage === 'Fechamento').length
                                        }, void 0, false, {
                                            fileName: "[project]/app/(dashboard)/jornada/page.tsx",
                                            lineNumber: 268,
                                            columnNumber: 15
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/app/(dashboard)/jornada/page.tsx",
                                    lineNumber: 266,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "text-2xl",
                                    children: "🎯"
                                }, void 0, false, {
                                    fileName: "[project]/app/(dashboard)/jornada/page.tsx",
                                    lineNumber: 272,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/app/(dashboard)/jornada/page.tsx",
                            lineNumber: 265,
                            columnNumber: 11
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/app/(dashboard)/jornada/page.tsx",
                        lineNumber: 264,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "card-elevated p-4",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "flex items-center justify-between",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                            className: "text-sm text-on-surface-variant",
                                            children: "Taxa Conversão"
                                        }, void 0, false, {
                                            fileName: "[project]/app/(dashboard)/jornada/page.tsx",
                                            lineNumber: 279,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                            className: "text-2xl font-bold text-secondary",
                                            children: [
                                                businesses.length > 0 ? Math.round(businesses.filter((b)=>b.journeyStage === 'Pós-venda').length / businesses.length * 100) : 0,
                                                "%"
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/app/(dashboard)/jornada/page.tsx",
                                            lineNumber: 280,
                                            columnNumber: 15
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/app/(dashboard)/jornada/page.tsx",
                                    lineNumber: 278,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "text-2xl",
                                    children: "📈"
                                }, void 0, false, {
                                    fileName: "[project]/app/(dashboard)/jornada/page.tsx",
                                    lineNumber: 284,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/app/(dashboard)/jornada/page.tsx",
                            lineNumber: 277,
                            columnNumber: 11
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/app/(dashboard)/jornada/page.tsx",
                        lineNumber: 276,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/app/(dashboard)/jornada/page.tsx",
                lineNumber: 241,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$dnd$2d$kit$2f$core$2f$dist$2f$core$2e$esm$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["DndContext"], {
                sensors: sensors,
                collisionDetection: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$dnd$2d$kit$2f$core$2f$dist$2f$core$2e$esm$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["closestCorners"],
                onDragStart: handleDragStart,
                onDragEnd: handleDragEnd,
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "grid grid-cols-1 lg:grid-cols-3 gap-6",
                        children: businessesByStage.map((stage)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$DroppableColumn$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                id: stage.id,
                                title: stage.label,
                                icon: stage.icon,
                                businesses: stage.businesses,
                                totalValue: stage.totalValue,
                                onBusinessClick: handleBusinessClick,
                                isUpdating: isUpdating
                            }, stage.id, false, {
                                fileName: "[project]/app/(dashboard)/jornada/page.tsx",
                                lineNumber: 298,
                                columnNumber: 13
                            }, this))
                    }, void 0, false, {
                        fileName: "[project]/app/(dashboard)/jornada/page.tsx",
                        lineNumber: 296,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$dnd$2d$kit$2f$core$2f$dist$2f$core$2e$esm$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["DragOverlay"], {
                        children: activeBusiness ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$DraggableBusinessCard$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["BusinessCardOverlay"], {
                            business: activeBusiness
                        }, void 0, false, {
                            fileName: "[project]/app/(dashboard)/jornada/page.tsx",
                            lineNumber: 314,
                            columnNumber: 13
                        }, this) : null
                    }, void 0, false, {
                        fileName: "[project]/app/(dashboard)/jornada/page.tsx",
                        lineNumber: 312,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/app/(dashboard)/jornada/page.tsx",
                lineNumber: 290,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$BusinessDetailModal$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                business: selectedBusiness,
                isOpen: isModalOpen,
                onClose: handleCloseModal
            }, void 0, false, {
                fileName: "[project]/app/(dashboard)/jornada/page.tsx",
                lineNumber: 320,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/app/(dashboard)/jornada/page.tsx",
        lineNumber: 239,
        columnNumber: 5
    }, this);
}
_s(JornadaPage, "GZtcjySB/Jddx4FZ85BeVKaqvYc=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$dnd$2d$kit$2f$core$2f$dist$2f$core$2e$esm$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useSensors"]
    ];
});
_c = JornadaPage;
var _c;
__turbopack_context__.k.register(_c, "JornadaPage");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
}]);

//# sourceMappingURL=_6654af1f._.js.map
# Google APIs Configuration
# Substitua pelos valores reais do seu projeto Google Cloud

# Google Sheets + Calendar API
GOOGLE_PROJECT_ID=your-project-id
GOOGLE_PRIVATE_KEY_ID=your-private-key-id
GOOGLE_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----\nyour-private-key-here\n-----END PRIVATE KEY-----"
GOOGLE_CLIENT_EMAIL=<EMAIL>
GOOGLE_CLIENT_ID=your-client-id

# IDs específicos
GOOGLE_SPREADSHEET_ID=14yzga-y6A-3kae92Lr3knQGDaVVXMZv3tOggUL43dCI
GOOGLE_CALENDAR_ID=your-calendar-id

# INSTRUÇÕES PARA CONFIGURAÇÃO:
# 
# 1. Vá para https://console.cloud.google.com/
# 2. Crie um novo projeto ou selecione um existente
# 3. Habilite as APIs:
#    - Google Sheets API
#    - Google Calendar API
# 4. Crie uma conta de serviço:
#    - Vá em "APIs & Services" > "Credentials"
#    - Clique em "Create Credentials" > "Service Account"
#    - Baixe o arquivo JSON da conta de serviço
# 5. Extraia os valores do JSON e substitua acima:
#    - project_id → GOOGLE_PROJECT_ID
#    - private_key_id → GOOGLE_PRIVATE_KEY_ID
#    - private_key → GOOGLE_PRIVATE_KEY (mantenha as aspas e \n)
#    - client_email → GOOGLE_CLIENT_EMAIL
#    - client_id → GOOGLE_CLIENT_ID
# 6. Compartilhe sua planilha com o email da conta de serviço (GOOGLE_CLIENT_EMAIL)
#    - Abra: https://docs.google.com/spreadsheets/d/14yzga-y6A-3kae92Lr3knQGDaVVXMZv3tOggUL43dCI/edit
#    - Clique em "Compartilhar"
#    - Adicione o email da conta de serviço com permissão de "Editor"
# 7. Para o calendário:
#    - Vá para Google Calendar
#    - Crie um calendário específico para o CRM
#    - Nas configurações do calendário, copie o "Calendar ID"
#    - Substitua GOOGLE_CALENDAR_ID pelo ID copiado
# 8. Reinicie o servidor: npm run dev

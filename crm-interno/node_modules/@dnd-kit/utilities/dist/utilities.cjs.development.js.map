{"version": 3, "file": "utilities.cjs.development.js", "sources": ["../src/hooks/useCombinedRefs.ts", "../src/execution-context/canUseDOM.ts", "../src/type-guards/isWindow.ts", "../src/type-guards/isNode.ts", "../src/execution-context/getWindow.ts", "../src/type-guards/isDocument.ts", "../src/type-guards/isHTMLElement.ts", "../src/type-guards/isSVGElement.ts", "../src/execution-context/getOwnerDocument.ts", "../src/hooks/useIsomorphicLayoutEffect.ts", "../src/hooks/useEvent.ts", "../src/hooks/useInterval.ts", "../src/hooks/useLatestValue.ts", "../src/hooks/useLazyMemo.ts", "../src/hooks/useNodeRef.ts", "../src/hooks/usePrevious.ts", "../src/hooks/useUniqueId.ts", "../src/adjustment.ts", "../src/event/hasViewportRelativeCoordinates.ts", "../src/event/isKeyboardEvent.ts", "../src/event/isTouchEvent.ts", "../src/coordinates/getEventCoordinates.ts", "../src/css.ts", "../src/focus/findFirstFocusableNode.ts"], "sourcesContent": ["import {useMemo} from 'react';\n\nexport function useCombinedRefs<T>(\n  ...refs: ((node: T) => void)[]\n): (node: T) => void {\n  return useMemo(\n    () => (node: T) => {\n      refs.forEach((ref) => ref(node));\n    },\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    refs\n  );\n}\n", "// https://github.com/facebook/react/blob/master/packages/shared/ExecutionEnvironment.js\nexport const canUseDOM =\n  typeof window !== 'undefined' &&\n  typeof window.document !== 'undefined' &&\n  typeof window.document.createElement !== 'undefined';\n", "export function isWindow(element: Object): element is typeof window {\n  const elementString = Object.prototype.toString.call(element);\n  return (\n    elementString === '[object Window]' ||\n    // In Electron context the Window object serializes to [object global]\n    elementString === '[object global]'\n  );\n}\n", "export function isNode(node: Object): node is Node {\n  return 'nodeType' in node;\n}\n", "import {isWindow} from '../type-guards/isWindow';\nimport {isNode} from '../type-guards/isNode';\n\nexport function getWindow(target: Event['target']): typeof window {\n  if (!target) {\n    return window;\n  }\n\n  if (isWindow(target)) {\n    return target;\n  }\n\n  if (!isNode(target)) {\n    return window;\n  }\n\n  return target.ownerDocument?.defaultView ?? window;\n}\n", "import {getWindow} from '../execution-context/getWindow';\n\nexport function isDocument(node: Node): node is Document {\n  const {Document} = getWindow(node);\n\n  return node instanceof Document;\n}\n", "import {getWindow} from '../execution-context/getWindow';\n\nimport {isWindow} from './isWindow';\n\nexport function isHTMLElement(node: Node | Window): node is HTMLElement {\n  if (isWindow(node)) {\n    return false;\n  }\n\n  return node instanceof getWindow(node).HTMLElement;\n}\n", "import {getWindow} from '../execution-context/getWindow';\n\nexport function isSVGElement(node: Node): node is SVGElement {\n  return node instanceof getWindow(node).SVGElement;\n}\n", "import {\n  isWindow,\n  isHTMLElement,\n  isDocument,\n  isNode,\n  isSVGElement,\n} from '../type-guards';\n\nexport function getOwnerDocument(target: Event['target']): Document {\n  if (!target) {\n    return document;\n  }\n\n  if (isWindow(target)) {\n    return target.document;\n  }\n\n  if (!isNode(target)) {\n    return document;\n  }\n\n  if (isDocument(target)) {\n    return target;\n  }\n\n  if (isHTMLElement(target) || isSVGElement(target)) {\n    return target.ownerDocument;\n  }\n\n  return document;\n}\n", "import {useEffect, useLayoutEffect} from 'react';\n\nimport {canUseDOM} from '../execution-context';\n\n/**\n * A hook that resolves to useEffect on the server and useLayoutEffect on the client\n * @param callback {function} Callback function that is invoked when the dependencies of the hook change\n */\nexport const useIsomorphicLayoutEffect = canUseDOM\n  ? useLayoutEffect\n  : useEffect;\n", "import {useCallback, useRef} from 'react';\n\nimport {useIsomorphicLayoutEffect} from './useIsomorphicLayoutEffect';\n\nexport function useEvent<T extends Function>(handler: T | undefined) {\n  const handlerRef = useRef<T | undefined>(handler);\n\n  useIsomorphicLayoutEffect(() => {\n    handlerRef.current = handler;\n  });\n\n  return useCallback(function (...args: any) {\n    return handlerRef.current?.(...args);\n  }, []);\n}\n", "import {useCallback, useRef} from 'react';\n\nexport function useInterval() {\n  const intervalRef = useRef<number | null>(null);\n\n  const set = useCallback((listener: Function, duration: number) => {\n    intervalRef.current = setInterval(listener, duration);\n  }, []);\n\n  const clear = useCallback(() => {\n    if (intervalRef.current !== null) {\n      clearInterval(intervalRef.current);\n      intervalRef.current = null;\n    }\n  }, []);\n\n  return [set, clear] as const;\n}\n", "import {useRef} from 'react';\nimport type {DependencyList} from 'react';\n\nimport {useIsomorphicLayoutEffect} from './useIsomorphicLayoutEffect';\n\nexport function useLatestValue<T extends any>(\n  value: T,\n  dependencies: DependencyList = [value]\n) {\n  const valueRef = useRef<T>(value);\n\n  useIsomorphicLayoutEffect(() => {\n    if (valueRef.current !== value) {\n      valueRef.current = value;\n    }\n  }, dependencies);\n\n  return valueRef;\n}\n", "import {useMemo, useRef} from 'react';\n\nexport function useLazyMemo<T>(\n  callback: (prevValue: T | undefined) => T,\n  dependencies: any[]\n) {\n  const valueRef = useRef<T>();\n\n  return useMemo(\n    () => {\n      const newValue = callback(valueRef.current);\n      valueRef.current = newValue;\n\n      return newValue;\n    },\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    [...dependencies]\n  );\n}\n", "import {useRef, useCallback} from 'react';\n\nimport {useEvent} from './useEvent';\n\nexport function useNodeRef(\n  onChange?: (\n    newElement: HTMLElement | null,\n    previousElement: HTMLElement | null\n  ) => void\n) {\n  const onChangeHandler = useEvent(onChange);\n  const node = useRef<HTMLElement | null>(null);\n  const setNodeRef = useCallback(\n    (element: HTMLElement | null) => {\n      if (element !== node.current) {\n        onChangeHandler?.(element, node.current);\n      }\n\n      node.current = element;\n    },\n    //eslint-disable-next-line\n    []\n  );\n\n  return [node, setNodeRef] as const;\n}\n", "import {useRef, useEffect} from 'react';\n\nexport function usePrevious<T>(value: T) {\n  const ref = useRef<T>();\n\n  useEffect(() => {\n    ref.current = value;\n  }, [value]);\n\n  return ref.current;\n}\n", "import {useMemo} from 'react';\n\nlet ids: Record<string, number> = {};\n\nexport function useUniqueId(prefix: string, value?: string) {\n  return useMemo(() => {\n    if (value) {\n      return value;\n    }\n\n    const id = ids[prefix] == null ? 0 : ids[prefix] + 1;\n    ids[prefix] = id;\n\n    return `${prefix}-${id}`;\n  }, [prefix, value]);\n}\n", "function createAdjustmentFn(modifier: number) {\n  return <T extends Record<U, number>, U extends string>(\n    object: T,\n    ...adjustments: Partial<T>[]\n  ): T => {\n    return adjustments.reduce<T>(\n      (accumulator, adjustment) => {\n        const entries = Object.entries(adjustment) as [U, number][];\n\n        for (const [key, valueAdjustment] of entries) {\n          const value = accumulator[key];\n\n          if (value != null) {\n            accumulator[key] = (value + modifier * valueAdjustment) as T[U];\n          }\n        }\n\n        return accumulator;\n      },\n      {\n        ...object,\n      }\n    );\n  };\n}\n\nexport const add = createAdjustmentFn(1);\nexport const subtract = createAdjustmentFn(-1);\n", "export function hasViewportRelativeCoordinates(\n  event: Event\n): event is Event & Pick<PointerEvent, 'clientX' | 'clientY'> {\n  return 'clientX' in event && 'clientY' in event;\n}\n", "import {getWindow} from '../execution-context';\n\nexport function isKeyboardEvent(\n  event: Event | undefined | null\n): event is KeyboardEvent {\n  if (!event) {\n    return false;\n  }\n\n  const {KeyboardEvent} = getWindow(event.target);\n\n  return KeyboardEvent && event instanceof KeyboardEvent;\n}\n", "import {getWindow} from '../execution-context';\n\nexport function isTouchEvent(\n  event: Event | undefined | null\n): event is TouchEvent {\n  if (!event) {\n    return false;\n  }\n\n  const {TouchEvent} = getWindow(event.target);\n\n  return TouchEvent && event instanceof TouchEvent;\n}\n", "import type {Coordinates} from './types';\nimport {isTouchEvent, hasViewportRelativeCoordinates} from '../event';\n\n/**\n * Returns the normalized x and y coordinates for mouse and touch events.\n */\nexport function getEventCoordinates(event: Event): Coordinates | null {\n  if (isTouchEvent(event)) {\n    if (event.touches && event.touches.length) {\n      const {clientX: x, clientY: y} = event.touches[0];\n\n      return {\n        x,\n        y,\n      };\n    } else if (event.changedTouches && event.changedTouches.length) {\n      const {clientX: x, clientY: y} = event.changedTouches[0];\n\n      return {\n        x,\n        y,\n      };\n    }\n  }\n\n  if (hasViewportRelativeCoordinates(event)) {\n    return {\n      x: event.clientX,\n      y: event.clientY,\n    };\n  }\n\n  return null;\n}\n", "export type Transform = {\n  x: number;\n  y: number;\n  scaleX: number;\n  scaleY: number;\n};\n\nexport interface Transition {\n  property: string;\n  easing: string;\n  duration: number;\n}\n\nexport const CSS = Object.freeze({\n  Translate: {\n    toString(transform: Transform | null) {\n      if (!transform) {\n        return;\n      }\n\n      const {x, y} = transform;\n\n      return `translate3d(${x ? Math.round(x) : 0}px, ${\n        y ? Math.round(y) : 0\n      }px, 0)`;\n    },\n  },\n  Scale: {\n    toString(transform: Transform | null) {\n      if (!transform) {\n        return;\n      }\n\n      const {scaleX, scaleY} = transform;\n\n      return `scaleX(${scaleX}) scaleY(${scaleY})`;\n    },\n  },\n  Transform: {\n    toString(transform: Transform | null) {\n      if (!transform) {\n        return;\n      }\n\n      return [\n        CSS.Translate.toString(transform),\n        CSS.Scale.toString(transform),\n      ].join(' ');\n    },\n  },\n  Transition: {\n    toString({property, duration, easing}: Transition) {\n      return `${property} ${duration}ms ${easing}`;\n    },\n  },\n});\n", "const SELECTOR =\n  'a,frame,iframe,input:not([type=hidden]):not(:disabled),select:not(:disabled),textarea:not(:disabled),button:not(:disabled),*[tabindex]';\n\nexport function findFirstFocusableNode(\n  element: HTMLElement\n): HTMLElement | null {\n  if (element.matches(SELECTOR)) {\n    return element;\n  }\n\n  return element.querySelector(SELECTOR);\n}\n"], "names": ["useCombinedRefs", "refs", "useMemo", "node", "for<PERSON>ach", "ref", "canUseDOM", "window", "document", "createElement", "isWindow", "element", "elementString", "Object", "prototype", "toString", "call", "isNode", "getWindow", "target", "ownerDocument", "defaultView", "isDocument", "Document", "isHTMLElement", "HTMLElement", "isSVGElement", "SVGElement", "getOwnerDocument", "useIsomorphicLayoutEffect", "useLayoutEffect", "useEffect", "useEvent", "handler", "handler<PERSON>ef", "useRef", "current", "useCallback", "args", "useInterval", "intervalRef", "set", "listener", "duration", "setInterval", "clear", "clearInterval", "useLatestValue", "value", "dependencies", "valueRef", "useLazyMemo", "callback", "newValue", "useNodeRef", "onChange", "onChangeHandler", "setNodeRef", "usePrevious", "ids", "useUniqueId", "prefix", "id", "createAdjustmentFn", "modifier", "object", "adjustments", "reduce", "accumulator", "adjustment", "entries", "key", "valueAdjustment", "add", "subtract", "hasViewportRelativeCoordinates", "event", "isKeyboardEvent", "KeyboardEvent", "isTouchEvent", "TouchEvent", "getEventCoordinates", "touches", "length", "clientX", "x", "clientY", "y", "changedTouches", "CSS", "freeze", "Translate", "transform", "Math", "round", "Scale", "scaleX", "scaleY", "Transform", "join", "Transition", "property", "easing", "SELECTOR", "findFirstFocusableNode", "matches", "querySelector"], "mappings": ";;;;;;SAEgBA;oCACXC;IAAAA;;;EAEH,OAAOC,aAAO,CACZ,MAAOC,IAAD;IACJF,IAAI,CAACG,OAAL,CAAcC,GAAD,IAASA,GAAG,CAACF,IAAD,CAAzB;GAFU;EAKZF,IALY,CAAd;AAOD;;ACZD;AACA,MAAaK,SAAS,GACpB,OAAOC,MAAP,KAAkB,WAAlB,IACA,OAAOA,MAAM,CAACC,QAAd,KAA2B,WAD3B,IAEA,OAAOD,MAAM,CAACC,QAAP,CAAgBC,aAAvB,KAAyC,WAHpC;;SCDSC,SAASC;EACvB,MAAMC,aAAa,GAAGC,MAAM,CAACC,SAAP,CAAiBC,QAAjB,CAA0BC,IAA1B,CAA+BL,OAA/B,CAAtB;EACA,OACEC,aAAa,KAAK,iBAAlB;EAEAA,aAAa,KAAK,iBAHpB;AAKD;;SCPeK,OAAOd;EACrB,OAAO,cAAcA,IAArB;AACD;;SCCee,UAAUC;;;EACxB,IAAI,CAACA,MAAL,EAAa;IACX,OAAOZ,MAAP;;;EAGF,IAAIG,QAAQ,CAACS,MAAD,CAAZ,EAAsB;IACpB,OAAOA,MAAP;;;EAGF,IAAI,CAACF,MAAM,CAACE,MAAD,CAAX,EAAqB;IACnB,OAAOZ,MAAP;;;EAGF,0DAAOY,MAAM,CAACC,aAAd,qBAAO,uBAAsBC,WAA7B,oCAA4Cd,MAA5C;AACD;;SCfee,WAAWnB;EACzB,MAAM;IAACoB;MAAYL,SAAS,CAACf,IAAD,CAA5B;EAEA,OAAOA,IAAI,YAAYoB,QAAvB;AACD;;SCFeC,cAAcrB;EAC5B,IAAIO,QAAQ,CAACP,IAAD,CAAZ,EAAoB;IAClB,OAAO,KAAP;;;EAGF,OAAOA,IAAI,YAAYe,SAAS,CAACf,IAAD,CAAT,CAAgBsB,WAAvC;AACD;;SCReC,aAAavB;EAC3B,OAAOA,IAAI,YAAYe,SAAS,CAACf,IAAD,CAAT,CAAgBwB,UAAvC;AACD;;SCIeC,iBAAiBT;EAC/B,IAAI,CAACA,MAAL,EAAa;IACX,OAAOX,QAAP;;;EAGF,IAAIE,QAAQ,CAACS,MAAD,CAAZ,EAAsB;IACpB,OAAOA,MAAM,CAACX,QAAd;;;EAGF,IAAI,CAACS,MAAM,CAACE,MAAD,CAAX,EAAqB;IACnB,OAAOX,QAAP;;;EAGF,IAAIc,UAAU,CAACH,MAAD,CAAd,EAAwB;IACtB,OAAOA,MAAP;;;EAGF,IAAIK,aAAa,CAACL,MAAD,CAAb,IAAyBO,YAAY,CAACP,MAAD,CAAzC,EAAmD;IACjD,OAAOA,MAAM,CAACC,aAAd;;;EAGF,OAAOZ,QAAP;AACD;;AC1BD;;;;;AAIA,MAAaqB,yBAAyB,GAAGvB,SAAS,GAC9CwB,qBAD8C,GAE9CC;;SCNYC,SAA6BC;EAC3C,MAAMC,UAAU,GAAGC,YAAM,CAAgBF,OAAhB,CAAzB;EAEAJ,yBAAyB,CAAC;IACxBK,UAAU,CAACE,OAAX,GAAqBH,OAArB;GADuB,CAAzB;EAIA,OAAOI,iBAAW,CAAC;sCAAaC;MAAAA;;;IAC9B,OAAOJ,UAAU,CAACE,OAAlB,oBAAOF,UAAU,CAACE,OAAX,CAAqB,GAAGE,IAAxB,CAAP;GADgB,EAEf,EAFe,CAAlB;AAGD;;SCZeC;EACd,MAAMC,WAAW,GAAGL,YAAM,CAAgB,IAAhB,CAA1B;EAEA,MAAMM,GAAG,GAAGJ,iBAAW,CAAC,CAACK,QAAD,EAAqBC,QAArB;IACtBH,WAAW,CAACJ,OAAZ,GAAsBQ,WAAW,CAACF,QAAD,EAAWC,QAAX,CAAjC;GADqB,EAEpB,EAFoB,CAAvB;EAIA,MAAME,KAAK,GAAGR,iBAAW,CAAC;IACxB,IAAIG,WAAW,CAACJ,OAAZ,KAAwB,IAA5B,EAAkC;MAChCU,aAAa,CAACN,WAAW,CAACJ,OAAb,CAAb;MACAI,WAAW,CAACJ,OAAZ,GAAsB,IAAtB;;GAHqB,EAKtB,EALsB,CAAzB;EAOA,OAAO,CAACK,GAAD,EAAMI,KAAN,CAAP;AACD;;SCZeE,eACdC,OACAC;MAAAA;IAAAA,eAA+B,CAACD,KAAD;;;EAE/B,MAAME,QAAQ,GAAGf,YAAM,CAAIa,KAAJ,CAAvB;EAEAnB,yBAAyB,CAAC;IACxB,IAAIqB,QAAQ,CAACd,OAAT,KAAqBY,KAAzB,EAAgC;MAC9BE,QAAQ,CAACd,OAAT,GAAmBY,KAAnB;;GAFqB,EAItBC,YAJsB,CAAzB;EAMA,OAAOC,QAAP;AACD;;SChBeC,YACdC,UACAH;EAEA,MAAMC,QAAQ,GAAGf,YAAM,EAAvB;EAEA,OAAOjC,aAAO,CACZ;IACE,MAAMmD,QAAQ,GAAGD,QAAQ,CAACF,QAAQ,CAACd,OAAV,CAAzB;IACAc,QAAQ,CAACd,OAAT,GAAmBiB,QAAnB;IAEA,OAAOA,QAAP;GALU;EAQZ,CAAC,GAAGJ,YAAJ,CARY,CAAd;AAUD;;SCdeK,WACdC;EAKA,MAAMC,eAAe,GAAGxB,QAAQ,CAACuB,QAAD,CAAhC;EACA,MAAMpD,IAAI,GAAGgC,YAAM,CAAqB,IAArB,CAAnB;EACA,MAAMsB,UAAU,GAAGpB,iBAAW,CAC3B1B,OAAD;IACE,IAAIA,OAAO,KAAKR,IAAI,CAACiC,OAArB,EAA8B;MAC5BoB,eAAe,QAAf,YAAAA,eAAe,CAAG7C,OAAH,EAAYR,IAAI,CAACiC,OAAjB,CAAf;;;IAGFjC,IAAI,CAACiC,OAAL,GAAezB,OAAf;GAN0B;EAS5B,EAT4B,CAA9B;EAYA,OAAO,CAACR,IAAD,EAAOsD,UAAP,CAAP;AACD;;SCvBeC,YAAeV;EAC7B,MAAM3C,GAAG,GAAG8B,YAAM,EAAlB;EAEAJ,eAAS,CAAC;IACR1B,GAAG,CAAC+B,OAAJ,GAAcY,KAAd;GADO,EAEN,CAACA,KAAD,CAFM,CAAT;EAIA,OAAO3C,GAAG,CAAC+B,OAAX;AACD;;ACRD,IAAIuB,GAAG,GAA2B,EAAlC;AAEA,SAAgBC,YAAYC,QAAgBb;EAC1C,OAAO9C,aAAO,CAAC;IACb,IAAI8C,KAAJ,EAAW;MACT,OAAOA,KAAP;;;IAGF,MAAMc,EAAE,GAAGH,GAAG,CAACE,MAAD,CAAH,IAAe,IAAf,GAAsB,CAAtB,GAA0BF,GAAG,CAACE,MAAD,CAAH,GAAc,CAAnD;IACAF,GAAG,CAACE,MAAD,CAAH,GAAcC,EAAd;IAEA,OAAUD,MAAV,SAAoBC,EAApB;GARY,EASX,CAACD,MAAD,EAASb,KAAT,CATW,CAAd;AAUD;;ACfD,SAASe,kBAAT,CAA4BC,QAA5B;EACE,OAAO,UACLC,MADK;sCAEFC;MAAAA;;;IAEH,OAAOA,WAAW,CAACC,MAAZ,CACL,CAACC,WAAD,EAAcC,UAAd;MACE,MAAMC,OAAO,GAAGzD,MAAM,CAACyD,OAAP,CAAeD,UAAf,CAAhB;;MAEA,KAAK,MAAM,CAACE,GAAD,EAAMC,eAAN,CAAX,IAAqCF,OAArC,EAA8C;QAC5C,MAAMtB,KAAK,GAAGoB,WAAW,CAACG,GAAD,CAAzB;;QAEA,IAAIvB,KAAK,IAAI,IAAb,EAAmB;UACjBoB,WAAW,CAACG,GAAD,CAAX,GAAoBvB,KAAK,GAAGgB,QAAQ,GAAGQ,eAAvC;;;;MAIJ,OAAOJ,WAAP;KAZG,EAcL,EACE,GAAGH;KAfA,CAAP;GAJF;AAuBD;;AAED,MAAaQ,GAAG,gBAAGV,kBAAkB,CAAC,CAAD,CAA9B;AACP,MAAaW,QAAQ,gBAAGX,kBAAkB,CAAC,CAAC,CAAF,CAAnC;;SC3BSY,+BACdC;EAEA,OAAO,aAAaA,KAAb,IAAsB,aAAaA,KAA1C;AACD;;SCFeC,gBACdD;EAEA,IAAI,CAACA,KAAL,EAAY;IACV,OAAO,KAAP;;;EAGF,MAAM;IAACE;MAAiB5D,SAAS,CAAC0D,KAAK,CAACzD,MAAP,CAAjC;EAEA,OAAO2D,aAAa,IAAIF,KAAK,YAAYE,aAAzC;AACD;;SCVeC,aACdH;EAEA,IAAI,CAACA,KAAL,EAAY;IACV,OAAO,KAAP;;;EAGF,MAAM;IAACI;MAAc9D,SAAS,CAAC0D,KAAK,CAACzD,MAAP,CAA9B;EAEA,OAAO6D,UAAU,IAAIJ,KAAK,YAAYI,UAAtC;AACD;;ACTD;;;;AAGA,SAAgBC,oBAAoBL;EAClC,IAAIG,YAAY,CAACH,KAAD,CAAhB,EAAyB;IACvB,IAAIA,KAAK,CAACM,OAAN,IAAiBN,KAAK,CAACM,OAAN,CAAcC,MAAnC,EAA2C;MACzC,MAAM;QAACC,OAAO,EAAEC,CAAV;QAAaC,OAAO,EAAEC;UAAKX,KAAK,CAACM,OAAN,CAAc,CAAd,CAAjC;MAEA,OAAO;QACLG,CADK;QAELE;OAFF;KAHF,MAOO,IAAIX,KAAK,CAACY,cAAN,IAAwBZ,KAAK,CAACY,cAAN,CAAqBL,MAAjD,EAAyD;MAC9D,MAAM;QAACC,OAAO,EAAEC,CAAV;QAAaC,OAAO,EAAEC;UAAKX,KAAK,CAACY,cAAN,CAAqB,CAArB,CAAjC;MAEA,OAAO;QACLH,CADK;QAELE;OAFF;;;;EAOJ,IAAIZ,8BAA8B,CAACC,KAAD,CAAlC,EAA2C;IACzC,OAAO;MACLS,CAAC,EAAET,KAAK,CAACQ,OADJ;MAELG,CAAC,EAAEX,KAAK,CAACU;KAFX;;;EAMF,OAAO,IAAP;AACD;;MCpBYG,GAAG,gBAAG5E,MAAM,CAAC6E,MAAP,CAAc;EAC/BC,SAAS,EAAE;IACT5E,QAAQ,CAAC6E,SAAD;MACN,IAAI,CAACA,SAAL,EAAgB;QACd;;;MAGF,MAAM;QAACP,CAAD;QAAIE;UAAKK,SAAf;MAEA,yBAAsBP,CAAC,GAAGQ,IAAI,CAACC,KAAL,CAAWT,CAAX,CAAH,GAAmB,CAA1C,cACEE,CAAC,GAAGM,IAAI,CAACC,KAAL,CAAWP,CAAX,CAAH,GAAmB,CADtB;;;GAT2B;EAc/BQ,KAAK,EAAE;IACLhF,QAAQ,CAAC6E,SAAD;MACN,IAAI,CAACA,SAAL,EAAgB;QACd;;;MAGF,MAAM;QAACI,MAAD;QAASC;UAAUL,SAAzB;MAEA,mBAAiBI,MAAjB,iBAAmCC,MAAnC;;;GAtB2B;EAyB/BC,SAAS,EAAE;IACTnF,QAAQ,CAAC6E,SAAD;MACN,IAAI,CAACA,SAAL,EAAgB;QACd;;;MAGF,OAAO,CACLH,GAAG,CAACE,SAAJ,CAAc5E,QAAd,CAAuB6E,SAAvB,CADK,EAELH,GAAG,CAACM,KAAJ,CAAUhF,QAAV,CAAmB6E,SAAnB,CAFK,EAGLO,IAHK,CAGA,GAHA,CAAP;;;GA/B2B;EAqC/BC,UAAU,EAAE;IACVrF,QAAQ;UAAC;QAACsF,QAAD;QAAW1D,QAAX;QAAqB2D;;MAC5B,OAAUD,QAAV,SAAsB1D,QAAtB,WAAoC2D,MAApC;;;;AAvC2B,CAAd,CAAZ;;ACbP,MAAMC,QAAQ,GACZ,wIADF;AAGA,SAAgBC,uBACd7F;EAEA,IAAIA,OAAO,CAAC8F,OAAR,CAAgBF,QAAhB,CAAJ,EAA+B;IAC7B,OAAO5F,OAAP;;;EAGF,OAAOA,OAAO,CAAC+F,aAAR,CAAsBH,QAAtB,CAAP;AACD;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}
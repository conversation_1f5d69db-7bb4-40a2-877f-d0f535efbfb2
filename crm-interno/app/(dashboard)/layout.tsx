'use client';

import React, { useState } from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';

interface DashboardLayoutProps {
  children: React.ReactNode;
}

export default function DashboardLayout({ children }: DashboardLayoutProps) {
  const pathname = usePathname();
  const [activeSection, setActiveSection] = useState('businesses');

  // Determinar seção ativa baseada na URL
  React.useEffect(() => {
    if (pathname.includes('businesses')) setActiveSection('businesses');
    else if (pathname.includes('influencers')) setActiveSection('influencers');
    else if (pathname.includes('campaigns')) setActiveSection('campaigns');
    else setActiveSection('businesses');
  }, [pathname]);

  const navigationItems = [
    {
      id: 'businesses',
      label: 'Negócios',
      icon: '🏢',
      href: '/dashboard/businesses',
      count: 12
    },
    {
      id: 'influencers',
      label: 'Influenciadores',
      icon: '👥',
      href: '/dashboard/influencers',
      count: 8
    },
    {
      id: 'campaigns',
      label: 'Campanhas',
      icon: '📢',
      href: '/dashboard/campaigns',
      count: 5
    }
  ];

  return (
    <div className="min-h-screen bg-surface-dim flex">
      {/* Sidebar Navigation - Gmail Style */}
      <div className="w-64 bg-surface shadow-sm flex flex-col">
        {/* Header */}
        <div className="p-6">
          <h1 className="text-2xl font-bold text-on-surface">CRM Criadores</h1>
          <p className="text-sm text-on-surface-variant mt-1">Gestão Inteligente</p>
        </div>

        {/* Navigation Menu */}
        <nav className="flex-1 px-3">
          <div className="space-y-1">
            {navigationItems.map((item) => (
              <Link
                key={item.id}
                href={item.href}
                onClick={() => setActiveSection(item.id)}
                className={`nav-item ${activeSection === item.id ? 'active' : ''}`}
              >
                <span className="text-xl mr-3">{item.icon}</span>
                <span className="flex-1">{item.label}</span>
                <span className="text-xs bg-surface-container px-2 py-1 rounded-full">
                  {item.count}
                </span>
              </Link>
            ))}
          </div>

          {/* Divider */}
          <div className="my-6 h-px bg-outline-variant mx-4"></div>

          {/* Quick Actions */}
          <div className="space-y-1">
            <button className="nav-item w-full text-left">
              <span className="text-xl mr-3">⭐</span>
              <span className="flex-1">Favoritos</span>
            </button>
            <button className="nav-item w-full text-left">
              <span className="text-xl mr-3">📊</span>
              <span className="flex-1">Relatórios</span>
            </button>
            <button className="nav-item w-full text-left">
              <span className="text-xl mr-3">⚙️</span>
              <span className="flex-1">Configurações</span>
            </button>
          </div>
        </nav>

        {/* Footer */}
        <div className="p-4 border-t border-outline-variant">
          <div className="flex items-center space-x-3">
            <div className="w-8 h-8 bg-primary rounded-full flex items-center justify-center">
              <span className="text-on-primary text-sm font-medium">U</span>
            </div>
            <div className="flex-1">
              <p className="text-sm font-medium text-on-surface">Usuário</p>
              <p className="text-xs text-on-surface-variant">Admin</p>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content Area */}
      <div className="flex-1 flex flex-col">
        {/* Top Bar */}
        <header className="bg-surface shadow-sm px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <h2 className="text-xl font-semibold text-on-surface">
                {navigationItems.find(item => item.id === activeSection)?.label || 'Dashboard'}
              </h2>
              <span className="text-sm text-on-surface-variant">
                {navigationItems.find(item => item.id === activeSection)?.count || 0} itens
              </span>
            </div>
            
            <div className="flex items-center space-x-3">
              <button className="btn-text">
                <span className="mr-2">🔍</span>
                Buscar
              </button>
              <button className="btn-outlined">
                <span className="mr-2">📥</span>
                Importar
              </button>
              <button className="btn-primary">
                <span className="mr-2">➕</span>
                Novo
              </button>
            </div>
          </div>
        </header>

        {/* Content Area */}
        <main className="flex-1 p-6 overflow-auto">
          {children}
        </main>
      </div>
    </div>
  );
}

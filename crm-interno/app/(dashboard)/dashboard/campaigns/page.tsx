import React from 'react';
import { getData } from '@/app/actions/sheetsActions';
import { transformData } from '@/lib/utils';
import CampaignCard from '@/components/CampaignCard';

// Dados de exemplo para demonstração (serão substituídos pelos dados do Google Sheets)
const mockCampaigns = [
  {
    campaignTitle: 'Campanha Verão 2024',
    brief: 'Campanha focada em produtos de verão, destacando roupas leves e acessórios para a estação. Público-alvo: jovens de 18-35 anos.',
    startDate: '2024-01-15',
    endDate: '2024-03-15',
    status: 'Ativa'
  },
  {
    campaignTitle: 'Black Friday Especial',
    brief: 'Promoção especial para Black Friday com descontos de até 70% em produtos selecionados. Foco em conversão e volume de vendas.',
    startDate: '2024-11-20',
    endDate: '2024-11-30',
    status: 'Planejamento'
  },
  {
    campaignTitle: 'Lançamento Produto X',
    brief: 'Campanha de lançamento do novo produto X, incluindo teasers, reviews de influenciadores e eventos de lançamento.',
    startDate: '2024-02-01',
    endDate: '2024-02-28',
    status: 'Em Aprovação'
  }
];

export default async function CampaignsPage() {
  let campaigns = mockCampaigns;

  // Tenta buscar dados do Google Sheets, mas usa dados mock se falhar
  try {
    const rawData = await getData('Campaigns');
    if (rawData && rawData.length > 0) {
      const transformedData = transformData(rawData);

      // Mapeia os dados transformados para o formato esperado pelo componente
      campaigns = transformedData.map((item: any) => ({
        campaignTitle: item.campaignTitle || item.Título || item['Título da Campanha'] || 'Campanha sem título',
        brief: item.brief || item.Briefing || item.Descrição || 'Descrição não informada',
        startDate: item.startDate || item.Início || item['Data de Início'] || new Date().toISOString().split('T')[0],
        endDate: item.endDate || item.Fim || item['Data de Fim'] || new Date().toISOString().split('T')[0],
        status: item.status || item.Status || item.Situação || 'Planejamento'
      }));
    }
  } catch (error) {
    console.log('Usando dados de exemplo - Google Sheets não configurado ainda');
  }

  return (
    <div>
      <div className="mb-6">
        <h1 className="text-3xl font-bold text-on-surface mb-2">Campanhas</h1>
        <p className="text-on-surface-variant">
          Gerencie suas campanhas de marketing e acompanhe o progresso
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {campaigns.map((campaign, index) => (
          <CampaignCard
            key={index}
            campaignTitle={campaign.campaignTitle}
            brief={campaign.brief}
            startDate={campaign.startDate}
            endDate={campaign.endDate}
            status={campaign.status}
          />
        ))}
      </div>

      {campaigns.length === 0 && (
        <div className="text-center py-12">
          <div className="text-4xl mb-4">📢</div>
          <h3 className="text-lg font-medium text-on-surface mb-2">
            Nenhuma campanha encontrada
          </h3>
          <p className="text-sm text-on-surface-variant">
            Configure o Google Sheets para ver os dados das campanhas.
          </p>
        </div>
      )}
    </div>
  );
}

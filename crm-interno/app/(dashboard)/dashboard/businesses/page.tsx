import React from 'react';
import { getData } from '@/app/actions/sheetsActions';
import { transformData } from '@/lib/utils';
import BusinessCard from '@/components/BusinessCard';

// Dados de exemplo para demonstração (serão substituídos pelos dados do Google Sheets)
const mockBusinesses = [
  {
    businessName: 'Loja de Roupas Fashion',
    journeyStage: 'Negociação',
    nextAction: 'Enviar proposta final com desconto de 15%'
  },
  {
    businessName: 'Restaurante Gourmet',
    journeyStage: 'Agendamento',
    nextAction: 'Agendar reunião para apresentação do projeto'
  },
  {
    businessName: 'Academia Fitness Plus',
    journeyStage: 'Fechamento',
    nextAction: 'Aguardar assinatura do contrato'
  },
  {
    businessName: 'Clínica de Estética',
    journeyStage: 'Proposta',
    nextAction: 'Revisar briefing e ajustar orçamento'
  }
];

export default async function BusinessesPage() {
  let businesses = mockBusinesses;

  // Tenta buscar dados do Google Sheets, mas usa dados mock se falhar
  try {
    const rawData = await getData('Businesses');
    if (rawData && rawData.length > 0) {
      const transformedData = transformData(rawData);

      // Mapeia os dados transformados para o formato esperado pelo componente
      businesses = transformedData.map((item: any) => ({
        businessName: item.businessName || item.Nome || item['Nome do Negócio'] || 'Negócio não informado',
        journeyStage: item.journeyStage || item.Estágio || item['Estágio da Jornada'] || 'Agendamento',
        nextAction: item.nextAction || item.Ação || item['Próxima Ação'] || 'Definir próxima ação'
      }));
    }
  } catch (error) {
    console.log('Usando dados de exemplo - Google Sheets não configurado ainda');
  }

  return (
    <div>
      <div className="mb-6">
        <h1 className="text-3xl font-bold text-on-surface mb-2">Negócios</h1>
        <p className="text-on-surface-variant">
          Acompanhe o pipeline de vendas e gerencie seus clientes
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {businesses.map((business, index) => (
          <BusinessCard
            key={index}
            businessName={business.businessName}
            journeyStage={business.journeyStage}
            nextAction={business.nextAction}
          />
        ))}
      </div>

      {businesses.length === 0 && (
        <div className="text-center py-12">
          <div className="text-4xl mb-4">🏢</div>
          <h3 className="text-lg font-medium text-on-surface mb-2">
            Nenhum negócio encontrado
          </h3>
          <p className="text-sm text-on-surface-variant">
            Configure o Google Sheets para ver os dados dos negócios.
          </p>
        </div>
      )}
    </div>
  );
}

import React from 'react';
import { getData } from '@/app/actions/sheetsActions';
import { transformData } from '@/lib/utils';
import InfluencerCard from '@/components/InfluencerCard';

// Dados de exemplo para demonstração (serão substituídos pelos dados do Google Sheets)
const mockInfluencers = [
  {
    avatarUrl: '/placeholder-avatar.svg',
    name: '<PERSON>',
    username: 'an<PERSON><PERSON><PERSON>',
    followers: 125000,
    engagementRate: 4.2
  },
  {
    avatarUrl: '/placeholder-avatar.svg',
    name: '<PERSON>',
    username: 'carlossant<PERSON>',
    followers: 89000,
    engagementRate: 6.8
  },
  {
    avatarUrl: '/placeholder-avatar.svg',
    name: '<PERSON>',
    username: 'ma<PERSON><PERSON><PERSON><PERSON>',
    followers: 234000,
    engagementRate: 3.1
  }
];

export default async function InfluencersPage() {
  let influencers = mockInfluencers;

  // Tenta buscar dados do Google Sheets, mas usa dados mock se falhar
  try {
    const rawData = await getData('Influencers');
    if (rawData && rawData.length > 0) {
      const transformedData = transformData(rawData);

      // Mapeia os dados transformados para o formato esperado pelo componente
      influencers = transformedData.map((item: any) => ({
        avatarUrl: item.avatarUrl || '/placeholder-avatar.svg',
        name: item.name || item.Nome || 'Nome não informado',
        username: item.username || item.Username || 'username',
        followers: parseInt(item.followers || item.Seguidores || '0'),
        engagementRate: parseFloat(item.engagementRate || item.Engajamento || '0')
      }));
    }
  } catch (error) {
    console.log('Usando dados de exemplo - Google Sheets não configurado ainda');
  }

  return (
    <div>
      <div className="mb-6">
        <h1 className="text-3xl font-bold text-on-surface mb-2">Influenciadores</h1>
        <p className="text-on-surface-variant">
          Gerencie sua rede de influenciadores e suas métricas de engajamento
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {influencers.map((influencer, index) => (
          <InfluencerCard
            key={index}
            avatarUrl={influencer.avatarUrl}
            name={influencer.name}
            username={influencer.username}
            followers={influencer.followers}
            engagementRate={influencer.engagementRate}
          />
        ))}
      </div>

      {influencers.length === 0 && (
        <div className="text-center py-12">
          <div className="text-4xl mb-4">👥</div>
          <h3 className="text-lg font-medium text-on-surface mb-2">
            Nenhum influenciador encontrado
          </h3>
          <p className="text-sm text-on-surface-variant">
            Configure o Google Sheets para ver os dados dos influenciadores.
          </p>
        </div>
      )}
    </div>
  );
}

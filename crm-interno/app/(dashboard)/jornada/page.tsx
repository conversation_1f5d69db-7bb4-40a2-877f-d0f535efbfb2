'use client';

import React, { useState } from 'react';
import { getData } from '@/app/actions/sheetsActions';
import { transformData } from '@/lib/utils';
import BusinessDetailModal from '@/components/BusinessDetailModal';

// Dados de exemplo para demonstração (serão substituídos pelos dados do Google Sheets)
const mockBusinesses = [
  {
    id: 1,
    businessName: 'Loja de Roupas Fashion',
    journeyStage: 'Agendamentos',
    nextAction: 'Agendar sessões de fotos com influenciadores',
    contactDate: '2024-01-15',
    value: 15000,
    description: 'Campanha de verão focada em roupas casuais para jovens de 18-30 anos',
    influencers: [
      { name: '<PERSON>', username: 'anasil<PERSON>', followers: 125000, engagementRate: 4.2 },
      { name: '<PERSON>', username: 'carlossantos', followers: 89000, engagementRate: 6.8 }
    ],
    campaigns: [
      { title: 'Campanha Verão 2024', status: 'Ativa', startDate: '2024-01-15', endDate: '2024-03-15' }
    ]
  },
  {
    id: 2,
    businessName: 'Restaurante Gourmet',
    journeyStage: 'Reunião Briefing',
    nextAction: 'Definir estratégia de conteúdo gastronômico',
    contactDate: '2024-01-10',
    value: 8000,
    description: 'Divulgação de pratos especiais e experiência gastronômica única',
    influencers: [
      { name: 'Maria Oliveira', username: 'mariaoliveira', followers: 234000, engagementRate: 3.1 }
    ],
    campaigns: []
  },
  {
    id: 3,
    businessName: 'Academia Fitness Plus',
    journeyStage: 'Entrega Final',
    nextAction: 'Finalizar edição dos vídeos de treino',
    contactDate: '2024-01-20',
    value: 25000,
    description: 'Campanha de motivação fitness com foco em resultados reais',
    influencers: [
      { name: 'João Fitness', username: 'joaofitness', followers: 156000, engagementRate: 5.4 },
      { name: 'Carla Strong', username: 'carlastrong', followers: 98000, engagementRate: 7.2 },
      { name: 'Pedro Muscle', username: 'pedromuscle', followers: 67000, engagementRate: 4.8 }
    ],
    campaigns: [
      { title: 'Transformação 90 Dias', status: 'Ativa', startDate: '2024-01-01', endDate: '2024-03-31' }
    ]
  },
  {
    id: 4,
    businessName: 'Clínica de Estética',
    journeyStage: 'Reunião Briefing',
    nextAction: 'Alinhar diretrizes de comunicação sobre procedimentos',
    contactDate: '2024-01-12',
    value: 12000,
    description: 'Divulgação de tratamentos estéticos com foco em naturalidade',
    influencers: [
      { name: 'Bella Beauty', username: 'bellabeauty', followers: 189000, engagementRate: 6.1 }
    ],
    campaigns: []
  },
  {
    id: 5,
    businessName: 'Loja de Eletrônicos',
    journeyStage: 'Agendamentos',
    nextAction: 'Coordenar reviews de produtos com tech influencers',
    contactDate: '2024-01-08',
    value: 18000,
    description: 'Reviews autênticos de gadgets e eletrônicos inovadores',
    influencers: [
      { name: 'Tech Master', username: 'techmaster', followers: 145000, engagementRate: 5.9 },
      { name: 'Gamer Pro', username: 'gamerpro', followers: 203000, engagementRate: 4.5 }
    ],
    campaigns: [
      { title: 'Tech Reviews 2024', status: 'Planejamento', startDate: '2024-02-01', endDate: '2024-04-30' }
    ]
  }
];

// Definir as fases da jornada (3 fases principais)
const journeyStages = [
  { id: 'Reunião Briefing', label: 'Reunião Briefing', color: 'bg-blue-100 text-blue-800', icon: '📋' },
  { id: 'Agendamentos', label: 'Agendamentos', color: 'bg-yellow-100 text-yellow-800', icon: '📅' },
  { id: 'Entrega Final', label: 'Entrega Final', color: 'bg-green-100 text-green-800', icon: '✅' }
];

export default function JornadaPage() {
  const [businesses, setBusinesses] = useState(mockBusinesses);
  const [selectedBusiness, setSelectedBusiness] = useState<any>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);

  // Carregar dados do Google Sheets
  React.useEffect(() => {
    async function loadData() {
      try {
        const rawData = await getData('Businesses');
        if (rawData && rawData.length > 0) {
          const transformedData = transformData(rawData);

          // Mapeia os dados transformados para o formato esperado pelo componente
          const businessesData = transformedData.map((item: any) => ({
            id: item.id || Math.random(),
            businessName: item.businessName || item.Nome || item['Nome do Negócio'] || 'Negócio não informado',
            journeyStage: item.journeyStage || item.Estágio || item['Estágio da Jornada'] || 'Reunião Briefing',
            nextAction: item.nextAction || item.Ação || item['Próxima Ação'] || 'Definir próxima ação',
            contactDate: item.contactDate || item.Data || item['Data de Contato'] || new Date().toISOString().split('T')[0],
            value: parseInt(item.value || item.Valor || item['Valor do Negócio'] || '0'),
            description: item.description || item.Descrição || 'Descrição não informada',
            influencers: JSON.parse(item.influencers || '[]'),
            campaigns: JSON.parse(item.campaigns || '[]')
          }));
          setBusinesses(businessesData);
        }
      } catch (error) {
        console.log('Usando dados de exemplo - Google Sheets não configurado ainda');
      }
    }

    loadData();
  }, []);

  const handleBusinessClick = (business: any) => {
    setSelectedBusiness(business);
    setIsModalOpen(true);
  };

  const handleCloseModal = () => {
    setIsModalOpen(false);
    setSelectedBusiness(null);
  };



  // Agrupar negócios por fase da jornada
  const businessesByStage = journeyStages.map(stage => ({
    ...stage,
    businesses: businesses.filter(business => business.journeyStage === stage.id),
    totalValue: businesses
      .filter(business => business.journeyStage === stage.id)
      .reduce((sum, business) => sum + business.value, 0)
  }));

  return (
    <div className="space-y-6">
      {/* Header com estatísticas gerais */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div className="card-elevated p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-on-surface-variant">Total de Negócios</p>
              <p className="text-2xl font-bold text-on-surface">{businesses.length}</p>
            </div>
            <div className="text-2xl">🏢</div>
          </div>
        </div>
        
        <div className="card-elevated p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-on-surface-variant">Valor Total</p>
              <p className="text-2xl font-bold text-primary">
                R$ {(businesses.reduce((sum, b) => sum + b.value, 0) / 1000).toFixed(0)}K
              </p>
            </div>
            <div className="text-2xl">💰</div>
          </div>
        </div>
        
        <div className="card-elevated p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-on-surface-variant">Em Fechamento</p>
              <p className="text-2xl font-bold text-green-600">
                {businesses.filter(b => b.journeyStage === 'Fechamento').length}
              </p>
            </div>
            <div className="text-2xl">🎯</div>
          </div>
        </div>
        
        <div className="card-elevated p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-on-surface-variant">Taxa Conversão</p>
              <p className="text-2xl font-bold text-secondary">
                {businesses.length > 0 ? Math.round((businesses.filter(b => b.journeyStage === 'Pós-venda').length / businesses.length) * 100) : 0}%
              </p>
            </div>
            <div className="text-2xl">📈</div>
          </div>
        </div>
      </div>

      {/* Kanban da Jornada - 3 Colunas */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {businessesByStage.map((stage) => (
          <div key={stage.id} className="card-elevated p-6 min-h-96">
            {/* Header da coluna */}
            <div className="mb-6">
              <div className="flex items-center justify-between mb-3">
                <div className="flex items-center">
                  <span className="text-2xl mr-3">{stage.icon}</span>
                  <h3 className="text-lg font-semibold text-on-surface">{stage.label}</h3>
                </div>
                <span className="text-sm bg-surface-container px-3 py-1 rounded-full font-medium">
                  {stage.businesses.length}
                </span>
              </div>

              <div className="text-sm text-on-surface-variant">
                Total: R$ {(stage.totalValue / 1000).toFixed(0)}K
              </div>
            </div>

            {/* Cards dos negócios */}
            <div className="space-y-4">
              {stage.businesses.map((business, index) => (
                <div
                  key={index}
                  className="bg-surface-container rounded-lg p-4 hover:shadow-md transition-all duration-200 cursor-pointer border-l-4 border-primary"
                  onClick={() => handleBusinessClick(business)}
                >
                  <h4 className="font-semibold text-on-surface mb-2">
                    {business.businessName}
                  </h4>

                  <p className="text-sm text-on-surface-variant mb-3 line-clamp-2">
                    {business.nextAction}
                  </p>

                  {/* Influenciadores */}
                  <div className="flex items-center mb-3">
                    <span className="text-xs text-on-surface-variant mr-2">👥</span>
                    <span className="text-sm font-medium text-secondary">
                      {business.influencers.length} influenciador{business.influencers.length !== 1 ? 'es' : ''}
                    </span>
                  </div>

                  <div className="flex items-center justify-between text-sm">
                    <span className="text-on-surface-variant">
                      {new Date(business.contactDate).toLocaleDateString('pt-BR')}
                    </span>
                    <span className="font-bold text-primary">
                      R$ {(business.value / 1000).toFixed(0)}K
                    </span>
                  </div>

                  {/* Indicador de clique */}
                  <div className="mt-2 text-xs text-on-surface-variant opacity-70">
                    Clique para ver detalhes →
                  </div>
                </div>
              ))}

              {stage.businesses.length === 0 && (
                <div className="text-center py-12 text-on-surface-variant">
                  <div className="text-4xl mb-3">{stage.icon}</div>
                  <p className="text-sm">Nenhum negócio nesta fase</p>
                  <p className="text-xs mt-1">Arraste projetos para cá</p>
                </div>
              )}
            </div>
          </div>
        ))}
      </div>

      {/* Modal de Detalhes */}
      <BusinessDetailModal
        business={selectedBusiness}
        isOpen={isModalOpen}
        onClose={handleCloseModal}
      />
    </div>
  );
}

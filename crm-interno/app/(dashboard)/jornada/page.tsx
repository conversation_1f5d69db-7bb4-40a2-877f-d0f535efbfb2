import React from 'react';
import { getData } from '@/app/actions/sheetsActions';
import { transformData } from '@/lib/utils';

// Dados de exemplo para demonstração (serão substituídos pelos dados do Google Sheets)
const mockBusinesses = [
  {
    businessName: 'Loja de Roupas Fashion',
    journeyStage: 'Negociação',
    nextAction: 'Enviar proposta final com desconto de 15%',
    contactDate: '2024-01-15',
    value: 15000
  },
  {
    businessName: 'Restaurante Gourmet',
    journeyStage: 'Agendamento',
    nextAction: 'Agendar reunião para apresentação do projeto',
    contactDate: '2024-01-10',
    value: 8000
  },
  {
    businessName: 'Academia Fitness Plus',
    journeyStage: 'Fechamento',
    nextAction: 'Aguardar assinatura do contrato',
    contactDate: '2024-01-20',
    value: 25000
  },
  {
    businessName: 'Clínica de Estética',
    journeyStage: 'Proposta',
    nextAction: 'Revisar briefing e ajustar orçamento',
    contactDate: '2024-01-12',
    value: 12000
  },
  {
    businessName: 'Loja de Eletrônicos',
    journeyStage: 'Agendamento',
    nextAction: 'Primeira reunião de apresentação',
    contactDate: '2024-01-08',
    value: 18000
  },
  {
    businessName: 'Consultoria Empresarial',
    journeyStage: 'Pós-venda',
    nextAction: 'Acompanhamento mensal do projeto',
    contactDate: '2024-01-05',
    value: 30000
  }
];

// Definir as fases da jornada
const journeyStages = [
  { id: 'Agendamento', label: 'Agendamento', color: 'bg-blue-100 text-blue-800', icon: '📅' },
  { id: 'Proposta', label: 'Proposta', color: 'bg-yellow-100 text-yellow-800', icon: '📋' },
  { id: 'Negociação', label: 'Negociação', color: 'bg-orange-100 text-orange-800', icon: '🤝' },
  { id: 'Fechamento', label: 'Fechamento', color: 'bg-green-100 text-green-800', icon: '✅' },
  { id: 'Pós-venda', label: 'Pós-venda', color: 'bg-purple-100 text-purple-800', icon: '🎯' },
  { id: 'Perdido', label: 'Perdido', color: 'bg-red-100 text-red-800', icon: '❌' }
];

export default async function JornadaPage() {
  let businesses = mockBusinesses;

  // Tenta buscar dados do Google Sheets, mas usa dados mock se falhar
  try {
    const rawData = await getData('Businesses');
    if (rawData && rawData.length > 0) {
      const transformedData = transformData(rawData);
      
      // Mapeia os dados transformados para o formato esperado pelo componente
      businesses = transformedData.map((item: any) => ({
        businessName: item.businessName || item.Nome || item['Nome do Negócio'] || 'Negócio não informado',
        journeyStage: item.journeyStage || item.Estágio || item['Estágio da Jornada'] || 'Agendamento',
        nextAction: item.nextAction || item.Ação || item['Próxima Ação'] || 'Definir próxima ação',
        contactDate: item.contactDate || item.Data || item['Data de Contato'] || new Date().toISOString().split('T')[0],
        value: parseInt(item.value || item.Valor || item['Valor do Negócio'] || '0')
      }));
    }
  } catch (error) {
    console.log('Usando dados de exemplo - Google Sheets não configurado ainda');
  }

  // Agrupar negócios por fase da jornada
  const businessesByStage = journeyStages.map(stage => ({
    ...stage,
    businesses: businesses.filter(business => business.journeyStage === stage.id),
    totalValue: businesses
      .filter(business => business.journeyStage === stage.id)
      .reduce((sum, business) => sum + business.value, 0)
  }));

  return (
    <div className="space-y-6">
      {/* Header com estatísticas gerais */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div className="card-elevated p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-on-surface-variant">Total de Negócios</p>
              <p className="text-2xl font-bold text-on-surface">{businesses.length}</p>
            </div>
            <div className="text-2xl">🏢</div>
          </div>
        </div>
        
        <div className="card-elevated p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-on-surface-variant">Valor Total</p>
              <p className="text-2xl font-bold text-primary">
                R$ {(businesses.reduce((sum, b) => sum + b.value, 0) / 1000).toFixed(0)}K
              </p>
            </div>
            <div className="text-2xl">💰</div>
          </div>
        </div>
        
        <div className="card-elevated p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-on-surface-variant">Em Fechamento</p>
              <p className="text-2xl font-bold text-green-600">
                {businesses.filter(b => b.journeyStage === 'Fechamento').length}
              </p>
            </div>
            <div className="text-2xl">🎯</div>
          </div>
        </div>
        
        <div className="card-elevated p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-on-surface-variant">Taxa Conversão</p>
              <p className="text-2xl font-bold text-secondary">
                {businesses.length > 0 ? Math.round((businesses.filter(b => b.journeyStage === 'Pós-venda').length / businesses.length) * 100) : 0}%
              </p>
            </div>
            <div className="text-2xl">📈</div>
          </div>
        </div>
      </div>

      {/* Kanban da Jornada */}
      <div className="grid grid-cols-1 lg:grid-cols-3 xl:grid-cols-6 gap-4">
        {businessesByStage.map((stage) => (
          <div key={stage.id} className="card-elevated p-4">
            {/* Header da coluna */}
            <div className="mb-4">
              <div className="flex items-center justify-between mb-2">
                <div className="flex items-center">
                  <span className="text-lg mr-2">{stage.icon}</span>
                  <h3 className="font-semibold text-on-surface">{stage.label}</h3>
                </div>
                <span className="text-xs bg-surface-container px-2 py-1 rounded-full">
                  {stage.businesses.length}
                </span>
              </div>
              
              <div className="text-xs text-on-surface-variant">
                Total: R$ {(stage.totalValue / 1000).toFixed(0)}K
              </div>
            </div>

            {/* Cards dos negócios */}
            <div className="space-y-3">
              {stage.businesses.map((business, index) => (
                <div key={index} className="bg-surface-container rounded-lg p-3 hover:shadow-sm transition-shadow">
                  <h4 className="font-medium text-sm text-on-surface mb-2">
                    {business.businessName}
                  </h4>
                  
                  <p className="text-xs text-on-surface-variant mb-2 line-clamp-2">
                    {business.nextAction}
                  </p>
                  
                  <div className="flex items-center justify-between text-xs">
                    <span className="text-on-surface-variant">
                      {new Date(business.contactDate).toLocaleDateString('pt-BR')}
                    </span>
                    <span className="font-medium text-primary">
                      R$ {(business.value / 1000).toFixed(0)}K
                    </span>
                  </div>
                </div>
              ))}
              
              {stage.businesses.length === 0 && (
                <div className="text-center py-8 text-on-surface-variant">
                  <div className="text-2xl mb-2">{stage.icon}</div>
                  <p className="text-xs">Nenhum negócio nesta fase</p>
                </div>
              )}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}

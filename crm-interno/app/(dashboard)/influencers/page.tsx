import React from 'react';
import { getData } from '@/app/actions/sheetsActions';
import { transformData } from '@/lib/utils';
import InfluencerCard from '@/components/InfluencerCard';

// Dados de exemplo para demonstração (serão substituídos pelos dados do Google Sheets)
const mockInfluencers = [
  {
    avatarUrl: '/placeholder-avatar.svg',
    name: '<PERSON>',
    username: 'an<PERSON><PERSON><PERSON>',
    followers: 125000,
    engagementRate: 4.2
  },
  {
    avatarUrl: '/placeholder-avatar.svg',
    name: '<PERSON>',
    username: 'carlossant<PERSON>',
    followers: 89000,
    engagementRate: 6.8
  },
  {
    avatarUrl: '/placeholder-avatar.svg',
    name: '<PERSON>',
    username: 'ma<PERSON><PERSON><PERSON><PERSON>',
    followers: 234000,
    engagementRate: 3.1
  }
];

export default async function InfluencersPage() {
  let influencers = mockInfluencers;

  // Tenta buscar dados do Google Sheets, mas usa dados mock se falhar
  try {
    const rawData = await getData('Influencers');
    if (rawData && rawData.length > 0) {
      const transformedData = transformData(rawData);

      // Mapeia os dados transformados para o formato esperado pelo componente
      influencers = transformedData.map((item: any) => ({
        avatarUrl: item.avatarUrl || '/placeholder-avatar.svg',
        name: item.name || item.Nome || 'Nome não informado',
        username: item.username || item.Username || 'username',
        followers: parseInt(item.followers || item.Seguidores || '0'),
        engagementRate: parseFloat(item.engagementRate || item.Engajamento || '0')
      }));
    }
  } catch (error) {
    console.log('Usando dados de exemplo - Google Sheets não configurado ainda');
  }

  return (
    <div className="space-y-6">
      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div className="card-elevated p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-on-surface-variant">Total</p>
              <p className="text-2xl font-bold text-on-surface">{influencers.length}</p>
            </div>
            <div className="text-2xl">👥</div>
          </div>
        </div>

        <div className="card-elevated p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-on-surface-variant">Alto Engajamento</p>
              <p className="text-2xl font-bold text-green-600">
                {influencers.filter(i => i.engagementRate >= 5).length}
              </p>
            </div>
            <div className="text-2xl">🔥</div>
          </div>
        </div>

        <div className="card-elevated p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-on-surface-variant">Seguidores Totais</p>
              <p className="text-2xl font-bold text-primary">
                {(influencers.reduce((acc, i) => acc + i.followers, 0) / 1000000).toFixed(1)}M
              </p>
            </div>
            <div className="text-2xl">📊</div>
          </div>
        </div>

        <div className="card-elevated p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-on-surface-variant">Engajamento Médio</p>
              <p className="text-2xl font-bold text-secondary">
                {(influencers.reduce((acc, i) => acc + i.engagementRate, 0) / influencers.length || 0).toFixed(1)}%
              </p>
            </div>
            <div className="text-2xl">⚡</div>
          </div>
        </div>
      </div>

      {/* Influencers Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {influencers.map((influencer, index) => (
          <InfluencerCard
            key={index}
            avatarUrl={influencer.avatarUrl}
            name={influencer.name}
            username={influencer.username}
            followers={influencer.followers}
            engagementRate={influencer.engagementRate}
          />
        ))}
      </div>

      {influencers.length === 0 && (
        <div className="text-center py-12">
          <div className="text-6xl mb-4">👥</div>
          <h3 className="text-xl font-medium text-on-surface mb-2">
            Nenhum influenciador encontrado
          </h3>
          <p className="text-on-surface-variant mb-6">
            Configure o Google Sheets para ver os dados dos influenciadores.
          </p>
          <button className="btn-primary">
            <span className="mr-2">➕</span>
            Adicionar Primeiro Influenciador
          </button>
        </div>
      )}
    </div>
  );
}

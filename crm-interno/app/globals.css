@import "tailwindcss";

:root {
  /* Material Design 3 Color System - Gmail Style */
  /* Primary Colors (Blue) */
  --primary: #1976d2;
  --on-primary: #ffffff;
  --primary-container: #d3e3fd;
  --on-primary-container: #001c38;

  /* Secondary Colors */
  --secondary: #5f6368;
  --on-secondary: #ffffff;
  --secondary-container: #e8eaed;
  --on-secondary-container: #1f1f1f;

  /* Tertiary Colors */
  --tertiary: #7c4dff;
  --on-tertiary: #ffffff;
  --tertiary-container: #e8e2ff;
  --on-tertiary-container: #1a0033;

  /* Error Colors */
  --error: #d93025;
  --on-error: #ffffff;
  --error-container: #fce8e6;
  --on-error-container: #410002;

  /* Surface Colors (Gmail Gray Palette) */
  --surface: #ffffff;
  --on-surface: #1f1f1f;
  --surface-variant: #f8f9fa;
  --on-surface-variant: #5f6368;
  --surface-container-lowest: #ffffff;
  --surface-container-low: #f8f9fa;
  --surface-container: #f1f3f4;
  --surface-container-high: #e8eaed;
  --surface-container-highest: #dadce0;
  --surface-dim: #f5f5f5;
  --surface-bright: #ffffff;

  /* Outline Colors */
  --outline: #dadce0;
  --outline-variant: #e8eaed;

  /* Other Colors */
  --inverse-surface: #2d2e30;
  --inverse-on-surface: #f1f3f4;
  --inverse-primary: #a8c7fa;
  --shadow: #000000;
  --scrim: #000000;

  /* Legacy variables for compatibility */
  --background: var(--surface);
  --foreground: var(--on-surface);
}

@theme inline {
  /* Material Design 3 Color Classes */
  --color-primary: var(--primary);
  --color-on-primary: var(--on-primary);
  --color-primary-container: var(--primary-container);
  --color-on-primary-container: var(--on-primary-container);

  --color-secondary: var(--secondary);
  --color-on-secondary: var(--on-secondary);
  --color-secondary-container: var(--secondary-container);
  --color-on-secondary-container: var(--on-secondary-container);

  --color-tertiary: var(--tertiary);
  --color-on-tertiary: var(--on-tertiary);
  --color-tertiary-container: var(--tertiary-container);
  --color-on-tertiary-container: var(--on-tertiary-container);

  --color-error: var(--error);
  --color-on-error: var(--on-error);
  --color-error-container: var(--error-container);
  --color-on-error-container: var(--on-error-container);

  --color-surface: var(--surface);
  --color-on-surface: var(--on-surface);
  --color-surface-variant: var(--surface-variant);
  --color-on-surface-variant: var(--on-surface-variant);
  --color-surface-container-lowest: var(--surface-container-lowest);
  --color-surface-container-low: var(--surface-container-low);
  --color-surface-container: var(--surface-container);
  --color-surface-container-high: var(--surface-container-high);
  --color-surface-container-highest: var(--surface-container-highest);
  --color-surface-dim: var(--surface-dim);
  --color-surface-bright: var(--surface-bright);

  --color-outline: var(--outline);
  --color-outline-variant: var(--outline-variant);

  --color-inverse-surface: var(--inverse-surface);
  --color-inverse-on-surface: var(--inverse-on-surface);
  --color-inverse-primary: var(--inverse-primary);
  --color-shadow: var(--shadow);
  --color-scrim: var(--scrim);

  /* Legacy compatibility */
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

/* Gmail-style Material Design 3 Components */
.btn-primary {
  @apply bg-primary text-on-primary px-6 py-2 rounded-full font-medium transition-all duration-200 hover:shadow-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2;
}

.btn-outlined {
  @apply border border-outline text-primary px-6 py-2 rounded-full font-medium transition-all duration-200 hover:bg-primary-container focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2;
}

.btn-text {
  @apply text-primary px-4 py-2 rounded-full font-medium transition-all duration-200 hover:bg-primary-container focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2;
}

.card-elevated {
  @apply bg-surface rounded-xl shadow-sm border border-outline-variant transition-all duration-200 hover:shadow-md;
}

.nav-tab {
  @apply flex items-center px-6 py-3 rounded-lg font-medium transition-all duration-200 cursor-pointer;
}

.nav-tab:hover {
  @apply bg-surface-container text-on-surface;
}

.nav-tab.active {
  @apply bg-primary-container text-on-primary-container;
}

.kanban-column {
  @apply bg-surface rounded-xl p-4 min-h-96;
}

.kanban-card {
  @apply bg-surface-container rounded-lg p-3 mb-3 hover:shadow-sm transition-all duration-200 cursor-pointer;
}

.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Drag & Drop Styles */
.dragging {
  opacity: 0.5;
  transform: rotate(5deg);
  z-index: 1000;
}

.drag-overlay {
  transform: rotate(5deg) scale(1.05);
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
}

.drop-zone-active {
  background-color: var(--primary-container);
  border: 2px dashed var(--primary);
  transform: scale(1.02);
}

.drop-zone-active::before {
  content: "↓ Solte aqui";
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: var(--primary);
  font-weight: 600;
  font-size: 0.875rem;
  animation: pulse 1s infinite;
}

/* Animações */
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.business-card-dragging {
  cursor: grabbing !important;
  transform: rotate(2deg) scale(1.05);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
  z-index: 1000;
}

.business-card-drag-handle {
  cursor: grab;
}

.business-card-drag-handle:active {
  cursor: grabbing;
}

/* Focus states */
.btn-primary:focus,
.btn-outlined:focus,
.btn-text:focus {
  outline: 2px solid var(--primary) !important;
  outline-offset: 2px !important;
}

@media (prefers-color-scheme: dark) {
  :root {
    /* Material 3 Color System - Dark Theme */
    --primary: #9CCCFF;
    --on-primary: #003353;
    --primary-container: #004A75;
    --on-primary-container: #CFE5FF;

    --secondary: #B9C8D8;
    --on-secondary: #24323E;
    --secondary-container: #3B4855;
    --on-secondary-container: #D5E4F4;

    --tertiary: #D3BFE6;
    --on-tertiary: #392B49;
    --tertiary-container: #504160;
    --on-tertiary-container: #EFDBFF;

    --error: #FFB4AB;
    --on-error: #690005;
    --error-container: #93000A;
    --on-error-container: #FFDAD6;

    --surface: #101418;
    --on-surface: #E1E2E8;
    --surface-variant: #43474E;
    --on-surface-variant: #C3C7CF;
    --surface-container-lowest: #0B0F13;
    --surface-container-low: #191C20;
    --surface-container: #1D2024;
    --surface-container-high: #272A2F;
    --surface-container-highest: #32353A;
    --surface-dim: #101418;
    --surface-bright: #36393E;

    --outline: #8D9199;
    --outline-variant: #43474E;

    --inverse-surface: #E1E2E8;
    --inverse-on-surface: #2E3135;
    --inverse-primary: #00629B;
    --shadow: #000000;
    --scrim: #000000;

    /* Legacy variables for compatibility */
    --background: var(--surface);
    --foreground: var(--on-surface);
  }
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: Arial, Helvetica, sans-serif;
  margin: 0;
  padding: 0;
}

/* Ensure proper styling for Material Design components */
.bg-surface-dim {
  background-color: var(--surface-dim) !important;
}

.bg-surface {
  background-color: var(--surface) !important;
}

.text-on-surface {
  color: var(--on-surface) !important;
}

.text-on-surface-variant {
  color: var(--on-surface-variant) !important;
}

.bg-primary {
  background-color: var(--primary) !important;
}

.text-on-primary {
  color: var(--on-primary) !important;
}

.bg-primary-container {
  background-color: var(--primary-container) !important;
}

.text-on-primary-container {
  color: var(--on-primary-container) !important;
}

.bg-surface-container {
  background-color: var(--surface-container) !important;
}

.bg-surface-container-high {
  background-color: var(--surface-container-high) !important;
}

.text-primary {
  color: var(--primary) !important;
}

.text-secondary {
  color: var(--secondary) !important;
}
